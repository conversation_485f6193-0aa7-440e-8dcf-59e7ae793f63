import {Component, EventEmitter, Input, Output} from '@angular/core';
import {AttachmentManagerPrintable} from "@oscar-pro/feature/attachment-manager";
import {PrintableEnum} from "../../enums/printable.enum";
@Component({
    selector: 'oscar-pro-attachment-manager-manage-selected',
    templateUrl: './attachment-manager-manage-selected.component.html',
    styleUrls: ['./attachment-manager-manage-selected.component.scss'],
    standalone: false
})
export class AttachmentManagerManageSelectedComponent {

  @Input() selectedPrintables: AttachmentManagerPrintable[] = [];
  @Output() printableDeselected: EventEmitter<AttachmentManagerPrintable[]> =
    new EventEmitter<AttachmentManagerPrintable[]>();
  @Output() closed: EventEmitter<void> = new EventEmitter<void>();
  @Output() previewed: EventEmitter<AttachmentManagerPrintable> =
    new EventEmitter<AttachmentManagerPrintable>();

  protected readonly PrintableEnum = PrintableEnum;

  deselectPrintable(printable: AttachmentManagerPrintable): void {
    this.printableDeselected.emit([printable]);
  }

  deselectAllPrintables(): void {
    this.printableDeselected.emit(this.selectedPrintables);
  }

  handleClose(): void {
    this.closed.emit();
  }

  handlePreviewPrintable(printable: AttachmentManagerPrintable): void {
    this.previewed.emit(printable);
  }
}
