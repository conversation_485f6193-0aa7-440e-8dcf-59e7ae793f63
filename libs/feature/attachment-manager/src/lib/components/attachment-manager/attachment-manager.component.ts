import { Component, OnInit, ViewChild } from '@angular/core';
import {
  ApiAttachmentManagerService,
  ApiOceanService,
  ApiPropertyService,
  ApiSystemPreferenceService,
  SystemPreferenceKey,
  UserPropertyKey
} from "@oscar-pro/data-access";
import {
  AttachmentManagerPrintable,
  AttachmentManagerSelectEvent, getByPrintWeightKey,
  CategoryOrderType,
  PrintableEnum,
  PrintableEnumEntry,
  Recipient
} from "@oscar-pro/feature/attachment-manager";
import { ActivatedRoute } from "@angular/router";
import { CheckboxState } from "@oscar-pro/weg-ui";
import { ToastrService } from "ngx-toastr";
import { DownloadService, LogService } from "@oscar-pro/util";
import {
  AttachmentManagerFaxComponent
} from '../attachment-manager-fax/attachment-manager-fax.component';
import {
  AttachmentManagerBaseOceanComponent
} from "../attachment-manager-base-ocean/attachment-manager-base-ocean.component";
import {OceanWarningModalComponent} from "../weg-prompt-modal/ocean-warning-modal.component";

@Component({
    selector: 'oscar-pro-attachment-manager',
    templateUrl: './attachment-manager.component.html',
    styleUrls: ['./attachment-manager.component.scss'],
    standalone: false
})
export class AttachmentManagerComponent extends AttachmentManagerBaseOceanComponent implements OnInit {
  @ViewChild('faxComponent') faxComponent!: AttachmentManagerFaxComponent;
  @ViewChild('promptModal') promptModal!: OceanWarningModalComponent;

  MAXIMUM_ATTACHMENT_JSON_LENGTH = 65535;

  override selectedPrintables: AttachmentManagerPrintable[] = [];
  sortedPrintableKeys: string[] = [];
  printablesMap: Map<string, AttachmentManagerPrintable[]> = new Map<string, AttachmentManagerPrintable[]>();

  activeTabId: TabId = 'attachmentsTab';
  activePanelTabId: PanelTabId = 'panelManageSelectedTab';

  /* Properties that are subsets of selectedPrintables and printablesMap respectively.
    * These properties are dynamically updated based on selection and search change events.
    * The purpose of these properties is to minimize the number of function calls during change
    * events and avoid iterating over the entire selectedPrintables/printablesMap
   */
  // Caches selected printables by groupKey to be fetched by the UI
  selectedPrintablesByType: Map<string, AttachmentManagerPrintable[]> = new Map<string, AttachmentManagerPrintable[]>();
  // Caches list of filtered and sorted printables to be fetched by the UI
  filteredPrintables: AttachmentManagerPrintable[] = [];

  faxRecipients: Recipient[] = [];

  providerNumber = '';
  searchKey = "";

  isFaxing = false;
  isPrinting = false;

  isPanelOpen = false;

  attachmentManagerDocumentationURL = '';
  categoryOrderType = CategoryOrderType.FrequentlyUsed;
  displayEmptyCategories = true;
  isSettingsPanelOpen = false;

  previewPrintable: AttachmentManagerPrintable | null = null;

  attachmentControlPrintablesElementId = 'attachment-control-printables';
  attachmentControlPrintablesElement: HTMLInputElement = window.opener?.document.getElementById(this.attachmentControlPrintablesElementId);

  coverPageState = false;

  // Map of printable types and the estimated time it takes to print each type
  printWeights: Record<string, number> = {};
  printingThreshold = 50;

  protected readonly PrintableEnum = PrintableEnum;

  protected isOceanEnabled = false;
  protected override isOceanEmailEnabled = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    apiAttachmentManagerService: ApiAttachmentManagerService,
    private apiOceanService: ApiOceanService,
    apiSystemPreferenceService: ApiSystemPreferenceService,
    private apiPropertyService: ApiPropertyService,
    private downloadService: DownloadService,
    private log: LogService,
    toastrService: ToastrService,
  ) {
    super(apiAttachmentManagerService, apiSystemPreferenceService, toastrService);
  }

  ngOnInit(): void {
    // get parameters from URL
    const params = this.activatedRoute.snapshot.queryParams;
    this.demographicNumber = params['demographicNumber'];
    this.providerNumber = params['providerNumber'];

    this.apiPropertyService.getProperty(
      SystemPreferenceKey.AttachmentManagerCategoryOrder
    ).subscribe(
      (property) => {
        if (property && property.value === CategoryOrderType.Alphabetical) {
          this.categoryOrderType = CategoryOrderType.Alphabetical;
        } else {
          this.categoryOrderType = CategoryOrderType.FrequentlyUsed; // Default to frequently used
        }
        this.loadPrintables();
      },
      () => {
        // If there's an error reading the preference, use the default
        this.categoryOrderType = CategoryOrderType.FrequentlyUsed;
        this.loadPrintables();
      }
    );

    this.apiSystemPreferenceService
    .readBooleanPreference(SystemPreferenceKey.AttachmentManagerOceanEmailEnabled, true)
    .subscribe(
      (property) => {
        this.isOceanEmailEnabled = property;
      },
      () => {
        this.isOceanEmailEnabled = false;
      }
    );

    this.apiPropertyService.getProperty(
        SystemPreferenceKey.AttachmentManagerDisplayEmptyCategories
    ).subscribe(
        (property) => {
          this.displayEmptyCategories = property ? property.value === 'true' : true;
        },
        () => {
          this.displayEmptyCategories = true;
        }
    );

    const attachmentManagerSystemPreferences = [SystemPreferenceKey.PrintWeightThreshold];
    this.apiSystemPreferenceService.readMultipleStringPreferences(attachmentManagerSystemPreferences)
    .subscribe((preferences) => {
      if (preferences[SystemPreferenceKey.PrintWeightThreshold]) {
        this.printingThreshold = parseFloat(preferences[SystemPreferenceKey.PrintWeightThreshold]);
      }
    });

    const preferenceNames: SystemPreferenceKey[] = [];
    for (const key in PrintableEnum) {
      if (PrintableEnum[key].printWeightKey) {
        preferenceNames.push(PrintableEnum[key].printWeightKey);
      }
    }

    this.apiSystemPreferenceService.readMultipleStringPreferences(preferenceNames
    ).subscribe((preferences) => {
      for (const key in preferences) {
        const printableEnumEntry = getByPrintWeightKey(key);
        if (printableEnumEntry) {
          this.printWeights[printableEnumEntry[0]] = parseFloat(preferences[key]);
        }
      }
    }, () => {
      this.toastrService.error('An error occurred while loading print weights. Printing large requests may not complete.');
    });

  }

  /**
   * Loads the printables from the API and sets up the UI
   * @private
   */
  private loadPrintables(): void {
    this.apiAttachmentManagerService.getPrintables(
      this.demographicNumber, this.providerNumber)
    .subscribe((printableGroups) => {
      this.printablesMap = new Map(Object.entries(printableGroups));

      this.printablesMap.forEach((printables: AttachmentManagerPrintable[], key: string) => {
        this.printablesMap.set(key, this.sortPrintables(printables));
      });

      this.sortedPrintableKeys = this.getSortedPrintableKeys();
      this.preselectPrintables();
    });

    this.apiOceanService.getOceanPreferences().subscribe((preferences) => {
      if (preferences) {
        this.isOceanWarningDismissed =
          preferences[SystemPreferenceKey.AttachmentManagerOceanWarningDismissed] == 'true';
        this.oceanWarningMessage =
          preferences[SystemPreferenceKey.AttachmentManagerOceanWarningMessage];
      }
    });

    this.apiSystemPreferenceService
    .readStringPreference(SystemPreferenceKey.AttachmentManagerDocumentationUrl)
    .subscribe(
      (data) => {
        this.attachmentManagerDocumentationURL = data;
      },
      (err) => {
        this.toastrService.error('An error occurred while loading the documentation url.');
      },
    );

    this.apiPropertyService
    .getProperty(UserPropertyKey.AttachmentManagerFaxCoverPageEnabled)
    .subscribe(
      (data) => {
        this.coverPageState = data ? data.value === 'true' : false;
      },
      (err) => {
        this.toastrService.error(
          'An error occurred while loading the fax cover page preference.',
        );
      },
    );
  }

  /**
   * Preselects the printables that were selected in the parent window.
   */
  preselectPrintables(): void {
    if (!this.attachmentControlPrintablesElement) {
      this.log.debug(`${(this.attachmentControlPrintablesElementId)} element not found or has no
        value attribute in parent window. Skipping preselecting printables.`);
      return;
    }
    const attachmentControlPrintables = this.attachmentControlPrintablesElement;
    const attachmentData: AttachmentManagerPrintable[] = JSON.parse(
      attachmentControlPrintables.value.replace(/^"(.*)"$/, '$1').replace(/\\/g, ""));
    attachmentData.forEach((widgetPrintable: AttachmentManagerPrintable) => {
      const printables = this.printablesMap.get(widgetPrintable.type);
      if (!printables) {
        return;
      }
      const selectedPrintable = printables.find(
        (printable: AttachmentManagerPrintable) => widgetPrintable.id === printable.id);
      if (selectedPrintable) {
        this.selectPrintables([selectedPrintable]);
      }
    });
  }

  /**
   * Handles the select and select all checkbox click event.
   * Expects that all printables are supported.
   * @param $event The select event which includes the checkbox state and the printables to select.
   */
  handleTogglePrintables($event: AttachmentManagerSelectEvent) {
    if ($event.checkboxState === CheckboxState.Checked) {
      this.selectPrintables($event.printables);
    } else {
      this.deselectPrintables($event.printables);
    }
  }

  /**
   * Appends the input printables to the selected printables list.
   * @param printables The printables to select.
   */
  selectPrintables(printables: AttachmentManagerPrintable[]) {
    for (const printable of printables) {
      if (!this.selectedPrintables.includes(printable)) {
        this.selectedPrintables.push(printable);

        const selectedPrintables = this.selectedPrintablesByType.get(printable.type);

        if (selectedPrintables) {
          selectedPrintables.push(printable);
        } else {
          this.selectedPrintablesByType.set(printable.type, [printable]);
        }
      }
    }
  }

  /**
   * Filters the input printables from the selected printables.
   * @param printables The printables to deselect.
   */
  deselectPrintables(printables: AttachmentManagerPrintable[]): void {
    this.selectedPrintables = this.selectedPrintables.filter(
      (printable: AttachmentManagerPrintable) => !printables.includes(printable)
    );
    this.selectedPrintablesByType.forEach((selectedPrintables: AttachmentManagerPrintable[], type: string) => {
      this.selectedPrintablesByType.set(type, selectedPrintables.filter(
        (printable: AttachmentManagerPrintable) => !printables.includes(printable)
      ));
    });

  }

  /**
   * Handles the print button click event.
   */
  handlePrint() {
    if (!this.validateHandlePrint()) {
      return;
    }
    if (this.isPrintingLargeRequest()) {
      return;
    }
    this.isPrinting = true;
    const demographicNumber = this.activatedRoute.snapshot.queryParams['demographicNumber'];
    this.apiAttachmentManagerService.print(demographicNumber, this.selectedPrintables).subscribe((blob) => {
      this.downloadService.download(window, document, blob);
      this.isPrinting = false;
    }, () => {
      this.toastrService.error('An error occurred while printing attachments.');
      this.isPrinting = false;
    });
  }

  validateHandlePrint(): boolean {
    if (this.selectedPrintables.length === 0) {
      this.toastrService.error('No attachments are selected');
      return false;
    }
    return true;
  }

  /**
   * Check if the selected printables is a large request. Currently, we consider a request large if
   * we estimate that it will take more than 50 seconds to print. This is based on a list of
   * SystemPreferences records that hold average print times for each type of printable (or 0 if
   * estimates are nearly negligible, such as encounter notes).
   */
  isPrintingLargeRequest(): boolean {

    const subTotals: { [key: string]: number } = {};
    let total = 0;
    for (const printable of this.selectedPrintables) {

      // In the future, we can account for file size or character count by increasing/decreasing weight here
      const weight = this.printWeights[printable.type] || 0;
      total += weight;
      if (subTotals[printable.type]) {
        subTotals[printable.type] += weight;
      } else {
        subTotals[printable.type] = weight;
      }
    }

    if (total > this.printingThreshold) {
      const errorMessage = `This selection's estimated printing time exceeds the threshold of ${this.printingThreshold} seconds. Please break it into smaller batches. Estimated print times:`;
      const subTotalMessages = Object.entries(subTotals).sort(
        (a, b) => {
          return b[1] - a[1];
        }
      ).map(([key, value]) => {
        return value > 0 ? `${PrintableEnum[key].title}: ${value}s` : '';
      });
      this.toastrService.error(`${errorMessage} \n ${subTotalMessages.join('\n')}`);
      return true;
    }
    return false;

  }

  openDocumentation() {
    window.open(this.attachmentManagerDocumentationURL, 'newwindow', 'width=900,height=700');
  }

  /**
   * Toggles the settings panel visibility
   */
  toggleSettingsPanel(): void {
    this.isSettingsPanelOpen = !this.isSettingsPanelOpen;
  }

  /**
   * Returns the printable keys based on the selected category order preference.
   * @private
   */
  private getSortedPrintableKeys(): string[] {
    if (this.categoryOrderType === CategoryOrderType.Alphabetical) {
      // Sort alphabetically by title
      return Object.entries(PrintableEnum).sort((a: PrintableEnumEntry, b: PrintableEnumEntry) => {
        return a[1].title.localeCompare(b[1].title);
      }).map((printableEnum: PrintableEnumEntry) => {
        return printableEnum[0];
      });
    } else {
      // Use the order defined in the PrintableEnum object (which is the frequently used order)
      return Object.keys(PrintableEnum);
    }
  }

  /**
   * Updates the category order preference
   * @param orderType The new order type
   */
  updateCategoryOrderPreference(orderType: CategoryOrderType): void {
    this.categoryOrderType = orderType;
    this.apiPropertyService.updateProperty(
      SystemPreferenceKey.AttachmentManagerCategoryOrder,
      orderType
    ).subscribe(() => {
      // Update the UI with the new order
      this.sortedPrintableKeys = this.getSortedPrintableKeys();
    });
  }

  /**
   * Updates the display empty categories preference
   * @param displayEmptyCategories The new display empty categories state
   */
  setDisplayEmptyCategories(displayEmptyCategories: boolean): void {
    const previous = this.displayEmptyCategories;
    this.displayEmptyCategories = displayEmptyCategories;
    this.apiPropertyService.updateProperty(
      SystemPreferenceKey.AttachmentManagerDisplayEmptyCategories,
      displayEmptyCategories.toString()
    ).subscribe({
      error: () => {
        this.toastrService.error('An error occurred while updating the display empty categories preference');
        this.displayEmptyCategories = previous;
      }
    });
  }

  selectTab(tabId: TabId) {
    this.activeTabId = tabId;
  }

  handleRemoveFaxRecipient(faxRecipient: Recipient) {
    this.faxRecipients = this.faxRecipients.filter((recipient: Recipient) => recipient !== faxRecipient);
  }

  handleAddFaxRecipient(faxRecipient: Recipient) {
    if (this.isRecipientSelected(faxRecipient)) {
      this.toastrService.error('Recipient already selected');
      return;
    }
    this.faxRecipients.push(faxRecipient);
  }

  isRecipientSelected(faxRecipient: Recipient): boolean {
    return this.faxRecipients.some(
      (recipient: Recipient) => recipient.faxNumber === faxRecipient.faxNumber);
  }

  handleSendFax() {
    if (!this.validateSendFax()) {
      return;
    }
    if (this.coverPageState) {
      const subject = this.faxComponent.getCoverPageSubject();
      const message = this.faxComponent.getCoverPageMessage();
      this.apiAttachmentManagerService.getCoverPage(subject, message).subscribe((coverPage) => {
        const printables = [coverPage, ...this.selectedPrintables];
        this.faxPrintables(printables);
      }, () => {
        this.toastrService.error('An error occurred while fetching the fax cover page.');
      });
    } else {
      this.faxPrintables(this.selectedPrintables);
    }
  }

  faxPrintables(printables: AttachmentManagerPrintable[]) {
    this.isFaxing = true;
    this.apiAttachmentManagerService.faxPrintables(
      this.demographicNumber,
      printables,
      this.mapFaxRecipientsToFaxNumbers(this.faxRecipients)
    ).subscribe(() => {
      this.toastrService.success('Fax sent successfully');
      this.isFaxing = false;
      this.faxRecipients = [];
    }, () => {
      this.toastrService.error("An error occurred while faxing");
      this.isFaxing = false;
    });
  }

  mapFaxRecipientsToFaxNumbers(recipients: Recipient[]): string[] {
    return recipients.map((recipient: Recipient) => this.getRawFaxNumber(recipient.faxNumber));
  }

  getRawFaxNumber(faxNumber: string): string {
    return faxNumber.replace(/[^0-9]/g, '');
  }

  validateSendFax(): boolean {
    if (this.faxRecipients.length === 0) {
      this.toastrService.error('No fax recipients are selected');
      return false;
    } else if (this.selectedPrintables.length === 0) {
      this.toastrService.error('No attachments are selected');
      return false;
    }
    return true;
  }

  /**
   * Given an array of AttachmentManagerPrintable objects, filter out the printables the do not
   * match the filter criteria.
   *
   * @param printables - Array of AttachmentManagerPrintable objects to filter
   * @returns an array of AttachmentManagerPrintable objects that have been matched by the filter criteria
   */
  filterPrintables(printables: AttachmentManagerPrintable[]): AttachmentManagerPrintable[] {
    if (this.searchIsActive()) {
      return printables.filter((printable) =>
        printable['name'] != null && printable['name'].toLowerCase().includes(this.searchKey.toLowerCase()));
    }
    return printables;
  }

  /**
   * Given a printableGroupKey, return an array of AttachmentManagerPrintable objects that have
   * been listed under the given key and are matched by the filter criteria.
   *
   * If no key is specified, then all printables are returned regardless of their group.
   *
   * @param printableGroupKey string representing the category of printables to fetch
   * @returns an array of AttachmentManagerPrintable objects
   */
  getPrintables(printableGroupKey): AttachmentManagerPrintable[] {
    return this.printablesMap.get(printableGroupKey) || [];
  }

  getSelectedPrintables(): Set<AttachmentManagerPrintable> {
    return new Set(this.selectedPrintables);
  }

  getSelectedPrintablesByType(printableGroupKey: string): Set<AttachmentManagerPrintable> {
    return new Set(this.selectedPrintablesByType.get(printableGroupKey)) || new Set();
  }

  getManageSelectedButtonLabel(): string {
    return `SELECTED ${this.selectedPrintables.length}`;
  }

  togglePanelOpen(): void {
    this.isPanelOpen = !this.isPanelOpen;
  }

  handleSetActivePanelTabId(tabId: PanelTabId): void {
    if (this.isPanelOpen && this.activePanelTabId === tabId) {
      this.isPanelOpen = false;
    } else {
      this.isPanelOpen = true;
      this.activePanelTabId = tabId;
    }
    if (this.activePanelTabId !== 'panelPreviewTab') {
      this.previewPrintable = null;
    }
  }

  handleOpenPreviewPanel(printable: AttachmentManagerPrintable): void {
    this.previewPrintable = printable;
    this.handleSetActivePanelTabId('panelPreviewTab');
  }

  selectAllPrintables(): void {
    this.sortedPrintableKeys.forEach((key: string) => {

      // eDocs are not associated with a specific demographic, so they should not be selected
      if (key === 'Edocs') {
        return;
      }

      const printables = this.printablesMap.get(key);
      if (printables) {
        this.selectPrintables(printables.filter((printable: AttachmentManagerPrintable) =>
          printable['supported']
        ));
      }
    });
  }

  deselectAllPrintables(): void {
    this.deselectPrintables(this.selectedPrintables);
  }

  onSearchChange(event): void {
    this.searchKey = event.target.value;
    this.updateFilteredPrintables();
  }

  updateFilteredPrintables(): void {
    this.filteredPrintables = [];
    this.printablesMap.forEach((printables) => {
      this.filteredPrintables = this.filteredPrintables.concat(printables);
    });

    this.filteredPrintables = this.sortPrintables(this.filteredPrintables);
    this.filteredPrintables = this.filterPrintables(this.filteredPrintables);
  }

  searchIsActive(): boolean {
    return this.searchKey.length >= 3;
  }

  /**
   * If all files are filtered out by search, return true.
   *
   * This can only be true if:
   *   - Search key is active (3 or more characters)
   *   - There is at least one group in printablesMap that has at least one printable
   */
  allFilesFiltered(): boolean {
    const printablesArray = Array.from(this.printablesMap.values());
    if (printablesArray.every(printables => printables.length == 0) || !this.searchIsActive())
      return false;

    return printablesArray.every(printables =>
      printables.filter(printable =>
        printable['name'] != null && printable['name'].toLowerCase().includes(this.searchKey.toLowerCase())
      ).length == 0);
  }

  saveToOcean(): void {
    if (!this.isSaveToOceanValid()) {
      return;
    }

    if (!this.isOceanWarningDismissed && this.oceanWarningMessage !== '') {
      // popup modal to dismiss
      this.showOceanWarningModal();
    } else {
      this.isSavingToOcean = true;
      this.apiAttachmentManagerService.saveToOcean(this.demographicNumber, this.selectedPrintables)
      .subscribe(() => {
        this.toastrService.success('Attachments saved for Ocean');
        this.isSavingToOcean = false;
        this.isSavedToOcean = true;
      }, (err): void => {
        this.toastrService.error('Failed to save attachments to Ocean');
        this.isSavingToOcean = false;
        console.error(err);
      });
    }
  }

  showOceanWarningModal(): void {
    this.promptModal.openModal();
  }

  onCloseOceanWarning($event: boolean): void {
    // dismiss the warning for the duration of this window
    this.isOceanWarningDismissed = true;
    if ($event) {
      // only update the preference if the user has checked the dismiss checkbox
      this.apiSystemPreferenceService.updatePreference(
          SystemPreferenceKey.AttachmentManagerOceanWarningDismissed,
          'true').subscribe(
          () => {
            this.toastrService.success('Ocean warning dismissed');
            this.saveToOcean();
          }
      );
    } else {
      this.saveToOcean();
    }
  }

  getSaveForOceanLabel(): string {
    return this.isSavedToOcean ? 'SAVED FOR OCEAN' : 'SAVE FOR OCEAN';
  }

  getSaveForOceanIcon() {
    return `fa-solid ${this.isSavedToOcean ? 'fa-check' : 'fa-save'}`;
  }

  sortPrintables(printables: AttachmentManagerPrintable[]): AttachmentManagerPrintable[] {
    return printables.sort((a: AttachmentManagerPrintable, b: AttachmentManagerPrintable) => {
      // If neither have dates, sort by name.
      if (!a.date && a.name && !b.date && b.name) {
        return a.name.localeCompare(b.name);
      } else if (!a.date) { // 1 = b is ordered first.
        return 1;
      } else if (!b.date) { // -1 = a is ordered first.
        return -1;
      } else if ((a.date instanceof Date || typeof a.date === 'string' || typeof a.date === 'number') &&
        (b.date instanceof Date || typeof b.date === 'string' || typeof b.date === 'number')) {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      } else { // Don't change the order
        return 0;
      }
    });
  }

  handleSaveToPrintable() {
    if (JSON.stringify(this.selectedPrintables).length >= this.MAXIMUM_ATTACHMENT_JSON_LENGTH) {
      this.toastrService
      .error('Selected attachments are too large to save to a single document');
      return;
    }
    window.opener.onAttachmentsSaved(this.selectedPrintables);
    window.close();
  }

  resetFilters() {
    this.searchKey = "";
  }

  setCoverPageState(toggleState: boolean): void {
    this.coverPageState = toggleState;

    this.apiPropertyService.updateProperty(
      UserPropertyKey.AttachmentManagerFaxCoverPageEnabled, this.coverPageState.toString())
    .subscribe({
      error: () => {
        this.toastrService.error('An error occurred while updating the fax cover page preference');
      }
    });
  }
}

type TabId = 'attachmentsTab' | 'faxTab';
type PanelTabId = 'panelManageSelectedTab' | 'panelPreviewTab';
