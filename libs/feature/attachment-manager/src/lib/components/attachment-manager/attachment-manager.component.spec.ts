import {ComponentFixture, TestBed} from '@angular/core/testing';

import {AttachmentManagerComponent} from './attachment-manager.component';
import { provideHttpClient } from '@angular/common/http';
import {APP_CONFIG} from '@oscar-pro/config';
import {UrlParamsPipe} from '@oscar-pro/util';
import {ActivatedRoute} from '@angular/router';
import {NO_ERRORS_SCHEMA} from '@angular/core';
import {ToastrService} from 'ngx-toastr';
import {By} from "@angular/platform-browser";
import {
  ApiAttachmentManagerService,
  ApiPropertyService,
  UserPropertyKey
} from "@oscar-pro/data-access";
import {of} from "rxjs";
import {Property} from '@oscar-pro/interfaces';
import {AttachmentManagerPrintable} from "@oscar-pro/feature/attachment-manager";
import {
  AttachmentManagerFaxComponent
} from '../attachment-manager-fax/attachment-manager-fax.component';
import { provideHttpClientTesting } from '@angular/common/http/testing';

describe('AttachmentManagerComponent', () => {
  let component: AttachmentManagerComponent;
  let fixture: ComponentFixture<AttachmentManagerComponent>;
  let mockPrintablesElement: HTMLInputElement;
  let toastrService: { success: jest.Mock; error: jest.Mock; warning: jest.Mock };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AttachmentManagerComponent, AttachmentManagerFaxComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {provide: APP_CONFIG, useValue: {}},
        UrlParamsPipe,
        {
          provide: ActivatedRoute, useValue: {
            snapshot: {
              queryParams: ''
            }
          }
        },
        {
          provide: ToastrService, useValue:
            {
              success: jest.fn(),
              error: jest.fn(),
              warning: jest.fn(),
            }
        },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    // Mocking window.opener.document.getElementById('attachment-control-printables')
    mockPrintablesElement = document.createElement('input');
    const mockDocument = {
      getElementById: jest.fn().mockReturnValue(mockPrintablesElement),
    };
    window.opener = {
      document: mockDocument,
    };

    fixture = TestBed.createComponent(AttachmentManagerComponent);
    component = fixture.componentInstance;
    toastrService = TestBed.inject(ToastrService) as any;
    fixture.detectChanges();
    jest.resetAllMocks();
  });

  const mockSearchInput = (searchKey: string) => {
    const inputElement = fixture.debugElement.query(By.css('#search')).nativeElement;
    inputElement.value = searchKey;
    inputElement.dispatchEvent(new Event('input'));
  }

  const mockDocument = (documentName: string, groupKey: string): AttachmentManagerPrintable => {
    const printable = {
      date: new Date(),
      id: 1,
      name: documentName,
      previewUrl: 'http://test.com',
      supported: true,
      type: groupKey,
      params: {},
      printableAttachments: [],
    };

    const printables = component.printablesMap.get(groupKey);
    if (printables) {
      printables.push(printable);
    } else {
      component.printablesMap.set(groupKey, [printable]);
    }
    if (!component.sortedPrintableKeys.includes(groupKey)) {
      component.sortedPrintableKeys.push(groupKey);
    }

    return printable;
  };

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it.each([
    ['no search key', '', true],
    ['search key matches document', 'bcd', true],
    ['search key does not match document', 'abc', false],
    ['search key does not match document but is too short to apply', 'ab', true],
  ])(
    'should apply search filter when fetching printables',
    (description, searchKey, expectedFileIncluded) => {

      const groupKey = 'test';
      mockDocument('bcdefg', groupKey);
      mockSearchInput(searchKey);
      const printables = component.filteredPrintables;

      expect(printables.length > 0).toEqual(expectedFileIncluded);
    });

  it('given two documents without filters includedPrintables should include both printables', () => {
    mockDocument('Document 1', 'Group1');
    mockDocument('Document 2', 'Group2');

    let printables = component.getPrintables('Group1');
    expect(printables.length).toEqual(1);
    expect(printables[0].name).toEqual('Document 1');

    printables = component.getPrintables('Group2');
    expect(printables.length).toEqual(1);
    expect(printables[0].name).toEqual('Document 2');

    component.updateFilteredPrintables();
    printables = component.filteredPrintables;
    expect(printables.length).toEqual(2);
    expect(printables.map(p => p.name)).toContain('Document 1');
    expect(printables.map(p => p.name)).toContain('Document 2');
  });

  it.each([
    [true],
    [false]
  ])(
    'should display message if all files excluded due to search',
    (hasDocument) => {

      mockSearchInput('123')
      if (hasDocument)
        mockDocument('456', 'test');
      fixture.detectChanges()

      expect(component.allFilesFiltered()).toEqual(hasDocument);
      const element = fixture.debugElement.query(By.css('#empty-search-message'));

      if (hasDocument)
        expect(element.nativeElement.textContent).toContain(' No results found. Reset filters.');
      else
        expect(element).toBeNull();

    });

  it('should add printable given a printable is selected', () => {
    const printable = mockDocument('Document 1', 'Group 1');

    expect(component.selectedPrintables.length).toEqual(0);
    expect(component.selectedPrintablesByType.size).toEqual(0);
    component.selectPrintables([printable]);

    expect(component.selectedPrintables.length).toEqual(1);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    component.selectPrintables([printable]);
    expect(component.selectedPrintables.length).toEqual(1);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    const printableNames = component.selectedPrintables.map(p => p.name);
    expect(printableNames).toContain('Document 1');

    const printableTypes = Array.from(component.selectedPrintablesByType.keys());
    expect(printableTypes).toContain('Group 1');
  });

  it('should add multiple printable given multiple printables are selected', () => {
    const printable1 = mockDocument('Document 1', 'Group 1');
    const printable2 = mockDocument('Document 2', 'Group 1');
    const printable3 = mockDocument('Document 3', 'Group 1');

    expect(component.selectedPrintables.length).toEqual(0);
    expect(component.selectedPrintablesByType.size).toEqual(0);

    component.selectPrintables([printable1, printable2]);
    expect(component.selectedPrintables.length).toEqual(2);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    const selectedPrintableNames = component.selectedPrintables.map(p => p.name);
    expect(selectedPrintableNames).toContain('Document 1');
    expect(selectedPrintableNames).toContain('Document 2');
    expect(selectedPrintableNames).not.toContain('Document 3');

    const printableTypes = Array.from(component.selectedPrintablesByType.keys());
    expect(printableTypes).toContain('Group 1');

  });

  it('should remove printable given a printable is deselected', () => {
    const printable = mockDocument('Document 1', 'Group 1');

    expect(component.selectedPrintables.length).toEqual(0);
    expect(component.selectedPrintablesByType.size).toEqual(0);

    component.deselectPrintables([printable]);
    expect(component.selectedPrintables.length).toEqual(0);
    expect(component.selectedPrintablesByType.size).toEqual(0);

    component.selectPrintables([printable]);
    expect(component.selectedPrintables.length).toEqual(1);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    component.deselectPrintables([printable]);
    expect(component.selectedPrintables.length).toEqual(0);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    component.deselectPrintables([printable]);
    expect(component.selectedPrintables.length).toEqual(0);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    const printableTypes = Array.from(component.selectedPrintablesByType.keys());
    expect(printableTypes).toContain('Group 1');

  });

  it('should remove multiple printables given multiple printables are deselected', () => {

    const printable1 = mockDocument('Document 1', 'Group 1');
    const printable2 = mockDocument('Document 2', 'Group 1');
    const printable3 = mockDocument('Document 3', 'Group 1');

    component.selectPrintables([printable1, printable2, printable3]);
    expect(component.selectedPrintables.length).toEqual(3);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    component.deselectPrintables([printable1, printable2]);
    expect(component.selectedPrintables.length).toEqual(1);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    const selectedPrintableNames = component.selectedPrintables.map(p => p.name);
    expect(selectedPrintableNames).not.toContain('Document 1');
    expect(selectedPrintableNames).not.toContain('Document 2');
    expect(selectedPrintableNames).toContain('Document 3');

    const printableTypes = Array.from(component.selectedPrintablesByType.keys());
    expect(printableTypes).toContain('Group 1');

  });

  it('should add or remove all printables to selectedPrintables given all printables are selected or deselected', () => {
    mockDocument('Document 1', 'Group 1');
    mockDocument('Document 2', 'Group 1');

    expect(component.selectedPrintables.length).toEqual(0);
    expect(component.selectedPrintablesByType.size).toEqual(0);

    component.selectAllPrintables();
    expect(component.selectedPrintables.length).toEqual(2);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    const printableNames = component.selectedPrintables.map(p => p.name);
    expect(printableNames).toContain('Document 1');
    expect(printableNames).toContain('Document 2');

    component.deselectAllPrintables();
    expect(component.selectedPrintables.length).toEqual(0);
    expect(component.selectedPrintablesByType.size).toEqual(1);

    const printableTypes = Array.from(component.selectedPrintablesByType.keys());
    expect(printableTypes).toContain('Group 1');

  });


  it.each([
    ['User wants cover page', true, true],
    ['User does not want cover page', false, false],
    ['User has not set a preference', null, false]
  ])('should check user preferences for default cover page state', (description, initialVal, expectedHasCoverPage) => {

    const apiPropertyService = TestBed.inject(ApiPropertyService);

    // Mock getProperty to either return a property or null
    jest.mock('')
    if (initialVal != null) {
      const property = {
        id: 1,
        name: UserPropertyKey.AttachmentManagerFaxCoverPageEnabled,
        value: initialVal.toString(),
        provider: {
          active: true,
          address: '123 Paper Street',
          comments: '',
          firstName: 'John',
          lastName: 'Smib',
          formattedName: 'John Smib',
          ohipNo: '999998',
          phone: '**********',
          practitionerNo: '123456',
          providerNo: '',
          providerType: 'doctor',
          providerComments: '',
          rmaNo: '',
          status: 'Active',
          thirdPartyOnly: false,
          workPhone: '**********',
        }
      };
      jest.spyOn(apiPropertyService, 'getProperty').mockReturnValue(of(property));
    }

    component.ngOnInit()
    expect(component.coverPageState).toBe(expectedHasCoverPage);
  });

  it.each([
    true,
    false
  ])('should save the cover page state on toggle', (initialCoverPageState) => {
    const apiPropertyService = TestBed.inject(ApiPropertyService);
    const savePropertySpy = jest.spyOn(apiPropertyService, 'updateProperty').mockReturnValue(of({} as Property));

    component.setCoverPageState(initialCoverPageState);
    expect(savePropertySpy).toHaveBeenCalledWith(UserPropertyKey.AttachmentManagerFaxCoverPageEnabled, initialCoverPageState.toString());
  });

  it.each([
    true,
    false
  ])('should include cover page on fax based on coverPageState', (coverPageState) => {

    const coverPage = {
      date: null,
      id: 1,
      name: 'Cover Page\n',
      params: {},
      previewUrl: "/oscar/printable/cover-page?preview=true&id&providerNumber=999998",
      supported: true,
      type: "CoverPage",
      printableAttachments: []
    };
    const mockPrintable = mockDocument('Document 1', 'group1');
    const expectedPrintables = coverPageState ? [coverPage, mockPrintable] : [mockPrintable];
    component.coverPageState = coverPageState;
    component.selectedPrintables = [mockPrintable];
    jest.spyOn(component.faxComponent, 'getCoverPageSubject').mockReturnValue('Subject');
    jest.spyOn(component.faxComponent, 'getCoverPageMessage').mockReturnValue('Message');

    const apiAttachmentManagerService = TestBed.inject(ApiAttachmentManagerService);
    const apiSpy = jest.spyOn(apiAttachmentManagerService, 'faxPrintables');
    jest.spyOn(component, 'validateSendFax').mockReturnValue(true);
    jest.spyOn(apiAttachmentManagerService, 'getCoverPage').mockReturnValue(of(coverPage as AttachmentManagerPrintable));

    component.handleSendFax();

    expect(apiSpy).toHaveBeenCalledTimes(1);
    const faxedPrintables = apiSpy.mock.calls[0][1];
    expect(faxedPrintables).toEqual(expectedPrintables);

  });

  it('should pass cover page fields to API service when sending fax', () => {

    const subject = 'Subject';
    const message = 'Message';
    const mockPrintable = mockDocument('Document 1', 'group1');
    component.coverPageState = true;
    component.selectedPrintables = [mockPrintable];

    const apiAttachmentManagerService = TestBed.inject(ApiAttachmentManagerService);
    const apiFaxSpy = jest.spyOn(apiAttachmentManagerService, 'faxPrintables');
    const apiCoverPageSpy = jest.spyOn(apiAttachmentManagerService, 'getCoverPage');
    jest.spyOn(component, 'validateSendFax').mockReturnValue(true);
    jest.spyOn(component.faxComponent, 'getCoverPageSubject').mockReturnValue(subject);
    jest.spyOn(component.faxComponent, 'getCoverPageMessage').mockReturnValue(message);

    component.handleSendFax();

    expect(apiCoverPageSpy).toHaveBeenCalledTimes(1);
    expect(apiAttachmentManagerService.getCoverPage).toHaveBeenCalledWith(subject, message);
  });

  it('should show error toast when selected printables are too large', () => {
    // generate and assign a large amount of data (> 65535 characters) to the printable name
    const mockPrintable = mockDocument('x'.repeat(65536), 'group1');

    component.selectedPrintables = [mockPrintable];
    component.handleSaveToPrintable();

    expect(toastrService.error).toHaveBeenCalledWith('Selected attachments are too large to save to a single document');
  });

  describe('isSaveToOceanValid', () => {
    it('should return false and show error when no printables are selected', () => {
      component.selectedPrintables = [];

      const result = component.isSaveToOceanValid();

      expect(result).toBe(false);
      expect(toastrService.error)
      .toHaveBeenCalledWith('Please select attachments to save for Ocean');
    });

    it('should return false and show error with Email message when non-supported Ocean printable is selected and isOceanEmailEnabled is true', () => {
      component.selectedPrintables = [{
        id: 1,
        name: 'Test Printable',
        date: new Date(),
        supported: false,
        type: 'Test Type',
        previewUrl: 'http://test.com',
        params: {},
        printableAttachments: [],
      }];

      component['isOceanEmailEnabled'] = true;
      jest.spyOn(component, 'isNonSupportedOceanPrintableSelected').mockReturnValue(true);

      const result = component.isSaveToOceanValid();

      expect(result).toBe(false);
      expect(toastrService.error)
      .toHaveBeenCalledWith(
          '<div>Only the following can be attached in Email:</div>' +
          '<ul>' +
          '<li>Documents</li>' +
          '<li>HRMs</li>' +
          '<li>Labs</li>' +
          '<li>eForms</li>' +
          '<li>eDocs</li>' +
          '<li>Smart Encounter Forms</li>' +
          '</ul>',
          '',
          { enableHtml: true }
      );
    });

    it('should return false and show error with Ocean message when non-supported Ocean printable is selected and isOceanEmailEnabled is false', () => {
      component.selectedPrintables = [{
        id: 1,
        name: 'Test Printable',
        date: new Date(),
        supported: false,
        type: 'Test Type',
        previewUrl: 'http://test.com',
        params: {},
        printableAttachments: [],
      }];

      component['isOceanEmailEnabled'] = false;
      jest.spyOn(component, 'isNonSupportedOceanPrintableSelected').mockReturnValue(true);

      const result = component.isSaveToOceanValid();

      expect(result).toBe(false);
      expect(toastrService.error)
      .toHaveBeenCalledWith(
          '<div>Only the following are currently able to save for Ocean:</div>' +
          '<ul>' +
          '<li>Documents</li>' +
          '<li>HRMs</li>' +
          '<li>Labs</li>' +
          '<li>eForms</li>' +
          '<li>eDocs</li>' +
          '<li>Smart Encounter Forms</li>' +
          '</ul>',
          '',
          { enableHtml: true }
      );
    });

    it('should return true when printables are selected and all are supported', () => {
      component.selectedPrintables = [{
        id: 1,
        name: 'Test Printable',
        date: new Date(),
        supported: true,
        type: 'Test Type',
        previewUrl: 'http://test.com',
        params: {},
        printableAttachments: [],
      }];

      jest.spyOn(component, 'isNonSupportedOceanPrintableSelected')
      .mockReturnValue(false);

      const result = component.isSaveToOceanValid();

      expect(result).toBe(true);
      expect(toastrService.error).not.toHaveBeenCalled();
    });
  });

  describe('handlePrint', () => {

    /**
     * Show that if no printables are selected, then printing will not proceed.
     */
    it('should not print if no printables are selected', () => {

      component.selectedPrintables = [];
      component.handlePrint();

      const apiAttachmentManagerService = TestBed.inject(ApiAttachmentManagerService);
      const apiPrintSpy = jest.spyOn(apiAttachmentManagerService, 'print');
      expect(apiPrintSpy).not.toHaveBeenCalled();

    });

    /**
     * Show that if the helper method isPrintingLargeRequest returns true, then printing will not
     * proceed.
     */
    it('should not print if printing a large request', () => {

      jest.spyOn(component, 'isPrintingLargeRequest').mockReturnValue(true);
      component.selectedPrintables = [];

      component.handlePrint();

      const apiAttachmentManagerService = TestBed.inject(ApiAttachmentManagerService);
      const apiPrintSpy = jest.spyOn(apiAttachmentManagerService, 'print');
      expect(apiPrintSpy).not.toHaveBeenCalled();

    });
  });

  describe ('isPrintingLargeRequest', () => {

    it ('is true if selected printable weights are too large', () => {

      component.printWeights = {
        'Eforms': 100,
        'Lab': 25,
        'Notes': 0,
      };
      component.selectedPrintables = [{
        id: 1,
        name: 'Test Printable',
        date: new Date(),
        supported: false,
        type: 'Eforms',
        previewUrl: 'http://test.com',
        params: {},
        printableAttachments: [],
      }];

      expect(component.isPrintingLargeRequest()).toBe(true);

    });

    it ('can change if totals exceed threshold', () => {

      component.printWeights = {
        'Lab': 25,
      };
      component.selectedPrintables = [];

      const expectedIsLargeAfterAddingLab = [false, false, true];

      for (const expectedResult of expectedIsLargeAfterAddingLab) {


        component.selectedPrintables.push({
          id: 1,
          name: 'Test Printable',
          date: new Date(),
          supported: false,
          type: 'Lab',
          previewUrl: 'http://test.com',
          params: {},
          printableAttachments: [],
        });
        expect(component.isPrintingLargeRequest()).toBe(expectedResult);
      }

    });

    it ('should print even if there are a lot of printables that are unweighted', () => {
      component.printWeights = {
        'Notes': 0,
      };
      component.selectedPrintables = [];
      for (let i = 0; i < 100; i++) {
        component.selectedPrintables.push({
          id: i,
          name: 'Test Printable',
          date: new Date(),
          supported: false,
          type: 'Notes',
          previewUrl: 'http://test.com',
          params: {},
          printableAttachments: [],
        });
      }

      expect(component.isPrintingLargeRequest()).toBe(false);
    });
  });
});
