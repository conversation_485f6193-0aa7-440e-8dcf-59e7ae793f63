import {Component, EventEmitter, Output} from '@angular/core';
import { Recipient } from '@oscar-pro/feature/attachment-manager';
import {ApiSpecialistService} from "@oscar-pro/data-access";
import {OptionValue, Specialist} from "@oscar-pro/interfaces";
import {ValidationService} from "@oscar-pro/util";
import {ToastrService} from "ngx-toastr";

@Component({
    selector: 'oscar-pro-attachment-manager-fax-recipient-selector',
    templateUrl: './attachment-manager-fax-recipient-selector.component.html',
    styleUrls: ['./attachment-manager-fax-recipient-selector.component.scss'],
    standalone: false
})
export class AttachmentManagerFaxRecipientSelectorComponent {
  @Output() recipientAdded = new EventEmitter<Recipient>();
  otherRecipientName = '';
  otherRecipientFaxNumber = '';

  specialistList: Specialist[] = [];
  specialistOptions: OptionValue[] = [];

  isFaxNumberInputInvalid = false;

  constructor(
    private apiSpecialistService: ApiSpecialistService,
    private toastrService: ToastrService,
    private validationService: ValidationService
  ) {}

  ngOnInit(): void {
    this.removePasswordIcons();
  }

  handleAddRecipient(recipient: Recipient) {
    this.recipientAdded.emit(recipient);
  }

  handleAddOtherRecipient() {
    if (!this.validationService.validateFaxNumber(this.otherRecipientFaxNumber)) {
      this.isFaxNumberInputInvalid = true;
      this.toastrService.error('Invalid fax number');
      return;
    }
    this.isFaxNumberInputInvalid = false;
    this.handleAddRecipient({
      name: this.otherRecipientName,
      faxNumber: this.otherRecipientFaxNumber,
    });
    this.clearOtherRecipientFields();
  }

  handleOtherRecipientNameChange($event: [Event, any]) {
    this.otherRecipientName = $event[1];
  }

  handleOtherRecipientFaxNumberChange($event: [Event, any]) {
    this.otherRecipientFaxNumber = $event[1];
  }

  handleSearchRecipients(keyword: string): void {
    this.specialistList = [] as Array<Specialist>;
    this.specialistOptions = [] as Array<OptionValue>;
    if (!keyword || !keyword.length) {
      return;
    }
    this.apiSpecialistService.searchFaxSpecialists(keyword).subscribe(
      (data: Specialist[]) => {
        this.specialistList = data;
        this.specialistList.forEach((specialist) =>
          this.specialistOptions.push({
            option: specialist.formattedNameFax,
            value: specialist.specialistId.toString(),
          })
        );
      },
      (error) => console.error(error)
    );
  }

  private clearOtherRecipientFields() {
    this.otherRecipientName = '';
    this.otherRecipientFaxNumber = '';
  }

  handleAddInputRecipient($event: string): void {
    const selectedSpecialist = this.specialistList.find(
      (specialist: Specialist) => specialist.specialistId.toString() === $event
    );
    if (selectedSpecialist) {
      this.handleAddRecipient({
        name: `${selectedSpecialist.firstName} ${selectedSpecialist.lastName}`,
        faxNumber: selectedSpecialist.fax,
      });
    }
  }

  removePasswordIcons(): void {
    const observer: MutationObserver = new MutationObserver((mutations: MutationRecord[]) => {
      mutations.forEach((mutation: MutationRecord) => {
        if (mutation.addedNodes.length) {
          const node = mutation.addedNodes[0];
          if (node instanceof HTMLElement && (
                node.matches('com-1password-layer-scout') ||
                node.matches('com-1password-button') ||
                node.matches('com-1password-menu') ||
                node.matches('div[data-lastpass-icon-root]'))) {
            node.remove();
          }
        }
      });
    });
    observer.observe(document.body, {childList: true, subtree: true});
  }
}
