import {
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import {
  AttachmentManagerPrintable,
} from "@oscar-pro/feature/attachment-manager";
import {CheckboxState} from "@oscar-pro/weg-ui";
import {AttachmentManagerSelectEvent} from "@oscar-pro/feature/attachment-manager";

@Component({
    selector: 'oscar-pro-attachment-manager-printables-table',
    templateUrl: './attachment-manager-printables-table.component.html',
    styleUrls: ['./attachment-manager-printables-table.component.scss'],
    standalone: false
})
export class AttachmentManagerPrintablesTableComponent {

  // data
  @Input() printables: AttachmentManagerPrintable[] = [];
  @Input() selectedPrintables: Set<AttachmentManagerPrintable> = new Set();
  @Input() showSectionColumn = false;

  // events
  @Output() toggleSelected: EventEmitter<AttachmentManagerSelectEvent> = new EventEmitter();
  @Output() previewed: EventEmitter<AttachmentManagerPrintable> = new EventEmitter();

  protected readonly CheckboxState = CheckboxState;

  /**
   * Handles when an individual printable is selected or deselected.
   * @param checkboxState The state of the checkbox.
   * @param printable The printable to select or deselect.
   */
  handleSelectPrintables(checkboxState: CheckboxState, printable: AttachmentManagerPrintable) {
    this.toggleSelected.emit({checkboxState, printables: [printable]});
  }

  handlePreview(printable: AttachmentManagerPrintable): void {
    this.previewed.emit(printable);
  }
}
