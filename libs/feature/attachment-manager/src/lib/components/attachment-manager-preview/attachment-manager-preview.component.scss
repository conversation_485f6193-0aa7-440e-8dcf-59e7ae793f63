.preview-panel {
  width: 45rem;
}

.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

img.preview {
  width: 100%;
}

.preview-wrapper {
  overflow-y: auto;
}

object.preview {
  height: 100%;
  width: 100%;
  border: none;
}

.attachment-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xxs);
  overflow: hidden;

  .title {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);

    h4 {
      color: var(--title-text);

      /* Header Lv4/H4 Bold */
      font-family: "Nimbus Sans L";
      font-size: 1rem;
      font-style: normal;
      font-weight: 700;
      line-height: 125%; /* 1.25rem */

      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    i {
      color: var(--secondary-default);

      /* Icons/Solid/FA Solid 14 */
      font-family: "Font Awesome 6 Pro";
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 900;
      line-height: 125%; /* 1.09375rem */
    }
  }

  .info {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);

    span, .date {
      color: var(--secondary-default);

      /* Paragraph Md/Regular */
      font-family: "Nimbus Sans L";
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 1.3125rem */
    }
  }
}
