import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { AttachmentManagerPrintable } from '@oscar-pro/feature/attachment-manager';
import { ApiPreviewService } from '@oscar-pro/data-access';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { APP_CONFIG } from '@oscar-pro/config';
import { AppConfig } from '@oscar-pro/interfaces';

@Component({
    selector: 'oscar-pro-attachment-manager-preview',
    templateUrl: './attachment-manager-preview.component.html',
    styleUrls: ['./attachment-manager-preview.component.scss'],
    standalone: false
})
export class AttachmentManagerPreviewComponent implements OnChanges {
  @Input() printable: AttachmentManagerPrintable | null = null;
  @Output() closed: EventEmitter<void> = new EventEmitter<void>();

  printablePdfUrl = '';
  documentTypes = ['Document', 'Edocs'];

  constructor(private apiPreviewService: ApiPreviewService,
              @Inject(APP_CONFIG) protected appConfig: AppConfig) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['printable'] && this.printable) {
      this.printablePdfUrl = '';
      this.getPrintablePdfUrl(this.printable).subscribe((url) => {
        this.printablePdfUrl = url;
      });
    }
  }

  handleClose(): void {
    this.closed.emit();
  }

  getPrintablePdfUrl(
    printable: AttachmentManagerPrintable,
  ): Observable<string> {
    const pdfParameters = '#toolbar=0&navpanes=0';
    if (this.isClassicPreviewUrl(printable.previewUrl)) {
      return this.apiPreviewService
        .getClassicPreview(printable.previewUrl)
        .pipe(map((byteArray) => {
          const blob = new Blob([byteArray], { type: 'application/pdf' });
          return `${URL.createObjectURL(blob)}${pdfParameters}`;
        }));
    }
    if (printable.type === 'Hrm') {
      return this.apiPreviewService
        .getHrmPreviewUrl(printable.id)
        .pipe(map((url) => `${url}${pdfParameters}`));
    }
    return of(`${printable.previewUrl}${pdfParameters}`);
  }

  isClassicPreviewUrl(previewUrl: string): boolean {
    return previewUrl.startsWith(`/${this.appConfig.oscarContext}`);
  }
}
