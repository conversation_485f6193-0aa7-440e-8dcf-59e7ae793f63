<div class="settings-container">
  <div class="settings-header">
    <h2>Settings</h2>
    <weg-action-icon-base
      icon="fa-solid fa-times"
      size="x-small"
      (clicked)="handleClose()"
    ></weg-action-icon-base>
  </div>
  <div class="divider"></div>
  <div class="settings-body">
    <div class="option-group">
      <div class="option" (click)="handleCategoryOrderChange(CategoryOrderType.Alphabetical)">
        <div class="radio-icon">
          <div [ngClass]="{'radio-selected': categoryOrderType === CategoryOrderType.Alphabetical, 'radio-unchecked': categoryOrderType !== CategoryOrderType.Alphabetical}"></div>
        </div>
        <div class="option-text">Order Categories Alphabetically</div>
      </div>
      <div class="option" (click)="handleCategoryOrderChange(CategoryOrderType.FrequentlyUsed)">
        <div class="radio-icon">
          <div [ngClass]="{'radio-selected': categoryOrderType === CategoryOrderType.FrequentlyUsed, 'radio-unchecked': categoryOrderType !== CategoryOrderType.FrequentlyUsed}"></div>
        </div>
        <div class="option-text">Order Categories by Frequently Used</div>
      </div>
    </div>
    <div class="divider"></div>
    <div class="option-group">
      <div class="option toggle-option">
        <label class="attachment-manager-settings__switch">
          <input
              type="checkbox"
              [checked]="displayEmptyCategories"
              (change)="handleDisplayEmptyCategoriesChange($event)">
          <span class="slider round"></span>
        </label>
        <div class="option-text">Display Empty Categories</div>
      </div>
    </div>
  </div>
</div>
