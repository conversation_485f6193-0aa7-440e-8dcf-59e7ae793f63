import { Component, Input, ViewChild } from '@angular/core';
import { OceanWarningModalComponent } from "../weg-prompt-modal/ocean-warning-modal.component";
import {
  ApiAttachmentManagerService,
  ApiOceanService,
  ApiSystemPreferenceService,
  SystemPreferenceKey,
} from "@oscar-pro/data-access";
import { ToastrService } from "ngx-toastr";
import { AttachmentManagerPrintable } from "@oscar-pro/feature/attachment-manager";
import {
  AttachmentManagerBaseOceanComponent
} from "../attachment-manager-base-ocean/attachment-manager-base-ocean.component";
import { IconPosition, Colour, Size } from "@oscar-pro/weg-ui";

@Component({
  selector: 'oscar-pro-attachment-manager-email',
  templateUrl: './attachment-manager-email.component.html',
  styleUrls: ['./attachment-manager-email.component.scss'],
  standalone: false
})
export class AttachmentManagerEmailComponent extends AttachmentManagerBaseOceanComponent {
  @ViewChild('promptModal') promptModal!: OceanWarningModalComponent;

  @Input() icon = '';
  @Input() iconPosition: IconPosition = 'left';
  @Input() colour: Colour = 'primary';
  @Input() size: Size = 'medium';
  @Input() override selectedPrintables: AttachmentManagerPrintable[] = [];
  @Input() override demographicNumber = 0;
  @Input() override isOceanEmailEnabled = false;
  @Input() override isOceanWarningDismissed = false;
  @Input() override oceanWarningMessage = '';
  @Input() attachmentControlPrintablesElement: HTMLInputElement | undefined;

  constructor(
      apiAttachmentManagerService: ApiAttachmentManagerService,
      private apiOceanService: ApiOceanService,
      apiSystemPreferenceService: ApiSystemPreferenceService,
      toastrService: ToastrService,
  ) {
    super(apiAttachmentManagerService, apiSystemPreferenceService, toastrService);
  }

  emailOcean(): void {
    if (!this.isSaveToOceanValid()) {
      return;
    }

    if (!this.isOceanWarningDismissed && this.oceanWarningMessage !== '') {
      this.showOceanWarningModal();
    } else {
      this.isSavingToOcean = true;
      this.apiAttachmentManagerService.saveToOcean(this.demographicNumber, this.selectedPrintables).subscribe(
          {
            next: (v) => {
              this.isSavingToOcean = false;
              this.isSavedToOcean = true;
              this.apiOceanService.sendEmail(this.demographicNumber).catch(
                  (error) => {
                    console.log("Error while sending email to ocean", error)
                  }
              )
            },
            error: (err) => {
              this.toastrService.error('Failed to save attachments to Ocean');
              this.isSavingToOcean = false;
              this.isSavedToOcean = false;
              console.error(err);
            }
          }
      );
    }
  }

  onCloseOceanWarning($event: boolean): void {
    // dismiss the warning for the duration of this window
    this.isOceanWarningDismissed = true;
    if ($event) {
      // only update the preference if the user has checked the dismiss checkbox
      this.apiSystemPreferenceService.updatePreference(
          SystemPreferenceKey.AttachmentManagerOceanWarningDismissed,
          'true').subscribe(
          () => {
            this.toastrService.success('Ocean warning dismissed');
            this.emailOcean();
          }
      );
    } else {
      this.emailOcean();
    }
  }

  showOceanWarningModal(): void {
    this.promptModal.openModal();
  }
}
