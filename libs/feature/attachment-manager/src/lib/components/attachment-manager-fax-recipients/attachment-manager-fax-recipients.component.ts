import {Component, EventEmitter, Input, Output} from '@angular/core';
import { Recipient } from '@oscar-pro/feature/attachment-manager';

@Component({
    selector: 'oscar-pro-attachment-manager-fax-recipients',
    templateUrl: './attachment-manager-fax-recipients.component.html',
    styleUrls: ['./attachment-manager-fax-recipients.component.scss'],
    standalone: false
})
export class AttachmentManagerFaxRecipientsComponent {
  @Input() recipients: Recipient[] = [];
  @Output() recipientRemoved = new EventEmitter<Recipient>();

  handleRemoveRecipient(recipient: Recipient) {
    this.recipientRemoved.emit(recipient);
  }
}
