import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  OnDestroy,
  Output,
  SimpleChanges,
} from '@angular/core';
import { BillingItem, BillingItemPremiumType } from '@oscar-pro/interfaces';

@Component({
    selector: 'oscar-pro-premium-code-checkbox',
    templateUrl: './premium-code-checkbox.component.html',
    styleUrls: ['./premium-code-checkbox.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class PremiumCodeCheckboxComponent implements OnInit, OnChanges, OnDestroy {
  public isChecked: boolean;

  /**
   * This determines if the component has been initialized
   */
  private isInitialized = false;
  /**
   * This PremiumCodeCheckboxComponent's premium code billing item
   */
  @Input() percentCodeItem: BillingItem;
  /**
   * A billing item to toggle a billing percent code to
   */
  @Input() billItem: BillingItem = {} as BillingItem;
  /**
   * Determines if the checkbox starts checked
   */
  @Input() startChecked: boolean;

  /**
   * Emitter to notify of an updated checked state
   */
  @Output() updatePercentTotal: EventEmitter<BillingItemPremiumType> =
    new EventEmitter<BillingItemPremiumType>();

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    if (this.isInitialized) {
      this.isChecked = this.startChecked;
      this.cdr.detectChanges();
    } else {
      this.isChecked = true;
      this.isInitialized = true;
      this.calculateItemFeeAndUpdateTotal();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['startChecked'] &&
      changes['startChecked'].currentValue !== undefined
    ) {
      this.isChecked = changes['startChecked'].currentValue;
      this.cdr.detectChanges(); // Manually trigger change detection
    }
  }

  onEventUpdatePercentTotal(event: Event) {
    event.preventDefault();
    this.calculateItemFeeAndUpdateTotal();
  }

  calculateItemFeeAndUpdateTotal() {
    let itemPercentageFee = 0;
    if (this.isChecked) {
      itemPercentageFee =
        +this.billItem.service?.value *
        +this.percentCodeItem.service?.percentage *
        +this.billItem.serviceCount;
    }
    this.updatePercentTotal.emit({
      serviceCode: this.billItem?.service?.serviceCode,
      itemPercentageFee: itemPercentageFee,
    });
  }

  ngOnDestroy(): void {
    this.calculateItemFeeAndUpdateTotal();
  }
}
