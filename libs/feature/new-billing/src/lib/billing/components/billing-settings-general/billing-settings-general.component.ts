import { Component, DestroyRef } from '@angular/core';
import { NewBillingFacade as BillingFacade } from '@oscar-pro/core-state';
import { ApiBillingService, BillingService } from '@oscar-pro/new-data-access';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { finalize, Observable } from 'rxjs';

@Component({
    selector: 'oscar-pro-billing-settings-general',
    templateUrl: './billing-settings-general.component.html',
    styleUrls: ['./billing-settings-general.component.scss'],
    standalone: false
})
export class BillingSettingsGeneralComponent {
  includeRPFromPatient$: Observable<boolean> = this.billingFacade.includeRPFromPatient$;
  processing = false;

  constructor(
    public billingService: BillingService,
    private billingFacade: BillingFacade,
    private apiBillingService: ApiBillingService,
    private destroyRef: DestroyRef,
  ) {}

  public onIncludeReferringPractitionerChange(event: Event): void {
    this.processing = true;
    const shouldInclude = (<HTMLInputElement>event.target).checked;
    this.apiBillingService.updateOption('includeReferringPractitionerFromPatientRecord', `${shouldInclude}`).pipe(
      takeUntilDestroyed(this.destroyRef),
      finalize(() => {
        this.processing = false;
      }),
    ).subscribe(() => {
      this.billingFacade.updateIncludeRPFromPatient(shouldInclude);
    });
  }
}
