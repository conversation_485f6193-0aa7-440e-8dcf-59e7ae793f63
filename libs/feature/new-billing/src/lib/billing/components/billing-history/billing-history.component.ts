import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { AppConfig, Bill } from '@oscar-pro/interfaces';
import {
  ObjectArrayFilterPipe,
  UrlParamsPipe,
  WindowRefService,
} from '@oscar-pro/util';
import { APP_CONFIG } from '@oscar-pro/config';
import { faChevronUp, faChevronDown } from '@fortawesome/free-solid-svg-icons';

@Component({
    selector: 'oscar-pro-billing-history',
    templateUrl: './billing-history.component.html',
    styleUrls: ['./billing-history.component.scss'],
    standalone: false
})
export class BillingHistoryComponent implements OnInit {
  @Input() billingHistory: Array<Bill>;
  @Input() billingHistoryCount: number;
  @Input() billingHistoryCurrentPage: number;
  @Input() isLoadingMore: boolean;
  @Input() showHistoryDetails: boolean;
  @Output() toggleHistoryDetails: EventEmitter<void> = new EventEmitter<void>();
  @Output() changeDaysBack: EventEmitter<number> = new EventEmitter<number>();
  @Output() changeServiceCode: EventEmitter<string> =
    new EventEmitter<string>();
  @Output() selectBillingHistoryPage: EventEmitter<number> =
    new EventEmitter<number>();

  @ViewChild(CdkVirtualScrollViewport)
  viewport: CdkVirtualScrollViewport;

  public serviceCode: string | null = null;
  public daysBack: number | null = null;
  protected readonly faChevronUp = faChevronUp;
  protected readonly faChevronDown = faChevronDown;

  public sortedBillingHistory: Array<Bill>;
  private billingStatuses = [
    { code: 'H', description: 'Capitated' },
    { code: 'O', description: 'Bill OHIP' },
    { code: 'P', description: 'Bill Patient' },
    { code: 'N', description: 'Do Not Bill' },
    { code: 'W', description: "Bill Worker's Compensation Board" },
    { code: 'B', description: 'Submitted  OHIP' },
    { code: 'S', description: 'Settled' },
    { code: 'X', description: 'Bad Debt' },
    { code: 'D', description: 'Deleted' },
    { code: 'I', description: 'Bonus Codes' },
  ];
  private nativeWindow: Window;
  private timer = null; // Timer

  public pageSize = 10;

  constructor(
    private objectArrayFilterPipe: ObjectArrayFilterPipe,
    private urlParamsPipe: UrlParamsPipe,
    private windowRefService: WindowRefService,
    @Inject(APP_CONFIG) private appConfig: AppConfig
  ) {}

  ngOnInit(): void {
    this.nativeWindow = this.windowRefService.getNativeWindow();
  }

  getBillStatus(billStatusCode: string): string {
    let status = '';
    const obj = this.objectArrayFilterPipe.transform(this.billingStatuses, {
      code: billStatusCode,
    })[0];
    if (obj) {
      status = obj['description'];
    }
    return status;
  }

  onChangeDaysBack(event: Event) {
    event.preventDefault();
    const daysBack = event.target['value'];
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.daysBack = daysBack;
      this.changeDaysBack.emit(parseInt(daysBack, 10));
    }, 1000);
  }

  onChangeServiceCode(event: Event) {
    event.preventDefault();
    const serviceCode = event.target['value'] || '';
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.serviceCode = serviceCode;
      this.changeServiceCode.emit(serviceCode);
    }, 1000);
  }

  onClickBillingId(event: MouseEvent, invoiceNo: number): void {
    event.preventDefault();
    const params = this.urlParamsPipe.transform({ billing_no: invoiceNo });
    const url = `/${this.appConfig.oscarContext}/billing/CA/ON/billingONCorrection.jsp${params}`;
    this.nativeWindow.open(url, '_blank', 'height=710,width=1024');
  }

  onSelectPage(page: number): void {
    this.selectBillingHistoryPage.emit(page);
  }

  onToggleHistoryDetails() {
    this.toggleHistoryDetails.emit();
  }
}
