import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BillingHeaderComponent } from './billing-header.component';
import { Component, NO_ERRORS_SCHEMA } from '@angular/core';
import { Bill, BillType, ClinicLocation, Provider, Site, VisitType } from '@oscar-pro/interfaces';
import { By } from '@angular/platform-browser';
import { BillingConstants } from '@oscar-pro/data-access';

const PROVIDERS = [
  {providerNo: '123', formattedName: 'Name 123'},
  {providerNo: '2', formattedName: 'Name 2'},
  {providerNo: '3', formattedName: 'Name 3'}
] as Provider [];

const SITES = [
  {
    'siteId': 1,
    'name': 'Ottawa',
    'providers': [PROVIDERS[0], PROVIDERS[1]]
  },
  {
    'siteId': 2,
    'name': 'Toronto',
    'providers': [PROVIDERS[0], PROVIDERS[2]]
  }
] as Site[];

@Component({
  standalone: false,
  template: `<oscar-pro-billing-header
    [bill]="bill"
    [billSites]="billSites"
    [billTypes]="billTypes"
    [multiSiteBilling]="multiSiteBilling"
    [providers]="providers"
    [selectedSiteId]="selectedSiteId"
    [selectedProviderNo]="selectedProviderNo"
    [sites]="sites"
    [visitTypes]="visitTypes"
    [isRmaEnabled]="isRmaEnabled"
  ></oscar-pro-billing-header>`,
})
class TestHostComponent {
  bill: Bill = {admissionDate: 1735718400000} as Bill;
  billSites: ClinicLocation[] = [
    {
      'clinicLocationNo': '0000',
      'name': 'Not Applicable'
    },
    {
      'clinicLocationNo': '9999',
      'name': 'Home Visit'
    }
  ] as ClinicLocation[];
  billTypes: BillType[] = [BillingConstants.billTypes[0], BillingConstants.billTypes[4]];
  multiSiteBilling = true;
  providers: Provider[] = PROVIDERS;
  selectedSiteId = 1;
  selectedProviderNo = '123';
  sites: Site[] = SITES;
  visitTypes: VisitType[] = [BillingConstants.visitTypes[0], BillingConstants.visitTypes[5]];
  isRmaEnabled = false;
}

describe('Billing Header Component', () => {
  let hostComponent: TestHostComponent;
  let component: BillingHeaderComponent;
  let fixture: ComponentFixture<TestHostComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BillingHeaderComponent, TestHostComponent],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TestHostComponent);
    hostComponent = fixture.componentInstance;
    component = fixture.debugElement.query(By.directive(BillingHeaderComponent)).componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set initial values', () => {
    expect(component.admissionDate).toEqual('2025-01-01');
    expect(component.siteOptions).toEqual([
      {
        option: 'Ottawa',
        value: '1'
      }, {
        option: 'Toronto',
        value: '2'
      }
    ]);
    expect(component.providerOptions).toEqual([
      {
        option: 'Name 123',
        value: '123'
      }, {
        option: 'Name 2',
        value: '2'
      }
    ]);
    expect(component.billTypeOptions).toEqual([
      {
        option: 'Bill OHIP',
        value: 'ODP',
      }, {
        option: '3rd Party',
        value: 'PAT',
      }
    ]);
    expect(component.billSiteOptions).toEqual([
      {
        option: '0000 | Not Applicable',
        value: '0000',
      }, {
        option: '9999 | Home Visit',
        value: '9999',
      }
    ]);
    expect(component.visitTypeOptions).toEqual([
      {
        option: '00 | Clinic Visit',
        value: '00',
      }, {
        option: '05 | Home Visit',
        value: '05',
      }
    ]);
    expect(component.selectedSite).toEqual(SITES[0]);
    expect(component.selectedProvider).toEqual({
      option: 'Name 123',
      value: '123'
    });
  });

  it('should update billing site', () => {
    jest.spyOn(component.updateBillingClinic, 'emit');
    component.onEventUpdateBillingSite('2');
    expect(component.updateBillingClinic.emit).toHaveBeenCalledWith(SITES[1]);
  });

  it('should reset provider when billing site without selected provider is chosen', () => {
    hostComponent.selectedProviderNo = '2';
    fixture.detectChanges();
    jest.spyOn(component.updateBillingPhysician, 'emit');
    component.onEventUpdateBillingSite('Toronto');
    expect(component.updateBillingPhysician.emit).toHaveBeenCalledWith(null);
  });
});
