import { Component, Input } from '@angular/core';
import { BillingDxCode } from '@oscar-pro/interfaces';
import { PanelService } from '@oscar-pro/util';

@Component({
    selector: 'oscar-pro-dx-panel',
    templateUrl: './dx-panel.component.html',
    styleUrls: ['./dx-panel.component.scss'],
    standalone: false
})
export class DxPanelComponent {
  @Input() serviceTypeName: string;
  @Input() dxCodes: Array<BillingDxCode>;
  @Input() selectDxCode: (dx: BillingDxCode) => void;

  constructor(private panelService: PanelService) {}

  close(): void {
    this.panelService.destroy();
  }

  onClickClose(event: MouseEvent): void {
    event.preventDefault();
    this.close();
  }

  onClickDxCode(event: MouseEvent, dx: BillingDxCode): void {
    event.preventDefault();
    this.selectDxCode(dx);
    this.close();
  }
}
