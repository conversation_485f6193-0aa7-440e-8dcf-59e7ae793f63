import { Component, EventEmitter, Input, Output } from '@angular/core';
import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { Appointment } from '@oscar-pro/interfaces';

@Component({
    selector: 'oscar-pro-billing-footer',
    templateUrl: './billing-footer.component.html',
    styleUrls: ['./billing-footer.component.scss'],
    standalone: false
})
export class BillingFooterComponent {
  public faPlus = faPlus;
  @Input() nextAppointment: Appointment | null;
  @Input() thirdParty: boolean;
  @Input() isPrint: boolean;
  @Input() isSettle: boolean;
  @Input() warningEnabled: boolean;
  @Output() saveAndAddBill: EventEmitter<void> = new EventEmitter<void>();
  @Output() cancelNewBill: EventEmitter<void> = new EventEmitter<void>();
  @Output() saveAndClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() settleChanged: EventEmitter<void> = new EventEmitter<void>();
  @Output() printChanged: EventEmitter<void> = new EventEmitter<void>();
}
