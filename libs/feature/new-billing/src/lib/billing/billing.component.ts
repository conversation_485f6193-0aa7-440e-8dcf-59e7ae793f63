import {
  Component,
  ElementRef,
  HostListener,
  Inject,
  LOCALE_ID,
  OnInit,
  ViewChild,
} from '@angular/core';
import * as _ from 'lodash';
import { NewBillingFacade as BillingFacade, UiState } from '@oscar-pro/core-state';
import { ApiBillingService, BillingService } from '@oscar-pro/new-data-access';

import {
  Appointment,
  Bill,
  BillingDxCode,
  BillingExtensions,
  BillingFavourite,
  BillingForm,
  BillingItem,
  BillingPaymentType,
  BillingService as IBillingService,
  ClinicLocation,
  ClinicNbr,
  Demographic,
  Property,
  Provider,
  ProviderPreferences,
  Site,
  SliCode,
  Specialist,
  VisitType,
  BillingHistoryFilters,
  Warning,
} from '@oscar-pro/interfaces';
import { WindowRefService } from '@oscar-pro/util';
import { BehaviorSubject, combineLatest, map, Observable } from 'rxjs';
import { BillingConstants } from '@oscar-pro/data-access';
import { ActivatedRoute } from '@angular/router';
import { filter } from 'rxjs/operators';
import { DOCUMENT } from '@angular/common';
import { OptionValue } from '@oscarpro/well-ui';

@Component({
    selector: 'oscar-pro-billing',
    templateUrl: './billing.component.html',
    styleUrls: ['./billing.component.scss'],
    standalone: false
})
export class BillingComponent implements OnInit {
  @ViewChild('serviceCodeInputId') serviceCodeInputEl: ElementRef;

  private nativeWindow: Window;

  public billingForms: Array<BillingForm> = new Array<BillingForm>();
  public billingGuidelines: object = {
    info: [] as Array<string>,
    warnings: [] as Array<string>,
  };
  public billingHistory: Array<Bill> = new Array<Bill>();
  public billingHistoryCount = 0;
  public billingHistoryFilter: BillingHistoryFilters = {
    demographicNo: null,
    daysBack: '',
    serviceCode: '',
    page: 0,
    resultsPerPage: 10,
  };
  public selectedDxCode$: BehaviorSubject<BillingDxCode | null> = new BehaviorSubject(null);
  public dxDefaultVal = '';
  public hst: number;
  public printInvoiceComment = false;
  public selectedForm: BillingForm;
  public selectedSiteId: number;
  public selectedSpecialist: Specialist;
  public superCodes: Array<BillingFavourite> = [] as Array<BillingFavourite>;
  public thirdParty = false;
  private urlParams = {
    demographicNo: null,
    appointmentNo: null,
  };
  public visitTypes: Array<VisitType> = BillingConstants.visitTypes;
  public isRmaEnabled = false;
  public clinicNbr: ClinicNbr[] = new Array<ClinicNbr>();
  public warningEnabled = false;
  public warnings: Array<Warning> = [];
  showHistoryDetails!: boolean;

  isRmaEnabled$: Observable<boolean> = this.billingFacade.isRmaEnabled$;
  clinicNbr$: Observable<Array<ClinicNbr>> = this.billingFacade.clinicNbr$;
  uiState$: Observable<UiState> = this.billingFacade.uiState$;
  multiSiteBilling$: Observable<boolean> = this.billingFacade.systemPreferences$.pipe(map(({multiSites}) => multiSites));
  appointment$: Observable<Appointment> = this.billingFacade.appointment$;
  demographic$: Observable<Demographic> = this.billingFacade.demographic$;
  billSites$: Observable<ClinicLocation[]> = this.billingFacade.billSites$;
  billingForms$: Observable<BillingForm[]> = this.billingFacade.billingForms$;
  defaultBillingForm$: Observable<BillingForm | undefined> = this.billingFacade.defaultBillingForm$;
  bill$: Observable<Bill> = this.billingFacade.bill$;
  hst$: Observable<number> = this.billingFacade.hst$;
  paymentTypes$: Observable<BillingPaymentType[]> =
    this.billingFacade.paymentTypes$;
  billingHistoryCount$: Observable<number> =
    this.billingFacade.billingHistoryCount$;
  billingHistory$: Observable<Array<Bill>> = this.billingFacade.billingHistory$;
  superCodes$: Observable<Array<BillingFavourite>> =
    this.billingFacade.superCodes$;
  billingExtensions$: Observable<BillingExtensions> =
    this.billingFacade.billingExtensions$;
  recentServices$: Observable<Array<IBillingService>> =
    this.billingFacade.recentServices$;
  userProperties$: Observable<Array<Property>> =
    this.billingFacade.userProperties$;
  sites$: Observable<Array<Site>> = this.billingFacade.sites$;
  providers$: Observable<Array<Provider>> = this.billingFacade.providers$;
  providerPreferences$: Observable<ProviderPreferences> =
    this.billingFacade.providerPreferences$;
  billItem$: Observable<BillingItem> = this.billingFacade.billItem$;
  billPercentCodes$: Observable<Array<BillingItem>> =
    this.billingFacade.billPercentCodes$;
  selectedSiteId$: Observable<number> = this.billingFacade.selectedSiteId$;
  selectedProviderNo$: Observable<string> = this.billingFacade.selectedProviderNo$;
  nextAppointment$: Observable<Appointment> =
    this.billingFacade.nextAppointment$;

  public bill: Bill;
  public billingExtensions: BillingExtensions;
  public billTypes = BillingConstants.billTypes;
  public dxCodesOptions: Array<OptionValue> = new Array<OptionValue>();
  public isPrint?: boolean;
  public isSettle?: boolean;

  constructor(
    @Inject(LOCALE_ID) private locale: string,
    @Inject(DOCUMENT) private document,
    private activatedRoute: ActivatedRoute,
    private apiBillingService: ApiBillingService,
    private billingService: BillingService,
    private windowRefService: WindowRefService,
    private billingFacade: BillingFacade
  ) {}

  ngOnInit(): void {
    this.nativeWindow = this.windowRefService.getNativeWindow();

    this.nativeWindow.resizeTo(screen.width, screen.height);

    // exclude top bar buttons from scroll; only make content scrollable
    this.document.body.classList.add('is-non-scrollable');

    this.activatedRoute.queryParams.subscribe((params) => {
      this.billingFacade.setUrlParams({
        appointmentNo: params['appointmentNo'],
        demographicNo: params['demographicNo'],
        sign: params['sign'],
      });
    });
    this.billingFacade.getClinicNbr();
    this.billingFacade.isRmaEnabled();

    this.billingFacade.loadCurrentProvider();

    //TODO: Update super-codes component to use state
    this.billingService.searchDxCodes.subscribe((data) =>
      this.billingFacade.searchDxCodes(data['keyword'], data['itemIndex'])
    );

    //TODO: Update super-codes component to use state
    this.billingService.searchServices.subscribe((keyword) =>
      this.billingFacade.searchServices(keyword)
    );

    //TODO: Update create component to use state
    this.billingService.searchThirdPartyAddresses.subscribe((keyword) =>
      this.billingFacade.searchThirdPartyAddress(keyword)
    );

    const showHistoryDetails = localStorage.getItem('showHistoryDetails');
    this.showHistoryDetails = !showHistoryDetails || showHistoryDetails === 'true';

    this.subscribeToDataStore();

    this.billingFacade.includeRPFromPatient$.subscribe((shouldInclude) => {
      this.billingFacade.getDemographicReferralDoctor(shouldInclude ? this.urlParams.demographicNo : null);
    });
  }

  @HostListener('window:keydown', ['$event'])
  handleShortcuts(event: KeyboardEvent) {
    const key = event.key.toLowerCase();
    if (event.ctrlKey && key === 's') {
      event.preventDefault();
      this.onEventSaveAndClose();
    }
    if (event.ctrlKey && event.altKey && key === 'a') {
      event.preventDefault();
      this.onEventSaveAndAddBill();
    }

    if (event.ctrlKey && event.altKey && key === 'n') {
      event.preventDefault();
      this.onEventSaveAndClose(true);
    }
  }

  updateViewCodeDescription(value: string): Observable<Property> {
    return this.apiBillingService.updateOption('view_code_description', value);
  }

  toggleViewCodeDescription(value: string) {
    this.updateViewCodeDescription(value).subscribe();
  }

  onSelectDxCode(dxCode: BillingDxCode): void {
    this.selectedDxCode$.next(dxCode);
    this.bill.billingItems.forEach((item: BillingItem, index: number) => {
      this.setDiagnosticCode(dxCode.dx.diagnosticCode, index);
    });
  }

  subscribeToDataStore() {
    combineLatest([
      this.isRmaEnabled$,
      this.clinicNbr$
    ]).subscribe(([isRmaEnabled, clinicNbr]) => {
      this.isRmaEnabled = isRmaEnabled;
      this.clinicNbr = clinicNbr;
      if (this.isRmaEnabled) {
        this.visitTypes = new Array<VisitType>();
        for (let i = 0; i < this.clinicNbr.length; i++) {
          this.visitTypes.push(<VisitType>{
            code: this.clinicNbr[i].code,
            description: this.clinicNbr[i].description,
          });
        }
      }
    });

    this.uiState$.subscribe((uiState: UiState) => {
      if (uiState.selectedForm) {
        this.selectedForm = uiState.selectedForm;
      }
      if (uiState.urlParams?.appointmentNo && uiState.urlParams.appointmentNo !== this.urlParams.appointmentNo) {
        this.billingFacade.getAppointmentInfo(uiState.urlParams.appointmentNo);
      }
      if (uiState.urlParams?.demographicNo) {
        this.billingFacade.initDemographicInfo(uiState.urlParams.demographicNo);
      }
      this.urlParams = uiState.urlParams;
      this.thirdParty = uiState.thirdParty;
      this.isPrint = this.thirdParty;
      this.selectedSpecialist = uiState.selectedSpecialist;
      this.warnings = uiState.warnings || [];
      this.setSpecialistInputVal();
    });

    this.bill$.pipe(filter(Boolean)).subscribe((bill: Bill) => {
      this.bill = bill;
    });

    combineLatest([
      this.userProperties$,
      this.providerPreferences$,
      this.selectedDxCode$,
      this.billItem$,
    ]).pipe(
      filter(([userProperties, providerPreferences]) => userProperties?.length && !_.isEmpty(providerPreferences)),
    ).subscribe(([_, providerPreferences, selectedDxCode, billItem]) => {
      const dxDefaultVal = selectedDxCode?.dx.diagnosticCode || providerPreferences?.defaultDxCode || billItem?.dx;
      if (this.dxDefaultVal !== dxDefaultVal) {
        this.dxDefaultVal = dxDefaultVal;
        this.onEventUpdateDxCode({ dxCode: this.dxDefaultVal });
      }
    });

    this.defaultBillingForm$.subscribe((defaultForm) => {
      this.selectedForm = defaultForm;
    });

    this.billingForms$.subscribe((billingForms) => {
      this.billingForms = billingForms;
    });

    this.billingExtensions$.subscribe((billingExtensions: BillingExtensions) => {
      this.billingExtensions = billingExtensions;
    });

    this.demographic$.subscribe((demographic: Demographic) => {
      this.loadHistory({demographicNo: demographic?.demographicNumber});
    });

    this.billingHistoryCount$.subscribe((billingHistoryCount: number) => {
      this.billingHistoryCount = billingHistoryCount;
    });

    this.billingHistory$.subscribe((billingHistory: Array<Bill>) => {
      this.billingHistory = billingHistory;
    });
  }

  createBillItem(
    service: IBillingService,
    serviceCount = '1',
    percent: string = null
  ): void {
    this.billingFacade.createBillItem(service, serviceCount, percent);
  }

  onUpdateDxCodeOptions(dxCodes: Array<OptionValue>): void {
    this.dxCodesOptions = dxCodes;
  }

  setSpecialistInputVal(): void {
    const refNameInputEl: HTMLInputElement =
      document.getElementById('refName') &&
      document.getElementById('refName') instanceof HTMLInputElement
        ? <HTMLInputElement>document.getElementById('refName')
        : null;

    let specialistName = '';
    if (this.selectedSpecialist) {
      specialistName = `${this.selectedSpecialist.lastName}, ${
        this.selectedSpecialist.firstName
      } (${
        this.selectedSpecialist.referralNo
          ? this.selectedSpecialist.referralNo
          : 'None'
      } - ${
        this.selectedSpecialist.specialty
          ? this.selectedSpecialist.specialty
          : 'None'
      })`;
    }
    if (refNameInputEl) {
      setTimeout(() => {
        refNameInputEl.value = specialistName;
      });
    }
  }

  setFacilityNum(facilityNum: string) {
    this.billingFacade.setFacilityNumber(facilityNum);
  }

  setLocation(locationCode: string): void {
    this.billingFacade.setLocation(locationCode);
  }

  setPayProgram(payProgramCode: string): void {
    this.billingFacade.setPayProgram(payProgramCode);
  }

  setVisitType(visitTypeCode: string): void {
    if (this.isRmaEnabled) {
      this.billingFacade.setRmaVisitType(visitTypeCode, this.clinicNbr);
    } else {
      this.billingFacade.setVisitType(visitTypeCode);
    }
  }

  setDiagnosticCode(dxCode: string, fieldNo: number | null = null): void {
    this.billingFacade.setDiagnosticCode(dxCode, fieldNo);
  }

  saveFavourite(favourite: BillingFavourite): void {
    this.billingFacade.saveFavourite(favourite);
  }

  updateBillingItem(
    billingItem: any,
    index: number,
  ): void {
    this.billingFacade.updateBillingItem(billingItem, index);
  }

  onEventCancelNewBill(): void {
    this.billingFacade.clearInvoice(this.billingExtensions);
    this.nativeWindow.close();
  }

  onEventUpdateAdmissionDate(admissionDate: number): void {
    this.billingFacade.updateBill({ admissionDate: admissionDate });
  }

  onEventUpdateManualReview(manualReview: string): void {
    this.billingFacade.updateBill({ manualReview: manualReview });
  }

  onEventUpdatePercentTotal() {
    this.billingFacade.updateBillTotals(this.billingExtensions);
  }

  onEventUpdateVisitType(visitType: VisitType): void {
    this.setVisitType(visitType.code);
  }

  onEventChangeDaysBack(daysBack: number): void {
    this.loadHistory({
      page: 0,
      daysBack: isNaN(daysBack) ? '' : String(daysBack),
    });
  }

  onEventChangeServiceCode(serviceCode: string): void {
    this.loadHistory({
      page: 0,
      serviceCode: serviceCode,
    });
  }

  onEventSelectBillingHistoryPage(page: number): void {
    this.loadHistory({page: page - 1}, true);
  }

  onToggleHistoryDetails() {
    this.showHistoryDetails = !this.showHistoryDetails;
    localStorage.setItem('showHistoryDetails', `${this.showHistoryDetails}`);
    if (this.showHistoryDetails && !this.billingHistory?.length && this.billingHistoryCount) {
      this.loadHistory({}, true);
    }
  }

  private loadHistory(filter: Partial<BillingHistoryFilters>, ignoreCount = false) {
    this.billingHistoryFilter = {
      ...this.billingHistoryFilter,
      ...filter,
    };
    this.billingFacade.getBillingHistory(this.billingHistoryFilter);
    if (!ignoreCount) {
      this.billingFacade.loadBillingHistoryCount(this.billingHistoryFilter);
    }
  }

  onEventUpdateThirdPartyAddress(data: {
    thirdPartyAddress: string;
    addressField: string;
  }) {
    this.billingFacade.updateBillingExtensionsWithAddress(
      data['thirdPartyAddress'],
      data['addressField']
    );
  }

  onEventUpdateBillSite(clinicLocationNo: string): void {
    this.setFacilityNum(clinicLocationNo);
  }

  onEventUpdateBillType(billType: string): void {
    this.setPayProgram(billType);
    let serviceType: string;
    if (BillingConstants.thirdPartyTypes.includes(billType)) {
      serviceType = 'PRI';
    } else if (BillingConstants.ohipTypes.includes(billType)) {
      serviceType = 'MFP';
    }
    if (serviceType) {
      const selectedForm = this.billingForms.find(
        (form) => form.serviceType === serviceType
      );
      this.onEventUpdateSelectedForm({
        ...selectedForm,
        billingType: {...selectedForm.billingType, billType }
      });
    }
  }

  onEventUpdateBillingClinic(site: Site): void {
    this.billingFacade.setBillClinic(site);
  }

  onEventUpdateServiceDate(serviceDate: string): void {
    this.billingFacade.setServiceDate(serviceDate);
  }

  onEventUpdateSliCode(sliCode: SliCode): void {
    this.setLocation(sliCode.code);
  }

  onEventUpdateSpecialist(specialist: Specialist | undefined): void {
    this.billingFacade.setSelectedSpecialist(specialist);
  }

  onEventUpdateBillingExtensions(billingExtensions: BillingExtensions): void {
    this.billingFacade.updateBillingExtensions(billingExtensions);
  }

  onEventUpdateWarningEnabled(enabled: boolean): void {
    this.warningEnabled = enabled;
  }

  onEventUpdateAllBillingItems(billingItems: BillingItem[]): void {
    billingItems.forEach((billingItem, index) => {
      if (billingItem.fee) {
        billingItem.itemPayments[0].paid = +billingItem.fee;
      }
      this.billingFacade.updateBillingItem(billingItem, index);
    });
  }

  onEventUpdateAllBillingItemsDiscount(billingItems: BillingItem[]): void {
    billingItems.forEach((billingItem, index) => {
      if (billingItem.fee) {
        billingItem.itemPayments[0].discount = +billingItem.fee;
      }
      this.billingFacade.updateBillingItem(billingItem, index);
    });
  }

  onEventChangeComment(comment: string) {
    this.billingFacade.setComment(comment);
  }

  onEventCreateBillItem(billingService: any): void {
    this.createBillItem(billingService);
  }

  onEventRemoveBillingItem(billingItem: BillingItem): void {
    this.billingFacade.removeBillingItem(billingItem, true);
  }

  onEventUpdateBillingItem(data: object): void {
    this.updateBillingItem(
      data['billingItem'],
      data['index'],
    );
  }

  onEventUpdateDxCode(data: object): void {
    this.setDiagnosticCode(data['dxCode'], data['itemIndex']);
  }

  onEventUpdateBillingPhysician(provider: Provider | null): void {
    provider
      ? this.billingFacade.setUpdatedBillingProviderDetails(provider)
      : this.billingFacade.resetBillingProviderDetails();
  }

  onEventUpdateSelectedForm(form: BillingForm): void {
    if (!form) return;
    this.billingFacade.updateForm(form);
  }

  onEventUpdatePaymentType(paymentTypeCode: string): void {
    this.billingFacade.setPaymentType(paymentTypeCode);
  }

  onEventUpdateSelectedFavourite(favourite: BillingFavourite): void {
    if (favourite) {
      if (favourite.billingItems.length) {
        favourite.billingItems
          .filter((item) => item !== null && !item.deleted)
          .forEach((item) => {
            this.createBillItem(
              item.service,
              item.serviceCount,
              item.favouritePercentage
            );
          });
      }

      if (favourite.dx) {
        this.setDiagnosticCode(favourite.dx);
      }

      if (favourite.location) {
        this.setFacilityNum(favourite.location);
      }

      if (favourite.sliCode) {
        this.setLocation(favourite.sliCode);
      }

      if (favourite.visitType) {
        this.setVisitType(favourite.visitType);
      }
    }
  }

  onEventCheckPrintInvoiceComment(): void {
    this.printInvoiceComment = !this.printInvoiceComment;
  }

  allBillingItemsHasDxCodes() {
    let hasDxCodes = true;
    this.bill.billingItems.forEach((item) => {
      if (item.dx === '') {
        hasDxCodes = false;
      }
    });
    return hasDxCodes;
  }

  validateBillingItemsDxCodes(): boolean {
    //validate if bill items has dx codes
    const warning = this.warnings.find(
      (warning) => warning.type === 'Missing Dx Codes'
    );
    if (!this.allBillingItemsHasDxCodes() && !warning && !this.thirdParty) {
      this.billingFacade.setWarnings([
        ...this.warnings,
        {
          type: 'Missing Dx Codes',
          text: 'Include them with each Service Code to avoid OHIP rejection and reconciliation. Or continue with save/close',
        },
      ]);
      return true;
    }

    return false;
  }

  onSettleChange(): void {
    this.isSettle = !this.isSettle;
  }

  onPrintChange(): void {
    this.isPrint = !this.isPrint;
  }

  onEventSaveAndClose(goToNextAppointment = false): void {
    if (this.isPrint && this.isSettle) {
      return this.onEventSaveAndPrint(true);
    }

    if (this.isPrint) {
      return this.onEventSaveAndPrint();
    }
    const shouldShowWarning = this.validateBillingItemsDxCodes();
    if (shouldShowWarning) return;
    this.billingFacade.saveBill(
      this.printInvoiceComment,
      goToNextAppointment,
      false,
      false,
      this.selectedSpecialist,
      this.billingHistoryFilter,
      false
    );
  }

  onEventSaveAndAddBill(): void {
    const shouldShowWarning = this.validateBillingItemsDxCodes();
    if (shouldShowWarning) return;
    this.billingFacade.saveBill(
      this.printInvoiceComment,
      false,
      false,
      false,
      this.selectedSpecialist,
      this.billingHistoryFilter,
      true
    );
  }

  onEventSaveAndPrint(settle = false): void {
    const shouldShowWarning = this.validateBillingItemsDxCodes();
    if (shouldShowWarning) return;
    this.billingFacade.saveBill(
      this.printInvoiceComment,
      false,
      settle,
      true,
      this.selectedSpecialist,
      this.billingHistoryFilter,
      false
    );
  }

  onPayment(paymentType: string) {
    const allItemsPaid = this.bill?.billingItems.every((item) =>
      item.itemPayments.every((payment) => payment.paid === +item.fee)
    );

    if (paymentType === 'all' || allItemsPaid) {
      this.isSettle = true;
    }
  }
}
