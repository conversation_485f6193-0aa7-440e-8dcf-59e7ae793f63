import {Component, OnInit} from '@angular/core';
import {faPlus, IconDefinition} from '@fortawesome/free-solid-svg-icons';
import {ApiOneIdService} from '@oscar-pro/data-access'
import {Viewlet} from "@oscar-pro/interfaces";

@Component({
    selector: 'oscar-pro-one-id-viewlet-config',
    templateUrl: './viewlet-config.component.html',
    styleUrls: ['./viewlet-config.component.scss'],
    standalone: false
})
export class ViewletConfigComponent implements OnInit {

  public createViewlet = false;
  public viewletList: Array<Viewlet> = new Array<Viewlet>();
  public plusIcon: IconDefinition = faPlus;

  constructor(
    private oneIdService: ApiOneIdService,
  ) {
  }

  ngOnInit() {
    this.getViewletList();
  }

  private getViewletList() {
    this.oneIdService.getViewletList()
      .subscribe(viewletList => {
        this.viewletList = viewletList;
      });
  }

  viewletDeletedHandler(viewlet: Viewlet) {
    const index = this.viewletList.map(u => u.id).indexOf(viewlet.id);
    this.viewletList.splice(index, 1);
  }

  onViewletEditHandler(viewlet: Viewlet) {
    this.viewletList.forEach(viewletItem => viewletItem.edit = viewletItem.id == viewlet?.id);
  }

  viewletAddedHandler(viewlet: Viewlet) {
    this.viewletList.push(viewlet);
    this.createViewlet = false;
  }

  addNewViewlet(enabled?: boolean) {
    if (enabled === undefined) {
      enabled = !this.createViewlet;
    }
    this.createViewlet = enabled;
  }
}
