import {Component, OnInit} from '@angular/core';
import {faPlus, IconDefinition} from '@fortawesome/free-solid-svg-icons';
import {ApiOneIdService, ApiProviderService} from '@oscar-pro/data-access';
import {ProviderSelectable, Uao} from '@oscar-pro/interfaces';
import { firstValueFrom } from 'rxjs';

@Component({
    selector: 'oscar-pro-one-id-uao-admin',
    templateUrl: './uao-admin.component.html',
    styleUrls: ['./uao-admin.component.scss'],
    standalone: false
})
export class UaoAdminComponent implements OnInit {

  public createUao = false;
  public uaoList: Array<Uao> = new Array<Uao>();
  public plusIcon: IconDefinition = faPlus;
  public providers: Array<ProviderSelectable> = new Array<ProviderSelectable>();

  constructor(
    private apiProviderService: ApiProviderService,
    private oneIdService: ApiOneIdService,
  ) {
  }

  async ngOnInit() {
    await this.getProviders();
    this.getUaoList();
  }

  private getUaoList() {
    this.oneIdService.getUaoList()
    .subscribe(uaoList => {
      this.uaoList = uaoList;
    });
  }

  private async getProviders(): Promise<void> {
    this.providers = await firstValueFrom(this.apiProviderService.getProviders()) as Array<ProviderSelectable>;
  }

  uaoDeletedHandler(uao: Uao) {
    const index = this.uaoList.map(u => u.id).indexOf(uao.id);
    this.uaoList.splice(index, 1);
  }

  onUaoEditHandler(uao: Uao) {
    this.uaoList.forEach(uaoItem => uaoItem.edit = uaoItem.id == uao.id);
  }

  uaoAddedHandler(uao: Uao) {
    this.uaoList.push(uao);
    this.createUao = false;
  }

  addNewUao(enabled?: boolean) {
    if (enabled === undefined) {
      enabled = !this.createUao;
    }
    this.createUao = enabled;
  }
}
