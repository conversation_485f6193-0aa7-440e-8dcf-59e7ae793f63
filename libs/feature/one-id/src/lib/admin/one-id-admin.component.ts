import {Component, ViewEncapsulation} from '@angular/core';
import {ActivatedRoute} from '@angular/router';

@Component({
    selector: 'oscar-pro-one-id-admin',
    templateUrl: './one-id-admin.component.html',
    styleUrls: ['./one-id-admin.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class OneIdAdminComponent {

  constructor(private route: ActivatedRoute) {
    this.route.queryParams.subscribe(params => {
      if ('iframe' in params && params['iframe'] === 'true') {
        document.body.classList.add('admin--iframe');
      }
    });
  }
}
