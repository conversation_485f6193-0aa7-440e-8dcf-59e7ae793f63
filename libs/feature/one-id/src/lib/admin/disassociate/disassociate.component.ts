import {Component} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {AuthService} from '@oscar-pro/auth';
import {ApiOneIdService} from '@oscar-pro/data-access';
import {User} from '@oscar-pro/interfaces';

@Component({
    selector: 'oscar-pro-one-id-disassociate',
    templateUrl: './disassociate.component.html',
    styleUrls: ['./disassociate.component.scss'],
    standalone: false
})
export class DisassociateComponent {
  public loading = true;
  private currentUser: User | undefined;
  public username = '';
  public error = false;
  public status: number | null = null;

  constructor(private oneIdService: ApiOneIdService,
              private authService: AuthService,
              private route: ActivatedRoute) {
    this.currentUser = this.authService.getCurrentUser();
    this.route.params.subscribe(params => {
      this.username = params['username'];
      this.disassociateProvider(params['username']);
    });
  }

  close() {
    window.close();
  }

  disassociateProvider(username: string) {
    this.oneIdService.disassociateProvider(username).subscribe(
        () => {
            this.loading = false;
            this.notifyParent(username, 200);
            this.close();
        },
        error => {
          this.status = error.status;
          this.notifyParent(username, error.status);
          this.error = true;
        }
    );
  }

  notifyParent(username: string, status: number) {
    if (!window.opener || !window.opener.disassociateHandler) { return; }
    window.opener.disassociateHandler(username, status, window);
  }
}
