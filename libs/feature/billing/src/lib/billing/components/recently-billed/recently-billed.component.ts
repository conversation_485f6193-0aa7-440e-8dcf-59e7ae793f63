import { Component, EventEmitter, Input, Output } from '@angular/core';
import { BillingService } from '@oscar-pro/interfaces';

@Component({
    selector: 'oscar-pro-recently-billed',
    templateUrl: './recently-billed.component.html',
    styleUrls: ['./recently-billed.component.scss'],
    standalone: false
})
export class RecentlyBilledComponent {
  @Input() recentServices: Array<BillingService>;

  @Output()
  createBillItem: EventEmitter<BillingService> = new EventEmitter<BillingService>();

  onClickServiceTag(event: MouseEvent, recentService: BillingService): void {
    event.preventDefault();

    this.createBillItem.emit(recentService);
  }
}
