import {
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  OnChanges,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  AppConfig,
  Appointment,
  Bill,
  BillingDxCode,
  BillingExtensions,
  BillingFavourite,
  BillingForm,
  BillingItem,
  BillingPaymentType,
  BillingService as IBillingService,
  BillingThirdPartyAddress,
  ClinicLocation,
  Demographic,
  EnhancedOptions,
  OptionValue,
  Property,
  Provider,
  ProviderPreferences,
  Specialist,
  User,
  VisitType,
} from '@oscar-pro/interfaces';
import {
  ApiBillingService,
  ApiPropertyService,
  ApiProviderService,
  ApiSpecialistService,
  BillingConstants,
  BillingService,
} from '@oscar-pro/data-access';
import { BillingService as NewBillingService } from '@oscar-pro/new-data-access';
import { AuthService } from '@oscar-pro/auth';
import {
  ObjectArrayFilterPipe,
  OptionValueFilterPipe,
  PanelService,
  UrlParamsPipe,
  WindowRefService,
} from '@oscar-pro/util';
import { DxPanelComponent } from '../dx-panel/dx-panel.component';
import { SuperCodesComponent } from '../super-codes/super-codes.component';
import { SlidePanelComponent } from '@oscar-pro/ui';
import { BillingFacade } from '@oscar-pro/core-state';
import { Observable } from 'rxjs/internal/Observable';
import * as _ from 'lodash';
import { APP_CONFIG } from '@oscar-pro/config';
import { faExclamationTriangle, faExternalLinkAlt, faPlus } from '@fortawesome/free-solid-svg-icons';
import { BreakpointObserver } from "@angular/cdk/layout";
import { isNil } from 'lodash';

@Component({
    selector: 'oscar-pro-billing-create',
    templateUrl: './create.component.html',
    styleUrls: ['./create.component.scss'],
    standalone: false
})
export class CreateComponent implements OnInit, OnChanges {
  @Input() appointment: Appointment;
  @Input() bill: Bill;
  @Input() demographic: Demographic;
  @Input() hasNextAppointment: boolean;
  @Input() hst: number;
  @Input() superCodes: Array<BillingFavourite>;
  @Input() billItem: BillingItem;
  @Input() billPercentCodes: Array<BillingItem> = [] as Array<BillingItem>;
  @Input() billingRefBoxDefault = false;
  @Input() clinicLocations: Array<ClinicLocation>;
  @Input() billingExtensions: BillingExtensions;
  @Input() paymentTypes: Array<BillingPaymentType>;
  @Input() printInvoiceComment = false;
  @Input() providerPreferences: ProviderPreferences;
  @Input() selectedSpecialist: Specialist;
  @Input() selectedForm: BillingForm;
  @Input() thirdParty: boolean;
  @Input() visitTypes: Array<VisitType>;
  @Input() dxDefaultVal: string;

  @Output() cancelNewBill: EventEmitter<null> = new EventEmitter<null>();
  @Output()
  checkDemographicRefDoc: EventEmitter<null> = new EventEmitter<null>();
  @Output()
  checkPrintInvoiceComment: EventEmitter<null> = new EventEmitter<null>();
  @Output()
  createBillItem: EventEmitter<IBillingService> = new EventEmitter<IBillingService>();
  @Output()
  removeBillingItem: EventEmitter<BillingItem> = new EventEmitter<BillingItem>();
  @Output() saveAndClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() saveAndAddBill: EventEmitter<null> = new EventEmitter<null>();
  @Output() saveAndPrint: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output()
  saveFavourite: EventEmitter<BillingFavourite> = new EventEmitter<BillingFavourite>();
  @Output()
  updateBillingItem: EventEmitter<object> = new EventEmitter<object>();
  @Output() updateDxCode: EventEmitter<object> = new EventEmitter<object>();
  @Output()
  updatePaymentType: EventEmitter<string> = new EventEmitter<string>();
  @Output()
  updatePercentTotal: EventEmitter<object> = new EventEmitter<object>();
  @Output()
  updateSelectedFavourite: EventEmitter<BillingFavourite> = new EventEmitter<BillingFavourite>();
  @Output()
  updateSpecialist: EventEmitter<Specialist> = new EventEmitter<Specialist>();
  @Output()
  updateComment: EventEmitter<string> = new EventEmitter<string>();
  @Output()
  updateThirdPartyAddress: EventEmitter<{
    thirdPartyAddress: string;
    addressField: string;
  }> = new EventEmitter<{ thirdPartyAddress: string; addressField: string }>();

  @ViewChild('billTo') billToTextareaEl: ElementRef;
  @ViewChild('refNum') refNumInputEl: ElementRef;
  @ViewChild('remitTo') remitToTextareaEl: ElementRef;

  private currentProvider: Provider;
  private currentUser: User;
  private nativeWindow: Window;
  private serviceList: any = [];
  private specialistList: Array<Specialist> = [] as Array<Specialist>;
  private thirdPartyAddressList: Array<BillingThirdPartyAddress> =
    [] as Array<BillingThirdPartyAddress>;
  private thirdPartyAddressSearchField: string;
  private favouriteList: any = [];

  public sliCodes = BillingConstants.sliCodes;
  public dxCodeOptions: OptionValue[] = [] as Array<OptionValue>;
  public favouriteOptions: OptionValue[] = [] as Array<OptionValue>;
  public paymentTypeOptions: OptionValue[] = [] as Array<OptionValue>;
  public serviceOptions: OptionValue[] = [] as Array<OptionValue>;
  public specialistOptions: OptionValue[] = [] as Array<OptionValue>;
  public thirdPartyAddressOptions: any = {
    billTo: [] as OptionValue[],
    remitTo: [] as OptionValue[],
  };
  public faExternalLinkAlt = faExternalLinkAlt;
  public faExclamation = faExclamationTriangle;
  public faPlus = faPlus;
  public isCondensedView = false;

  superCodes$: Observable<Array<BillingFavourite>> =
    this.billingFacade.superCodes$;

  public superCodeEnhancedOptions: Array<EnhancedOptions>;
  public filteredSuperCodeEnhancedOptions: Array<EnhancedOptions>;

  constructor(
    @Inject(LOCALE_ID) private locale: string,
    private apiBillingService: ApiBillingService,
    private apiPropertyService: ApiPropertyService,
    private apiProviderService: ApiProviderService,
    private apiSpecialistService: ApiSpecialistService,
    private authService: AuthService,
    private billingService: BillingService,
    public newBillingService: NewBillingService,
    private objectArrayFilter: ObjectArrayFilterPipe,
    private optionValueFilter: OptionValueFilterPipe,
    // private qvService: QuickViewService,
    private urlParamsPipe: UrlParamsPipe,
    private windowRefService: WindowRefService,
    private panelService: PanelService,
    private billingFacade: BillingFacade,
    private breakpointObserver: BreakpointObserver,
  @Inject(APP_CONFIG) private appConfig: AppConfig
  ) {
    this.nativeWindow = this.windowRefService.getNativeWindow();

    this.superCodes$.subscribe((superCodes: BillingFavourite[]) => {
      this.superCodes = superCodes;
      const enhancedOptions= this.superCodes.map(el => {
        return { label: el.name, id: el.id }
      });
      this.superCodeEnhancedOptions = [{group: 'default', options: enhancedOptions }];
    });

    this.billingService.searchDxCodesResults.subscribe(
      (data) => {
        if (!isNil(data['itemIndex'])) {
          const options = [];
          data['results'].forEach(dxCode => options.push(
            {option: dxCode.diagnosticCode + ' - ' + dxCode.description, value: dxCode.diagnosticCode}));
          this.billingFacade.updateBillingItemDxSearchOptions(options, data['itemIndex']);
        } else {
          data['results'].forEach(dxCode => this.dxCodeOptions.push(
            {option: dxCode.diagnosticCode + ' - ' + dxCode.description , value: dxCode.diagnosticCode}));
        }
      }, () => {
        this.dxCodeOptions = [] as Array<OptionValue>;
        this.bill.billingItems.forEach(item => item.dxCodeSearchOptions = [] as Array<OptionValue>);
      }
    );

    this.billingService.searchServicesResults.subscribe(
      (data) => {
        this.serviceList = !isNil(data) ? data : ([] as Array<IBillingService>);
        this.serviceList.forEach((service) =>
          this.serviceOptions.push({
            option: service.serviceCode + ' - ' + service.description,
            value: service.serviceCode,
          })
        );
      },
      () => (this.serviceOptions = [] as Array<OptionValue>)
    );

    this.billingService.searchAllBillingFavouritesResults.subscribe(
      (data) => {
        this.favouriteList = data;
        this.favouriteList.forEach((favourite) => {
            this.favouriteOptions.push({
              option: favourite.name,
              value: favourite,
            })
          }
        );
      },
      () => (this.serviceOptions = [] as Array<OptionValue>)
    );

    this.billingService.searchThirdPartyAddressesResults.subscribe(
      (data) => {
        this.thirdPartyAddressList =
          !isNil(data) ? data : ([] as Array<BillingThirdPartyAddress>);
        if (this.thirdPartyAddressSearchField === 'billTo') {
          this.thirdPartyAddressList.forEach((company) =>
            this.thirdPartyAddressOptions.billTo.push({
              option:
                company.companyName
                + ' - '
                + company.address
                + ' ('
                + company.city
                + ') \n '
                + company.telephone,
              value: company.id.toString(),
            })
          );
        } else if (this.thirdPartyAddressSearchField === 'remitTo') {
          this.thirdPartyAddressList.forEach((company) =>
            this.thirdPartyAddressOptions.remitTo.push({
              option:
                company.companyName
                + ' - '
                + company.address
                + ' ('
                + company.city
                + ') \n '
                + company.telephone,
              value: company.id.toString(),
            })
          );
        }
      },
      () =>
        (this.thirdPartyAddressOptions = [
          [] as Array<BillingThirdPartyAddress>,
          [] as Array<BillingThirdPartyAddress>,
        ]),
      () => (this.thirdPartyAddressSearchField = null)
    );
  }

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
    if (this.currentUser) {
      this.apiProviderService.getProvider(this.currentUser.providerNo).subscribe(
        (data) => (this.currentProvider = data),
        (error) => console.error(error)
      );
    }

    this.apiPropertyService.getBillingProperties().subscribe((data) => {
      if (data) {
        const remitToIndex = this.getPropertyIndex(data, "remit_to_other_text");
        const billToIndex = this.getPropertyIndex(data, "bill_to_other_text");
        if (this.propertyExists(data, remitToIndex) && this.hcTypeIsQuebecOrOther()) {
          this.setThirdPartyAddress(data, remitToIndex, "remitTo");
        }
        if (this.propertyExists(data, billToIndex) && this.hcTypeIsQuebecOrOther()) {
          this.setThirdPartyAddress(data, billToIndex, "billTo");
        }
      }
    });
    // if page is less than 1270px condense billing items on page
    this.breakpointObserver.observe("(max-width: 1425px)")
      .subscribe(result => {
        this.isCondensedView = result.matches;
      });
  }

  ngOnChanges() {

    if (this.paymentTypes) {
      this.paymentTypeOptions = this.optionValueFilter.transform(
        this.paymentTypes,
        { option: 'paymentType', value: 'id' }
      );
    }
    this.paymentTypeOptions.splice(0, 0, {
      option: '- Payment Method -',
      value: '',
    });
    if (this.selectedSpecialist) {
      this.refNumInputEl.nativeElement.value = this.selectedSpecialist
        .referralNo
        ? this.selectedSpecialist.referralNo
        : '';
    }
  }

  onBlurPrice(event: [Event, any], billingItem: BillingItem, index: number) {
    if (billingItem?.service?.value === '') {
      const newBillingItem = {
        ...billingItem,
        service: {
          ...billingItem.service,
          value: '0'
        }
      };

      this.updateBillingItem.emit({
        billingItem: newBillingItem,
        index: index,
        isThirdParty: this.thirdParty,
      });
    }
  }

  onBlurQty(event: [Event, any], billingItem: BillingItem, index: number) {
    if (billingItem?.serviceCount === '') {
      const newBillingItem = {
        ...billingItem,
        serviceCount: '1'
      };

      this.updateBillingItem.emit({
        billingItem: newBillingItem,
        index: index,
        isThirdParty: this.thirdParty,
      });
    }
  }

  onClickCancelNewBill(event: MouseEvent): void {
    event.preventDefault();
    this.cancelNewBill.emit();
  }

  onClickOpenHcvWindow(event: MouseEvent): void {
    event.preventDefault();
    const params = {
      hc: `${this.demographic.hin}${this.demographic.ver}`,
      providerNo: this.currentProvider.providerNo,
    };
    const url = `/CardSwipe/${this.urlParamsPipe.transform(params)}`;
    const windowFeatures = 'height=500,width=500';
    this.nativeWindow.open(url, '_blank', windowFeatures);
  }

  onClickOpenMasterWindow(event: MouseEvent): void {
    event.preventDefault();
    const params = {
      demographic_no: this.demographic.demographicNumber,
      displaymode: 'edit',
    };
    const url = `/${
      this.appConfig.oscarContext
    }/demographic/demographiccontrol.jsp${this.urlParamsPipe.transform(
      params
    )}`;
    const windowFeatures = 'height=710,width=1024';
    this.nativeWindow.open(url, '_blank', windowFeatures);
  }

  onClickOpenReferringPractitionerWindow(event: MouseEvent): void {
    event.preventDefault();
    let keyword = '';
    if (
      <HTMLInputElement>document.getElementById('refName')
      && (<HTMLInputElement>document.getElementById('refName')).value
    ) {
      keyword = (<HTMLInputElement>document.getElementById('refName')).value;
    }
    const params = {
      keyword: keyword,
      param: "document.getElementById('refNum').value",
      param2: "document.getElementById('refName').value",
      submit: 'Search',
    };

    const url = `/${
      this.appConfig.oscarContext
    }/billing/CA/ON/searchRefDoc.jsp${this.urlParamsPipe.transform(params)}`;
    const window = this.nativeWindow.open(url, 'att', 'height=600,width=700');
    const timer = setInterval(() => {
      if (window.closed) {
        clearInterval(timer);
        const refNum = <HTMLInputElement>document.getElementById('refNum');
        this.apiSpecialistService
          .findSpecialistById(parseInt(refNum.value))
          .subscribe(
            (data) => {
              (<HTMLInputElement>document.getElementById('refName')).value =
                this.getSpecialistName(data);
              this.updateSpecialist.emit(data);
            },
            (error) => console.error(error)
          );
      }
    }, 500);
  }

  onClickRemoveBillingItem(eventObject, billingItem: BillingItem): void {
    event.preventDefault();
    this.removeBillingItem.emit(billingItem);
  }

  onClickSaveAndCloseNewBill(event: MouseEvent): void {
    event.preventDefault();
    this.saveAndClose.emit();
  }

  onClickSaveAndAddBill(event: MouseEvent): void {
    event.preventDefault();
    this.saveAndAddBill.emit();
  }

  onClickSaveAndNextNewBill(event: MouseEvent): void {
    event.preventDefault();
    this.saveAndClose.emit(true);
  }

  onClickSaveAndPrintNewBill(event: MouseEvent): void {
    event.preventDefault();
    this.saveAndPrint.emit();
  }

  onClickSaveSettleAndPrintNewBill(event: MouseEvent): void {
    event.preventDefault();
    this.saveAndPrint.emit(true);
  }

  onEventCheckPrintInvoiceComment(): void {
    this.checkPrintInvoiceComment.emit();
  }

  onEventCheckRefBox(): void {
    this.checkDemographicRefDoc.emit();
  }

  onEventUpdatePaymentType(selectedPayment: any): void {
    this.updatePaymentType.emit(selectedPayment.option);
  }

  onEventCreateBillItem(serviceCode: string): void {
    const service: IBillingService = <IBillingService>(
      this.objectArrayFilter.transform(this.serviceList, {
        serviceCode: serviceCode,
      })[0]
    );
    this.serviceOptions = [] as Array<OptionValue>;
    this.createBillItem.emit(service);
  }

  onChangeDX(event: Event, itemIndex: number = null) {
    if (event.target['value'] === '') {
      // Reset dx code because it is blank
      this.updateDxCode.emit({ dxCode: event.target['value'], itemIndex: itemIndex });
    }
  }

  onEventUpdateDxCode(dxCode: string, itemIndex: number = null): void {
    event.preventDefault();
    this.updateDxCode.emit({ dxCode: dxCode, itemIndex: itemIndex });
  }

  onBlurUpdateDxCode(dxValue: string, options: OptionValue[], itemIndex: number = null): void {
    const dxCode = dxValue?.trim();
    if (!dxCode || options.some(({value}) => value === dxCode)) {
      this.updateDxCode.emit({ dxCode: dxCode, itemIndex: itemIndex });
    }
  }

  onEventUpdatePercentTotal() {
    this.updatePercentTotal.emit();
  }

  onEventUpdateSelectedFavourite(favourite: any): void {
    const selectedFavourite = <BillingFavourite>(
      this.objectArrayFilter.transform(this.superCodes, { name: favourite })[0]
    );
    this.updateSelectedFavourite.emit(selectedFavourite);
  }

  onEventUpdateThirdPartyAddressTextArea(
    event: Event,
    addressField: string
  ): void {
    this.updateThirdPartyAddress.emit({
      thirdPartyAddress: event.target['value'],
      addressField: addressField,
    });
  }

  onEventUpdateThirdPartyBillTo(thirdPartyAddressId: string): void {
    const thirdPartyAddress: BillingThirdPartyAddress = <
      BillingThirdPartyAddress
    >this.objectArrayFilter.transform(this.thirdPartyAddressList, {
      id: +thirdPartyAddressId,
    })[0];
    this.updateThirdPartyAddress.emit({
      thirdPartyAddress:
        this.billingFacade.thirdPartyAddressToString(thirdPartyAddress),
      addressField: 'billTo',
    });
  }

  onEventUpdateThirdPartyRemitTo(thirdPartyAddressId: string): void {
    const thirdPartyAddress: BillingThirdPartyAddress = <
      BillingThirdPartyAddress
    >this.objectArrayFilter.transform(this.thirdPartyAddressList, {
      id: +thirdPartyAddressId,
    })[0];
    this.updateThirdPartyAddress.emit({
      thirdPartyAddress:
        this.billingFacade.thirdPartyAddressToString(thirdPartyAddress),
      addressField: 'remitTo',
    });
  }

  onChangeDiscount(
    event: Event,
    billingItem: BillingItem,
    index: number
  ): void {
    event.preventDefault();
    const newBillingItem = _.cloneDeep(billingItem);
    const eventTarget = (<HTMLInputElement> event.target)
    if (eventTarget.checked) {
      newBillingItem.itemPayments[0].discount = +billingItem.fee;
    } else if (eventTarget.type == "text") {
      newBillingItem.itemPayments[0].discount = <number>event.target['value'];
    } else {
      newBillingItem.itemPayments[0].discount = 0;
    }
    this.updateBillingItem.emit({
      billingItem: newBillingItem,
      index: index,
      isThirdParty: this.thirdParty,
    });
  }

  onChangePayment(event: Event, billingItem: BillingItem, index: number) {
    event.preventDefault();
    const newBillingItem = _.cloneDeep(billingItem);
    const eventTarget = (<HTMLInputElement> event.target)
    if (eventTarget.checked) {
      newBillingItem.itemPayments[0].paid = +billingItem.fee;
    } else if (eventTarget.type == "text") {
      newBillingItem.itemPayments[0].paid = <number>event.target['value'];
    } else {
      newBillingItem.itemPayments[0].paid = 0;
    }

    this.updateBillingItem.emit({
      billingItem: newBillingItem,
      index: index,
      isThirdParty: this.thirdParty,
    });
  }

  onCheckAll(className: string) {
    Array.from(document.getElementsByClassName(className))
        .forEach(element => this.dispatchChangeEvent(<HTMLInputElement> element))
  }

  dispatchChangeEvent(element: HTMLInputElement) {
    element.checked = true;
    element.dispatchEvent(new Event('change'));
  }

  onChangeComment(event: Event) {
    event.preventDefault();
    this.updateComment.emit(<string>event.target['value']);
  }

  onEventUpdatePayee(event: Event) {
    event.preventDefault();

    this.updateThirdPartyAddress.emit({
      thirdPartyAddress: event.target['value'],
      addressField: 'payee',
    });
  }

  onInputPercentage(event: Event, billingItem: BillingItem, index: number) {
    event.preventDefault();
    const newBillingItem = {
      ...billingItem,
      service: {
        ...billingItem.service,
        percentage: <string>event.target['value'],
      },
    };
    this.updateBillingItem.emit({
      billingItem: newBillingItem,
      index: index,
      isThirdParty: this.thirdParty,
    });
  }

  onInputPrice(event: Event, billingItem: BillingItem, index: number): void {
    event.preventDefault();
    const newBillingItem = {
      ...billingItem,
      service: {
        ...billingItem.service,
        value: <string>event.target['value'],
      },
    };

    this.updateBillingItem.emit({
      billingItem: newBillingItem,
      index: index,
      isThirdParty: this.thirdParty,
    });
  }

  onInputQty(event: Event, billingItem: BillingItem, index: number): void {
    event.preventDefault();
    const newBillingItem = {
      ...billingItem,
      serviceCount: <string>event.target['value'],
    };
    this.updateBillingItem.emit({
      billingItem: newBillingItem,
      index: index,
      isThirdParty: this.thirdParty,
    });
  }

  onInputSearchSpecialist(keyword: string): void {
    this.specialistList = [] as Array<Specialist>;
    this.specialistOptions = [] as Array<OptionValue>;
    if (keyword && keyword.length) {
      this.apiSpecialistService.searchSpecialists(keyword).subscribe(
        (data) => {
          this.specialistList = data;
          this.specialistList.forEach((specialist) =>
            this.specialistOptions.push({
              option: this.getSpecialistName(specialist, true, true),
              value: specialist.specialistId.toString(),
            })
          );
        },
        (error) => console.error(error)
      );
    }
  }

  onClickSelectSpecialist(specialistId: number): void {
    const specialist: Specialist = <Specialist>this.objectArrayFilter.transform(
      this.specialistList,
      {
        specialistId: specialistId,
      }
    )[0];
    (<HTMLInputElement>document.getElementById('refName')).value =
      this.getSpecialistName(specialist);
    this.updateSpecialist.emit(specialist);
  }

  onClickDx(event: MouseEvent): void {
    event.preventDefault();

    const inputs = {
      serviceTypeName: this.selectedForm?.serviceTypeName,
      dxCodes: this.selectedForm?.dxCodes,
      isActive: true,
    };
    // this.qvService.init(BillingDxQvComponent, inputs, {});

    const outputs = {
      selectDxCode: this.selectDxCode.bind(this),
    };

    const billingPanelRef = this.panelService.init(
      SlidePanelComponent,
      inputs,
      outputs,
      'billing-panel'
    );

    const billingPanelInstance = <SlidePanelComponent>billingPanelRef.instance;

    billingPanelInstance.componentType = DxPanelComponent;
    billingPanelInstance.componentConfig = { inputs, outputs };
  }

  selectDxCode(dxCode: BillingDxCode) {
    this.updateDxCode.emit({ dxCode: dxCode.dx.diagnosticCode, fieldNo: 0 });
  }

  onClickManageBillingAddresses(field: string): void {
    let keyword = '';

    if (
      !isNil(field)
      && <HTMLInputElement>document.getElementById(`${field}Search`)
      && (<HTMLInputElement>document.getElementById(`${field}Search`)).value
    ) {
      keyword = (<HTMLInputElement>document.getElementById(`${field}Search`))
        .value;
    }

    const params = {
      keyword: keyword,
      param: field,
    };
    const url = `/${
      this.appConfig.oscarContext
    }/billing/CA/ON/onSearch3rdBillAddr.jsp${this.urlParamsPipe.transform(
      params
    )}`;
    this.nativeWindow.open(url, 'att', 'height=600,width=700');
  }

  onClickManageFavourites(event: MouseEvent): void {
    event.preventDefault();

    const inputs = {
      bill: this.bill,
      clinicLocations: this.clinicLocations,
      favouriteOptions: this.favouriteOptions,
      serviceList: this.serviceList,
      sliCodes: this.sliCodes,
      superCodes: this.superCodes,
      visitTypes: this.visitTypes,
      isActive: true,
    };

    const outputs = {
      saveFavourite: this.saveFavourite,
    };

    const billingPanelRef = this.panelService.init(
      SlidePanelComponent,
      inputs,
      outputs,
      'billing-panel'
    );

    const inboxPanelInstance = <SlidePanelComponent>billingPanelRef.instance;

    inboxPanelInstance.componentType = SuperCodesComponent;
    inboxPanelInstance.componentConfig = { inputs, outputs };
  }

  getPaymentTypeDisplay(selectedPaymentType): string {
    let paymentTypeName = '- Payment Method -';
    if (selectedPaymentType && this.paymentTypes.length > 0) {
      const paymentType: BillingPaymentType = <BillingPaymentType>(
        this.objectArrayFilter.transform(this.paymentTypes, {
          id: +selectedPaymentType,
        })[0]
      );
      paymentTypeName =
        !isNil(paymentType) ? paymentType.paymentType : paymentTypeName;
    }
    return paymentTypeName;
  }

  getRosterStatusDisplay(rosterStatus: string): string {
    let rosterStatusDisplay = rosterStatus;

    if (rosterStatusDisplay === 'RO') {
      rosterStatusDisplay = 'EN';
    } else if (rosterStatusDisplay === 'NR') {
      rosterStatusDisplay = 'NE';
    }

    return rosterStatusDisplay;
  }

  getSpecialistName(
    specialist: Specialist,
    includeSpecialty = false,
    includeReferralNumber = false
  ): string {
    let specialistName = '';
    if (specialist) {
      specialistName =
        `${specialist.lastName}, ${specialist.firstName}`
        + (includeReferralNumber
          ? ` (${specialist.referralNo ? specialist.referralNo : 'None'} `
          : '')
        + (includeSpecialty
          ? `- ${specialist.specialty ? specialist.specialty : 'None'})`
          : '');
    }
    return specialistName;
  }

  identifyBillingItem(index: number, item: any) {
    return item.id;
  }

  identifyPremiumCode(index: number, item: any) {
    return item.serviceCode;
  }

  searchDxCodes(keyword: string, itemIndex: number = null): void {
    if (!isNil(itemIndex)) {
      this.billingFacade.updateBillingItemDxSearchOptions([], itemIndex);
    } else {
      this.dxCodeOptions = [] as Array<OptionValue>;
    }
    this.billingService.searchDxCodes.emit({ keyword: keyword, itemIndex: itemIndex });
  }

  searchServices(keyword: string): void {
    this.serviceList = [] as Array<IBillingService>;
    this.serviceOptions = [] as Array<OptionValue>;
    this.billingService.searchServices.emit(keyword);
  }

  searchFavourites(keyword: string): void {
    this.filteredSuperCodeEnhancedOptions = _.cloneDeep(
        this.superCodeEnhancedOptions
    );
    const subOptions = this.filteredSuperCodeEnhancedOptions[0]['options'].filter(
        (word) => word.label.toLowerCase().indexOf(keyword) > -1
    );
    this.filteredSuperCodeEnhancedOptions[0]['options'] = subOptions;
  }

  searchThirdPartyAddress(keyword: string, field: string): void {
    this.thirdPartyAddressList = [] as Array<BillingThirdPartyAddress>;
    this.thirdPartyAddressOptions = {
      billTo: [] as Array<OptionValue>,
      remitTo: [] as Array<OptionValue>,
    };
    this.thirdPartyAddressSearchField = field;
    this.billingService.searchThirdPartyAddresses.emit(keyword);
  }

  hasInvalidHin(): boolean {
    const invalidTypes = ["OT", "QC"]
    return this.bill?.payProgram === "ODP"
      && (!this.demographic.hin
      || invalidTypes.includes(this.demographic.hcType));
  }

  private propertyExists(propertyList: Property[], index: number) {
    return index > -1 && propertyList[index]
  }

  private hcTypeIsQuebecOrOther(): boolean {
    return this.demographic.hcType === "QC" || this.demographic.hcType === "OT";
  }

  private getPropertyIndex(propertyList: Property[], propertyName: string): number {
    return propertyList.findIndex(
      (property) => property.name === propertyName
    );
  }

  private setThirdPartyAddress(propertyList: Property[], index: number, addressField: string) {
    this.updateThirdPartyAddress.emit({
      thirdPartyAddress: propertyList[index].value,
      addressField: addressField,
    })
  }
}
