import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { AppConfig, Bill } from '@oscar-pro/interfaces';
import {
  ObjectArrayFilterPipe,
  UrlParamsPipe,
  WindowRefService,
} from '@oscar-pro/util';
import { APP_CONFIG } from '@oscar-pro/config';

@Component({
    selector: 'oscar-pro-billing-history',
    templateUrl: './billing-history.component.html',
    styleUrls: ['./billing-history.component.scss'],
    standalone: false
})
export class BillingHistoryComponent implements OnInit {
  @Input() billingHistory: Array<Bill>;
  @Input() billingHistoryCount: number;
  @Input() billingHistoryCurrentPage: number;
  @Input() isLoadingMore: boolean;
  @Output() changeDaysBack: EventEmitter<number> = new EventEmitter<number>();
  @Output() changeServiceCode: EventEmitter<string> = new EventEmitter<string>();
  @Output() selectBillingHistoryPage: EventEmitter<number> =
    new EventEmitter<number>();

  @ViewChild(CdkVirtualScrollViewport)
  viewport: CdkVirtualScrollViewport;

  public sortedBillingHistory: Array<Bill>;
  private billingStatuses = [
    { code: 'H', description: 'Capitated' },
    { code: 'O', description: 'Bill OHIP' },
    { code: 'P', description: 'Bill Patient' },
    { code: 'N', description: 'Do Not Bill' },
    { code: 'W', description: "Bill Worker's Compensation Board" },
    { code: 'B', description: 'Submitted  OHIP' },
    { code: 'S', description: 'Settled' },
    { code: 'X', description: 'Bad Debt' },
    { code: 'D', description: 'Deleted' },
    { code: 'I', description: 'Bonus Codes' },
  ];
  private nativeWindow: Window;
  private timer = null; // Timer

  public daysBack: string = null;
  public serviceCode: string = null;
  public pageSize = 5;

  constructor(
    private objectArrayFilterPipe: ObjectArrayFilterPipe,
    private urlParamsPipe: UrlParamsPipe,
    private windowRefService: WindowRefService,
    @Inject(APP_CONFIG) private appConfig: AppConfig
  ) {}

  ngOnInit(): void {
    this.nativeWindow = this.windowRefService.getNativeWindow();
  }

  getBillStatus(billStatusCode: string): string {
    let status = '';
    const obj = this.objectArrayFilterPipe.transform(this.billingStatuses, {
      code: billStatusCode,
    })[0];
    if (obj) {
      status = obj['description'];
    }
    return status;
  }

  onChangeDaysBack(event) {
    event.preventDefault();
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.changeDaysBack.emit(parseInt(this.daysBack, 10));
    }, 1000);
  }

  onChangeServiceCode(event) {
    event.preventDefault();
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.changeServiceCode.emit(this.serviceCode);
    }, 1000);
  }

  onClickBillingId(event: MouseEvent, invoiceNo: number): void {
    event.preventDefault();
    const params = this.urlParamsPipe.transform({ billing_no: invoiceNo });
    const url = `/${this.appConfig.oscarContext}/billing/CA/ON/billingONCorrection.jsp${params}`;
    this.nativeWindow.open(url, '_blank', 'height=710,width=1024');
  }

  onSelectPage(page: number): void {
    this.selectBillingHistoryPage.emit(page);
  }
}
