import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
} from '@angular/core';
import { ApiProviderService, BillingConstants } from '@oscar-pro/data-access';
import {
  Appointment,
  Bill,
  BillingForm,
  BillingItem,
  BillType,
  ClinicLocation,
  Demographic,
  EnhancedOptions,
  OptionValue,
  OscarBillingDefaults,
  Provider,
  Site,
  SliCode,
  VisitType,
} from '@oscar-pro/interfaces';
import { ObjectArrayFilterPipe, OptionValueFilterPipe } from '@oscar-pro/util';
import * as _ from 'lodash';
import { isNil } from 'lodash';

@Component({
    selector: 'oscar-pro-billing-top-bar',
    templateUrl: './top-bar.component.html',
    styleUrls: ['./top-bar.component.scss'],
    standalone: false
})
export class TopBarComponent implements OnChanges {
  @Input() demographic: Demographic;
  @Input() oscarProperties: OscarBillingDefaults;
  @Input() bill: Bill;
  @Input() billItem: BillingItem;
  @Input() billSites: Array<ClinicLocation>;
  @Input() billTypes: Array<BillType>;
  @Input() billingForms: Array<BillingForm>;
  @Input() multiSiteBilling: boolean;
  @Input() providers: Array<Provider>;
  @Input() selectedForm: BillingForm;
  @Input() selectedSiteId: number;
  @Input() sites: Array<Site> = new Array<Site>();
  @Input() visitTypes: Array<VisitType>;
  @Input() isRmaEnabled: boolean;
  @Input() defaultMRP: Provider;
  @Input() appointment: Appointment;

  @Output()
  updateAdmissionDate: EventEmitter<string> = new EventEmitter<string>();
  @Output() updateBillSite: EventEmitter<string> = new EventEmitter<string>();
  @Output() updateBillType: EventEmitter<string> = new EventEmitter<string>();
  @Output()
  updateBillingForm: EventEmitter<BillingForm> = new EventEmitter<BillingForm>();
  @Output()
  updateBillingClinic: EventEmitter<Site> = new EventEmitter<Site>();
  @Output()
  updateBillingPhysician: EventEmitter<Provider> = new EventEmitter<Provider>();
  @Output()
  updateManualReview: EventEmitter<string> = new EventEmitter<string>();
  @Output()
  updateServiceDate: EventEmitter<string> = new EventEmitter<string>();
  @Output()
  updateSliCode: EventEmitter<SliCode> = new EventEmitter<SliCode>();
  @Output()
  updateVisitType: EventEmitter<VisitType> = new EventEmitter<VisitType>();

  public billSiteOptions: Array<OptionValue> = [] as Array<OptionValue>;
  public billTypeOptions: Array<OptionValue> = [] as Array<OptionValue>;
  public manualReview: string;
  public providerOptions: Array<OptionValue> = [] as Array<OptionValue>;
  public siteOptions: Array<OptionValue> = [] as Array<OptionValue>;
  public sliCodeOptions: Array<OptionValue> = [] as Array<OptionValue>;
  public visitTypeOptions: Array<OptionValue> = [] as Array<OptionValue>;
  public sliCodes = BillingConstants.sliCodes;

  public enhancedProviderOptions: Array<EnhancedOptions> =
    new Array<EnhancedOptions>();
  public filteredEnhancedProviderOptions: Array<EnhancedOptions> =
    new Array<EnhancedOptions>();
  public selectedProviderValue = '';

  public enhancedSiteOptions: Array<EnhancedOptions> =
    new Array<EnhancedOptions>();
  public filteredEnhancedSiteOptions: Array<EnhancedOptions> =
    new Array<EnhancedOptions>();

  constructor(
    private apiProviderService: ApiProviderService,
    private objectArrayFilter: ObjectArrayFilterPipe,
    private optionValueFilter: OptionValueFilterPipe
  ) {}

  ngOnChanges(): void {
    this.resetDropdownValues();

    this.billTypeOptions = this.optionValueFilter.transform(this.billTypes, {
      option: 'description',
      value: 'code',
    });
    if (this.sites && this.multiSiteBilling) {
      this.siteOptions = this.optionValueFilter.transform(this.sites, {
        option: 'name',
        value: 'siteId',
        color: 'bgColor',
      });
      const siteOptions = this.siteOptions.map(({ option, value }) => ({
        id: value,
        label: option,
      }));
      this.enhancedSiteOptions = [{ group: 'default', options: siteOptions }];

      let selectedSite;
      if (this.bill?.clinic) {
        selectedSite = <Site>this.objectArrayFilter.transform(this.sites, {
          siteId: this.bill.clinic,
        })[0];
      }
      this.updateProviderOptions(
        selectedSite ? selectedSite.providers : this.providers
      );

      const providers = this.providerOptions.map(({ option, value }) => ({
        id: value,
        label: option,
      }));
      this.enhancedProviderOptions = [{ group: 'default', options: providers }];

      if (this.appointment?.provider && this.appointment?.provider?.ohipNo) {
        const appointmentProvider = providers.find(
          (provider) => provider.id === this.appointment.provider.providerNo
        );
        this.selectedProviderValue = appointmentProvider
          ? appointmentProvider.label
          : '';
      }
      if (this.defaultMRP && this.selectedProviderValue == '') {
        const mrpProvider = providers.find(
          (provider) => provider.id === this.bill?.providerNo
        );
        this.selectedProviderValue = mrpProvider
          ? mrpProvider.label
          : '';
      }
      if (this.bill?.providerNo && this.selectedProviderValue == '') {
        const billProvider = providers.find(
          (provider) => provider.id === this.bill?.providerNo
        );
        this.selectedProviderValue = billProvider
          ? billProvider.label
          : '';
      }
    } else {
      this.providerOptions = this.optionValueFilter.transform(this.providers, {
        option: 'formattedName',
        value: 'providerNo',
      });

      const providers = this.providerOptions.map(({ option, value }) => ({
        id: value,
        label: option,
      }));
      this.enhancedProviderOptions = [{ group: 'default', options: providers }];

      if (this.appointment?.provider && this.appointment?.provider?.ohipNo) {
        this.selectedProviderValue = providers.find(
          (provider) => provider.id === this.appointment.provider.providerNo
        )?.label;
      } else if (this.defaultMRP?.providerNo) {
        const defaultMRPOption = providers.find(
          (provider) => provider.id === this.bill?.providerNo
        );
        this.selectedProviderValue = defaultMRPOption
          ? defaultMRPOption.label
          : null;
      } else if (this.bill?.providerNo) {
        this.selectedProviderValue = providers.find(
          (provider) => provider.id === this.bill?.providerNo
        ).label;
      } else {
        this.selectedProviderValue = null;
      }
    }

    for (let i = 0; i < this.billSites.length; i++) {
      this.billSiteOptions.push(<OptionValue>{
        option: `${this.billSites[i].clinicLocationNo} | ${this.billSites[i].name}`,
        value: this.billSites[i].clinicLocationNo,
      });
    }

    for (let i = 0; i < this.sliCodes.length; i++) {
      this.sliCodeOptions.push(<OptionValue>{
        option: `${this.sliCodes[i].code} | ${this.sliCodes[i].description}`,
        value: this.sliCodes[i].code,
      });
    }

    for (let i = 0; i < this.visitTypes.length; i++) {
      this.visitTypeOptions.push(<OptionValue>{
        option: `${this.visitTypes[i].code} | ${this.visitTypes[i].description}`,
        value: this.visitTypes[i].code,
      });
    }
  }

  resetDropdownValues() {
    this.billTypeOptions = [];
    this.siteOptions = [];
    this.providerOptions = [];
    this.billSiteOptions = [];
    this.sliCodeOptions = [];
    this.visitTypeOptions = [];
  }

  getSiteNameFromId(value) {
    if (value && this.siteOptions?.length) {
      return this.siteOptions.find((siteOption => `${value}` === `${siteOption.value}`))?.option;
    } else if (value) {
      return String(value);
    } else {
      return '';
    }
  }

  getDescription(options: Array<OptionValue>, selectedValue: string): string {
    const selectedOption = this.objectArrayFilter.transform(options, {
      value: selectedValue,
    })[0];
    return !isNil(selectedOption) ? selectedOption?.option : '';
  }

  shouldSetDemographicDateJoinedAsDefaultForAdmissionDate(): boolean {
    return this.oscarProperties?.inPatient
        && this.demographic.dateJoined !== undefined;
  }

  onEventUpdateAdmissionDate(admissionDate: any): void {
    this.updateAdmissionDate.emit(admissionDate);
  }

  onEventUpdateBillSite(clinicLocationNo: any): void {
    this.updateBillSite.emit(clinicLocationNo.option);
  }

  onEventUpdateBillType(billType: any): void {
    this.updateBillType.emit(billType.option);
  }

  onEventUpdateBillingClinic(siteId: any): void {
    const site = <Site>(
      this.objectArrayFilter.transform(this.sites, { siteId: siteId.option })[0]
    );
    this.updateProviderOptions(site ? site.providers : new Array<Provider>());
    this.updateBillingClinic.emit(site);
  }

  onEventUpdateBillingPhysician(providerNo: any): void {
    const provider = <Provider>this.objectArrayFilter.transform(
      this.providers,
      {
        providerNo: providerNo.option,
      }
    )[0];
    this.updateBillingPhysician.emit(provider);
  }

  onEventUpdateManualReview(): void {
    this.updateManualReview.emit(this.manualReview);
  }

  onEventUpdateServiceDate(serviceDate: any): void {
    this.updateServiceDate.emit(serviceDate);
  }

  onEventUpdateSliCode(code: any): void {
    const sliCode = <SliCode>(
      this.objectArrayFilter.transform(this.sliCodes, { code: code.option })[0]
    );
    this.updateSliCode.emit(sliCode);
  }

  onEventUpdateVisitType(visitTypeCode: any): void {
    const visitType: VisitType = <VisitType>this.objectArrayFilter.transform(
      this.visitTypes,
      {
        code: visitTypeCode.option,
      }
    )[0];
    this.updateVisitType.emit(visitType);
  }

  updateProviderOptions(
    siteProviders: Array<Provider> = new Array<Provider>()
  ) {
    this.providerOptions = this.optionValueFilter.transform(siteProviders, {
      option: 'formattedName',
      value: 'providerNo',
    });
  }

  onBillingTypeahead(keyword: any): void {
    this.filteredEnhancedProviderOptions = _.cloneDeep(
      this.enhancedProviderOptions
    );
    const subOptions = this.enhancedProviderOptions[0]['options'].filter(
      (word) => word.label.toLowerCase().indexOf(keyword) > -1
    );
    this.filteredEnhancedProviderOptions[0]['options'] = subOptions;
  }

  onEventUpdateBillingProvider(selectedItem: string | number): void {
    const selectedProvider = this.providerOptions.find(
      (provider) => provider.option === selectedItem
    );

    if (selectedItem) {
      const provider = <Provider>this.objectArrayFilter.transform(
        this.providers,
        {
          providerNo: selectedProvider.value,
        }
      )[0];

      this.updateBillingPhysician.emit(provider);
    } else {
      this.updateBillingPhysician.emit(null);
    }
  }
  onBillingSiteTypeahead(keyword: any): void {
    this.filteredEnhancedSiteOptions = _.cloneDeep(this.enhancedSiteOptions);
    const subOptions = this.enhancedSiteOptions[0]['options'].filter(
      (word) => word.label.toLowerCase().indexOf(keyword) > -1
    );
    this.filteredEnhancedSiteOptions[0]['options'] = subOptions;
  }

  onEventUpdateBillingSite(selectedItem: string | number): void {
    const selectedSite = this.siteOptions.find(
      (site) => site.option === selectedItem
    );

    if (selectedSite) {
      const site = <Site>this.objectArrayFilter.transform(this.sites, {
        siteId: selectedSite.value,
      })[0];
      this.updateProviderOptions(site ? site.providers : new Array<Provider>());
      this.updateBillingClinic.emit(site);
    } else {
      this.updateBillingClinic.emit(null);
      this.updateProviderOptions(new Array<Provider>());
    }
    this.selectedProviderValue = '';
  }

  onInputBillingSite(): void {
    this.selectedProviderValue = '';
    this.updateBillingPhysician.emit(null);
  }
}
