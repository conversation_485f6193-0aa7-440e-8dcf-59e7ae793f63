import {
  Component,
  DoCheck,
  EventEmitter,
  Input,
  IterableDiffer,
  IterableDiffers,
  Output,
} from '@angular/core';
import { BillingItem } from '@oscar-pro/interfaces';
import { BillingFacade } from '@oscar-pro/core-state';

@Component({
    selector: 'oscar-pro-premium-code',
    templateUrl: './premium-code.component.html',
    styleUrls: ['./premium-code.component.scss'],
    standalone: false
})
export class PremiumCodeComponent implements DoCheck {
  private _iterableDiffer: IterableDiffer<BillingItem>;
  private billingCodePercentageFeeMap: Map<string, number> = new Map<
    string,
    number
  >();
  /**
   * This PremiumCodeCheckboxComponent's premium code billing item
   */
  @Input() percentCodeItem: BillingItem;
  /**
   * The existing other bill items
   */
  @Input() billingItems: Array<BillingItem>;

  /**
   * Emitter to notify of an updated checked state
   */
  @Output() updatePercentTotal: EventEmitter<null> = new EventEmitter<null>();
  @Output() removeBillingItem: EventEmitter<BillingItem> =
    new EventEmitter<BillingItem>();

  constructor(
    private _iterableDiffers: IterableDiffers,
    private billingFacade: BillingFacade
  ) {
    this._iterableDiffer = _iterableDiffers.find([]).create(null);
  }

  ngDoCheck() {
    const changes = this._iterableDiffer.diff(this.billingItems);
    if (changes) {
      /* if the billingItems array changes, remove all entries from billingCodePercentageFeeMap
         code that do not exist in the array
      */
      changes.forEachRemovedItem((removedItem) => {
        if (
          this.billingCodePercentageFeeMap.has(removedItem.item.serviceCode)
        ) {
          this.billingCodePercentageFeeMap.delete(removedItem.item.serviceCode);
        }
      });
      this.calculateFeeAndUpdateTotal();
    }
  }

  onEventItemUpdated(eventObject) {
    const updatedServiceCode = <string>eventObject['serviceCode'];
    const itemPercentageFee = <number>eventObject['itemPercentageFee'];
    this.billingCodePercentageFeeMap.set(updatedServiceCode, itemPercentageFee);
    this.calculateFeeAndUpdateTotal();
  }

  onClickRemoveBillingItem(event: MouseEvent, billingItem: BillingItem): void {
    this.removeBillingItem.emit(billingItem);
  }

  calculateFeeAndUpdateTotal() {
    let totalFee = 0;
    this.billingItems.forEach((thisItem) => {
      const thisPercentFee = this.billingCodePercentageFeeMap.get(
        thisItem.serviceCode
      );
      if (thisPercentFee) {
        totalFee += thisPercentFee;
      }
    });

    this.billingFacade.updateBillItemRecord(this.percentCodeItem, {
      fee: totalFee.toFixed(2),
    });
    this.billingFacade.updateBillPercentCode(this.percentCodeItem, {
      fee: totalFee.toFixed(2),
    });

    this.updatePercentTotal.emit();
  }

  onBlurQty(event: [Event, any], billingItem: BillingItem) {
    this.billingFacade.updateBillItemRecord(this.percentCodeItem, {
      fee: billingItem.serviceCount,
    });
  }

  onInputQty(event: Event, billingItem: BillingItem): void {
    event.preventDefault();
    this.billingFacade.updateBillItemRecord(this.percentCodeItem, {
      fee: billingItem.serviceCount,
    });
  }

}
