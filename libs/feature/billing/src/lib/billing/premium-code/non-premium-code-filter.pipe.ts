import { Pipe, PipeTransform } from '@angular/core';
import { BillingItem } from '@oscar-pro/interfaces';

@Pipe({
    name: 'nonPremiumCodeFilter',
    pure: false,
    standalone: false
})
export class NonPremiumCodeFilterPipe implements PipeTransform {
  tmpArray = [];

  transform(allBillingItems: Array<BillingItem>) {
    // create a new list and push the filtered items from to the temp list, this means the returned array is always 'fresh'
    this.tmpArray.length = 0;
    const arr = allBillingItems.filter((item) => !item.service.percentCode);
    this.tmpArray.push(...arr);
    return this.tmpArray;
  }
}
