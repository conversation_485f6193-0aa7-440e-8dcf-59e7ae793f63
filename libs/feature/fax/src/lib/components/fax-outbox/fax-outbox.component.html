<div *ngIf="isPageFinishedLoading" class="fax-outbox">
  <h1 class="fax-heading-1">Electronic Fax Outbox</h1>
  <div class="fax-header">
    <!-- Start Date -->
    <div class="fax-header-field">
      <label class="well-label-inside">
        <span>Start Date</span>
        <p-datepicker
          [ngModel]="startDate"
          (ngModelChange)="onChangeStartDate($event)"
          [iconDisplay]="'input'"
          [showIcon]="true"
          [showButtonBar]="true"
          [inputStyleClass]="'well-input'"
          dataType="string"
          dateFormat="yy-mm-dd"
          placeholder="YYYY-MM-DD"
          todayButtonStyleClass="well-button secondary"
          clearButtonStyleClass="hidden"
        >
          <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
            <i class="fa fa-calendar pointer-events-none" (click)="clickCallBack($event)"></i>
          </ng-template>
        </p-datepicker>
      </label>
    </div>

    <!-- End Date-->
    <div class="fax-header-field">
      <label class="well-label-inside">
        <span>End Date</span>
        <p-datepicker
          [ngModel]="endDate"
          (ngModelChange)="onChangeEndDate($event)"
          [iconDisplay]="'input'"
          [showIcon]="true"
          [showButtonBar]="true"
          dataType="string"
          dateFormat="yy-mm-dd"
          placeholder="YYYY-MM-DD"
          [inputStyleClass]="'well-input'"
          todayButtonStyleClass="well-button secondary"
          clearButtonStyleClass="hidden"
        >
          <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
            <i class="fa fa-calendar pointer-events-none" (click)="clickCallBack($event)"></i>
          </ng-template>
        </p-datepicker>
      </label>
    </div>

    <!-- Fax Status -->
    <div class="fax-header-field">
      <well-dropdown
        label="Status"
        [selectedValue]="faxStatusOptions[0].value"
        [options]="faxStatusOptions"
        [notClearable]="true"
        (updateOption)="onChangeFaxStatus($event)"
        [maxHeight]="'none'"
        [labelPlacement]="'inside'"
      ></well-dropdown>
    </div>

    <!-- Account Type -->
    <div class="fax-header-field">
      <well-dropdown
        label="Account"
        [selectedValue]="faxAccountOptions[0].value"
        [options]="faxAccountOptions"
        [notClearable]="true"
        (updateOption)="onChangeAccountType($event)"
        [maxHeight]="'none'"
        [labelPlacement]="'inside'"
      ></well-dropdown>
    </div>

    <!-- Demographic ID -->
    <div class="fax-header-field">
      <well-input
        label="Demographic Id"
        [ngModel]="demographic"
        [labelPlacement]="'inside'"
        (ngModelChange)="onChangeDemographic($event)"
      ></well-input>
    </div>

    <!-- Sent To -->
    <div class="fax-header-field">
      <well-input
        label="Sent To"
        [labelPlacement]="'inside'"
        (input)="onChangeSentTo($event)"
      ></well-input>
    </div>

    <button class="well-button primary"
            (click)="onClickSearch()"
            [disabled]="loading">
      <span>Search</span>
    </button>
  </div>

  <div class="fax-header-buttons">
    <div
      class="well-chip"
      [class.active]="!faxOutboxFilter.archived"
      (click)="onChangeArchivedStatus(false)"
      pTooltip="Show only unarchived faxes"
      tooltipPosition="top"
    >
      Unarchived
    </div>
    <div
      class="well-chip"
      [class.active]="faxOutboxFilter.archived"
      (click)="onChangeArchivedStatus(true)"
      pTooltip="Show only archived faxes"
      tooltipPosition="top"
    >
      Archived
    </div>
  </div>

  @if (!loading) {
    <well-table [tableData]="faxResults?.records || []"
                [tableColumns]="tableColumns"
                [tableRowTemplate]="tablerow">
      <ng-template let-item #tablerow>
        <well-column>
          {{ formatFaxResultDate(item.dateSent) }}
        </well-column>

        <well-column>
          {{ item.displayName }}
        </well-column>

        <well-column>
          <button
            class="well-button text"
            (click)="onClickOpenMasterWindow(item.demographicId)">
            {{ item.demographicId || "N/A" }}
          </button>
        </well-column>

        <well-column>
          {{ item.sentTo | phoneFormat }}
        </well-column>

        <well-column>
              <span class="well-tag bold" [class]="getTag(item.status)">
                {{ FaxOutboxResult.toDisplayStatus(item.status) }}
            </span>
        </well-column>

        <well-column>
          {{ item.deliveryDate ? formatFaxResultDate(item.deliveryDate) : "N/A" }}
        </well-column>

        <well-column>
          <div class="flex gap-1">
            <button (click)="onClickViewFax(item)"
                    pTooltip="View Fax"
                    tooltipPosition="top"
                    class="well-button action">
              <i class="fa-solid fa-eye"></i>
            </button>
            <button (click)="onClickArchiveFax(item)"
                    [pTooltip]="!item.archived ? 'Archive' : 'Unarchive'"
                    [tooltipPosition]="!item.archived ? 'top' : 'left'"
                    class="well-button action">
              <i class="fa-solid fa-{{!item.archived ? 'box-archive' : 'boxes-packing'}}"></i>
            </button>
            @if (item.status === FaxDeliveryStatus.Error && !item.archived) {
              <button (click)="onClickResendFax(item)"
                      pTooltip="Resend"
                      tooltipPosition="left"
                      class="well-button action">
                <i class="fa-solid fa-rotate"></i>
              </button>
            }
          </div>
        </well-column>
      </ng-template>
    </well-table>
    @if (faxResults) {
      <oscar-pro-paginator
        [currentPage]="faxResults.page"
        [pageSize]="faxResults.perPage"
        [totalCount]="faxResults.total"
        (selectPage)="onSelectPage($event)">
      </oscar-pro-paginator>
    }
  } @else {
    <well-backdrop [title]="'Processing report.'"
                   description="This may take a few minutes."></well-backdrop>
  }
</div>
