import {Component, DestroyRef, Inject, OnInit} from '@angular/core';
import {AppConfig, OptionValue, PagedResponse} from '@oscar-pro/interfaces';
import {
  FaxAccount,
  FaxArchiveParams,
  FaxDeliveryStatus,
  FaxDeliveryStatusOptions,
  FaxOutboxFilter,
  FaxOutboxResult,
} from '@oscar-pro/feature/fax';
import {ApiFaxService} from '@oscar-pro/data-access';
import {finalize} from 'rxjs';
import {ToastrService} from 'ngx-toastr';
import * as dayjs from 'dayjs';
import {Dayjs} from 'dayjs';
import {OUTBOX_HEADERS} from '../../constants/fax-inbox.component';
import {APP_CONFIG} from '@oscar-pro/config';
import {UrlParamsPipe, WindowRefService} from '@oscar-pro/util';
import {DialogService} from 'primeng/dynamicdialog';
import {FaxArchiveDialogComponent} from '../fax-archive-dialog/fax-archive-dialog.component';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import {ActivatedRoute, Params} from "@angular/router";

@Component({
    selector: 'oscar-pro-fax-outbox',
    templateUrl: './fax-outbox.component.html',
    styleUrls: ['./fax-outbox.component.scss'],
    standalone: false
})
export class FaxOutboxComponent implements OnInit{
  public faxAccounts: FaxAccount[] = [];
  public faxAccountOptions: OptionValue[] = [] as Array<OptionValue>;
  public faxStatusOptions: OptionValue[] = FaxDeliveryStatusOptions;
  public faxOutboxFilter: FaxOutboxFilter = new FaxOutboxFilter();
  public faxResults!: PagedResponse<FaxOutboxResult>;
  public defaultDate: Dayjs = dayjs();
  public startDate!: string;
  public endDate!: string;
  public demographic!: string;
  public isPageFinishedLoading = false;
  public tableColumns = OUTBOX_HEADERS;
  public loading = false;

  protected readonly FaxOutboxResult = FaxOutboxResult;
  protected readonly FaxDeliveryStatus = FaxDeliveryStatus;

  private ALL_FAX_ACCOUNTS = '-1';
  private nativeWindow: Window;

  constructor(
    private apiFaxService: ApiFaxService,
    private toastrService: ToastrService,
    @Inject(APP_CONFIG) private appConfig: AppConfig,
    private urlParamsPipe: UrlParamsPipe,
    private windowRefService: WindowRefService,
    private dialogService: DialogService,
    private destroyRef: DestroyRef,
    private activatedRoute: ActivatedRoute,
  ) {
    this.nativeWindow = this.windowRefService.getNativeWindow();
  }

  ngOnInit(): void {
    this.getFaxAccounts();
    this.activatedRoute.queryParams
    .pipe(takeUntilDestroyed(this.destroyRef))
    .subscribe((params) => {
      this.setDefaultFilters(params)
    });
  }

  public getFaxAccounts(): void {
    this.apiFaxService.searchFaxAccounts().pipe(
      takeUntilDestroyed(this.destroyRef),
      finalize(() => {
        this.isPageFinishedLoading = true;
      }),
    ).subscribe({
      next: (faxAccountResults: FaxAccount[]) => {
        this.faxAccounts = faxAccountResults.filter(
          (faxAccountResult) => faxAccountResult.outboundFaxEnabled == true,
        );
        this.faxAccountOptions = this.setFaxAccountOptions();
      },
      error: () => {
        this.toastrService.error('Error getting fax accounts');
      },
    });
  }

  setFaxAccountOptions(): OptionValue[] {
    const faxAccountOptions: OptionValue[] = [];
    faxAccountOptions.push({ option: 'All', value: this.ALL_FAX_ACCOUNTS });
    for (const faxAccount of this.faxAccounts) {
      faxAccountOptions.push({
        option: faxAccount.displayName,
        value: faxAccount.id?.toString() || '',
      });
    }
    return faxAccountOptions;
  }

  /* Fax Outbox Filter onChange Events */
  onChangeStartDate(event: string): void {
    this.faxOutboxFilter.startDate = dayjs(event);
  }

  onChangeEndDate(endDate: string): void {
    this.faxOutboxFilter.endDate = dayjs(endDate);
  }

  onChangeFaxStatus(event: any): void {
    if (event.option !== FaxDeliveryStatus.All) {
      this.faxOutboxFilter.status = event.option;
    } else {
      this.faxOutboxFilter.status = null;
    }
  }

  onChangeAccountType(event: any): void {
    if (event.option !== this.ALL_FAX_ACCOUNTS) {
      this.faxOutboxFilter.faxAccountId = event.option;
    } else {
      this.faxOutboxFilter.faxAccountId = null;
    }
  }

  onChangeDemographic(event: Event): void {
    this.faxOutboxFilter.demographicId = parseInt(
      (event.target as HTMLInputElement).value,
    );
  }

  onChangeSentTo(event: Event): void {
    this.faxOutboxFilter.sentTo = (event.target as HTMLInputElement).value;
  }

  onChangeArchivedStatus(event: boolean): void {
    if (this.faxOutboxFilter.archived === event) {
      return;
    }
    this.faxOutboxFilter.archived = event;
    this.onClickSearch(1);
  }

  setDefaultFilters(params: Params): void {
    let startRange = dayjs().subtract(3, 'days').startOf('day');
    const today = dayjs().endOf('day');
    if (params['demographicNumber'] && parseInt(params['demographicNumber'])) {
      this.faxOutboxFilter.demographicId = params['demographicNumber'];
      startRange = dayjs().subtract(1, 'year').startOf('day');
    }
    this.startDate = startRange.format('YYYY-MM-DD');
    this.endDate = today.format('YYYY-MM-DD');
    this.demographic = this.faxOutboxFilter.demographicId?.toString() || '';
    this.faxOutboxFilter.startDate = startRange;
    this.faxOutboxFilter.endDate = today;
    this.faxOutboxFilter.archived = false;
    this.faxOutboxFilter.page = 1;
    this.faxOutboxFilter.perPage = 50;
    this.onClickSearch(this.faxOutboxFilter.page);
  }

  public onClickSearch(page?: number): void {
    this.faxOutboxFilter.page = page || 1;

    if (!this.faxOutboxFilter.startDate) {
      this.faxOutboxFilter.startDate = this.defaultDate;
    }
    if (!this.faxOutboxFilter.endDate) {
      this.faxOutboxFilter.endDate = this.defaultDate;
    }

    this.loading = true

    this.apiFaxService.getOutboundFaxResults(
      this.faxOutboxFilter,
    ).pipe(
      takeUntilDestroyed(this.destroyRef),
      finalize(() => {
        this.loading = false;
      }),
    ).subscribe({
        next: (response: PagedResponse<FaxOutboxResult>) => {
          this.faxResults = response;
          this.faxOutboxFilter.page = response.page;
        },
        error: () => {
          this.toastrService.error('Error getting fax results');
        },
      })
  }

  /* Individual Grid Item Actions */
  onClickViewFax(faxResult: FaxOutboxResult): void {
    this.apiFaxService.view(faxResult.id);
  }

  onClickResendFax(faxResult: FaxOutboxResult): void {
    faxResult.status = FaxDeliveryStatus.InProgress;
    this.apiFaxService.resendFax(faxResult.id).pipe(
      takeUntilDestroyed(this.destroyRef),
      finalize(() => {
        this.onClickSearch(this.faxOutboxFilter.page);
      }),
    ).subscribe({
      error: (e) => {
        this.toastrService.error(e.message || 'Error resending fax');
      },
    });
  }

  onClickArchiveFax(faxResult: FaxOutboxResult): void {
    const ref = this.dialogService.open(FaxArchiveDialogComponent, {
      header: !faxResult.archived ? 'Archiving Reason' : 'Unarchiving Confirmation',
      width: '400px',
      closable: true,
      modal: true,
      data: { faxResult },
    });

    ref.onClose.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(result => {
      if (result) {
        const params: FaxArchiveParams  = {
          ...result,
          faxIds: [faxResult.id],
          archive: !faxResult.archived,
        }
        this.onArchiveFax(params);
      }
    });
  }

  onArchiveFax(params: FaxArchiveParams): void {
    this.apiFaxService.archiveFax(params).pipe(
      takeUntilDestroyed(this.destroyRef),
    ).subscribe({
      next: () => {
        const page = this.faxOutboxFilter.page;
        this.onClickSearch(this.faxResults?.records?.length === 1 && page > 1 ? page - 1 : page);
      },
      error: (e) => {
        this.toastrService.error(e.message || 'Error archiving fax');
      },
    });
  }

  public onSelectPage(page: number): void {
    this.faxOutboxFilter.page = page;
    this.onClickSearch(page);
  }

  public formatFaxResultDate(date: Dayjs): string {
    return date.format('YYYY-MM-DD HH:mm');
  }

  onClickOpenMasterWindow(demographicNumber: string): void {
    if (!demographicNumber) return
    const params = {
      demographic_no: demographicNumber,
      displaymode: 'edit',
    };
    const url = `/${
      this.appConfig.oscarContext
    }/demographic/demographiccontrol.jsp${this.urlParamsPipe.transform(
      params
    )}`;
    const windowFeatures = 'height=710,width=1024';
    this.nativeWindow.open(url, '_blank', windowFeatures);
  }

  public getTag(status: string): string | void  {
    switch (status) {
      case FaxDeliveryStatus.Error:
        return 'error';
      case FaxDeliveryStatus.Queued:
        return 'warning';
      case FaxDeliveryStatus.Delivered:
        return 'success';
      default:
        return ;
    }
  }
}
