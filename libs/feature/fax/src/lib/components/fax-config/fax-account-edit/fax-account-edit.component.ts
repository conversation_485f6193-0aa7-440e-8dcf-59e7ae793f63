import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FaxAccount,
  FaxAccountConnectionStatus,
  FaxCoverLetter,
} from '@oscar-pro/feature/fax';
import { ApiFaxService, ApiProviderService } from '@oscar-pro/data-access';
import { ToastrService } from 'ngx-toastr';
import { AlertService } from '@oscar-pro/util';
import { OptionValue, Provider } from '@oscar-pro/interfaces';

@Component({
    selector: 'oscar-pro-fax-account-edit',
    templateUrl: './fax-account-edit.component.html',
    styleUrls: ['./fax-account-edit.component.scss'],
    standalone: false
})
export class FaxAccountEditComponent implements OnInit {
  @Input() faxAccount!: FaxAccount;
  @Output() actionCancel: EventEmitter<void> = new EventEmitter();
  @Output() actionSave: EventEmitter<FaxAccount> = new EventEmitter();
  @Output() actionDelete: EventEmitter<void> = new EventEmitter();

  defaultCoverLetterOption: OptionValue = {} as OptionValue;
  faxAccountCopy!: FaxAccount;
  pristine = true;
  inboxOptions: Array<OptionValue> = [];
  initialized = false;
  unclaimedLabel = 'Unclaimed';
  coverLetterOptions: OptionValue[] = [] as Array<OptionValue>;
  canDisplayCoverPageOptions = false;

  constructor(
    private alertService: AlertService,
    private apiFaxService: ApiFaxService,
    private apiProviderService: ApiProviderService,
    private toastrService: ToastrService,
  ) {}

  ngOnInit(): void {
    // copy the account object so that changes are not reflected in the parent
    this.faxAccountCopy = this.faxAccount.copy();

    this.apiProviderService.getProviders().subscribe(
      (providers: Provider[]): void => {
        this.inboxOptions = this.mapProviderOptions(providers);
        this.initialized = true;
      },
      (error: unknown): void => {
        console.error(error);
        this.toastrService.error('Error loading providers');
      },
    );

    if (this.faxAccountCopy.outboundFaxEnabled) {
      this.getCoverLetterOptions(this.faxAccountCopy.id as number);
    }
  }

  private mapProviderOptions(providers: Provider[]): OptionValue[] {
    const options: OptionValue[] = providers.map(
      (provider: Provider): OptionValue => {
        return {
          value: provider.providerNo,
          option: provider.formattedName + ' (' + provider.providerNo + ')',
        };
      },
    );
    options.unshift({
      value: '',
      option: this.unclaimedLabel,
    });
    return options;
  }

  /* == page actions == */

  public onCancel(): void {
    this.actionCancel.emit();
  }

  public onSave(): void {
    this.pristine = false;
    if (this.faxAccountCopy.isValid()) {
      this.persistChanges();
    } else {
      this.displayInvalidFormMessage();
    }
  }

  protected persistChanges(): void {
    const onSuccess = (account: FaxAccount): void => {
      this.actionSave.emit(account);
    };
    const onError = (error: unknown): void => {
      console.error(error);
      this.toastrService.error('Error saving account');
    };
    // perform api calls
    if (this.isEditMode) {
      this.apiFaxService
        .updateFaxAccount(this.faxAccountCopy.id as number, this.faxAccountCopy)
        .subscribe(
          (account: FaxAccount) => onSuccess(account),
          (error: unknown) => onError(error),
        );
    } else {
      this.apiFaxService.createFaxAccount(this.faxAccountCopy).subscribe(
        (account: FaxAccount) => onSuccess(account),
        (error: unknown) => onError(error),
      );
    }
  }

  public async onDelete(): Promise<void> {
    const confirmed = await this.alertService.confirmMessage(
      'Are you sure you want to delete this fax integration?',
    );
    if (!confirmed) {
      return;
    }

    this.apiFaxService.deleteFaxAccount(this.faxAccountCopy.id as number).subscribe(
      (): void => {
        this.actionDelete.emit();
      },
      (error: unknown): void => {
        console.error(error);
        this.toastrService.error('Error deleting account');
      },
    );
  }

  public onTestConnection(): void {
    this.pristine = false;
    if (this.faxAccountCopy.isValid()) {
      this.testConnection();
    } else {
      this.displayInvalidFormMessage();
    }
  }

  public onCreateJWT(): void {
    this.apiFaxService.getClientId().subscribe(
      (clientId: string): void => {
        window.open(
          'https://developers.ringcentral.com/console/my-credentials/create?client_id='
            + clientId,
          '_blank',
        );
      },
      (error: unknown): void => {
        console.error(error);
        this.toastrService.error('Error fetching clientId');
      },
    );
  }

  protected testConnection(): void {
    this.apiFaxService
      .testConnection(this.faxAccountCopy.id as number, this.faxAccountCopy)
      .subscribe(
        (connectionStatus: FaxAccountConnectionStatus): void => {
          switch (connectionStatus) {
            case FaxAccountConnectionStatus.Success: {
              this.toastrService.info('Connection Successful!');
              break;
            }
            case FaxAccountConnectionStatus.Failure:
            case FaxAccountConnectionStatus.Unauthorized: {
              this.toastrService.warning(
                'Connection Invalid. Please check your credentials.',
              );
              break;
            }
            default: {
              this.toastrService.warning(
                'Connection Unavailable. Please check your network status.',
              );
              break;
            }
          }
        },
        (error: unknown): void => {
          console.error(error);
          this.toastrService.error('Error testing connection');
        },
      );
  }

  protected displayInvalidFormMessage(): void {
    this.toastrService.error(
      'Form is invalid or incomplete. Please correct the highlighted fields.',
    );
  }

  /* == model update handlers == */

  public updateFaxAccountEnabled(inputValue: boolean): void {
    this.faxAccountCopy.enabled = inputValue;
  }

  public updateFaxAccountInboundEnabled(inputValue: boolean): void {
    this.faxAccountCopy.inboundFaxEnabled = inputValue;
  }

  public updateFaxAccountOutboundEnabled(inputValue: boolean): void {
    this.faxAccountCopy.outboundFaxEnabled = inputValue;

    if (this.faxAccountCopy.outboundFaxEnabled) {
      this.getCoverLetterOptions(this.faxAccountCopy.id as number);
    } else {
      this.canDisplayCoverPageOptions = false;
    }
  }

  public updateFaxAccountId($event: Event, inputValue: string): void {
    this.faxAccountCopy.accountId = inputValue;
  }

  public updateFaxAccountSecret($event: Event, inputValue: string): void {
    this.faxAccountCopy.accountSecret = inputValue;
  }

  public updateFaxAccountDisplayName($event: Event, inputValue: string): void {
    this.faxAccountCopy.displayName = inputValue;
  }

  public updateFaxAccountCoverLetter(event: any): void {
    this.faxAccountCopy.coverLetterOption = event.option;
  }

  public updateFaxAccountSelectedInbox(event: any): void {
    this.faxAccountCopy.inboxProviderId = event.option;
  }

  private getCoverLetterOptions(accountId: number): void {
    this.apiFaxService.getFaxCoverPageOptions(accountId).subscribe(
      (coverLetters: FaxCoverLetter[]): void => {
        this.setCoverLetterOptions(coverLetters);
        this.defaultCoverLetterOption = this.coverLetterOptions.find(
          ({ value }) => value === this.faxAccountCopy.coverLetterOption,
        ) as OptionValue;
        this.canDisplayCoverPageOptions = true;
      },
      (error: unknown): void => {
        console.error(error);
        this.toastrService.error(
          'Error getting cover letter options. Please ensure your fax account connection settings are correct and try again.',
        );
      },
    );
  }

  private setCoverLetterOptions(coverLetters: FaxCoverLetter[]): void {
    this.coverLetterOptions = coverLetters.map(
      (coverLetter: FaxCoverLetter) => {
        return {
          option: coverLetter.name,
          value: coverLetter.id,
        };
      },
    );
  }

  /* == model update handlers == */

  get isEditMode(): boolean {
    return !!this.faxAccountCopy.id;
  }
}
