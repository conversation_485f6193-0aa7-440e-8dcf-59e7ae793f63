import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { FaxOutboxResult } from '@oscar-pro/feature/fax';

@Component({
    selector: 'oscar-pro-fax-archive-dialog',
    templateUrl: './fax-archive-dialog.component.html',
    styleUrls: ['../../styles/layout.scss', './fax-archive-dialog.component.scss'],
    standalone: false
})
export class FaxArchiveDialogComponent {
  form: FormGroup;
  reasons: string[] = [
    'No longer need to send',
    'Wrong fax number',
    'Resent another way',
    'Sent in error',
    'Other'
  ];
  isUnarchive = false;
  faxResult?: FaxOutboxResult;
  readonly MAX_NOTES_LENGTH = 500;

  constructor(
    private fb: FormBuilder,
    private ref: DynamicDialogRef,
    private config: DynamicDialogConfig,
  ) {
    this.faxResult = this.config?.data?.faxResult;
    this.isUnarchive = !!this.faxResult?.archived;
    this.form = this.fb.group({
      reason: [this.faxResult?.archiveReason || this.reasons[0], Validators.required],
      notes: [this.faxResult?.archiveNotes || '', Validators.maxLength(this.MAX_NOTES_LENGTH)],
    });
  }

  confirm() {
    if (this.form.valid) {
      this.ref.close(this.form.value);
    } else {
      this.form.markAllAsTouched();
    }
  }

  cancel() {
    this.ref.close();
  }

}
