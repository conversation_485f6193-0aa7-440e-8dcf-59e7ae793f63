import {Component} from "@angular/core";
import {ApiGatewayService} from "@oscar-pro/data-access";

@Component({
    selector: 'oscar-pro-gateway-config',
    templateUrl: './gateway-config.component.html',
    styleUrls: ['./gateway-config.component.scss'],
    standalone: false
})
export class GatewayConfigComponent {

  public apiKey: string = null;
  public gatewayId: string = null;
  public gatewayKey: string = null;

  constructor(public apiGatewayService: ApiGatewayService) {
  }

  public updateGatewayId(event: Event, value: string): void {
    this.gatewayId = value;
  }

  public updateGatewayKey(event: Event, value: string): void {
    this.gatewayKey = value;
  }

  public async generateApiKey(event?: Event): Promise<void> {
    this.apiKey = await this.apiGatewayService.generateApiKey();
  }

  public async saveGatewayKey(event?: Event): Promise<void> {
    if(!this.gatewayKey) {
      return;
    }
    await this.apiGatewayService.setGatewayApiKey(this.gatewayKey);
  }

  public async saveGatewayId(event?: Event): Promise<void> {
    if(!this.gatewayId) {
      return;
    }
    await this.apiGatewayService.setGatewayApiId(this.gatewayId);
  }
}
