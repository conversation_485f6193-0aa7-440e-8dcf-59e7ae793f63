import { Component, ElementRef, EventEmitter, Input, Output, ViewChild, } from '@angular/core';

@Component({
    selector: 'oscar-pro-gateway-confirmation-modal',
    templateUrl: './gateway-confirmation-modal.component.html',
    styleUrls: ['./gateway-confirmation-modal.component.scss'],
    standalone: false
})
export class GatewayConfirmationModalComponent {
  @Input() title = '';
  @Input() actionText = '';
  @Input() destructive = false;
  @ViewChild('dialog') dialog!: ElementRef;
  @Output() confirmEvent: EventEmitter<void> = new EventEmitter<void>();
  @Output() cancelEvent: EventEmitter<void> = new EventEmitter<void>();

  ngAfterViewInit() {
    // close modal without saving when clicking outside of the dialog
    this.dialog.nativeElement.addEventListener('click', (event: MouseEvent) => {
      const target = event.target as Element;
      if (target.nodeName === 'DIALOG') {
        this.closeModal();
      }
    });
  }

  openModal() {
    this.dialog.nativeElement.showModal();
  }

  closeModal() {
    this.dialog.nativeElement.close();
    this.cancelEvent.emit();
  }

  closeModalAndConfirm() {
    this.confirmEvent.emit();
    this.dialog.nativeElement.close();
  }
}
