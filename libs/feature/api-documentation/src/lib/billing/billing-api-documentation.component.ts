import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';
import SwaggerUI from 'swagger-ui-dist/swagger-ui-bundle.js';
import { EnvironmentVarsPipe } from '@oscar-pro/util';

@Component({
    selector: 'oscar-pro-billing-api-documentation',
    templateUrl: './billing-api-documentation.component.html',
    styleUrl: './billing-api-documentation.component.css',
    standalone: false
})
export class BillingApiDocumentationComponent implements AfterViewInit{
  @ViewChild('billingapidocumentation', {static: true})
  billingApiDocElement: ElementRef | undefined;
  apiDocumentationUrl: string | undefined;
  xsrfToken: string | undefined;
  xXsrfToken: string;

  constructor(
    private environmentVars: EnvironmentVarsPipe
  ) {
     const hostName = window.location.hostname + "/";
     const apiBasePath = this.environmentVars.transform("proContext", true);
     const apiBaseDocsPath = this.environmentVars.transform("apiBaseDocumentationPath", true);
     const billingApiDocsPath = this.environmentVars.transform("billingApiDocumentationPath", true);
     this.xsrfToken = this.environmentVars.transform("xsrfToken");
     this.xXsrfToken = this.environmentVars.transform("xXsrfToken");
     this.apiDocumentationUrl = `https://${hostName}${apiBasePath}${apiBaseDocsPath}/${billingApiDocsPath}`;
  }

  ngAfterViewInit(): void {
    SwaggerUI({
      url: this.apiDocumentationUrl,
      domNode: this.billingApiDocElement?.nativeElement,
      requestInterceptor: (req) => {
        const xsrfToken = this.getCookie(this.xsrfToken);
        if (xsrfToken) {
          req.headers[this.xXsrfToken] = xsrfToken;
        }
        return req;
      },
    });
  }

  private getCookie(name: string | undefined): string | undefined{
    const value = document.cookie;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift();
    }

    return undefined;
  }
}
