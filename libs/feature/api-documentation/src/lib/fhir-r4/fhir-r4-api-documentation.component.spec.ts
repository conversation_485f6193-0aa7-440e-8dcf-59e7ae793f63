// TODO: check if the tests still work
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FhirR4ApiDocumentationComponent } from './fhir-r4-api-documentation.component';
import { EnvironmentVarsPipe } from '@oscar-pro/util';
import { APP_CONFIG } from '@oscar-pro/config';
jest.mock("swagger-ui-dist/swagger-ui-bundle.js", () => {
  return {
    default: jest.fn(() => Promise.resolve())
  };
});

describe('ApiDocumentationComponent', () => {
  let component: FhirR4ApiDocumentationComponent;
  let fixture: ComponentFixture<FhirR4ApiDocumentationComponent>;
  const oscarContext = 'oscar';
  const proContext = 'kaiemr';
  const apiPrefix = 'api';

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FhirR4ApiDocumentationComponent],
      providers:  [ {
        provide: APP_CONFIG,
        useValue: {
          proContext: proContext,
          apiPrefix: apiPrefix,
          oscarContext: oscarContext,
        },
      },
      EnvironmentVarsPipe],
    }).compileComponents();

    fixture = TestBed.createComponent(FhirR4ApiDocumentationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
