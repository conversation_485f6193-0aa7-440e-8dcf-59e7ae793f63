import { Component, Inject, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from '@oscar-pro/auth';
import { AppConfig } from '@oscar-pro/interfaces';
import { APP_CONFIG } from "@oscar-pro/config";
import { LogService } from '@oscar-pro/util';

@Component({
    template: '<div>Logging out...</div>',
    standalone: false
})
export class LogoutComponent implements OnInit {

  // redirect parameter url mapping
  private redirects: Map<string, string> = new Map([
    ['oscar', `/${this.appConfig.oscarContext}/index.jsp`],
    ['kaiemr', `/${this.appConfig.proContext}/`]
  ]);
  private readonly redirect: string = '/';

  constructor(private auth: AuthService,
              protected logger: LogService,
              private route: ActivatedRoute,
              @Inject(APP_CONFIG) private appConfig: AppConfig) {
    this.redirect = this.redirects.get('kaiemr') as string; // default to kaiemr redirect
    if (this.route.snapshot.params['redirect']) {
      this.redirect = this.redirects.get(this.route.snapshot.params['redirect']) as string;
    }
    this.logger.debug2("LogoutComponent, redirect=" + this.redirect);
  }

  ngOnInit() {
    console.log("User LogoutComponent, redirect= " + this.redirect);
    this.auth.logout();
    this.auth.destroySession().subscribe(() => {
        window.location.href = this.redirect;
        console.log("User LogoutComponent, redirecting to = " + this.redirect);
      }
    );
  }
}
