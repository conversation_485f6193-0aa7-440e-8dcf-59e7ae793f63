import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { OktaSignIn, RenderResult } from '@okta/okta-signin-widget';
import { LogService } from "@oscar-pro/util";
import { AuthOktaService } from "@oscar-pro/auth-okta";

@Component({
    selector: 'oscar-pro-login-oidc',
    templateUrl: './login-oidc.component.html',
    styleUrls: ['./login-oidc.component.scss'],
    encapsulation: ViewEncapsulation.None // allows applying styles to okta-widget component
    ,
    standalone: false
})
export class LoginOidcComponent implements OnInit {
  user: any;
  widgetInitialized = false;

  constructor(public authOktaService: AuthOktaService,
              protected logger: LogService) {
  }

  ngOnInit() {
    this.login();
  }

  login() {
    this.logger.debug1("LoginOidcComponent.login() was called...   initialize and render the sign-in widget");
    this.authOktaService.getSignInWidget$().subscribe(
        (oktaSignIn: OktaSignIn) => {

          this.logger.debug1("LoginOidcComponent.login() -- subscribe fired ... now render the widget, response will callback");
          //  For hints, see:  https://gist.github.com/andymarch/560abdc9ea86d52030db0a2486c7c217
          this.logger.debug2("  widgetInitialized=" + this.widgetInitialized);

          try {
            if (oktaSignIn && !this.widgetInitialized) {
              oktaSignIn.renderEl({el: '#sign-in-widget'},
                  (response: RenderResult) => {
                    this.logger.debug1("LoginOidcComponent.login() -- renderEl response is " + response);
                    if (response.status === 'SUCCESS') {
                      this.user = response?.tokens?.idToken?.claims['login'];
                      oktaSignIn.remove();
                      this.logger.debug2("LoginOktaComponent.login() -- okta login was a SUCCESS! user: " + this.user);
                    } else {
                      console.error("LoginOidcComponent.oktaSignInWidget, encountered an error ");
                    }
                  },
                  (error) => {
                    // this is currently never called, instead an exception is thrown if userOidcProvisioning fails
                    console.error("LoginOidcComponent.login() -- renderEl error: " + JSON.stringify(error));
                  }
              );
              this.widgetInitialized = true;
            }
          } catch (err) {
            console.error(" oktaSignIn.renderEl failed! : " + err);
          }
        });

  }

  ngOnDestroy(): void {
    const widget = this.authOktaService.signInWidget;
    if (widget) {
      this.logger.debug2("LoginOidcComponent.ngOnDestroy() -- removing sign-in widget");
      widget.remove();
    }
    this.widgetInitialized = false;
  }
}
