import { ApiWellAiVoiceService } from "@oscar-pro/data-access";
import { AuthService } from "@oscar-pro/auth";
import { Component, Injectable, OnDestroy, OnInit } from "@angular/core";
import { LogService } from "@oscar-pro/util";
import { Subject } from "rxjs";
import { WellAiVoiceScript } from "@oscar-pro/interfaces";

@Component({
    selector: 'well-ai-voice',
    templateUrl: './well-ai-voice.component.html',
    styleUrls: ['./well-ai-voice.component.scss'],
    standalone: false
})
@Injectable()
export class WellAiVoiceComponent implements OnInit, OnDestroy {
  isAuthenticated = false;
  private componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
      public authService: AuthService,
      private apiWellAiVoiceService: ApiWellAiVoiceService,
      protected logger: LogService
  ) {}

  ngOnInit() {
    this.logger.debug("WellAiVoiceComponent.ngOnInit() -  Setting up subscribe for isAuthenticated state...");
    this.authService.isAuthenticated$.subscribe(
        (nextVal: boolean) => {
          this.isAuthenticated = nextVal;
          this.logger.debug2("WellAiVoiceComponent: isAuthenticated subscribe() - isAuthenticated event: " +nextVal)
        },
        (error) => {  console.warn("### WellAiVoiceComponent : Auth error " + error)},
        () => {  console.warn("### WellAiVoiceComponent : isAuthenticated has completed --- BUT IT SHOULD NOT COMPLETE!!!")}
    );
    this.generateWellAiVoiceTag();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  private generateWellAiVoiceTag() {
    this.apiWellAiVoiceService.isWellAiVoiceEnabled().subscribe((result) => {
      if (result) {
        this.loadWellAiVoiceScript();
      }
    });
  }

  private loadWellAiVoiceScript() {
    this.apiWellAiVoiceService.getWellAiVoiceScript().subscribe((result) => {
      this.loadScript(result);
    });
  }

  private loadScript(taliScipt: WellAiVoiceScript) {
    return new Promise(() => {
      let script = document.createElement('script');
      script.type = 'text/javascript';
      script.id = taliScipt.id;
      script.src = taliScipt.src;
      script.setAttribute("client-id", taliScipt.clientId);
      document.getElementsByTagName('body')[0].appendChild(script);
    });
  }
}
