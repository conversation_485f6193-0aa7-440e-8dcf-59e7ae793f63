import {
  ChangeDetectorRef,
  Component,
  Inject,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core';
import {Observable, Subject} from 'rxjs';
import {takeUntil, tap} from 'rxjs/operators';
import { faPause, faList } from '@fortawesome/free-solid-svg-icons';
import { InboxFacade } from '@oscar-pro/core-state';
import {
  InboxFilter,
  ModalService,
  ObjectArrayFilterPipe,
  WindowRefService
} from '@oscar-pro/util';
import {
  FieldOption,
  Provider,
  OptionValue,
  InboxItemWAttrs,
  InboxResults,
  AppConfig,
  Option,
  Property,
} from '@oscar-pro/interfaces';
import { ConfirmationComponent, ModalComponent } from '@oscar-pro/ui';
import { DocumentUploadComponent } from './components/document-upload/document-upload.component';
import { ForwardItemComponent } from './components/forward-item/forward-item.component';
import {ApiInboxService, ApiPropertyService, InboxService} from '@oscar-pro/data-access';
import { ActivatedRoute } from '@angular/router';
import { APP_CONFIG } from '@oscar-pro/config';
import * as _ from 'lodash';
import { ToastrService } from 'ngx-toastr';


@Component({
    selector: 'oscar-pro-inbox',
    templateUrl: './inbox.component.html',
    styleUrls: ['./inbox.component.scss'],
    standalone: false
})
export class InboxComponent implements OnInit, OnDestroy {
  loading$: Observable<boolean> = this.inboxFacade.loading$;
  inboxItems$: Observable<Array<InboxItemWAttrs>> =
    this.inboxFacade.inboxItems$;
  inboxResults$: Observable<InboxResults> = this.inboxFacade.inboxResults$;
  inboxItemsCount$: Observable<number> = this.inboxFacade.inboxItemsCount$;
  inboxOptions$: Observable<Array<Option>> = this.inboxFacade.inboxOptions$;
  providerOptions$: Observable<Array<OptionValue>> =
    this.inboxFacade.providerOptions$;
  currentInboxItem$: Observable<InboxItemWAttrs> =
    this.inboxFacade.currentInboxItem$;
  itemsEditedSinceLastLoad$: Observable<number> =
    this.inboxFacade.itemsEditedSinceLastLoad;
  inboxFilter$: Observable<InboxFilter> = this.inboxFacade.inboxFilter$;

  private loading = false;
  public inboxItems: Array<InboxItemWAttrs> = new Array<InboxItemWAttrs>();
  private inboxItemsCount = 0;
  public currentInboxItem: InboxItemWAttrs;
  public acknowledgedItemIndex: number;

  private componentDestroyed$: Subject<boolean> = new Subject();
  private inboxFilter: InboxFilter = new InboxFilter();
  private inboxResults: InboxResults;

  public providers: Array<Provider> = new Array<Provider>();
  public providerOptions: Array<OptionValue> = [] as Array<OptionValue>;

  public selectedInboxItems: Array<InboxItemWAttrs> =
    new Array<InboxItemWAttrs>();

  public hasSelected = false;
  public allSelected = false;
  public listMode = true;

  public faPause = faPause;
  public faList = faList;

  public uploadWarning =
    'Do not upload this file unless you have confirmed that it is from a trusted source and have verified the content to be free of harmful content including viruses, malware and/or unknown external links.  Failure to complete this due diligence on uploaded files may compromise the security of OSCAR and the privacy of patient health information';

  public typeFilters: Array<FieldOption>;
  public typesSelectAllOption: FieldOption = {} as FieldOption;

  private nativeWindow: Window;
  private itemsEditedSinceLastLoad = 0;
  public defaultStartDate: string;

  constructor(
    private changeDetectorRef: ChangeDetectorRef,
    private activatedRoute: ActivatedRoute,
    private apiInboxService: ApiInboxService,
    private inboxService: InboxService,
    private modalService: ModalService,
    private inboxFacade: InboxFacade,
    private windowRefService: WindowRefService,
    private objectArrayFilterPipe: ObjectArrayFilterPipe,
    private propertyService: ApiPropertyService,
    private cdr: ChangeDetectorRef,
    private toastr: ToastrService,
    @Inject(APP_CONFIG) private appConfig: AppConfig
  ) {
    this.nativeWindow = this.windowRefService.getNativeWindow();
  }

  ngOnInit(): void {
    this.applyDefaultStartDatePreference().subscribe(() => {
      this.subscribeToDataStore();

      this.inboxService.getUploadWarningMessage.subscribe(() => {
        this.inboxService.returnUploadWarningMessage(this.uploadWarning);
      });

      this.activatedRoute.queryParams.subscribe((params) => {
        const updatedFilter: { [key: string]: any } = {};
        if (params['providerNo']) {
          updatedFilter.providerNumber = params['providerNo'];
        }
        if (params['displayInListMode']) {
          updatedFilter.displayInListMode = params['displayInListMode'] === 'true';
        }
        if (params['demoNo']) {
          updatedFilter.demographicNumber = params['demoNo'];
        }
        if(this.defaultStartDate){
          updatedFilter.startDate = this.defaultStartDate;
        }
        this.inboxFacade.setInboxFilter(updatedFilter);
      });

      this.inboxService.getNextInboxItem.subscribe((labId: any) => {
        this.currentInboxItem = this.inboxItems.filter(
          (item) =>
            item.id.segmentId == labId.segmentId
            && item.id.labType == labId.labType
        )[0];
        this.nextInboxItem();
      });

      this.inboxService.getPreviousInboxItem.subscribe((labId: any) => {
        this.currentInboxItem = this.inboxItems.filter(
          (inboxItem) =>
            inboxItem.id.segmentId == labId.segmentId
            && inboxItem.id.labType == labId.labType
        )[0];
        this.previousInboxItem();
      });

      this.getInboxItems();
      this.getInboxOptions();
      this.getProviders();
    });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  subscribeToDataStore() {
    this.loading$.subscribe((loading: boolean) => {
      this.loading = loading;
    });
    this.inboxItems$.subscribe((inboxItems: Array<InboxItemWAttrs>) => {
      this.inboxItems = inboxItems;
      this.changeDetectorRef.detectChanges();
    });
    this.inboxItemsCount$.subscribe((inboxItemsCount: number) => {
      this.inboxItemsCount = inboxItemsCount;
    });
    this.currentInboxItem$.subscribe((inboxItem: InboxItemWAttrs) => {
      this.currentInboxItem = inboxItem;
    });
    this.inboxResults$.subscribe((inboxResults: InboxResults) => {
      this.inboxResults = inboxResults;
      this.buildTypesFilter(inboxResults)
    });
    this.itemsEditedSinceLastLoad$.subscribe(
      (itemsCount) => (this.itemsEditedSinceLastLoad = itemsCount)
    );
    this.inboxFilter$.subscribe((inboxFilter: InboxFilter) => {
      this.inboxFilter = inboxFilter;
    });
  }

  buildTypesFilter(inboxResults: InboxResults) {
    const filters = [];
    Object.entries(inboxResults).forEach(([key, value]) => {
      if (key === 'All') {
        this.typesSelectAllOption = {
          option: key,
          value: value['label'],
          count: value['count'],
          checked: this.typesSelectAllOption?.checked !== false,
          selected: this.typesSelectAllOption?.checked !== false,
        };
      } else {
        const existingFilter = this.typeFilters?.find((o) => o.option === key);

        filters.push({
          option: key,
          value: value['label'],
          count: value['count'],
          checked: existingFilter?.checked ? existingFilter.checked : false,
          selected: existingFilter?.selected ? existingFilter.selected : false,
        });
      }
    });
    this.typeFilters = filters;
  }

  onUpdateTypes(typeFilters: Array<FieldOption>) {
    const updatedTypeFilters = typeFilters.reduce(
      (obj, item) => ({
        ...obj,
        [item.option]: item.checked,
      }),
      {}
    );
    this.onEventUpdateInboxFilter(updatedTypeFilters);
  }

  nextInboxItem(): void {
    let index = this.inboxItems.indexOf(this.currentInboxItem, 0);

    if (
      typeof this.currentInboxItem === 'undefined'
      && this.acknowledgedItemIndex
    ) {
      index = this.acknowledgedItemIndex++;
    } else if (index < this.inboxItems.length - 1) {
      index++;
    } else {
      index = 0;
    }

    this.currentInboxItem = this.inboxItems[index];

    this.inboxService.returnNextInboxItem({
      inboxItem: this.currentInboxItem,
      inboxItemIndex: index,
      hasNext: index + 1 < this.inboxItems.length,
      hasPrevious: index > 0,
    });
  }

  previousInboxItem() {
    let index = this.inboxItems.indexOf(this.currentInboxItem, 0);
    if (index > 0) {
      index--;
    }
    this.currentInboxItem = this.inboxItems[index];

    this.inboxService.returnPreviousInboxItem({
      inboxItem: this.currentInboxItem,
      inboxItemIndex: index,
      hasPrevious: index > 0,
      hasNext: index + 1 < this.inboxItems.length,
    });
  }

  resetInboxItems() {
    this.inboxFacade.resetInboxItems();
  }

  reloadInboxItems() {
    this.inboxFacade.loadInboxItems(this.inboxItems, this.inboxResults);
  }

  getInboxItems(): void {
    this.resetInboxItems();
    this.inboxFacade.getInboxItems();
  }

  getMoreInboxItems(): void {
    this.inboxFacade.getMoreInboxItems(this.itemsEditedSinceLastLoad);
  }

  getProviders(): void {
    this.inboxFacade.getProviders();
  }

  getInboxOptions(): void {
    this.inboxFacade.getInboxOptions();
  }

  onEventUpdateInboxFilter(filters: {
    [key: string]: string | number | boolean;
  }): void {
    this.inboxFacade.updateInboxFilter(filters);
    this.getInboxItems();
  }

  onClickUploadNewDoc(event: MouseEvent): void {
    event.preventDefault();
    const inputs = {
      isActive: true,
      qvComponent: true,
    };
    const outputs = {};

    const inboxUploadModalRef = this.modalService.init(
      ModalComponent,
      inputs,
      {},
      'inbox-upload-new-doc-modal'
    );

    const inboxUploadModalInstance = <ModalComponent>(
      inboxUploadModalRef.instance
    );

    inboxUploadModalInstance.componentConfig = { inputs, outputs };

    inboxUploadModalInstance.componentType = DocumentUploadComponent;

    inboxUploadModalInstance.refreshParent
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.getInboxItems();
      });
  }

  onInboxItemScroll(): void {
    if (!this.loading && !(this.inboxItems.length >= this.inboxItemsCount)) {
      this.changeDetectorRef.detectChanges();
      setTimeout(() => {
        this.getMoreInboxItems();
      }, 500);
    }
  }

  onEventSelectAll(): void {
    this.allSelected = !this.allSelected;
    this.selectAll(this.allSelected);
    this.checkIfHasSelected();
  }

  selectAll(isSelected: boolean): void {
    this.inboxFacade.toggleSelectAllInboxItems(isSelected);
    this.allSelected = isSelected;
  }

  checkIfHasSelected(): void {
    this.selectedInboxItems = new Array<InboxItemWAttrs>();
    let hasSelected = false;
    for (let i = 0; i < this.inboxItems.length; i++) {
      if (this.inboxItems[i].__selected) {
        hasSelected = true;
        this.selectedInboxItems.push(this.inboxItems[i]);
      }
    }
    this.hasSelected = hasSelected;
  }

  onEventLabAcknowledged(labId: any): void {
    const status = this.inboxFilter.get("selectedStatus");
    this.acknowledgedItemIndex = this.inboxItems.findIndex(
      (item) =>
        item.id.segmentId === labId.segmentId
        && item.id.labType === labId.labType
    );
    if (status !== "A" && status !== "L") {
      this.filterInboxItem(labId);
    }
    this.reloadInboxItems();
  }

  filterInboxItem(labId: any): void {
    this.inboxItems = this.inboxItems.filter(
      (item) =>
        !(
          item.id.segmentId == labId.segmentId
          && item.id.labType == labId.labType
        )
    );
    this.reduceInboxCount(labId);
  }

  reduceInboxCount(labId): void {
    if (labId.labType === "DOC") {
      this.inboxResults.documents.count = +this.inboxResults.documents.count - 1;
    } else if (labId.labType === "HL7") {
      this.inboxResults.labs.count = +this.inboxResults.labs.count - 1;
    } else if (labId.labType === "HRM") {
      this.inboxResults.hrm.count = +this.inboxResults.hrm.count - 1;
    }
  }

  onEventReloadLabEntry(labId: any): void {
    // get lab to update
    if (this.listMode) {
      const filteredInboxItems = this.inboxItems.filter(
        (item) =>
          item.id.segmentId == labId.segmentId
          && item.id.labType == labId.labType
      );
      if (filteredInboxItems.length > 0) {
        const matchedItem = filteredInboxItems[0];
        const matchedItemIndex = this.inboxItems.indexOf(matchedItem, 0);
        this.apiInboxService.getInboxItem(matchedItem.id).subscribe(
          (updatedItem) => {
            const updatedItems = _.cloneDeep(this.inboxItems);
            updatedItems[matchedItemIndex] = updatedItem;
            this.inboxItems = [...updatedItems];
          },
          (err) => console.error(err)
        );
      }
    }
  }

  onClickMode(mode: string): void {
    if (mode === 'list') {
      this.listMode = true;
      this.inboxFacade.setInboxFilterResultSize(50);
    } else {
      if (this.hasSelected) {
        this.selectAll(false);
        this.checkIfHasSelected();
      }
      this.listMode = false;
      this.inboxFacade.setInboxFilterResultSize(5);
    }
    this.getInboxItems();
  }

  onEventSelectInboxItem(index: number): void {
    this.inboxFacade.toggleSelectedInboxItemByIndex(index);
    this.checkIfAllSelected();
    this.checkIfHasSelected();
  }

  onEventUpdateHeaderOption(id: string, value: string): Observable<Property> {
    return this.apiInboxService.updateOption(id, value);
  }

  onEventInboxItemFiled(inboxId: any): void {
    this.fileInboxItems(inboxId);
  }

  fileInboxItems(inboxId) {
    this.inboxFacade.increaseItemsEditedSinceLastLoad();
    const status = this.inboxFilter.get("selectedStatus");
    if (status !== "F" && status !== "A") {
      this.filterInboxItem(inboxId);
    }
    this.reloadInboxItems();
  }

  getUnassignedItems(): InboxItemWAttrs[] {
    return this.objectArrayFilterPipe.transform(
      this.selectedInboxItems,
      {firstName: null, lastName: null}
    );
  }

  checkIfAllSelected(): void {
    let allSelected = true;
    for (let i = 0; i < this.inboxItems.length; i++) {
      if (!this.inboxItems[i].__selected) {
        allSelected = false;
        break;
      }
    }
    this.allSelected = allSelected;
  }

  onClickFile(event: MouseEvent): void {
    event.preventDefault();
    const unassignedItems = this.getUnassignedItems();
    if (this.hasSelected) {
      if (unassignedItems.length > 0) {
        const inputs = {
          isActive: true,
          selectedInboxItems: this.selectedInboxItems,
        };

        const outputs = {
          fileConfirmation: 'fileConfirmation',
          cancelFile: 'cancelFile',
        };

        const inboxFileModalRef = this.modalService.init(
          ModalComponent,
          inputs,
          {},
          'inbox-upload-modal'
        );

        const inboxFileModalInstance = <ModalComponent>inboxFileModalRef.instance;
        inboxFileModalInstance.componentType = ConfirmationComponent;
        inboxFileModalInstance.componentConfig = {inputs, outputs};

        inboxFileModalInstance.dataChange.pipe().subscribe((modalContent) => {
          if (modalContent.type === 'fileConfirmation') {
            this.inboxFacade.fileSelectedItems(this.selectedInboxItems);
            this.selectedInboxItems.forEach((item) => this.fileInboxItems(item.id));
            this.modalService.destroy();
          } else if (modalContent.type === 'cancelFile') {
            this.modalService.destroy();
          }
        });
      } else {
        this.inboxFacade.fileSelectedItems(this.selectedInboxItems);
        this.selectedInboxItems.forEach((item) => this.fileInboxItems(item.id));
      }
    }
  }

  onClickForward(event: MouseEvent): void {
    event.preventDefault();

    if (this.hasSelected) {
      const inputs = {
        isActive: true,
        selectedInboxItems: this.selectedInboxItems,
      };

      const outputs = {};

      const inboxForwardModalRef = this.modalService.init(
        ModalComponent,
        inputs,
        {},
        'inbox-upload-modal'
      );

      const inboxForwardModalInstance = <ModalComponent>(
        inboxForwardModalRef.instance
      );

      inboxForwardModalInstance.componentType = ForwardItemComponent;
      inboxForwardModalInstance.componentConfig = { inputs, outputs };

      inboxForwardModalInstance.refreshParent
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.getInboxItems();
        });
    }
  }

  onHeaderLinkClick(linkId: string) {
    if (linkId === 'forwarding_rules') {
      const url = `/${
        this.appConfig.oscarContext
      }/oscarMDS/ForwardingRules.jsp?providerNo=${this.inboxFacade.getCurrentProviderNo()}`;
      this.nativeWindow.open(url, 'Forwarding Rule', 'height=660,width=960');
    }

    if (linkId === 'default_inbox_start_date_range') {
      const url = `/${this.appConfig.oscarContext}/provider/providerDefaultInboxStartDateRange.jsp`;
      const popup = this.nativeWindow.open(url, 'Default Inbox Start Date Range', 'height=500,width=860');

      if (popup) {
        const checkPopupClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkPopupClosed);
            this.refreshDefaultStartDate();
          }
        }, 500);
      }
    }
  }

  onHeaderOptionClick(event: any) {
    this.onEventUpdateHeaderOption(event.id, event.value).subscribe(
      () => {
        this.toastr.success(event.title + ' has been updated as: ' + event.value, 'Success', {
          positionClass: 'toast-bottom-center'
        });
        this.getInboxOptions();
      },
      () => {
        this.toastr.error(' failed to update.', 'Error', {
          positionClass: 'toast-bottom-center'
        });
      }
    );
  }

  private fetchAndSetDefaultStartDate(data: Property): void {
    const days = data?.value && data.value !== 'None' && data.value.trim() !== '' ? parseInt(data.value, 10) : 180;
    this.setDefaultStartDate(days);
    this.cdr.detectChanges();
  }

  private applyDefaultStartDatePreference(): Observable<Property> {
    return this.propertyService.getProperty("default_inbox_start_date_range").pipe(
      tap(this.fetchAndSetDefaultStartDate.bind(this)),
    );
  }

  private setDefaultStartDate(days: number): void {
    const today = new Date();
    today.setDate(today.getDate() - days);
    this.defaultStartDate = today.toISOString().slice(0,10);
    this.inboxFacade.setInboxFilter({ startDate: this.defaultStartDate });
    this.cdr.detectChanges();
  }

  private refreshDefaultStartDate(): void {
    this.applyDefaultStartDatePreference()
    .subscribe(() => {
      this.getInboxItems();
    });
  }
}
