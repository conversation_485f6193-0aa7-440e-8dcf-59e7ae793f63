import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DocumentUploadComponent } from './document-upload.component';
import { DocumentsComponent } from './documents/documents.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import {
  AddSpecialAttributesPipe,
  EnvironmentVarsPipe,
  ModalService,
  StripSpecialAttributesPipe,
  UrlParamsPipe
} from '@oscar-pro/util';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { APP_CONFIG } from '@oscar-pro/config';
import { ToastrService} from 'ngx-toastr';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('DocumentUploadComponent', () => {
  let component: DocumentUploadComponent;
  let fixture: ComponentFixture<DocumentUploadComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DocumentUploadComponent, DocumentsComponent],
      providers: [
        HttpClient,
        HttpHandler,
        AddSpecialAttributesPipe,
        StripSpecialAttributesPipe,
        UrlParamsPipe,
        EnvironmentVarsPipe,
        { provide: APP_CONFIG, useValue: {}},
        { provide: ModalService, useValue: {close: jest.fn()} },
        {provide: ToastrService, useValue: jest.fn()},
        {
          provide: Store,
          useValue: {
            pipe: () => of(null),
            dispatch: jest.fn(),
          }
        },
      ],
      schemas: [ CUSTOM_ELEMENTS_SCHEMA ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DocumentUploadComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
