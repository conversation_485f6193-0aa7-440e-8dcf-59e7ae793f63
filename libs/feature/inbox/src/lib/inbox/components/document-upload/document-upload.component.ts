import { Component } from '@angular/core';
import { ModalService } from '@oscar-pro/util';

type UploadType = 'documents' | 'labs' | 'hrm';

@Component({
    selector: 'oscar-pro-document-upload',
    templateUrl: './document-upload.component.html',
    styleUrls: ['./document-upload.component.scss'],
    standalone: false
})
export class DocumentUploadComponent {
  public uploadType: UploadType = 'documents';

  constructor(private modalService: ModalService) {}

  onClickDocType(event: Event, documentType: UploadType): void {
    event.preventDefault();
    this.uploadType = documentType;
  }

  onEventCloseModal($event: Event) {
    this.modalService.close();
  }
}
