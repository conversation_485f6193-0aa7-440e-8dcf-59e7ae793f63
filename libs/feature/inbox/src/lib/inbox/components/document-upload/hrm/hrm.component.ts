import {
  Component,
  EventEmitter,
  OnInit,
  Output,
} from '@angular/core';

import { InboxService } from '@oscar-pro/data-access';
import { ConfidentialityStatement } from '@oscar-pro/interfaces';
import { InboxFacade } from '@oscar-pro/core-state';
import { Observable } from 'rxjs';
import * as _ from 'lodash';

@Component({
    selector: 'oscar-pro-hrm',
    templateUrl: './hrm.component.html',
    styleUrls: ['./hrm.component.scss'],
    standalone: false
})
export class HrmComponent implements OnInit {
  @Output() closeModal: EventEmitter<any> = new EventEmitter<any>();

  fetchHrmInProgress$: Observable<boolean> =
    this.inboxFacade.fetchHrmInProgress$;
  fetchHrmError$: Observable<boolean> = this.inboxFacade.fetchHrmError$;
  providerConfidentialityStatement$: Observable<ConfidentialityStatement> =
    this.inboxFacade.providerConfidentialityStatement$;
  public providerConfidentialityStatement: ConfidentialityStatement = {
    providerNo: null,
    statement: '',
  };
  public uploadWarningMessage = '';

  pondOptions = {
    server: {
      process: (fieldName, file, metadata, load, error) => {
        const fileUpload = this.inboxFacade.uploadHrmFile(file);
        fileUpload.subscribe(
          (uploadResponse) => {
            if (uploadResponse.uploadSuccess) {
              const msg: string =
                uploadResponse.fileName + ' uploaded successfully.';
              this.inboxFacade.addSuccessMessage(msg);
              load();
            } else {
              const msg: string = uploadResponse.fileName + ' failed to upload: ';
              error(msg);
            }
          },
          () => {
            const msg = 'HRM failed to upload.';
            error(msg);
          }
        );
      },
    },
  };

  constructor(
    private inboxService: InboxService,
    private inboxFacade: InboxFacade
  ) {
    this.subscribeToDataStore();
  }

  subscribeToDataStore() {
    this.providerConfidentialityStatement$.subscribe(
      (confidentialityStatement: ConfidentialityStatement) => {
        this.providerConfidentialityStatement = _.cloneDeep(
          confidentialityStatement
        );
      }
    );
  }
  ngOnInit(): void {
    this.inboxService.sendUploadWarningMessage.subscribe((warningMessage) => {
      this.uploadWarningMessage = warningMessage;
    });
    this.inboxService.requestUploadWarningMessage();
    this.inboxFacade.getConfidentialityStatement();
  }

  onClickDisableOutageMessage(event: MouseEvent): void {
    event.preventDefault();
    this.inboxFacade.disableOutageMessage();
  }

  onClickFetchNewData(event: MouseEvent): void {
    event.preventDefault();
    this.inboxFacade.fetchHRM();
  }

  onClickSaveConfidentialityStatement(event: MouseEvent): void {
    event.preventDefault();
    this.inboxFacade.saveConfidentialityStatement(
      this.providerConfidentialityStatement?.statement
    );
  }
}
