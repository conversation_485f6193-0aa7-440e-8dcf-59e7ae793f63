import { Component, Input } from '@angular/core';
import {
  faExclamationTriangle,
  faExclamationCircle,
  faHospital,
  faExclamation,
} from '@fortawesome/free-solid-svg-icons';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';

const DISPLAY_ICON = {
  A: faExclamation,
  P: faHospital,
  S: faExclamationCircle,
  T: faExclamationTriangle,
};

@Component({
    selector: 'oscar-pro-inbox-priority-icon',
    templateUrl: './priority-icon.component.html',
    styleUrls: ['./priority-icon.component.scss'],
    standalone: false
})
export class PriorityIconComponent {
  @Input() priority: string;
  @Input() displayText: string;

  public faExclamationTriangle = faExclamationTriangle;
  public faHospital = faHospital;
  public faExclamationCircle = faExclamationCircle;
  public faExclamation = faExclamation;

  hasIcon() {
    return Object.prototype.hasOwnProperty.call(DISPLAY_ICON, this.priority);
  }

  displayIconBasedOnPriority(): IconDefinition {
    return DISPLAY_ICON[this.priority];
  }
}
