@import 'apps/oscar-pro/src/styles/colours';

cdk-virtual-scroll-viewport {
  height: calc(100% - 58px);

  &::-webkit-scrollbar {
    width: 1em;
  }

  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  }

  &::-webkit-scrollbar-thumb {
    background-color: $lightgrey;
  }
}

table {
  background-color: transparent;
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0 5px;
  font-size: 0.9em;
  color: #000000;
  height: 100%;

  .warning {
    color: $warning-orange;
  }

  thead tr th {
    font-size: 15px;
    color: $primary-color;
    font-weight: 600;
    padding: 7px;
    margin-top: 15px;
    min-width: 20px;
    position: sticky;
    height: 50px;
    background-color: $white;
    box-shadow: inset 0 -1px 0 $lightgrey;
    z-index: 1000;
    a {
      text-decoration: none;
      color: $primary-color;
    }
  }

  tbody tr:nth-child(even) {
    background: var(--row-fill);
  }

  tbody tr {
    background: $white;
    box-shadow: 0 1px 0 0 $grey-light;
    border-radius: 4px;

    &.is-flagged {
      color: $danger-bright;
      background-color: $danger-light;
    }

    &:hover:not(.disabled) {
      cursor: pointer;
      box-shadow: 5px 5px 12px 0 $lightgrey;
      filter: brightness(97%);
    }

    td {
      border-bottom: 2px solid $light;
      padding: 7px;
      vertical-align: middle;
    }
  }

  .tag {
    align-items: center;
    display: inline-flex;
    height: 2em;
    justify-content: center;
    line-height: 1.5;
    padding-left: 0.75em;
    padding-right: 0.75em;
    white-space: nowrap;

    &.is-new {
      background: $tag-new-bg !important;
      border-radius: 5px !important;
      color: $tag-new !important;
      font-size: 14px !important;
    }
  }
}

@keyframes fadeInAnimation {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.tooltip-container {
  position: relative;

  .tooltip {
    background-color: $white;
    display: inline-block;
    border-radius: 4px;
    box-shadow: 0 10px 25px 0 rgb(0 0 0 / 17%);
    position: absolute;
    bottom: calc(100% + 10px);
    left: -10px;
    padding: 8px 14px;
    font-size: 0.85em;
    pointer-events: none;
    opacity: 0;
    transition: 0.3s all;

    &:hover .tooltip {
      opacity: 1 !important;
    }

    &.tooltip-warning {
      background-color: $tooltip-warning-bg;
      color: $tooltip-warning;
      white-space: nowrap;
    }
  }
}

.tooltip-container:hover .tooltip {
  opacity: 1;
}

.chevron {
  visibility: hidden;
  padding: 0 3px;

  &.top,
  &.bottom,
  &.right,
  &.left {
    visibility: visible;
  }
}
.chevron::before {
  border-style: solid;
  border-width: 0.2em 0.2em 0 0;
  content: '';
  display: inline-block;
  height: 0.45em;
  left: 0.15em;
  position: relative;
  top: 0.15em;
  transform: rotate(-45deg);
  vertical-align: top;
  width: 0.45em;
}

.chevron.right:before {
  left: 0;
  transform: rotate(45deg);
}

.chevron.bottom:before {
  top: 0;
  transform: rotate(135deg);
}

.chevron.left:before {
  left: 0.25em;
  transform: rotate(-135deg);
}

.large-col,
.xlarge-col {
  @apply text-left;
}

.medium-col {
  width: 140px !important;
}

.large-col {
  width: 150px !important;
}

.xlarge-col {
  width: 280px !important;
}

.small-col {
  width: 60px !important;
}

.xsmall-col {
  width: 40px !important;
}

.unread {
  font-weight: bold;
}

.hidden {
  display: none;
}

