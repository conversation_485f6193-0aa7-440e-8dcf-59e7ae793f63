import {Component, EventEmitter, Inject, Input, Output} from '@angular/core';
import { AuthService } from '@oscar-pro/auth';
import { APP_CONFIG } from '@oscar-pro/config';
import {AppConfig, InboxComment, InboxId, InboxItemWAttrs, User} from '@oscar-pro/interfaces';
import {ModalService, PanelService, UrlParamsPipe} from '@oscar-pro/util';
import {ModalComponent} from "@oscar-pro/ui";
import {AddCommentComponent} from "../add-comment/add-comment.component";
import {takeUntil} from "rxjs/operators";
import {ApiHl7Service, ApiInboxService, ApiPropertyService} from "@oscar-pro/data-access";
import {InboxFacade} from "@oscar-pro/core-state";
import {Observable, Subject} from "rxjs";
import { isNil } from 'lodash';
import { ToastrService } from 'ngx-toastr';

@Component({
    selector: 'oscar-pro-lab',
    templateUrl: './lab.component.html',
    styleUrls: ['./lab.component.scss'],
    standalone: false
})
export class LabComponent {
  @Input() inboxItem: InboxItemWAttrs;
  @Input() inline = false;
  @Input() isLoading = false;
  @Output() inboxItemFiled: EventEmitter<InboxId> = new EventEmitter<InboxId>();
  @Output() labAcknowledged: EventEmitter<InboxId> = new EventEmitter<InboxId>();

  private currentUser: User;
  public labType: string;
  private componentDestroyed$: Subject<boolean> = new Subject();
  public disableAcknowledgeComment: boolean;

  constructor(
    private urlParamsPipe: UrlParamsPipe,
    private authService: AuthService,
    private modalService: ModalService,
    private apiInboxService: ApiInboxService,
    private inboxFacade: InboxFacade,
    private apiHl7Service: ApiHl7Service,
    private panelService: PanelService,
    private propertyService: ApiPropertyService,
    private toastr: ToastrService,
    @Inject(APP_CONFIG) private appConfig: AppConfig
  ) {
    this.currentUser = this.authService.getCurrentUser();
  }

  ngOnInit(): void {
    this.propertyService
    .getProperty('lab_ack_comment')
    .pipe(takeUntil(this.componentDestroyed$))
    .subscribe(
      (data) => {
        this.disableAcknowledgeComment =
          !isNil(data) && data.value ? 'yes' === data.value : false;
      },
      (error) => console.error(error)
    );
  }

  labUrl(): string {
    const params = {
      segmentID: this.inboxItem?.id.segmentId,
      providerNo: this.currentUser?.providerNo,
      searchProviderNo: this.currentUser?.providerNo,
      status: this.inboxItem?.status,
      showLatest: true,
      hideLinkDemographicPopup: true,
      hideTaliToolbar: true,
      hideClassicButtons: true,
    };
    const url = `/${
      this.appConfig.oscarContext
    }/lab/CA/ALL/labDisplay.jsp${this.urlParamsPipe.transform(params)}`;

    return url;
  }

  onClickAcknowledge(event: MouseEvent) {
    event.preventDefault();
    this.acknowledgeAndComment(event);
  }

  onClickFile(event: MouseEvent) {
    event.preventDefault();
    const selectedInboxItem = [this.inboxItem];
    this.apiInboxService
    .fileSelected(selectedInboxItem, this.currentUser.providerNo)
    .subscribe(
      () => {
        this.toastr.success('Lab Filed Successfully.', 'Success', {
          positionClass: 'toast-bottom-center'
        });
        this.inboxItemFiled.emit(this.inboxItem.id);
      },
      () => {
        this.toastr.error('Failed to File Lab.', 'Error', {
          positionClass: 'toast-bottom-center'
        });
      }
    );
  }

  onClickPrint(event: MouseEvent) {
    event.preventDefault();
    const iframe = document.querySelector(
      '.oscar-lab-iframe'
    ) as HTMLIFrameElement;
    this.apiInboxService
    .getLabType(Number(this.getCurrentSegmentId()))
    .subscribe((data) => {
      if (data) {
        this.labType = data['type'];
        if (this.labType === 'OLIS_HL7') {
          iframe.contentWindow.postMessage('printFromFrameOlisHL7');
        } else {
          iframe.contentWindow.postMessage('printFromFrameHL7');
        }
      }
    });
  }

  onClickAddComment(event: MouseEvent, acknowledge = false) {
    event.preventDefault();

    const inputs = {
      isActive: true,
      qvComponent: true,
      existingTitle: this.inboxItem.label,
      headerTitle: 'Lab Label',
    };
    enum outputs {
      saveComment = 'saveComment',
      cancelComment = 'cancelComment',
    }

    const commentModalRef = this.modalService.init(
      ModalComponent,
      inputs,
      outputs,
      'inbox-upload-modal'
    );
    const commentModalInstance = <ModalComponent>commentModalRef.instance;
    commentModalInstance.size = 'small';
    commentModalInstance.componentType = AddCommentComponent;
    commentModalInstance.componentConfig = { inputs: inputs, outputs: outputs };

    commentModalInstance.dataChange
    .pipe(takeUntil(this.componentDestroyed$))
    .subscribe((modalContent) => {
      if (modalContent.type === 'saveComment') {
        this.validateModalSaveComment(modalContent.data, acknowledge);
      } else if (modalContent.type === 'cancelComment') {
        this.modalCancelComment();
      }
    });
  }

  getCurrentSegmentId() {
    const iframe = document.querySelector(
      '.oscar-lab-iframe'
    ) as HTMLIFrameElement;
    const currentLabUrl = new URL(iframe.contentWindow.location.href);
    return currentLabUrl.searchParams.get('segmentID');
  }

  acknowledgeAndComment(event: MouseEvent): void {
    if (!this.disableAcknowledgeComment) {
      this.onClickAddComment(event, true);
    } else {
      this.validateAcknowledge();
    }
  }

  validateAcknowledge(comment = ''): void {
    this.hasLinkedDemographic().subscribe((hasLinkedDemographic) => {
      // Acknowledge button is pressed
      // If demographic is not linked, we validate, otherwise go straight to acknowledge
      if (!hasLinkedDemographic && !this.validateNoLinkedDemographic()) {
        // Validation cancelled
        return;
      }
      // Validation confirmed
      this.acknowledge(comment);
    });
  }

  validateModalSaveComment(inboxComment: InboxComment, acknowledge: boolean) {
    this.hasLinkedDemographic().subscribe((hasLinkedDemographic) => {
      // Force comments on acknowledge preference is enabled
      // Acknowledge button is clicked
      if (acknowledge) {
        // Demographic is not linked
        if (!hasLinkedDemographic) {
          if (this.validateNoLinkedDemographic()) {
            this.modalSaveComment(inboxComment);
            this.acknowledge();
          } else {
            this.modalCancelComment();
          }
          return;
        }
        // Demographic is linked
        this.modalSaveComment(inboxComment);
        this.acknowledge();
        return;
      }

      // Force comments on acknowledge preference is disabled
      // Comment button is clicked
      // Demographic is linked
      if (hasLinkedDemographic) {
        this.modalSaveComment(inboxComment);
        return;
      }
      // Demographic is not linked
      this.toastr.error('Please relate lab to a patient.', 'Error', {
        positionClass: 'toast-bottom-center'
      });
      this.modalCancelComment();
    });
  }

  validateNoLinkedDemographic(): boolean {
    return confirm(
      'This lab has not been matched to a patient.\n'
      + 'Are you sure you want to acknowledge it?\n'
      + 'Press OK to acknowledge or Cancel to return to lab.'
    );
  }

  acknowledge(comment = ''): void {
    this.apiHl7Service
    .acknowledgeLab(
      this.currentUser.providerNo,
      this.inboxItem.id.labType,
      Number(this.getCurrentSegmentId()),
      this.inboxItem.accessionNumber,
      comment
    )
    .subscribe(
      (data) => {
        if (data) {
          this.toastr.success('Lab Acknowledged Successfully.', 'Success', {
            positionClass: 'toast-bottom-center'
          });
          this.inboxFacade.increaseItemsEditedSinceLastLoad();
          this.inboxFacade.updateDocumentStatus(
            Number(this.getCurrentSegmentId()),
            'A'
          );
          this.labAcknowledged.emit(this.inboxItem.id);
        }

        this.panelService.destroy();
      },
      () => {
        this.toastr.error('Failed to Acknowledge Lab.', 'Error', {
          positionClass: 'toast-bottom-center'
        });
      }
    );
  }

  modalSaveComment(inboxComment: InboxComment) {
    this.updateLabel(inboxComment.label);
    this.saveComment(inboxComment.comment);
    this.modalCancelComment();
    this.reloadLabFrame();
  }

  updateLabel(label: string): void {
    if (!label || label === this.inboxItem.label) {
      return;
    }
    this.apiHl7Service
    .updateLabel(Number(this.getCurrentSegmentId()), label)
    .subscribe(
      () => {
        this.toastr.success('Label updated.', 'Success', {
          positionClass: 'toast-bottom-center'
        });
      },
      () => {
        this.toastr.error('Failed to update label.', 'Error', {
          positionClass: 'toast-bottom-center'
        });
      }
    );
  }

  saveComment(comment = ''): void {
    if (comment.length <= 0) {
      return;
    }
    this.apiHl7Service
    .addComment(Number(this.getCurrentSegmentId()), comment)
    .pipe(takeUntil(this.componentDestroyed$))
    .subscribe(
      () => {
        this.toastr.success('Comment added.', 'Success', {
          positionClass: 'toast-bottom-center'
        });
      },
      () => {
        this.toastr.error('Failed to add comment.', 'Error', {
          positionClass: 'toast-bottom-center'
        });
      }
    );
  }

  modalCancelComment() {
    this.modalService.destroy();
  }

  hasLinkedDemographic(): Observable<boolean> {
    return this.apiHl7Service.hasLinkedDemographic(
      Number(this.getCurrentSegmentId())
    );
  }

  reloadLabFrame() {
    const iframe = document.querySelector(
      '.oscar-lab-iframe'
    ) as HTMLIFrameElement;
    iframe.contentWindow.location.reload();
  }
}
