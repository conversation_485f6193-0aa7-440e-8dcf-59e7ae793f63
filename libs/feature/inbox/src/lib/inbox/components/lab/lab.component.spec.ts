import { ComponentFixture, fakeAsync, flush, TestBed } from '@angular/core/testing';
import { LabComponent } from './lab.component';
import {
  AddSpecialAttributesPipe,
  ModalService,
  ObjectArrayFilterPipe, PanelService,
  UrlParamsPipe
} from '@oscar-pro/util';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { AuthService } from '@oscar-pro/auth';
import { ApiHl7Service, ApiInboxService, ApiPropertyService } from '@oscar-pro/data-access';
import { of, throwError } from 'rxjs';
import { APP_CONFIG } from '@oscar-pro/config';
import { Store } from '@ngrx/store';
import { SafeHtmlPipe } from '@oscar-pro/util';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ToastrService} from 'ngx-toastr';
import { InboxFacade } from '@oscar-pro/core-state';
import { InboxItemWAttrs } from '@oscar-pro/interfaces';

describe('LabComponent', () => {
  let component: LabComponent;
  let fixture: ComponentFixture<LabComponent>;
  let apiInboxServiceMock: any;
  let apiHl7ServiceMock: any;
  let inboxFacadeMock: any;
  let panelServiceMock: any;
  let modalServiceMock: any;

  let toastrService: {
    success: jest.Mock,
    error: jest.Mock,
    warning: jest.Mock,
  };

  beforeEach(async () => {
    toastrService = {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
    };


    apiInboxServiceMock = {
      fileSelected: jest.fn(),
      getLabType: jest.fn()
    };

    apiHl7ServiceMock = {
      acknowledgeLab: jest.fn(),
      updateLabel: jest.fn(),
      addComment: jest.fn(),
      hasLinkedDemographic: jest.fn()
    };

    inboxFacadeMock = {
      increaseItemsEditedSinceLastLoad: jest.fn(),
      updateDocumentStatus: jest.fn()
    };

    modalServiceMock = {
      init: jest.fn(),
      destroy: jest.fn()
    };

    panelServiceMock = {
      destroy: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [ LabComponent, SafeHtmlPipe ],
      providers: [
        UrlParamsPipe,
        AddSpecialAttributesPipe,
        ObjectArrayFilterPipe,
        provideHttpClientTesting(),
        HttpClient,
        HttpHandler,
        { provide: APP_CONFIG, useValue: {} },
        { provide: AuthService, useValue: {getCurrentUser: jest.fn().mockReturnValue({providerMo: '321'})} },
        { provide: ApiPropertyService, useValue: {getProperty: () => of(null)} },
        {provide: ToastrService, useValue: toastrService},
        { provide: ApiInboxService, useValue: apiInboxServiceMock },
        { provide: ApiHl7Service, useValue: apiHl7ServiceMock },
        { provide: InboxFacade, useValue: inboxFacadeMock },
        { provide: ModalService, useValue: modalServiceMock },
        { provide: PanelService, useValue: panelServiceMock },
        {
          provide: Store,
          useValue: {
            pipe: () => of(null),
            dispatch: jest.fn(),
          }
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LabComponent);
    component = fixture.componentInstance;
    component.inboxItem = {
      id: { segmentId: 123, labType: 'HL7' },
      accessionNumber: 'ABC123',
      label: 'Test Lab',
      abnormal: false,
      acknowledgeCount: 0,
      dateReceived: Date.now(),
      dateTime: Date.now(),
      discipline: '',
      disciplineDisplay: '',
      finalRes: '',
      finalResultsCount: '',
      firstName: '',
      healthNumber: '',
      labPatientId: '',
      lastName: '',
      lastUpdateDate: '',
      pastAcknowledgeCount: 0,
      priority: '',
      read: false,
      reportStatus: '',
      requestingClient: '',
      resultStatus: '',
      sex: '',
      status: '',
      __selected: false
    } as InboxItemWAttrs;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should show success toast if lab is filed successfully', fakeAsync(() => {
    apiInboxServiceMock.fileSelected.mockReturnValue(of({}));

    component.onClickFile(new MouseEvent('click'));
    flush();
    fixture.detectChanges();

    expect(toastrService.success).toHaveBeenCalledWith(
      'Lab Filed Successfully.',
      'Success',
      { positionClass: 'toast-bottom-center' }
    );
  }));

  it('should show error toast if lab fails to file', fakeAsync(() => {
    apiInboxServiceMock.fileSelected.mockReturnValue(
      throwError(() => new Error('File error'))
    );

    component.onClickFile(new MouseEvent('click'));
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(
      'Failed to File Lab.',
      'Error',
      { positionClass: 'toast-bottom-center' }
    );
  }));

  it('should show error toast if no demographic is linked', fakeAsync(() => {
    apiHl7ServiceMock.hasLinkedDemographic.mockReturnValue(of(false));

    component.validateModalSaveComment({ label: 'Test Label', comment: 'Comment' }, false);

    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(
      'Please relate lab to a patient.',
      'Error',
      { positionClass: 'toast-bottom-center' }
    );
  }));

  it('should show success toast if lab is acknowledged successfully', fakeAsync(() => {
    apiHl7ServiceMock.acknowledgeLab.mockReturnValue(of(true));

    component.acknowledge();
    flush();
    fixture.detectChanges();

    expect(toastrService.success).toHaveBeenCalledWith(
      'Lab Acknowledged Successfully.',
      'Success',
      { positionClass: 'toast-bottom-center' }
    );
  }));

  it('should show error toast if lab fails to be acknowledged', fakeAsync(() => {
    apiHl7ServiceMock.acknowledgeLab.mockReturnValue(
      throwError(() => new Error('Ack error'))
    );

    component.acknowledge();
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(
      'Failed to Acknowledge Lab.',
      'Error',
      { positionClass: 'toast-bottom-center' }
    );
  }));

  it('should show success toast if label is updated', fakeAsync(() => {
    apiHl7ServiceMock.updateLabel.mockReturnValue(of({}));

    component.updateLabel('New Label');
    flush();
    fixture.detectChanges();

    expect(toastrService.success).toHaveBeenCalledWith(
      'Label updated.',
      'Success',
      { positionClass: 'toast-bottom-center' }
    );
  }));

  it('should show error toast if label update fails', fakeAsync(() => {
    apiHl7ServiceMock.updateLabel.mockReturnValue(
      throwError(() => new Error('Update label error'))
    );

    component.updateLabel('New Label');
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(
      'Failed to update label.',
      'Error',
      { positionClass: 'toast-bottom-center' }
    );
  }));

  it('should show success toast if comment is added successfully', fakeAsync(() => {
    apiHl7ServiceMock.addComment.mockReturnValue(of({}));

    component.saveComment('A new comment');
    flush();
    fixture.detectChanges();

    expect(toastrService.success).toHaveBeenCalledWith(
      'Comment added.',
      'Success',
      { positionClass: 'toast-bottom-center' }
    );
  }));

  it('should show error toast if adding comment fails', fakeAsync(() => {
    apiHl7ServiceMock.addComment.mockReturnValue(
      throwError(() => new Error('Add comment error'))
    );

    component.saveComment('A new comment');
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(
      'Failed to add comment.',
      'Error',
      { positionClass: 'toast-bottom-center' }
    );
  }));
});
