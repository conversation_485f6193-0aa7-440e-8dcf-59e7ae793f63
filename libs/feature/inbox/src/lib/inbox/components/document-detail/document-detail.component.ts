import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { InboxService } from '@oscar-pro/data-access';
import { InboxId, InboxItemWAttrs } from '@oscar-pro/interfaces';
import {Observable, Subject} from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {InboxFilter} from "@oscar-pro/util";
import {InboxFacade} from "@oscar-pro/core-state";

@Component({
    selector: 'oscar-pro-document-detail',
    templateUrl: './document-detail.component.html',
    styleUrls: ['./document-detail.component.scss'],
    standalone: false
})
export class DocumentDetailComponent implements OnInit {
  @Input() hasNext: boolean;
  @Input() hasPrevious: boolean;
  @Input() inboxItem: InboxItemWAttrs;
  @Input() inboxItemIndex: number;
  @Input() isActive: boolean;
  @Input() isListView: boolean;

  @Output()
  labAcknowledged: EventEmitter<InboxId> = new EventEmitter<InboxId>();
  @Output()
  reloadLabEntry: EventEmitter<InboxId> = new EventEmitter<InboxId>();
  @Output()
  inboxItemFiled: EventEmitter<InboxId> = new EventEmitter();

  public inboxProviderNumber: string;

  private componentDestroyed$: Subject<boolean> = new Subject();
  private inboxFilter$: Observable<InboxFilter> = this.inboxFacade.inboxFilter$;

  constructor(private inboxService: InboxService,
              private inboxFacade: InboxFacade) {}

  ngOnInit(): void {
    this.inboxService.sendNextInboxItem
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((data) => {
        this.hasNext = data['hasNext'];
        this.hasPrevious = data['hasPrevious'];
        this.inboxItem = data['inboxItem'];
        this.inboxItemIndex = data['inboxItemIndex'];
      });

    this.inboxService.sendPreviousInboxItem
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((data) => {
        this.hasPrevious = data['hasPrevious'];
        this.hasNext = data['hasNext'];
        this.inboxItem = data['inboxItem'];
        this.inboxItemIndex = data['inboxItemIndex'];
      });

    this.inboxFilter$
    .pipe(takeUntil(this.componentDestroyed$))
    .subscribe((inboxFilter: InboxFilter) => {
      this.inboxProviderNumber = inboxFilter.get('providerNumber').toString();
    });
  }

  close(): void {
    this.isActive = false;
  }

  onClickClose(event: MouseEvent): void {
    event.preventDefault();
    this.close();
  }

  onEventLabAcknowledged(labId: any): void {
    this.labAcknowledged.emit(labId);
  }

  onEventNextInboxItem(currentInboxItem: any): void {
    // TODO: Pass this up as an event so that we dont have to handle this service call in here
    // this.inboxService.requestNextInboxItem(currentInboxItem);
  }

  onEventReloadLabEntry(labId: any): void {
    //TODO: See if we can remove this
    this.reloadLabEntry.emit(labId);
  }

  onEventInboxItemFiled(inboxId: any): void {
    this.inboxItemFiled.emit(inboxId);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
