import {Component, EventEmitter, Input, Output} from '@angular/core';
import {Comment, ProviderStatusItem} from "@oscar-pro/interfaces";
import {ApiPropertyService} from "@oscar-pro/data-access";
import { isNil } from 'lodash';

@Component({
    selector: 'oscar-pro-provider-status-table',
    templateUrl: './provider-status-table.component.html',
    styleUrls: ['./provider-status-table.component.scss'],
    standalone: false
})
export class ProviderStatusTableComponent {
  @Input() statusItems: Array<ProviderStatusItem>;
  @Input() statusItemsForRemovedProvider: Array<ProviderStatusItem>;
  @Output() removeProvider: EventEmitter<string> = new EventEmitter<string>();
  @Output() removeComment: EventEmitter<Comment> = new EventEmitter<Comment>();
  private showCommentsFor: number;
  expandAllRow = false;
  showRemovedProviders = false;

  constructor(private propertyService: ApiPropertyService) {
  }
  ngOnInit(): void {
    this.propertyService.getProperty("expand_all_comment_row").subscribe(
      (data) => {
        if(!isNil(data) && data.value) {
          this.expandAllRow = 'true' === data.value;
        } else {
          this.expandAllRow = false;
        }
      },
      () => console.error("unable to load expand_all_comment_row from property")
    );

    this.propertyService.getProperty("show_removed_providers").subscribe(
      (data) => {
        if(!isNil(data) && data.value) {
          this.showRemovedProviders = 'true' === data.value;
        } else {
          this.showRemovedProviders = false;
        }
      },
      () => console.error("unable to load show_removed_providers from property")
    );
  }

  displayRemovedProviders(): void {
    this.showRemovedProviders = this.showRemovedProviders !== true;
    this.propertyService.updateProperty("show_removed_providers",this.showRemovedProviders.toString()).subscribe();
  }

  showAllProviderComments(): void {
    this.expandAllRow = this.expandAllRow !== true;
    this.propertyService.updateProperty("expand_all_comment_row",this.expandAllRow.toString()).subscribe();
    this.showProviderCommentsFor(null);
  }

  onClickReadComments(itemId: number): void {
    this.showProviderCommentsFor(!isNil(itemId) ? itemId : null);
  }

  showProviderCommentsFor(itemId: number): void {
    this.showCommentsFor = this.showCommentsFor == itemId ? null : itemId;
  }

  isProviderCommentsShown(item: ProviderStatusItem): boolean {
    return (item.comments.length > 0 && this.showCommentsFor === item.id) || this.expandAllRow;
  }

  descriptiveStatus(status: string): string {
    if (status) {
      if (status === 'Acknowledged' || status === 'Signed Off') {
        return 'acknowledge-font';
      } else if (status === 'Not Acknowledged' || status === 'Not Signed Off') {
        return 'not-acknowledge-font';
      } else {
        return '';
      }
    } else {
      return '';
    }
  }

  getCommentTitle(numberOfComments: number) {
    return numberOfComments + (numberOfComments == 1 ? " comment" : " comments");
  }

  onClickRemoveProvider(
    event: MouseEvent,
    item: ProviderStatusItem
  ): void {
    event.preventDefault();
    if (item.isMrp) {
      if (confirm('Are you sure you want to remove "' + item.providerFormattedName + '" (MRP) from this document?')) {
        this.removeProvider.emit(item.providerNumber);
      }
    } else {
      this.removeProvider.emit(item.providerNumber);
    }
  }

  onClickRemoveComment(
    event: MouseEvent,
    comment: Comment
  ): void {
    event.preventDefault();
    if (comment.deleted === false) {
      comment.deleted = true;
      this.removeComment.emit(comment);
    }
  }

}

