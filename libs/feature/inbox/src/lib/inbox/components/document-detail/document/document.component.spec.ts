import { ComponentFixture, TestBed, fakeAsync, flush } from '@angular/core/testing';
import { DocumentComponent } from './document.component';
import { Pipe, PipeTransform, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AuthService, InitPropertyService } from '@oscar-pro/auth';
import {
  ApiDocumentService,
  ApiInboxService,
  ApiPropertyService,
  ApiSystemPreferenceService,
  InboxService,
} from '@oscar-pro/data-access';
import { of, throwError } from 'rxjs';
import { APP_CONFIG } from '@oscar-pro/config';
import { Store } from '@ngrx/store';
import { ToastrService } from 'ngx-toastr';
import {
  AddSpecialAttributesPipe,
  ObjectArrayFilterPipe,
  UrlParamsPipe,
  WindowRefService
} from '@oscar-pro/util';
import { InboxFacade } from '@oscar-pro/core-state';
import { ModalService, AlertService, PanelService } from '@oscar-pro/util';
import { InboxComment, Provider, ProviderDocumentRouting, Specialist } from '@oscar-pro/interfaces';
import { RouterTestingModule } from '@angular/router/testing';
import { ActivatedRoute } from '@angular/router';

@Pipe({name: 'addSpecialAttributes', standalone: false})
class MockAddSpecialAttributesPipe implements PipeTransform {
  transform(value: any, ...args: any[]): any {
    return value;
  }
}

@Pipe({name: 'objectArrayFilter', standalone: false})
class MockObjectArrayFilterPipe implements PipeTransform {
  transform(value: any, ...args: any[]): any {
    return value;
  }
}

@Pipe({name: 'urlParams', standalone: false})
class MockUrlParamsPipe implements PipeTransform {
  transform(value: any, ...args: any[]): any {
    return '';
  }
}

describe('DocumentComponent', () => {
  let component: DocumentComponent;
  let fixture: ComponentFixture<DocumentComponent>;
  let mockProviderDocumentRouting: ProviderDocumentRouting;
  let toastrService: ToastrService;
  let apiDocumentServiceMock: {
    removeProvider: jest.Mock;
    linkProvider: jest.Mock;
    file: jest.Mock;
    sendFax: jest.Mock;
    addComment: jest.Mock;
    deleteCover: jest.Mock;
    updateTitle: jest.Mock;
    updateStatus: jest.Mock;
    getPage: jest.Mock;
    getPagePreviewUrl: jest.Mock;
    saveDocument: jest.Mock;
    removeDemographic: jest.Mock;
  };
  const MOCK_PROVIDER_NO = '999999';
  const MOCK_DOCUMENT_NO = 456;
  const MOCK_INBOX_ID = '321';
  const UNLINK_SUCCESS_MESSAGE = 'Provider Removed';
  const SUCCESS_TITLE = 'Success';
  const UNLINK_WARNING_MESSAGE = 'Unable to remove provider from Provider Status Table. Please try again.';
  const WARNING_TITLE = 'Warning';
  const TOAST_POSITION = {positionClass: 'toast-bottom-center'};
  const LINK_SUCCESS_MESSAGE = 'Provider has been linked to this document.';
  const LINK_WARNING_MESSAGE = 'Provider already linked';
  const SPECIALIST_REMOVED_MESSAGE = 'Specialist Removed';
  const FAX_REMOVED_MESSAGE = 'Fax Removed';
  const ERROR_TITLE_MESSAGE = 'Failed to update title.';
  const ERROR_TITLE = 'Error';
  const PREVIEW_WARNING_MESSAGE = 'Could not load page preview url for document number ';
  const provider = {providerNo: MOCK_PROVIDER_NO} as Provider;

  beforeEach(async () => {
    const activatedRouteMock = {
      snapshot: {
        params: {id: '1'},
      },
      queryParams: of({providerNo: MOCK_PROVIDER_NO, documentNo: '456', view: 'test'}),
    };

    const appConfigMock = {
      apiPrefix: 'api',
      proContext: 'context',
    };

    apiDocumentServiceMock = {
      removeProvider: jest.fn(),
      linkProvider: jest.fn(),
      file: jest.fn(),
      sendFax: jest.fn(),
      addComment: jest.fn(),
      deleteCover: jest.fn(),
      updateTitle: jest.fn(),
      updateStatus: jest.fn(),
      getPage: jest.fn(),
      getPagePreviewUrl: jest.fn(),
      saveDocument: jest.fn(),
      removeDemographic: jest.fn(),
    };

    const apiSystemPreferenceServiceMock = {
      readBooleanPreference: jest.fn(() => of(true)),
    };

    const inboxFacadeMock = {
      getDocumentById: jest.fn(() => of({id: MOCK_DOCUMENT_NO})),
      updateDocumentStatus: jest.fn(),
      increaseItemsEditedSinceLastLoad: jest.fn(),
    };

    const toastrServiceMock = {
      success: jest.fn(),
      warning: jest.fn(),
      error: jest.fn(),
    };

    const apiInboxServiceMock = {
      getInboxItem: jest.fn(() => of()),
      markAsRead: jest.fn(() => of()),
    };

    const authServiceMock = {
      getCurrentUser: jest.fn(() => ({providerNo: '123'}))
    };

    const modalServiceMock = {
      destroy: jest.fn(),
    }

    mockProviderDocumentRouting = {
      provider: provider,
      id: 123,
      status: 'A',
      documentNo: MOCK_DOCUMENT_NO.toString(),
      labType: 'DOC',
      timestamp: '2024-12-17T00:00:00Z',
      providerNo: MOCK_PROVIDER_NO,
      deleted: false
    };

    await TestBed.configureTestingModule({
      declarations: [
        DocumentComponent,
        MockAddSpecialAttributesPipe,
        MockObjectArrayFilterPipe,
        MockUrlParamsPipe,
      ],
      imports: [HttpClientTestingModule, RouterTestingModule, BrowserAnimationsModule],
      providers: [
        {provide: APP_CONFIG, useValue: appConfigMock},
        {provide: ActivatedRoute, useValue: activatedRouteMock},
        {provide: AuthService, useValue: authServiceMock},
        {provide: ApiPropertyService, useValue: {getProperty: jest.fn(() => of(null))}},
        {provide: ApiInboxService, useValue: apiInboxServiceMock},
        {provide: ApiDocumentService, useValue: apiDocumentServiceMock},
        {provide: ApiSystemPreferenceService, useValue: apiSystemPreferenceServiceMock},
        {provide: ToastrService, useValue: toastrServiceMock},
        {
          provide: WindowRefService,
          useValue: {getNativeWindow: jest.fn(() => ({location: {reload: jest.fn()}}))},
        },
        {provide: InitPropertyService, useValue: {getAppInitProperties: jest.fn(() => of({}))}},
        {provide: InboxService, useValue: {}},
        {provide: InboxFacade, useValue: inboxFacadeMock},
        {provide: ModalService, useValue: modalServiceMock},
        {provide: AlertService, useValue: {}},
        {provide: PanelService, useValue: {}},
        {
          provide: Store,
          useValue: {
            pipe: jest.fn(() => of(null)),
            dispatch: jest.fn(),
          },
        },
        {provide: AddSpecialAttributesPipe, useClass: MockAddSpecialAttributesPipe},
        {provide: ObjectArrayFilterPipe, useClass: MockObjectArrayFilterPipe},
        {provide: UrlParamsPipe, useClass: MockUrlParamsPipe},
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    toastrService = TestBed.inject(ToastrService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DocumentComponent);
    component = fixture.componentInstance;

    component.document = {
      documentNo: MOCK_DOCUMENT_NO,
      comments: [{providerNo: MOCK_PROVIDER_NO, comment: 'test comment'}],
      numberOfPages: 2,
    } as any;
    component.inboxItem = {id: MOCK_INBOX_ID} as any;
    component.linkedProviders = [];
    component.removedProviders = [];

    jest.spyOn(component as any, 'getDocumentDetails').mockImplementation(() => of({id: MOCK_DOCUMENT_NO}));

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should show success message when a provider is successfully removed', fakeAsync(() => {
    apiDocumentServiceMock.removeProvider.mockReturnValue(of({}));
    component.linkedProviders = [{provider: {providerNo: MOCK_PROVIDER_NO}, status: ''}] as any;

    component.onEventRemoveProviderViaStatusTable(MOCK_PROVIDER_NO);
    flush();
    fixture.detectChanges();

    expect(apiDocumentServiceMock.removeProvider).toHaveBeenCalledWith(MOCK_PROVIDER_NO, MOCK_DOCUMENT_NO);
    expect(toastrService.success).toHaveBeenCalledWith(UNLINK_SUCCESS_MESSAGE, SUCCESS_TITLE, TOAST_POSITION);
  }));

  it('should show warning if provider is not found in linkedProviders', fakeAsync(() => {
    apiDocumentServiceMock.removeProvider.mockReturnValue(of({}));
    component.linkedProviders = [{provider: {providerNo: MOCK_PROVIDER_NO}, status: ''}] as any;

    component.onEventRemoveProviderViaStatusTable('777777');
    flush();
    fixture.detectChanges();

    expect(apiDocumentServiceMock.removeProvider).toHaveBeenCalledWith('777777', component.document.documentNo);
    expect(toastrService.warning).toHaveBeenCalledWith(UNLINK_WARNING_MESSAGE, WARNING_TITLE, TOAST_POSITION);
  }));

  it('should call apiDocumentService.linkProvider and display success message', fakeAsync(() => {
    const provider: Provider = {providerNo: MOCK_PROVIDER_NO} as Provider;
    const mockProviderDocumentRouting: ProviderDocumentRouting = {
      provider,
      status: 'Active',
    } as ProviderDocumentRouting;
    apiDocumentServiceMock.linkProvider.mockReturnValue(of(mockProviderDocumentRouting));

    jest.spyOn(component, 'markDocumentEdited').mockImplementation(() => undefined);
    jest.spyOn(component, 'addLinkedProviderStatus').mockImplementation(() => undefined);
    jest.spyOn(component, 'getDocumentDetails').mockImplementation(() => undefined);

    component.onEventLinkProvider(provider);
    flush();
    fixture.detectChanges();

    expect(apiDocumentServiceMock.linkProvider).toHaveBeenCalledWith(provider.providerNo, component.document.documentNo);
    expect(component.markDocumentEdited).toHaveBeenCalled();
    expect(component.addLinkedProviderStatus).toHaveBeenCalledWith(mockProviderDocumentRouting);
    expect(component.getDocumentDetails).toHaveBeenCalledWith(component.document.documentNo);

    expect(toastrService.success).toHaveBeenCalledWith(LINK_SUCCESS_MESSAGE, SUCCESS_TITLE, TOAST_POSITION);
  }));

  it('should display already archived warning message when provider already archived', fakeAsync(() => {
    const provider: Provider = {providerNo: MOCK_PROVIDER_NO} as Provider;
    component.linkedProviders = [
      {provider: {providerNo: MOCK_PROVIDER_NO}, providerNo: MOCK_PROVIDER_NO},
    ] as any;

    jest.spyOn(component, 'markDocumentEdited').mockImplementation(() => undefined);
    jest.spyOn(component, 'addLinkedProviderStatus').mockImplementation(() => undefined);
    jest.spyOn(component, 'getDocumentDetails').mockImplementation(() => undefined);

    component.onEventLinkProvider(provider);
    flush();
    fixture.detectChanges();

    expect(toastrService.warning).toHaveBeenCalledWith(LINK_WARNING_MESSAGE, WARNING_TITLE, TOAST_POSITION);
  }));

  it('should show error toast if propertyService.getProperty fails in ngOnInit', fakeAsync(() => {
    const errorMessage = 'Property Error';
    const propertyService = TestBed.inject(ApiPropertyService);
    jest.spyOn(propertyService, 'getProperty').mockReturnValue(throwError(() => new Error(errorMessage)));

    fixture.destroy();
    fixture = TestBed.createComponent(DocumentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    flush();

    expect(toastrService.error).toHaveBeenCalledWith(errorMessage, 'Error', TOAST_POSITION);
  }));

  it('should show error toast if systemPreferenceService.readBooleanPreference fails in ngOnInit', fakeAsync(() => {
    const errorMessage = 'System Preference Error';
    const systemPreferenceService = TestBed.inject(ApiSystemPreferenceService);
    jest.spyOn(systemPreferenceService, 'readBooleanPreference').mockReturnValue(throwError(() => new Error(errorMessage)));

    fixture.destroy();
    fixture = TestBed.createComponent(DocumentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    flush();

    expect(toastrService.error).toHaveBeenCalledWith(errorMessage, 'Error', TOAST_POSITION);
  }));

  it('should show error toast if sendFax fails', fakeAsync(() => {
    const errorMessage = 'Fax send failed';
    apiDocumentServiceMock.sendFax.mockReturnValue(throwError(() => new Error(errorMessage)));

    component.demographic = {demographicNumber: 1} as any;
    component.linkedFaxes = ['111-111-1111'];

    component.sendFax();
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(errorMessage, 'Error', TOAST_POSITION);
  }));

  it('should show error toast if addComment fails', fakeAsync(() => {
    const errorMessage = 'Add comment failed';
    apiDocumentServiceMock.addComment.mockReturnValue(throwError(() => new Error(errorMessage)));

    component.onEventSaveComment('New comment');
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith('Failed to add comment.', 'Error', TOAST_POSITION);
  }));

  it('should show error toast if deleteCover fails', fakeAsync(() => {
    const errorMessage = 'deleteCover error';
    apiDocumentServiceMock.deleteCover.mockReturnValue(throwError(() => new Error(errorMessage)));

    component.deleteCover(new MouseEvent('click'));
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(errorMessage, 'Error', TOAST_POSITION);
  }));

  it('should show error toast if acknowledge fails', fakeAsync(() => {
    const errorMessage = 'Acknowledge error';
    apiDocumentServiceMock.updateStatus.mockReturnValue(throwError(() => new Error(errorMessage)));

    component.acknowledge('some comment');
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(errorMessage, 'Error', TOAST_POSITION);
  }));

  it('should show error toast if loadPage fails', fakeAsync(() => {
    const errorMessage = 'Page load error';
    apiDocumentServiceMock.getPage.mockReturnValue(throwError(() => new Error(errorMessage)));

    component.loadPage();
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(errorMessage, 'Error', TOAST_POSITION);
  }));

  it('should show error toast if loadPagePreviewUrl fails', fakeAsync(() => {
    const errorMessage = 'Preview Error';
    apiDocumentServiceMock.getPagePreviewUrl.mockReturnValue(throwError(() => new Error(errorMessage)));

    component.loadPagePreviewUrl();
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(errorMessage, 'Error', TOAST_POSITION);
  }));

  it('should show warning toast if fax number already added', () => {
    const faxNumber = '123-456-7890';
    component.linkedFaxes = ['123-456-7890'];

    component.linkFaxNumber(faxNumber);

    expect(toastrService.warning).toHaveBeenCalledWith('Fax number already added', 'Warning', TOAST_POSITION);
  });

  it('should show success toast if specialist is removed', () => {
    const specialist: Specialist = {specialistId: 'sp1', name: 'Dr. Test'} as any;
    component.linkedSpecialists = [specialist];

    component.onEventRemoveSpecialist(specialist);

    expect(component.linkedSpecialists).toEqual([]);
    expect(toastrService.success).toHaveBeenCalledWith(SPECIALIST_REMOVED_MESSAGE, SUCCESS_TITLE, TOAST_POSITION);
  });

  it('should show immediate success toast for comment added when label matches docDesc and acknowledge=true', fakeAsync(() => {
    const inboxComment: InboxComment = {
      label: 'Original Title',
      comment: 'New Comment'
    };

    jest.spyOn(component, 'updateTitle').mockImplementation(() => {
      // do nothing
    });
    jest.spyOn(component, 'acknowledge').mockImplementation(() => {
      // do nothing
    });

    component.modalSaveComment(inboxComment, true);
    flush();
    fixture.detectChanges();

    expect(toastrService.success).toHaveBeenCalledWith('Comment added.', 'Success', TOAST_POSITION);
  }));

  it('should show success toast if fax is removed', () => {
    const faxNumber = '111-222-3333';
    component.linkedFaxes = [faxNumber];

    component.onEventRemoveFax(faxNumber);

    expect(component.linkedFaxes).toEqual([]);
    expect(toastrService.success).toHaveBeenCalledWith(FAX_REMOVED_MESSAGE, SUCCESS_TITLE, TOAST_POSITION);
  });

  it('should show error toast if updateTitle fails', fakeAsync(() => {
    const errorMessage = 'Update title error';
    apiDocumentServiceMock.updateTitle.mockReturnValue(throwError(() => new Error(errorMessage)));

    component.updateTitle('New Title');
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(ERROR_TITLE_MESSAGE, ERROR_TITLE, TOAST_POSITION);
  }));

  it('should show warning toast if loadPagePreviewUrl returns no body', fakeAsync(() => {
    apiDocumentServiceMock.getPagePreviewUrl.mockReturnValue(of({body: null}));

    component.loadPagePreviewUrl();
    flush();
    fixture.detectChanges();

    expect(toastrService.warning).toHaveBeenCalledWith(PREVIEW_WARNING_MESSAGE, WARNING_TITLE, TOAST_POSITION);
  }));

  it('should show error toast if the observation date is invalid', () => {
    const invalidDateText = 'invalid-date-string';

    component.onInvalidObservationDate(invalidDateText);

    expect(toastrService.error).toHaveBeenCalledWith(
      `Invalid Observation Date: ${invalidDateText}`, ERROR_TITLE, TOAST_POSITION);
  });

  it('should show success toast if updateTitle succeeds', fakeAsync(() => {
    const newTitle = 'New Document Title';
    apiDocumentServiceMock.updateTitle.mockReturnValue(of({}));
    jest.spyOn(component, 'updateTitleWithDocument').mockImplementation(() => {
      /*do nothing*/
    });
    jest.spyOn(component, 'markDocumentEdited').mockImplementation(() => {
      /*do nothing*/
    });

    component.updateTitle(newTitle);
    flush();
    fixture.detectChanges();

    expect(toastrService.success).toHaveBeenCalledWith(
      `Document title has been updated as:${newTitle}`, SUCCESS_TITLE, TOAST_POSITION);
  }));

  it('should show success toast if fileDocument succeeds', fakeAsync(() => {
    const docNo = component.document.documentNo;
    apiDocumentServiceMock.file.mockReturnValue(of({}));
    component.demographic = {demographicNumber: 123} as any;

    component.onClickFile(new MouseEvent('click'));
    flush();
    fixture.detectChanges();

    expect(apiDocumentServiceMock.file).toHaveBeenCalledWith(docNo);
    expect(toastrService.success).toHaveBeenCalledWith('Document filed.', SUCCESS_TITLE, TOAST_POSITION);
  }));

  it('should show warning toast if demographic is not linked and allowFaxingUnassignedDocuments is false when sending fax', fakeAsync(() => {
    component.demographic = null;
    component.allowFaxingUnassignedDocuments = false;
    component.linkedFaxes = [];
    component.linkedSpecialists = [];

    component.sendFax();
    flush();
    fixture.detectChanges();

    expect(toastrService.warning).toHaveBeenCalledWith('Please link a demographic to continue', WARNING_TITLE, TOAST_POSITION);
  }));

  it('should show success toast if removing provider is undone', fakeAsync(() => {
    apiDocumentServiceMock.updateStatus.mockReturnValue(of(mockProviderDocumentRouting));

    component.onEventUndoRemoveProvider(mockProviderDocumentRouting);
    flush();
    fixture.detectChanges();

    expect(toastrService.success).toHaveBeenCalledWith(
      'Provider Remove Undone', SUCCESS_TITLE, TOAST_POSITION);
  }));

  it('should show error toast if document save fails', fakeAsync(() => {
    const saveError = new Error('Save failed');
    jest.spyOn(apiDocumentServiceMock, 'saveDocument').mockReturnValue(throwError(() => saveError));

    component['apiDocumentServiceSave']();
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith('Document could not be saved.', ERROR_TITLE, TOAST_POSITION
    );
  }));

  it('should show warning toast if specialist is already added', () => {
    const specialist: Specialist = {specialistId: '1', name: 'Dr. Test'} as any;
    component.linkedSpecialists = [specialist];

    component.onEventLinkSpecialist(specialist);

    expect(toastrService.warning).toHaveBeenCalledWith(
      'Specialist already added', WARNING_TITLE, TOAST_POSITION);
  });

  it('should show success toast if specialist is added', () => {
    const specialist: Specialist = {specialistId: '1', name: 'Dr. Test'} as any;
    component.linkedSpecialists = [];

    component.onEventLinkSpecialist(specialist);
    expect(component.linkedSpecialists).toContain(specialist);
  });

  it('should show warning toast if document filing fails', fakeAsync(() => {
    apiDocumentServiceMock.file.mockReturnValue(throwError(() => new Error('Filing Failed')));
    component.demographic = {demographicNumber: 123} as any;

    component.onClickFile(new MouseEvent('click'));
    flush();
    fixture.detectChanges();

    expect(toastrService.warning).toHaveBeenCalledWith(
      'Document could not be filed.', WARNING_TITLE, TOAST_POSITION);
  }));

  it('should show error toast if removing demographic fails', fakeAsync(() => {
    const errorMessage = 'Remove demographic failed';
    apiDocumentServiceMock.removeDemographic = jest.fn().mockReturnValue(throwError(() => new Error(errorMessage)));

    component.onEventRemoveDemographic();
    flush();
    fixture.detectChanges();

    expect(toastrService.error).toHaveBeenCalledWith(
      errorMessage,
      ERROR_TITLE,
      TOAST_POSITION
    );
  }));

});
