import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ApiThirdPartyAppService } from "@oscar-pro/data-access";
import { ThirdPartyApplication } from '@oscar-pro/interfaces';
import { forkJoin } from 'rxjs';

@Component({
    selector: 'oscar-pro-third-party-app-config',
    templateUrl: './app-config.component.html',
    styleUrls: ['./app-config.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class AppConfigComponent implements OnInit {

  public allApps: Array<ThirdPartyApplication> = new Array<ThirdPartyApplication>();
  public existingApps: Array<ThirdPartyApplication> = new Array<ThirdPartyApplication>();

  constructor(private thirdPartyAppService: ApiThirdPartyAppService) { }

  ngOnInit() {
    this.loadApps();
  }

  enableApp(app: ThirdPartyApplication) {
    this.thirdPartyAppService.enableApp(app).subscribe(
      data => {
        if (data)
          this.allApps = this.allApps.filter(obj => obj !== app);
        this.existingApps.push(data);
      },
      error => console.error(error)
    );
  }

  disableApp(app: ThirdPartyApplication) {
    this.thirdPartyAppService.disableApp(app).subscribe(
      success => {
        this.loadApps();
      },
      error => console.error(error)
    );
  }

  loadApps() {
    forkJoin({
      allApps: this.thirdPartyAppService.getAllApps(),
      enabledApps: this.thirdPartyAppService.getEnabledApps()
    }).subscribe(({allApps, enabledApps}) => {
      enabledApps.forEach(function (existingApp) {
        allApps = allApps.filter(app => app.name !== existingApp.name);
      });
      this.existingApps = enabledApps;
      this.allApps = allApps;
    });
  }
}
