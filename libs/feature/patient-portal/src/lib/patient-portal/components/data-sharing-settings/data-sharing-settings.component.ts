import {Component, Input, OnInit} from '@angular/core';
import {DataSharingService} from "@oscar-pro/data-access";
import {DataSharingSettings, GracePeriodUnitEnum, OptionValue, ResourceTypeEnum} from "@oscar-pro/interfaces";
import {ObjectArrayFilterPipe} from "@oscar-pro/util";
import {PatientPortalFacade} from "@oscar-pro/core-state";
import { isNil } from 'lodash';

type menuItem = 'hs' | 'gp' | 'avm';

@Component({
    selector: 'oscar-pro-data-sharing-settings',
    templateUrl: './data-sharing-settings.component.html',
    styleUrls: ['./data-sharing-settings.component.scss'],
    standalone: false
})
export class DataSharingSettingsComponent implements OnInit {

  @Input() demographicNumber: number | undefined;
  @Input() isOrganizationSettings = false;

  public selectedItem: menuItem = 'hs';
  autoSyncAll = false;

  originalSettings: DataSharingSettings = {
    id: undefined,
    allHistory: true,
    gracePeriod: 1,
    gracePeriodUnit: GracePeriodUnitEnum.Weeks,
    isOrganizationSettings: true,
    createdDateTime: undefined,
    updatedDateTime: undefined,
    demographicNumber:undefined,
    resourceSyncSettings: [
      {
        autoSync: false,
        resourceType: ResourceTypeEnum.Allergy,
        id: null,
        createdDateTime: null,
        updatedDateTime: null,
      },
      {
        autoSync: false,
        resourceType: ResourceTypeEnum.Condition,
        id: null,
        createdDateTime: null,
        updatedDateTime: null,
      },
      {
        autoSync: false,
        resourceType: ResourceTypeEnum.LabResults,
        id: null,
        createdDateTime: null,
        updatedDateTime: null,
      },
      {
        autoSync: false,
        resourceType: ResourceTypeEnum.Measurements,
        id: null,
        createdDateTime: null,
        updatedDateTime: null,
      },
      {
        autoSync: false,
        resourceType: ResourceTypeEnum.Medication,
        id: null,
        createdDateTime: null,
        updatedDateTime: null,
      },
      {
        autoSync: false,
        resourceType: ResourceTypeEnum.Preventions,
        id: null,
        createdDateTime: null,
        updatedDateTime: null,
      },
      {
        autoSync: false,
        resourceType: ResourceTypeEnum.Procedure,
        id: null,
        createdDateTime: null,
        updatedDateTime: null,
      }
    ]
  };

  settings: DataSharingSettings = this.originalSettings;

  public gracePeriodOptions: Array<OptionValue>;
  public gracePeriodUnitOptions: Array<OptionValue>;

  constructor(
    private dataSharingService: DataSharingService,
    private objectArrayFilterPipe: ObjectArrayFilterPipe,
    private patientPortalFacade: PatientPortalFacade
  ) {
    this.gracePeriodUnitOptions = [
      {value: GracePeriodUnitEnum.Hours, option: 'Hours'},
      {value: GracePeriodUnitEnum.Days, option: 'Days'},
      {value: GracePeriodUnitEnum.Weeks, option: 'Weeks'}
    ];
    this.gracePeriodOptions = [
      {value: '1', option: '1'},
      {value: '2', option: '2'},
      {value: '3', option: '3'},
      {value: '4', option: '4'},
      {value: '5', option: '5'},
      {value: '6', option: '6'},
      {value: '6', option: '7'},
      {value: '8', option: '8'},
      {value: '9', option: '9'},
      {value: '10', option: '10'}
    ];
  }

  ngOnInit(): void {
    if (!isNil(this.demographicNumber) && !this.isOrganizationSettings) {
      this.dataSharingService.getPatientSettings(this.demographicNumber).subscribe(
        data => {
          if (!isNil(data)) {
            this.processData(data);
          }
        },
        () => this.handleError('Error getting demographic settings.')
      );
    } else if (this.isOrganizationSettings) {
      this.dataSharingService.getOrganizationSettings().subscribe(
        (data: DataSharingSettings) => {
          if (data) {
            this.processData(data);
          }
        },() => this.handleError('Error getting organization settings.')
      );
    }
  }

  save(): void {
    if (this.isOrganizationSettings) {
      return this.saveOrganizationSettings();
    } else if (this.demographicNumber && this.demographicNumber > 0) {
      this.savePatientSettings();
    }
  }

  saveOrganizationSettings(): void {
    this.dataSharingService.saveOrganizationSettings(this.settings).subscribe(
      data => {
        if (data) {
          this.settings = data;
          this.displaySuccessfulSaveMessage();
        }
      },
      (error) => console.error(error)
    );
  }

  savePatientSettings(): void {
    this.dataSharingService.savePatientSettings(this.demographicNumber, this.settings).subscribe(
      data => {
        if (data) {
          this.settings = data;
          this.displaySuccessfulSaveMessage();
        }
      },
      (error) => console.error(error)
    );
  }

  cancel(): void {
    this.settings = structuredClone(this.originalSettings);
  }

  onEventUpdateGracePeriodUnit(selectedType: any): void {
    this.settings.gracePeriodUnit = <GracePeriodUnitEnum> selectedType;
    console.log(this.settings.gracePeriodUnit);
  }

  onEventUpdateGracePeriod(selectedType: any): void {
    this.settings.gracePeriod = + this.objectArrayFilterPipe.transform(this.gracePeriodOptions, {value: selectedType})[0]['value'];
    console.log(this.settings.gracePeriod);
  }

  syncAll() {
    if (this.autoSyncAll) {
      this.settings.resourceSyncSettings.forEach(function (resourceSettings) {
        resourceSettings.autoSync = true;
      });
    }
  }

  onClickHistoricalSync(event: MouseEvent): void {
    event.preventDefault();
    this.selectedItem = 'hs';
  }

  onClickGracePeriodSync(event: MouseEvent): void {
    event.preventDefault();
    this.selectedItem = 'gp';
  }

  onClickAutoVsManual(event: MouseEvent): void {
    event.preventDefault();
    this.selectedItem = 'avm';
  }

  processData(data: DataSharingSettings) {
    this.processTimeStamps(data);
    this.settings = data;
    this.originalSettings = structuredClone(data);
    this.autoSyncAll = data.allHistory;
  }

  processTimeStamps(data: DataSharingSettings): void {
    data.createdDateTime = new Date(data.createdDateTime as Date);
    data.updatedDateTime = new Date(data.updatedDateTime as Date);
  }

  /* UI MESSAGES */
  displaySuccessfulSaveMessage() {
    this.handleSuccess('Saved data sharing settings.');
  }

  handleError(message: string, tooltip?: string) {
    console.error(message);
    this.patientPortalFacade.addErrorMessage({
      val: message,
      help: tooltip ? tooltip : '',
    });
    return;
  }

  handleSuccess(message: string) {
    this.patientPortalFacade.addSuccessMessage(message);
    return;
  }
}
