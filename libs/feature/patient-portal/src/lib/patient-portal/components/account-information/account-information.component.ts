import { Component, Input, OnInit } from '@angular/core';
import {FormControl, Validators} from '@angular/forms';
import { faCheckCircle, faExclamationCircle, faTimesCircle } from '@fortawesome/free-solid-svg-icons';

import {ApiDemographicService, ApiPatientPortalService} from "@oscar-pro/data-access";
import {Demographic, OptionValue} from "@oscar-pro/interfaces";
import {ObjectArrayFilterPipe} from "@oscar-pro/util";
import {ActivatedRoute} from "@angular/router";
import {PatientPortalFacade} from "@oscar-pro/core-state";

type registrationStatus = 'registered' | 'pending' | 'terminated' | 'notSent';

@Component({
    selector: 'oscar-pro-patient-portal-account-information',
    templateUrl: './account-information.component.html',
    styleUrls: ['./account-information.component.scss'],
    standalone: false
})

export class AccountInformationComponent implements OnInit {

  @Input() demographicNumber: number | undefined;

  public faCheckCircle = faCheckCircle;
  public faExclamationCircle = faExclamationCircle;
  public faTimesCircle = faTimesCircle;

  constructor(
    private activatedRoute: ActivatedRoute,
    private apiDemographicService: ApiDemographicService,
    private apiPatientPortalService: ApiPatientPortalService,
    private objectArrayFilterPipe: ObjectArrayFilterPipe,
    private patientPortalFacade: PatientPortalFacade
  ) { }

  public status: registrationStatus = 'pending';

  public hideEditEmail = true;
  public hideEditPhone = true;
  public hideTerminateModal = true;

  public demographic: Demographic | undefined;
  public demographicCellPhone = new FormControl('',[ ]);
    // string | undefined;
  public demographicOriginalCellPhone: string | undefined;
  public demographicEmail = new FormControl('',[ Validators.email ]);

  public terminationReason: string | undefined;
  public otherTerminationReason: string | undefined;

  public portalRegisteredDateTime: Date | undefined;
  public showOtherInput = false;

  public terminationReasonOptions: Array<OptionValue> = [
    { value: 'Wrong patient', option: 'Wrong patient' },
    { value: 'No longer a patient', option: 'No longer a patient' },
    { value: 'Patient requested', option: 'Patient requested' },
    { value: 'Enrolled in error', option: 'Enrolled in error' },
    { value: 'Other', option: 'Other' }
  ];

  ngOnInit() {
    const patientPortalExtensions: string[] =
      [
        'demo_cell',
        'patient_portal_requester_name',
        'patient_portal_last_request_date_time',
        'patient_portal_registered_date_time',
        'patient_portal_termination_reason',
        'patient_portal_termination_date_time',
        'patient_portal_terminator_name'
      ];
    const routeParams = this.activatedRoute.snapshot.paramMap;
    this.demographicNumber = Number(routeParams.get('id'));

      this.apiDemographicService.getDemographic(this.demographicNumber).subscribe(
        data => {
          if (data) {
            this.demographic = data;
            this.demographicEmail.setValue(data.email.trim());
          }
        },
        error => this.handleError(error)
      );

      this.apiDemographicService.getDemographicExtensions(this.demographicNumber, patientPortalExtensions).subscribe(
        results => {
          if (results) {
            this.demographicOriginalCellPhone = results['demo_cell'];
            this.demographicCellPhone.setValue(results['demo_cell']);
            const portalRequestDateTime: Date = results['patient_portal_last_request_date_time'];
            this.portalRegisteredDateTime = results['patient_portal_registered_date_time'];
            const terminationDateTime: Date = results['patient_portal_termination_date_time'];
            this.terminationReason = results['patient_portal_termination_reason'];
            if (terminationDateTime) {
              this.status = 'terminated';
            } else if (portalRequestDateTime && !this.portalRegisteredDateTime) {
              this.status = 'pending';
            } else if (portalRequestDateTime && this.portalRegisteredDateTime) {
              this.status = 'registered'
            } else if (!portalRequestDateTime) {
              this.status = 'notSent';
            }
          }
        },
        () => this.handleError('Error getting demographic information.')
      );
  }

  /* EMAIL */
  editEmail() {
    this.hideEditEmail = false;
  }

  saveEmail() {
    if (!this.demographicEmail.value || this.demographicEmail.value.trim().length <= 0) {
     this.handleError('Email is required.');
     this.resetEmail();
     return;
    }
    this.demographicEmail.setValue(this.demographicEmail.value.trim());
    if (this.demographicNumber) {
      this.apiDemographicService.saveDemographicEmail(this.demographicNumber, this.demographicEmail.value).subscribe(
        () => {
          this.hideEditEmail = true;
          this.handleSuccess('Email saved.');
        },
        () => this.handleError('Error saving email.')
      );
    }
  }

  cancelEditEmail() {
    this.resetEmail();
    this.hideEditEmail = true;
  }

  resetEmail() {
    if (this.demographic) {
      this.demographicEmail.setValue(this.demographic.email.trim());
    }
  }

  /* PHONE */
  editPhone() {
    this.hideEditPhone = false;
  }

  savePhone() {
    if (!this.demographicCellPhone.value || this.demographicCellPhone.value.length <= 0) {
      this.handleError('Phone is required.');
      this.resetPhone();
      return;
    }
    if (this.demographicNumber) {
      this.apiDemographicService.saveDemographicCellphone(this.demographicNumber, this.demographicCellPhone.value).subscribe(
        () => {
          this.hideEditPhone = true;
          this.demographicOriginalCellPhone = this.demographicCellPhone.value || undefined;
          this.handleSuccess('Phone saved.');
        },
        () => this.handleError('Error saving phone.')
        );
    }
  }

  cancelEditPhone() {
    this.resetPhone();
    this.hideEditPhone = true;
  }

  resetPhone() {
    this.demographicCellPhone.setValue(this.demographicOriginalCellPhone || null);
  }

  /* TERMINATION */
  openTerminateModal() {
    this.hideTerminateModal = false;
  }

  terminate() {
    if (!this.terminationReason || this.terminationReason.length <= 0) {
      this.handleTerminationReasonRequiredError();
    }
    let reason = this.terminationReason;
    if (this.terminationReason == 'Other') {
      if (!this.otherTerminationReason || this.otherTerminationReason.length <= 0) {
        this.handleTerminationReasonRequiredError();
      }
      reason = this.terminationReason + ": " + this.otherTerminationReason;
    }
    if (this.demographicNumber) {
      this.apiPatientPortalService.deletePatientPortalMapping(this.demographicNumber, reason as string).subscribe(
        () => {
          this.status = 'terminated';
          this.hideTerminateModal = true;
          this.showOtherInput = false;
          this.terminationReason = reason;
          this.handleSuccess('Registration terminated.');
        },
        () => this.handleError('Error terminating registration.')
      );
    }
  }

  cancelTermination() {
    this.hideTerminateModal = true;
    this.terminationReason = "";
    this.otherTerminationReason = "";
    this.showOtherInput = false;
  }

  onEventUpdateTerminationReason(selectedTerminationReason: string): void {
    this.terminationReason = this.objectArrayFilterPipe.transform(this.terminationReasonOptions, { value: selectedTerminationReason })[0]['value'];
    this.showOtherInput = selectedTerminationReason == 'Other';
  }

  /* INVITATION */
  sendInvite() {
    if (!this.demographicEmail.value || this.demographicEmail.value.length <= 0) {
      this.handleError('Email is required.');
      return;
    }
    if (!this.demographicCellPhone.value || this.demographicCellPhone.value.length <= 0) {
      this.handleError('Phone is required.');
      return;
    }
    if (this.demographic) {
      const mapping = {
        firstName: this.demographic.firstName,
        lastName: this.demographic.lastName,
        email: this.demographicEmail.value,
        phone: this.demographicCellPhone.value,
        demographicNumber: this.demographicNumber
      };
      this.apiPatientPortalService.register(mapping).subscribe(
        () => {
          this.status = 'pending';
          this.handleSuccess('Invite sent!');
        },
        error => { this.handleError(error.error ? error.error : error, '') }
      );
    }
  }

  /* UI MESSAGES */
  handleError(message: string, tooltip?: string) {
    console.error(message);
    this.patientPortalFacade.addErrorMessage({
      val: message,
      help: tooltip ? tooltip : '',
    });
    return;
  }

  handleSuccess(message: string) {
    this.patientPortalFacade.addSuccessMessage(message);
    return;
  }

  handleTerminationReasonRequiredError() {
    console.error("Termination reason is required");
    this.handleError('Termination reason is required.');
    return;
  }
}
