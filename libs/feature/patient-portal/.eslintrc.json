{"extends": ["../../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "extends": ["plugin:@nx/angular", "plugin:@angular-eslint/template/process-inline-templates"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "oscar<PERSON>ro", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "oscar-pro", "style": "kebab-case"}], "@angular-eslint/prefer-standalone": "off"}}, {"files": ["*.html"], "extends": ["plugin:@nx/angular-template"], "rules": {}}]}