import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
} from '@angular/core';
import { IconDefinition } from '@fortawesome/free-solid-svg-icons';
import { Size } from '@oscar-pro/interfaces';
import { Link } from '@oscar-pro/interfaces';
import { Option } from '@oscar-pro/interfaces';

@Component({
    selector: 'oscar-pro-flyout',
    templateUrl: './flyout.component.html',
    styleUrls: ['./flyout.component.scss'],
    standalone: false
})
export class FlyoutComponent {
  @Input() icon: IconDefinition | undefined;
  @Input() size: Size = 'default';
  @Input() label = '';
  @Input() links: Link[] = [];
  @Input() options: Option[] = [];
  @Input() align: 'default' | 'right' = 'default';

  @Output()
  public linkClicked = new EventEmitter<string>();

  @Output()
  public optionClicked = new EventEmitter<Option>();

  public isOpen = false;

  constructor(private _ref: ElementRef) {}

  @HostListener('document:click', ['$event', '$event.target'])
  onDocumentClicked(event: MouseEvent, targetElement: HTMLElement) {
    if (
      targetElement &&
      document.body.contains(targetElement) &&
      !this._ref.nativeElement.contains(targetElement)
    ) {
      this.isOpen = false;
    }
  }

  onOptionChange(title: string, id: string, value: boolean) {
    this.optionClicked.emit({
      title,
      id,
      value: !value
    });
    this.isOpen = false;
  }
}
