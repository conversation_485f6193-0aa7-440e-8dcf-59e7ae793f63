import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { CloseContainerService } from '@oscar-pro/util';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { isNil } from 'lodash';
import { faCalendar } from '@fortawesome/free-solid-svg-icons';

@Component({
    selector: 'oscar-pro-datepicker',
    templateUrl: './datepicker.component.html',
    styleUrls: ['./datepicker.component.scss'],
    standalone: false
})
export class DatepickerComponent implements OnChanges, OnInit {
  @Input() allowDateFuture = true;
  @Input() allowDatePast = true;
  @Input() allowDateText = false;
  @Input() title!: string;
  @Input() defaultDate!: Date;
  @Input() defaultText = 'YYYY-MM-DD';
  @Input() size = 'medium';
  @Input() text!: string;
  @Input() filterStartDate = false;
  @Input() filterEndDate = false;
  @Input() isWellUiEnabled = false;

  @Output() changeDate: EventEmitter<any> = new EventEmitter<any>();
  @Output() invalidDate: EventEmitter<any> = new EventEmitter<any>();

  @ViewChild('txtDate') dateInputEl!: ElementRef;

  localeString = 'en';
  navDate: any;
  weekDaysHeaderArr: Array<string> = [];
  gridArr: Array<any> = [];
  selectedDate: moment.Moment | undefined;

  public isFocused = false;
  public isOpen = false;
  public isUserKeyInput = false;
  public readonly faCalendar = faCalendar;

  private debounceObserver: Subject<string> = new Subject<string>();

  constructor(
    private eRef: ElementRef,
    private closeContainerService: CloseContainerService
  ) {
    // initialize debounce Subject observer/emitter for changed input events
    this.debounceObserver
      .pipe(debounceTime(200))
      .subscribe((value) => this.dateFromInput(value));
  }

  ngOnInit() {
    moment.locale(this.localeString);
    this.navDate = moment();
    try {
      const momentFromDateStr = moment(this.defaultText, 'YYYY-MM-DD');
      if (momentFromDateStr.isValid()) {
        this.navDate = momentFromDateStr;
        this.selectedDate = moment(this.navDate);
      }
    } catch (e) {
      console.error(e);
    } finally {
      this.makeHeader();
      this.makeGrid();
      this.text = this.defaultText;
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['defaultText']?.currentValue) {
      this.text = this.defaultText;
    }
  }

  @HostListener('document:click', ['$event'])
  clickout(event) {
    if (!this.eRef.nativeElement.contains(event.target)) {
      if (this.isOpen) {
        this.isOpen = false;
        this.isFocused = false;
      }
    }
  }

  @HostListener('document:keydown.escape', ['$event']) onKeydownHandler(
    event: KeyboardEvent
  ) {
    if (this.isOpen) {
      this.isOpen = false;
      this.isFocused = false;
      this.closeContainerService.closeable(true);
      event.stopImmediatePropagation();
    }
  }

  changeNavMonth(num: number) {
    if (this.canChangeNavMonth(num)) {
      this.navDate.add(num, 'month');
      this.makeGrid();
    }
  }

  canChangeNavMonth(num: number) {
    return (
      (this.allowDateFuture && num === 1) || (this.allowDatePast && num === -1)
    );
  }

  changeNavYear(num: number) {
    if (this.canChangeNavYear(num)) {
      this.navDate.add(num, 'year');
      this.makeGrid();
    }
  }

  canChangeNavYear(num: number) {
    return (
      (this.allowDateFuture && num === 1) || (this.allowDatePast && num === -1)
    );
  }

  dateFromInput(dateStr: string) {
    const testDate = moment(dateStr, 'YYYY-MM-DD');
    if (testDate.isValid()) {
      this.selectedDate = testDate;
      this.navDate = moment(this.selectedDate);
      this.makeGrid();
    }
  }

  dateFromNum(num: number, referenceDate: any): any {
    const returnDate = moment(referenceDate);
    return returnDate.date(num);
  }

  isAvailable(num: number): boolean {
    const dateToCheck = this.dateFromNum(num, this.navDate);
    if (dateToCheck.isBefore(moment(), 'day')) {
      return false;
    } else {
      return true;
    }
  }

  isDaySelected(day: any): boolean {
    const d = this.dateFromNum(day.value, this.navDate);
    return (
      !isNil(this.selectedDate) &&
      !isNil(d) &&
      moment(this.selectedDate).isSame(d)
    );
  }

  makeHeader() {
    const weekDaysArr: Array<number> = [0, 1, 2, 3, 4, 5, 6];
    weekDaysArr.forEach((day) =>
      this.weekDaysHeaderArr.push(moment().weekday(day).format('ddd'))
    );
  }

  makeGrid() {
    this.gridArr = [];

    const firstDayDate = moment(this.navDate).startOf('month');
    const initialEmptyCells = firstDayDate.weekday();
    const lastDayDate = moment(this.navDate).endOf('month');
    const lastEmptyCells = 6 - lastDayDate.weekday();
    const daysInMonth = this.navDate.daysInMonth();
    const arrayLength = initialEmptyCells + lastEmptyCells + daysInMonth;

    for (let i = 0; i < arrayLength; i++) {
      const obj: any = {};
      if (i < initialEmptyCells || i > initialEmptyCells + daysInMonth - 1) {
        obj.value = 0;
        obj.available = false;
      } else {
        obj.value = i - initialEmptyCells + 1;
        obj.available = true;
      }
      this.gridArr.push(obj);
    }
  }

  onBlur() {
    this.closeContainerService.closeable(true);
    if (this.isUserKeyInput) {
      // attempt to select typed date
      this.setSelectedDate(this.navDate, true);
    }
    this.isFocused = false;
  }

  onClickClear(event: MouseEvent) {
    event.preventDefault();
    this.selectedDate = undefined;
    this.text = '';
    this.emitFilterDate();
    this.isOpen = false;
    this.isFocused = false;
    this.closeContainerService.closeable(true);
  }

  onClickDatepicker(event: Event) {
    event.preventDefault();
    this.isOpen = true;
    this.closeContainerService.closeable(false);
  }

  onFocus() {
    this.isFocused = true;
    this.closeContainerService.closeable(false);
  }

  onDatePickerClick(e: FocusEvent) {
    // prevent input blur event from triggering save attempt
    e.preventDefault();
  }

  onInput(): void {
    this.isOpen = true;
    this.isFocused = true;
    this.debounceObserver.next(this.dateInputEl.nativeElement.value);
    this.closeContainerService.closeable(false);
  }

  onKeydown(e: KeyboardEvent): void {
    const key = e.keyCode;
    this.isUserKeyInput = true;
    if (this.isOpen && this.isFocused && key === 13) {
      this.setSelectedDate(this.navDate, this.allowDateText);
      e.stopPropagation();
      this.closeContainerService.closeable(true);
    }
  }

  selectDay(e: Event, day: any) {
    e.stopPropagation();
    if (day.available) {
      this.isUserKeyInput = false;
      this.setSelectedDate(this.dateFromNum(day.value, this.navDate));
    }
  }

  setSelectedDate(date, textEntry = false) {
    if (
      !textEntry ||
      (textEntry &&
        this.text.match(
          '^(\\d{4})-?([0][1-9]|1[0-2])?-?([0-2][1-9]|[1-3]0|3[01])?$'
        ))
    ) {
      let answer = true;
      if (new Date().getFullYear() < date.toDate().getFullYear()) {
        answer = confirm(
          'Are you sure you want to set the date to the year ' +
            date.toDate().getFullYear()
        );
      }
      if (answer) {
        this.selectedDate = date;
        this.text = moment(this.selectedDate).format('YYYY-MM-DD');
        this.emitFilterDate();
        this.isOpen = false;
        this.isFocused = false;
      }
    } else {
      this.invalidDate.emit(this.text);
    }
    this.isUserKeyInput = false;
  }

  emitFilterDate() {
    if (this.filterStartDate) {
      this.changeDate.emit({ startDate: this.text });
    } else if (this.filterEndDate) {
      this.changeDate.emit({ endDate: this.text });
    } else {
      this.changeDate.emit(this.selectedDate);
    }
  }
}
