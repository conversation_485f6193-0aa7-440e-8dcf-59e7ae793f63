import {
  Component,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FilePond } from 'filepond';
import { EnvironmentVarsPipe, ModalService } from '@oscar-pro/util';

@Component({
    selector: 'oscar-pro-file-uploader',
    templateUrl: './file-uploader.component.html',
    styleUrls: ['./file-uploader.component.scss'],
    standalone: false
})
export class FileUploaderComponent implements OnInit {
  @Input() pondConfig: any;
  @Input() showSeparator = false;

  pondOptions = {
    class: 'my-filepond',
    allowDrop: true,
    multiple: false,
    labelIdle:
      '<div style="width:100%;height:100%;" class="droparea">'
      + '<img alt="" src="'
      + this.environmentVars.transform('staticAssetsPrefix', true)
      + 'public/images/icons/cloud-upload.png" class="upload-image">'
      + '    <div class="droparea-label-dropping"><p class="instructions">Release mouse button to add file.</span></p></div><div class="droparea-label"><p class="instructions">Drag document here or <span class="highlight">browse</span></p>'
      + '    </div>',
    imagePreviewHeight: 80,
    imageResizeMode: 'cover' as const,
    imageResizeTargetWidth: 100,
    imageResizeTargetHeight: 80,
    fileMetadataObject: {
      poster:
        'https://www.acquia.com/sites/default/files/media/image/2021-10/Icon%20-%20Whitepaper%20-%20Default_0.png',
    },
    filePosterHeight: 80,
    iconRemove:
      '<svg aria-hidden="true" width="11" height="12" viewBox="-1 -1 13 15" fill="none" xmlns="http://www.w3.org/2000/svg">\n'
      + '  <path fill-rule="evenodd" clip-rule="evenodd" d="M9.17362 3.71875H1.7356C1.59867 3.71875 1.48767 3.82975 1.48767 3.96668V10.9088C1.48767 11.4566 1.93169 11.9006 2.47941 11.9006H8.42982C8.97754 11.9006 9.42156 11.4566 9.42156 10.9088V3.96668C9.42156 3.82975 9.31055 3.71875 9.17362 3.71875ZM4.5868 10.1642C4.5868 10.3696 4.42029 10.5361 4.2149 10.5361C4.0095 10.5361 3.843 10.3696 3.843 10.1642V5.70136C3.843 5.49596 4.0095 5.32946 4.2149 5.32946C4.42029 5.32946 4.5868 5.49596 4.5868 5.70136V10.1642ZM6.69434 10.5361C6.89974 10.5361 7.06624 10.3696 7.06624 10.1642V5.70136C7.06624 5.49596 6.89974 5.32946 6.69434 5.32946C6.48895 5.32946 6.32244 5.49596 6.32244 5.70136V10.1642C6.32244 10.3696 6.48895 10.5361 6.69434 10.5361Z" fill="#667B96"/>\n'
      + '  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.05785 1.98347H10.4132C10.6871 1.98347 10.9091 2.20548 10.9091 2.47934C10.9091 2.7532 10.6871 2.97521 10.4132 2.97521H0.495868C0.222008 2.97521 0 2.7532 0 2.47934C0 2.20548 0.222008 1.98347 0.495868 1.98347H2.85124C2.88412 1.98347 2.91565 1.97041 2.9389 1.94716C2.96215 1.92391 2.97521 1.89238 2.97521 1.8595V1.23967C2.97521 0.555019 3.53023 0 4.21488 0H6.69422C7.37887 0 7.93388 0.555019 7.93388 1.23967V1.8595C7.93388 1.92797 7.98939 1.98347 8.05785 1.98347ZM3.96697 1.23932V1.85916C3.96697 1.92762 4.02248 1.98312 4.09094 1.98312H6.81821C6.88668 1.98312 6.94218 1.92762 6.94218 1.85916V1.23932C6.94218 1.10239 6.83118 0.991388 6.69425 0.991388H4.21491C4.07798 0.991388 3.96697 1.10239 3.96697 1.23932Z" fill="#667B96"/>\n'
      + '    </svg>',
    labelTapToRetry: '',
    instantUpload: false,
    allowMultiple: true,
    maxParallelUploads: 20,
    allowRevert: false,
    server: {
      timeout: 7000,
      revert: null,
      restore: null,
      process: () => {
        console.log('File upload process needs to be implemented');
      },
    },
  };

  @ViewChild('myPond')
  myPond!: FilePond;

  public allowUpload = false;
  public allowComplete = false;
  public uploadComplete = false;
  public fileUploadError: string | null = null;

  constructor(
    private modalService: ModalService,
    private environmentVars: EnvironmentVarsPipe
  ) {}

  ngOnInit(): void {
    this.pondOptions = Object.assign({}, this.pondOptions, this.pondConfig);
  }

  checkForRemainingUploadFiles(files) {
    const remainingFiles = files.filter((item) => item.status !== 5);
    if (remainingFiles.length > 0) {
      this.modalService.disableClose(
        'You have files that have not completed uploading. Are you sure you want to close?'
      );
      this.allowUpload = true;
      this.allowComplete = false;
      this.uploadComplete = false;
    } else {
      this.modalService.enableClose();
      this.allowUpload = false;
      this.allowComplete = true;
      this.uploadComplete = true;
    }
  }

  onPondProcessFile() {
    this.checkForRemainingUploadFiles(this.myPond.getFiles());
  }

  onPondUpdateFiles(files: any) {
    if (files.items.length > 0) {
      this.checkForRemainingUploadFiles(files.items);
    } else {
      this.allowUpload = false;
      this.allowComplete = false;
    }
  }

  onClickSubmit(event: MouseEvent): void {
    event.preventDefault();
    const filePondFiles = this.myPond.getFiles();
    for (const pondFile of filePondFiles) {
      // Pond file status 5 is already completed uploading
      if (pondFile.status !== 5) {
        this.myPond.processFile(pondFile.id);
      }
    }
  }

  onClickComplete() {
    this.modalService.close();
  }
}
