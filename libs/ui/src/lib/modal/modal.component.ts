import {
  Component,
  ComponentFactoryResolver,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  AfterViewInit,
  Output,
  ViewChild,
  ViewContainerRef,
  ChangeDetectorRef,
} from '@angular/core';
import { Size } from '@oscar-pro/interfaces';
import {
  AlertService,
  CloseContainerService,
  ModalService,
  WindowScrollingService,
} from '@oscar-pro/util';

@Component({
    selector: 'oscar-pro-modal',
    templateUrl: './modal.component.html',
    styleUrls: ['./modal.component.scss'],
    standalone: false
})
export class ModalComponent implements OnInit, AfterViewInit {
  @Input() isActive!: boolean;
  @Input() componentType!: any;
  @Input() size: Size = 'default';
  @Input() componentConfig: any = {};
  @Output() refreshParent: EventEmitter<any> = new EventEmitter<any>();
  @Output() dataChange: EventEmitter<any> = new EventEmitter<any>();

  @ViewChild('modalContent', { read: ViewContainerRef }) div!: ViewContainerRef;

  constructor(
    protected modalService: ModalService,
    protected windowScrollingService: WindowScrollingService,
    protected el: ElementRef,
    protected cfr: ComponentFactoryResolver,
    protected cdRef: ChangeDetectorRef,
    protected alertService: AlertService,
    protected closeContainerService: CloseContainerService
  ) {}

  ngOnInit(): void {
    this.windowScrollingService.disable();
    this.modalService.closeModal.subscribe(() => {
      this.close(null);
    });
  }

  ngAfterViewInit(): void {
    const compFactory = this.cfr.resolveComponentFactory(this.componentType);
    const componentViewRef = this.div.createComponent(compFactory);
    this.applyChildComponentConfig(componentViewRef);
    this.cdRef.detectChanges();
  }

  @HostListener('document:keydown.escape', ['$event']) onKeydownHandler(
    event: KeyboardEvent
  ) {
    this.close(event);
    this.closeContainerService.closeable(true);
  }

  applyChildComponentConfig(childComponentViewRef: any) {
    for (const key of Object.keys(this.componentConfig.inputs ?? {})) {
      childComponentViewRef.instance[key] = this.componentConfig.inputs[key];
    }
    for (const key of Object.keys(this.componentConfig.outputs ?? {})) {
      childComponentViewRef.instance[key].subscribe((data: any) => {
        const outputValue = this.componentConfig.outputs[key];

        // if a function is passed as the output value, call it with the data
        if(outputValue instanceof Function) {
          outputValue(data);
        }
        this.dataChange.emit({ type: this.componentConfig.outputs[key], data });
      });
    }
  }

  close($event): void {
    this.refreshParent.emit();
    this.windowScrollingService.enable();
    this.modalService.destroy();
  }

  removeModal() {
    const modal = this.el.nativeElement.querySelector('.modal');
    modal.classList.remove('is-active');
    setTimeout(() => {
      this.close(null);
    }, 250);
  }

  onClickClose(event: MouseEvent): void {
    event.preventDefault();
    if (this.modalService.isCloseable()) {
      this.removeModal();
    } else {
      this.alertService
        .showWarningMessage(
          'Warning',
          this.modalService.getCloseErrorMessage(),
          true
        )
        .then((result) => {
          if (result.isConfirmed) {
            this.modalService.enableClose();
            this.removeModal();
          }
        });
    }
  }

  dynamicClickClose(): void {
    const modal = this.el.nativeElement.querySelector('.modal');
    modal.classList.remove('is-active');
    setTimeout(() => {
      this.close(null);
    }, 250);
  }
}
