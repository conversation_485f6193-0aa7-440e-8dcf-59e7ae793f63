import {Component, EventEmitter, Input, Output} from '@angular/core';

@Component({
    selector: 'oscar-pro-checkbox',
    templateUrl: './checkbox.component.html',
    styleUrls: ['./checkbox.component.scss'],
    standalone: false
})
export class CheckboxComponent {
  @Input() placeholder!: string;
  @Input() isDisabled = false;
  @Input() isChecked = false;
  @Output() inputClicked: EventEmitter<Event> = new EventEmitter();

  onCheckboxChange(event: Event): void {
    event.preventDefault();
    this.isChecked = !this.isChecked;
    this.inputClicked.emit(event);
  }
}
