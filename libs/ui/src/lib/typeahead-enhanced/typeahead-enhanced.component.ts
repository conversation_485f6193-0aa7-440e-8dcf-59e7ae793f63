import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { Keys } from '@oscar-pro/data-access';
import { EnhancedOption, EnhancedOptions } from '@oscar-pro/interfaces';
import { CloseContainerService } from '@oscar-pro/util';
const VALID_INPUT_KEYS = [Keys.TAB, Keys.ENTER, Keys.UP, Keys.DOWN];
@Component({
    selector: 'oscar-pro-typeahead-enhanced',
    templateUrl: './typeahead-enhanced.component.html',
    styleUrls: ['./typeahead-enhanced.component.scss'],
    standalone: false
})
export class TypeaheadEnhancedComponent
  implements OnInit, OnChanges, AfterViewInit
{
  @Input() options!: Array<EnhancedOptions>;
  @Input() validOptions!: Array<EnhancedOptions>;
  @Input() isCondensed = false;
  @Input() label = '';
  @Input() placeholder = '';
  @Input() defaultValue = '';
  @Input() isEnabled = true;
  @Input() input: {
    id: null | string;
    name: null | string;
    val: null | string;
  } = { id: null, name: null, val: null };
  @Input() debounceTimeInMils = 500;
  @Input() clearOnSelection = false;
  @Input() maxLength!: number;
  @Input() showGroups = false;
  @Input() multiSelect = false;
  @Input() emptySelectionDefault: EnhancedOption | null = null;
  @Input() forceOptionSelection = true;
  @Input() revertTextOnDeselect = false;
  @Output() blurEvent: EventEmitter<any> = new EventEmitter<any>();
  @Output() selectEvent: EventEmitter<string> = new EventEmitter<string>();
  @Output() typeaheadEvent: EventEmitter<string> = new EventEmitter<string>();
  @Output() inputEvent: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild('keyword') keywordInputEl!: ElementRef;
  @ViewChild('dm') dropDownListEl!: ElementRef;
  public inputResults = '';
  public inputActive = false;
  public arrowKeyId: null | number = null;
  public selectedOption!: string | Array<string>;
  public plateClasses = '';
  public arrowKeyIndex = 0;
  public flattenedOptions: any[] = [];
  private clearInput = true;
  private defaultPlaceholder = '';
  public hoverActive = false;
  private debounceObserver: Subject<string> = new Subject<string>();
  constructor(
    private eRef: ElementRef,
    private closeContainerService: CloseContainerService
  ) {}
  ngOnInit(): void {
    this.debounceObserver
      .pipe(debounceTime(this.debounceTimeInMils))
      .subscribe((value) => this.typeaheadEvent.emit(value));
    this.selectedOption = this.defaultValue;
    this.defaultPlaceholder = this.placeholder;
  }
  ngAfterViewInit() {
    this.inputActive = false;
    if (this.input.id) {
      this.keywordInputEl.nativeElement.id = this.input.id;
    }
    if (this.input.name) {
      this.keywordInputEl.nativeElement.name = this.input.name;
    }
  }
  ngOnChanges(changes: SimpleChanges) {
    if (
      changes['defaultValue']
      && changes['defaultValue'].currentValue?.length > 0
    ) {
      this.selectedOption = this.defaultValue;
    }

    // Note: flattening options to be able to track keyboard index
    this.flattenedOptions = [];
    if (changes['options']?.currentValue) {
      changes['options'].currentValue.forEach((obj) => {
        Object.keys(obj).forEach((key) => {
          if (key === 'options') {
            this.flattenedOptions = [...this.flattenedOptions, ...obj[key]];
          }
        });
      });

      // if dropdown is displaying dropdown values close options, otherwise set closable true
      this.closeContainerService.closeable(
        changes['options'].currentValue.length == 0
      );
    }
  }

  @HostListener('document:click', ['$event'])
  clickout(event) {
    if (!this.eRef.nativeElement.contains(event.target)) {
      if (this.inputActive || this.isOpen()) {
        this.arrowKeyIndex = 0;
        this.inputActive = false;
        if (this.forceOptionSelection && !this.revertTextOnDeselect) {
          this.validateInput();
        }
        this.closeDropdown();
        this.closeContainerService.closeable(true);
        this.revertText();
      }
      this.clearInput = true;
    }
  }
  @HostListener('document:keydown.escape', ['$event']) onKeydownHandler(
    event: KeyboardEvent
  ) {
    if (this.isOpen()) {
      this.closeDropdown();
      this.closeContainerService.closeable(true);
      this.revertText();
      event.stopImmediatePropagation();
    }
  }

  revertText(): void {
    if (
      this.revertTextOnDeselect
      && this.keywordInputEl.nativeElement.value !== this.selectedOption
    ) {
      this.keywordInputEl.nativeElement.value = this.selectedOption;
    }
  }

  validateInput() {
    const inputVal = this.keywordInputEl.nativeElement.value;
    // If there's no input, stop
    if (inputVal == '') {
      this.plateClasses = '';
      return;
    }
    if (this.getInputValue(this.validOptions, inputVal)) {
      this.plateClasses = '';
    } else {
      this.plateClasses = 'warning';
    }
  }
  isOpen() {
    return this.options && this.options.length > 0;
  }
  onBlur(): void {
    this.arrowKeyId = null;
    this.closeDropdown();
    if (this.blurEvent) {
      this.blurEvent.emit();
    }
  }
  onFocus(): void {
    this.inputActive = true;
    this.debounceObserver.next(this.keywordInputEl.nativeElement.value);
  }
  onInput(): void {
    this.inputActive = true;
    this.debounceObserver.next(this.keywordInputEl.nativeElement.value);
    this.inputEvent.emit();
  }
  onSelect(event: Event, selectedObj: string | number): void {
    event.preventDefault();
    this.arrowKeyId = null;
    this.updateSelected(selectedObj);
    this.closeContainerService.closeable(true);
  }
  onMouseOver(event: MouseEvent) {
    this.hoverActive = true;
  }
  onMouseOut(event: MouseEvent) {
    this.hoverActive = false;
  }
  getInputValue(options, optionId) {
    for (const option of options) {
      const result = option.options.find(function (o) {
        return (
          o.id.toString().toLowerCase() === optionId.toString().toLowerCase()
        );
      });
      if (result) {
        return result['label'];
      }
    }
  }
  updateSelected(selectedVal: string | number): void {
    let selectedOption = '';
    if (!this.clearOnSelection) {
      selectedOption = this.getInputValue(this.options, selectedVal);
      if (this.maxLength && selectedOption.length > this.maxLength) {
        selectedOption = selectedOption.substring(0, this.maxLength).trim();
      }
    }
    if (!this.multiSelect) {
      this.selectedOption = selectedOption;
      this.keywordInputEl.nativeElement.value = selectedOption;
      this.options = new Array<any>();
      this.selectEvent.emit(selectedOption);
      this.clearInput = false;
    } else {
      if (!Array.isArray(this.selectedOption)) {
        this.selectedOption = [selectedOption];
      } else {
        if (!this.selectedOption.includes(selectedOption)) {
          this.selectedOption.push(selectedOption);
        } else {
          const index = this.selectedOption.indexOf(selectedOption);
          if (index > -1) {
            this.selectedOption.splice(index, 1);
          }
          if (this.selectedOption.length === 0) {
            this.selectedOption = '';
          }
        }
      }
      if (Array.isArray(this.selectedOption)) {
        this.inputResults = '';
        this.placeholder = '';
        for (const option of this.selectedOption) {
          this.inputResults += '<span class="pill">' + option + '</span>';
        }
      }
    }
    this.arrowKeyId = null;
    this.inputActive = false;
  }
  onClear() {
    if (this.emptySelectionDefault) {
      this.selectedOption = this.emptySelectionDefault.label;
      this.keywordInputEl.nativeElement.value = '';
      this.selectEvent.emit(this.emptySelectionDefault.label as string);
    } else {
      this.selectedOption = '';
      this.keywordInputEl.nativeElement.value = '';
      this.clearInput = true;
      this.inputResults = '';
      this.placeholder = this.defaultPlaceholder;
      this.selectEvent.emit('');
    }
    this.arrowKeyId = null;
  }
  clearMultiSelect() {
    this.selectedOption = [];
    this.keywordInputEl.nativeElement.value = '';
    this.arrowKeyId = null;
    this.inputResults = '';
    this.placeholder = this.defaultPlaceholder;
  }
  onKeydown(e: KeyboardEvent): void {
    this.hoverActive = false;
    const key = e.keyCode;
    if (this.isOpen() && VALID_INPUT_KEYS.includes(key)) {
      e.preventDefault();
      switch (key) {
        case Keys.TAB:
        case Keys.ENTER: {
          if (
            this.flattenedOptions.length > 0
            && this.flattenedOptions[this.arrowKeyIndex]
          ) {
            this.updateSelected(this.flattenedOptions[this.arrowKeyIndex].id);
            this.arrowKeyIndex = 0;
          } else {
            this.arrowKeyIndex = 0;
          }
          break;
        }
        case Keys.UP: {
          this.arrowKeyIndex === 0
            ? (this.arrowKeyIndex = this.flattenedOptions.length - 1)
            : this.arrowKeyIndex--;
          break;
        }
        case Keys.DOWN: {
          this.arrowKeyIndex === this.flattenedOptions.length - 1
            ? (this.arrowKeyIndex = 0)
            : this.arrowKeyIndex++;
          break;
        }
      }
      this.dropDownListEl.nativeElement.scrollTop =
        this.arrowKeyIndex < 4
          ? 0
          : (this.arrowKeyIndex - 4)
            * (this.dropDownListEl.nativeElement.scrollHeight
              / this.options.length);
    }
  }

  private closeDropdown() {
    this.inputActive = false;
    this.options = new Array<any>();
  }
}
