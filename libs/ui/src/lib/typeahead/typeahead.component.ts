import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  AfterViewInit,
  OnInit,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { OptionValue } from '@oscar-pro/interfaces';
import { CloseContainerService, ObjectArrayFilterPipe } from '@oscar-pro/util';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { Keys } from '@oscar-pro/data-access';

const VALID_INPUT_KEYS = [Keys.TAB, Keys.ENTER, Keys.UP, Keys.DOWN];
const ACCEPT_KEYS = [Keys.TAB, Keys.ENTER];

@Component({
    selector: 'oscar-pro-typeahead',
    templateUrl: './typeahead.component.html',
    styleUrls: ['./typeahead.component.scss'],
    standalone: false
})
export class TypeaheadComponent implements AfterViewInit, OnInit, OnChanges {
  @Input() classes = '';
  @Input() plateClasses = '';
  @Input() clearOnSelection = false;
  @Input() defaultVal = '';
  @Input() input: any = { id: null, name: null, val: null };
  @Input() label!: string;
  @Input() maxLength!: number;
  @Input() options!: Array<OptionValue>;
  @Input() placeholder = '';
  @Input() debounceTimeInMils = 100;
  @Input() isEnabled = true;
  @Input() outline = false;
  @Input() allowBlankInput = false;

  @Output() blurEvent: EventEmitter<string> = new EventEmitter<string>();
  @Output() selectEvent: EventEmitter<string> = new EventEmitter<string>();
  @Output() typeaheadEvent: EventEmitter<string> = new EventEmitter<string>();

  @ViewChild('keyword') keywordInputEl!: ElementRef;
  @ViewChild('dm') dropDownListEl!: ElementRef;

  public arrowKeyIndex = 0;
  public activeOrFocused = false;
  private debounceObserver: Subject<string> = new Subject<string>();
  private clearInput = true;
  private selectedOption!: string;

  constructor(
    private objectArrayFilterPipe: ObjectArrayFilterPipe,
    private eRef: ElementRef,
    private closeContainerService: CloseContainerService
  ) {}

  ngOnInit(): void {
    // initialize debounce Subject observer/emitter for changed input events
    this.debounceObserver
      .pipe(debounceTime(this.debounceTimeInMils))
      .subscribe((value) => this.typeaheadEvent.emit(value));

    this.selectedOption = this.defaultVal;
  }

  ngAfterViewInit() {
    if (this.input.id) {
      this.keywordInputEl.nativeElement.id = this.input.id;
    }

    if (this.input.name) {
      this.keywordInputEl.nativeElement.name = this.input.name;
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['options']?.currentValue?.length > 0) {
      this.closeContainerService.closeable(false);
    }
  }

  @HostListener('document:click', ['$event'])
  clickout(event) {
    if (!this.eRef.nativeElement.contains(event.target)) {
      if (this.activeOrFocused || this.isOpen()) {
        this.activeOrFocused = false;
        this.validateInput();
        this.options = new Array<OptionValue>();
        this.closeContainerService.closeable(true);
        this.blurEvent.emit(this.keywordInputEl.nativeElement.value);
      }
      this.clearInput = true;
    }
  }

  @HostListener('document:keydown.escape', ['$event']) onKeydownHandler(
    event: KeyboardEvent
  ) {
    if (this.activeOrFocused) {
      this.activeOrFocused = false;
      this.closeContainerService.closeable(true);
      this.options = new Array<OptionValue>();
      event.stopImmediatePropagation();
    }
  }
  @HostListener('document:keydown', ['$event'])
  handleKeydown(event: KeyboardEvent): void {
    if (!this.activeOrFocused) {
      return;
    }
    this.onKeydown(event);
  }

  validateInput() {
    const inputVal = this.keywordInputEl.nativeElement.value;

    // If there's no change, stop
    if (inputVal == this.selectedOption) {
      return;
    }

    if (
      this.validCodes().includes(inputVal)
      || this.isAllowBlankInput(inputVal)
    ) {
      this.updateSelected(inputVal);
      this.plateClasses = '';
    } else {
      this.plateClasses = 'warning';
    }
  }

  validCodes(): Array<string> {
    return this.options?.map((x) => x.value) || [];
  }

  isOpen(): boolean {
    return this.options && this.options.length > 0;
  }

  onInput(): void {
    this.activeOrFocused = true;
    this.debounceObserver.next(this.keywordInputEl.nativeElement.value);
  }

  onKeydown(e: KeyboardEvent): void {
    if (!this.activeOrFocused) {
      return;
    }

    const key = e.keyCode;
    if (this.isOpen() && VALID_INPUT_KEYS.includes(key)) {
      e.preventDefault();

      switch (key) {
        case Keys.TAB:
        case Keys.ENTER: {
          if (this.options.length > 0 && this.options[this.arrowKeyIndex]) {
            this.updateSelected(this.options[this.arrowKeyIndex].value);
            this.arrowKeyIndex = 0;
          } else {
            this.validateInput();
          }
          this.closeContainerService.closeable(true);
          break;
        }
        case Keys.UP: {
          this.arrowKeyIndex === 0
            ? (this.arrowKeyIndex = this.options.length - 1)
            : this.arrowKeyIndex--;
          break;
        }
        case Keys.DOWN: {
          this.arrowKeyIndex === this.options.length - 1
            ? (this.arrowKeyIndex = 0)
            : this.arrowKeyIndex++;
          break;
        }
      }
      //updates scrollbar location based on selected index
      this.dropDownListEl.nativeElement.scrollTop =
        this.arrowKeyIndex < 4
          ? 0
          : (this.arrowKeyIndex - 4)
            * (this.dropDownListEl.nativeElement.scrollHeight
              / this.options.length);
    } else if (!this.isOpen() && ACCEPT_KEYS.includes(key)) {
      const inputVal = this.keywordInputEl.nativeElement.value;
      if (inputVal === '') {
        this.plateClasses = '';
        return;
      }
      this.validateInput();
    }
  }

  onSelect(event: MouseEvent, selectedObj: string): void {
    event.preventDefault();
    this.arrowKeyIndex = 0;
    this.updateSelected(selectedObj);
  }

  updateSelected(selectedVal: string): void {
    if (!this.activeOrFocused) {
      return;
    }
    this.activeOrFocused = false;
    let selectedOption = '';
    if (!this.clearOnSelection && !this.isAllowBlankInput(selectedVal)) {
      selectedOption = this.objectArrayFilterPipe.transform(this.options, {
        value: selectedVal,
      })[0]?.option || '';

      if (this.maxLength && selectedOption.length > this.maxLength) {
        selectedOption = selectedOption.substring(0, this.maxLength).trim();
      }
    }
    this.selectedOption = selectedOption;
    this.keywordInputEl.nativeElement.value = selectedOption;
    this.options = new Array<OptionValue>();
    this.selectEvent.emit(selectedVal);
    this.clearInput = false;
    this.plateClasses = '';
    this.closeContainerService.closeable(true);
  }

  private isAllowBlankInput(inputVal: string) {
    return this.allowBlankInput && inputVal === '';
  }
}
