import { Component, EventEmitter, Input, OnChanges, Output } from "@angular/core";
import { FieldOption } from "@oscar-pro/interfaces";
import { ChipEvent } from "../chip/chip-event";

@Component({
    selector: 'oscar-pro-chip-list',
    templateUrl: './chip-list.component.html',
    styleUrls: ['./chip-list.component.scss'],
    standalone: false
})
export class ChipListComponent implements OnChanges {
  @Input() chips: Array<FieldOption> = [];
  @Input() enableSelectAll = false;
  @Input() selectAllOption!: FieldOption;
  @Output() updateChipsSelected: EventEmitter<Array<FieldOption>> =
    new EventEmitter<Array<FieldOption>>();

  ngOnChanges() {
    if (this.enableSelectAll && this.selectAllOption.checked) {
      this.chips.forEach((chip) => (chip.checked = true));
    }
  }

  onSelectChip(event: ChipEvent) {
    const selectedChip = this.chips.find((chip) =>
      chip.option === event.option
    );
    if (selectedChip) {
      if ((selectedChip.checked && !selectedChip.selected) || event.isDoubleClick) {
        return this.onSelectChipExclusively(event);
      } else {
        selectedChip.checked = !selectedChip.checked;
        selectedChip.selected = selectedChip.checked;
      }
    }

    const allTypesSelected = this.chips.every(
      (chip) => chip.checked && chip.selected
    );
    this.selectAllOption.checked = allTypesSelected;
    this.selectAllOption.selected = allTypesSelected;
    if (allTypesSelected) {
      this.setAllChips(this.selectAllOption.checked, false)
    }

    this.updateChipsSelected.emit(this.chips);
  }

  onSelectChipExclusively(event: ChipEvent) {
    this.setAllChips(false, false);
    const selectedChip = this.chips.find((chip) =>
      chip.option === event.option
    );
    if (selectedChip) {
      selectedChip.checked = true;
      selectedChip.selected = true;
    }
    this.selectAllOption.checked = false;
    this.selectAllOption.selected = false;

    this.updateChipsSelected.emit(this.chips);
  }

  onSelectAllChips() {
    this.selectAllOption.checked = !this.selectAllOption.checked;
    this.setAllChips(this.selectAllOption.checked, false);
    this.updateChipsSelected.emit(this.chips);
  }

  setAllChips(checked: boolean, selected: boolean) {
    this.chips.forEach((chip) => {
      chip.checked = checked
      chip.selected = selected
    });
  }
}
