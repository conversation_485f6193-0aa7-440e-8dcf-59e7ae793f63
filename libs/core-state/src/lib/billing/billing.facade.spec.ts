import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { BillingFacade } from './billing.facade';
import * as BillingActions from './billing.actions';
import {
  Bill,
  BillingForm, BillingItem,
  BillingService as IBillingService,
  Provider,
  ProviderPreferences, Specialist
} from '@oscar-pro/interfaces';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  AlertService,
  ObjectArrayFilterPipe,
  UrlParamsPipe,
  WindowRefService,
} from '@oscar-pro/util';
import {
  ApiBillingService,
  ApiDemographicService,
  ApiDiagnosticService,
  BillingService,
} from '@oscar-pro/data-access';
import { AuthService } from '@oscar-pro/auth';
import { Store } from '@ngrx/store';
import { APP_CONFIG } from '@oscar-pro/config';
import { of } from 'rxjs';
import { RouterTestingModule } from '@angular/router/testing';
import { EventEmitter } from '@angular/core';
import spyOn = jest.spyOn;
import { Router } from '@angular/router';

describe('BillingFacade', () => {
  let facade: BillingFacade;

  const oscarContext = 'oscar';
  const proContext = 'kaiemr';
  const apiPrefix = 'api';

  // finalize the test bed
  // changes to the test bed should be done before calling this function
  const finalizeTestBed = () => {
    facade = TestBed.inject(BillingFacade);
  };

  beforeEach(() => {
    // set up common test mocks
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        UrlParamsPipe,
        {
          provide: APP_CONFIG,
          useValue: {
            proContext: proContext,
            apiPrefix: apiPrefix,
            oscarContext: oscarContext,
          },
        },
        {
          provide: ObjectArrayFilterPipe,
          useValue: {
            transform: () => {
              return [
                {
                  code: 'CD1',
                  description: 'mock description',
                },
              ];
            },
          },
        },
        {
          provide: WindowRefService,
          useValue: {
            getNativeWindow: () => {
              return {
                location: {
                  href: '',
                  reload: jest.fn(),
                },
                resizeTo: jest.fn(),
                close: jest.fn(),
                opener: {
                  document: {
                    location: {
                      reload: jest.fn(),
                    }
                  }
                }
              };
            },
          },
        },
        {
          provide: ApiBillingService,
          useValue: {
            saveInvoice: jest.fn(() => of({id: 1})),
            getBill: jest.fn(),
            getBillItems: jest.fn(),
            getBillExtensions: jest.fn(),
            getBillProviders: jest.fn(),
            getBillDemographics: jest.fn(),
            getUpdatedBillingProviderDetails: jest.fn(),
          },
        },
        {
          provide: ApiDemographicService,
          useValue: {
            getDemographicReferralDoctor: jest.fn(() => of({})),
          },
        },
        {
          provide: ApiDiagnosticService,
          useValue: {
            search: jest.fn(),
            findByDemographic: jest.fn(),
          },
        },
        {
          provide: AuthService,
          useValue: {},
        },
        {
          provide: BillingService,
          useValue: {
            searchDxCodesResults: new EventEmitter<any>(),
            isThirdParty: jest.fn(() => false),
            createExtension: jest.fn(),
            calculateItemFee: jest.fn(() => ''),
            updateBillTotals: jest.fn(() => ({newBillingExtensions: {}, newBill: {}})),
            getUpdatedBillingProviderDetails: jest.fn(() => ({})),
            getForm: jest.fn(() => ({})),
            isBillValid: jest.fn(() => ({
              error: [],
              codeErrors: [],
              success: '',
            })),
            searchServicesResults: {
              emit: jest.fn(),
            },
            createWarnings: jest.fn(),
          },
        },
        {
          provide: AlertService,
          useValue: {
            confirmMessage: jest.fn(() => Promise.resolve(true)),
          },
        },
        {
          provide: Store,
          useValue: {
            pipe: () => of({}),
            dispatch: jest.fn(),
          },
        },
        {
          provide: Router,
          useValue: {
            navigateByUrl: jest.fn(() => Promise.resolve(true)),
          },
        },
      ],
    });
  });

  it('should be created', () => {
    finalizeTestBed();
    expect(facade).toBeTruthy();
  });

  it('should call processAddBillItem', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of({billingItems: []})),
        dispatch: jest.fn(),
      },
    });
    finalizeTestBed();
    spyOn(facade, 'processAddBillItem');
    const service = {} as IBillingService;
    facade.createBillItem(service, '1', '50');
    expect(facade.processAddBillItem).toHaveBeenCalled();
  });

  it('should update Billing Dx', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of([{billingItems: [{}]}])),
        dispatch: jest.fn(),
      },
    });
    const store = TestBed.inject(Store);
    spyOn(store, 'dispatch');
    finalizeTestBed();
    expect(store.dispatch).toHaveBeenCalledWith(BillingActions.updateBillingDx({dx: ''}));
  });

  it('should call updateBillingExtensions', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() =>
          of({
            thirdParty: true,
            payee: {},
            provider: {},
            providerNo: {},
          })
        ),
        dispatch: jest.fn(),
      },
    });
    finalizeTestBed();
    spyOn(facade, 'updateBillingExtensions');
    const provider = {} as Provider;
    facade.setBillingProvider(provider);
    expect(facade.updateBillingExtensions).toHaveBeenCalled();
  });

  it('should updateForm', () => {
    const store = TestBed.inject(Store);
    spyOn(store, 'dispatch');
    finalizeTestBed();
    facade.updateBillType([], {} as ProviderPreferences);
    expect(store.dispatch).toHaveBeenCalledWith(BillingActions.updateBillingForm({form: {} as BillingForm}));
  });

  it('should createAndSaveInvoice', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of({
          billingItems: [{}],
          urlParams: {},
        })),
        dispatch: jest.fn(),
      },
    });
    const apiBillingService = TestBed.inject(ApiBillingService);
    spyOn(apiBillingService, 'saveInvoice');
    finalizeTestBed();
    facade.saveBill(false, false, false, false, {} as Specialist, false, {}, false);
    expect(apiBillingService.saveInvoice).toHaveBeenCalled();
  });

  it('should createAndSaveInvoice after confirmation', fakeAsync(() => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of({
          billingItems: [{}],
          urlParams: {},
        })),
        dispatch: jest.fn(),
      },
    });
    TestBed.overrideProvider(BillingService, {
      useValue: {
        searchDxCodesResults: new EventEmitter<any>(),
        isThirdParty: jest.fn(() => false),
        createExtension: jest.fn(),
        calculateItemFee: jest.fn(() => ''),
        updateBillTotals: jest.fn(() => ({newBillingExtensions: {}, newBill: {}})),
        getUpdatedBillingProviderDetails: jest.fn(() => ({})),
        getForm: jest.fn(() => ({})),
        isBillValid: jest.fn(() => ({
          error: [],
          codeErrors: [],
          success: '',
        })),
        searchServicesResults: {
          emit: jest.fn(),
        },
        createWarnings: jest.fn(() => 'Continue?'),
      },
    });
    const apiBillingService = TestBed.inject(ApiBillingService);
    spyOn(apiBillingService, 'saveInvoice');
    finalizeTestBed();
    facade.saveBill(false, false, false, false, {} as Specialist, false, {}, false);
    tick();
    expect(apiBillingService.saveInvoice).toHaveBeenCalled();
  }));

  it('should navigate to next appointment', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of({
          billingItems: [{}],
          urlParams: {},
          demographicNo: 1,
          appointmentNo: 1,
        })),
        dispatch: jest.fn(),
      },
    });
    const apiBillingService = TestBed.inject(ApiBillingService);
    const router = TestBed.inject(Router);
    spyOn(apiBillingService, 'saveInvoice');
    spyOn(router, 'navigateByUrl');
    finalizeTestBed();
    facade.saveBill(false, true, false, false, {} as Specialist, false, {}, false);
    expect(apiBillingService.saveInvoice).toHaveBeenCalled();
    expect(router.navigateByUrl).toHaveBeenCalledWith(`/billing?demographicNo=1&appointmentNo=1`);
  });

  it('should add a maximum error messages on updateBillingItem', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of({billingItems: []})),
        dispatch: jest.fn(),
      },
    });
    const store = TestBed.inject(Store);
    spyOn(store, 'dispatch');
    finalizeTestBed();
    facade.updateBillingItem({service: {}, serviceCount: 100}, 0, false);
    expect(store.dispatch).toHaveBeenCalledWith(BillingActions.addErrorMessage({
      error: {
        val: 'Service codes can only be billed a maximum of 99 times.',
        help: '',
      }
    }));
  });

  it('should add a numeric error messages on updateBillingItem', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of({billingItems: []})),
        dispatch: jest.fn(),
      },
    });
    const store = TestBed.inject(Store);
    spyOn(store, 'dispatch');
    finalizeTestBed();
    facade.updateBillingItem({service: {}, serviceCount: 'a'}, 0, false);
    expect(store.dispatch).toHaveBeenCalledWith(BillingActions.addErrorMessage({
      error: {
        val: 'Service code quantity must be numeric.',
        help: '',
      }
    }));
  });

  it('given existing bill item when getPremiumCodeCount then return bill item count', () => {
    finalizeTestBed();
    (facade as any)._bill.billingItems = [{
      serviceCode: 'E003C',
      serviceCount: '37'
    } as BillingItem];
    expect(facade.getPremiumCodeCount()).toEqual('37');
  });

  it('given no existing bill item when getPremiumCodeCount then return 1', () => {
    finalizeTestBed();
    (facade as any)._bill.billingItems = [];
    expect(facade.getPremiumCodeCount()).toEqual('1');
  });

  it('given more than one esisting bill item when getPremiumCodeCount' +
    ' then return 1', () => {
    finalizeTestBed();
    (facade as any)._bill.billingItems = [{
      serviceCode: 'E003C',
      serviceCount: '37'
    } as BillingItem, {
      serviceCode: 'E003C',
      serviceCount: '37'
    } as BillingItem];
    expect(facade.getPremiumCodeCount()).toEqual('1');
  });

  it('should handle null billing items gracefully in getPremiumCodeCount', () => {
    finalizeTestBed();
    (facade as any)._bill.billingItems = null;
    expect(facade.getPremiumCodeCount()).toEqual('1');
  });

  it('should add a maximum error messages on processAddBillItem', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of({billingItems: []})),
        dispatch: jest.fn(),
      },
    });
    const store = TestBed.inject(Store);
    spyOn(store, 'dispatch');
    finalizeTestBed();
    const billingItem = {} as BillingItem;
    facade.processAddBillItem({
      ...billingItem,
      service: {} as IBillingService,
      serviceCount: '100'
    });
    expect(store.dispatch).toHaveBeenCalledWith(BillingActions.addErrorMessage({
      error: {
        val: 'Cannot bill service code more than 99 times.',
        help: '',
      }
    }));
  });

  it('should add a numeric error messages on processAddBillItem', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of({billingItems: []})),
        dispatch: jest.fn(),
      },
    });
    const store = TestBed.inject(Store);
    spyOn(store, 'dispatch');
    finalizeTestBed();
    const billingItem = {} as BillingItem;
    facade.processAddBillItem({...billingItem, service: {} as IBillingService, serviceCount: 'a'});
    expect(store.dispatch).toHaveBeenCalledWith(BillingActions.addErrorMessage({
      error: {
        val: 'Service code quantity must be numeric.',
        help: '',
      }
    }));
  });

  it('should add an Invalid error messages on processAddBillItem', () => {
    TestBed.overrideProvider(Store, {
      useValue: {
        pipe: jest.fn(() => of({
          billingItems: [],
          error: [{}],
        })),
        dispatch: jest.fn(),
      },
    });
    const store = TestBed.inject(Store);
    spyOn(store, 'dispatch');
    finalizeTestBed();
    const billingItem = {} as BillingItem;
    facade.processAddBillItem({...billingItem, service: {serviceCode: 'a'} as IBillingService});
    expect(store.dispatch).toHaveBeenCalledWith(BillingActions.addErrorMessage({
      error: {
        val: 'Invalid service code length.',
        help: '',
      }
    }));
  });

  describe('createAndSaveInvoice', () => {
    beforeEach(() => {
      TestBed.overrideProvider(Store, {
        useValue: {
          pipe: jest.fn(() => of({
            urlParams: {
              demographicNo: 1,
              sign: false,
            }
          })),
          dispatch: jest.fn(),
        },
      });
      finalizeTestBed();
    });

    it('should loadBillingHistoryCount', () => {
      jest.spyOn(facade, 'loadBillingHistoryCount');
      facade.createAndSaveInvoice(false, false, false, false, {}, false);
      expect(facade.loadBillingHistoryCount).toHaveBeenCalledWith({
        demographicNo: 1,
        daysBack: '',
        serviceCode: '',
        page: 0
      });
    });
  });

  describe('when filter results are empty', () => {
    let spy;
    beforeEach(() => {
      TestBed.overrideProvider(ObjectArrayFilterPipe, {
        useValue: {
          transform: (): [] => {
            return [];
          },
        },
      });
      finalizeTestBed();

      spy = jest.spyOn<BillingFacade, 'dispatch'>(facade, 'dispatch');
    });

    it('setFacilityNumber should do nothing', () => {
      facade.setFacilityNumber('mockFacilityNumber');
      expect(spy).toHaveBeenCalledTimes(0);
    });

    it('setLocation should do nothing', () => {
      facade.setLocation('mockLocation');
      expect(spy).toHaveBeenCalledTimes(0);
    });

    it('setPayProgram should do nothing', () => {
      facade.setPayProgram('mockProgram');
      expect(spy).toHaveBeenCalledTimes(0);
    });

    it('setVisitType should do nothing', () => {
      facade.setVisitType('mockVisitType');
      expect(spy).toHaveBeenCalledTimes(0);
    });

    it('setRmaVisitType should do nothing', () => {
      facade.setRmaVisitType('mockRmaVisitType', []);
      expect(spy).toHaveBeenCalledTimes(0);
    });
  });

  describe('when valid filter results are found', () => {
    let spy;
    beforeEach(() => {
      finalizeTestBed();

      spy = jest.spyOn<BillingFacade, 'dispatch'>(facade, 'dispatch');
    });

    it('should dispatch an action on setFacilityNumber', () => {
      const mockFacilityNumber = 'mockFacilityNumber';
      const newBill = {
        facilityNum: mockFacilityNumber,
      } as Bill;
      const expectedAction = BillingActions.updateBill({newBill});

      facade.setFacilityNumber(mockFacilityNumber);

      expect(spy).toHaveBeenCalledWith(expectedAction);
    });

    it('should dispatch an action on setLocation', () => {
      const mockLocation = 'mockLocation';
      const newBill = {
        location: mockLocation,
      } as Bill;
      const expectedAction = BillingActions.updateBill({newBill});

      facade.setLocation(mockLocation);

      expect(spy).toHaveBeenCalledWith(expectedAction);
    });

    it('should dispatch an action on setPayProgram', () => {
      const mockProgram = 'mockProgram';
      const newBill = {
        payProgram: mockProgram,
      } as Bill;
      const thirdParty = false;
      const expectedAction1 = BillingActions.updateBill({newBill});
      const expectedAction2 = BillingActions.setThirdParty({thirdParty});

      facade.setPayProgram(mockProgram);

      expect(spy).toHaveBeenCalledWith(expectedAction1);
      expect(spy).toHaveBeenCalledWith(expectedAction2);
    });

    it('should dispatch an action on setVisitType', () => {
      const mockVisitType = 'mockVisitType';
      const newBill = {
        visitType: mockVisitType,
      } as Bill;
      const expectedAction = BillingActions.updateBill({newBill});

      facade.setVisitType(mockVisitType);

      expect(spy).toHaveBeenCalledWith(expectedAction);
    });

    it('should dispatch an action on setVisitType', () => {
      const mockRmaVisitType = 'mockRmaVisitType';
      const newBill = {
        visitType: mockRmaVisitType,
      } as Bill;
      const expectedAction = BillingActions.updateBill({newBill});

      facade.setRmaVisitType(mockRmaVisitType, []);

      expect(spy).toHaveBeenCalledWith(expectedAction);
    });
    it('should dispatch updateIncludeRPFromPatient action with true', () => {
      const includeRPFromPatient = true;
      const expectedAction = BillingActions.updateIncludeRPFromPatient({includeRPFromPatient});

      facade.updateIncludeRPFromPatient(includeRPFromPatient);

      expect(facade['store'].dispatch).toHaveBeenCalledWith(expectedAction);
    });

    it('should dispatch updateIncludeRPFromPatient action with false', () => {
      const includeRPFromPatient = false;
      const expectedAction = BillingActions.updateIncludeRPFromPatient({includeRPFromPatient});

      facade.updateIncludeRPFromPatient(includeRPFromPatient);

      expect(facade['store'].dispatch).toHaveBeenCalledWith(expectedAction);
    });
  });

  describe('setServiceDate', () => {
    let spy;
    beforeEach(() => {
      finalizeTestBed();

      spy = jest.spyOn<BillingFacade, 'dispatch'>(facade, 'dispatch');
    });

    it('should dispatch an action on setServiceDate', () => {
      // Use a valid date string
      const mockServiceDate = '2023-05-15';

      facade.setServiceDate(mockServiceDate);

      // Verify the action was dispatched with a Date object
      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          newBill: expect.objectContaining({
            billingDate: expect.any(Date)
          }),
          type: "[Billing] Updating Bill"
        })
      );
    });
  });
});
