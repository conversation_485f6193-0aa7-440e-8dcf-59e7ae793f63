import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'stripSpecialAttributes',
    standalone: false
})

export class StripSpecialAttributesPipe implements PipeTransform {
  transform(arr: Array<object>): Array<object> {
    const startsWith = '__';
    for (let i = 0; i < arr.length; i++) {
      const keys = Object.keys(arr[i]);
      for (let j = 0; j < keys.length; j++) {
        if (keys[j].startsWith(startsWith)) {
          delete arr[i][keys[j]];
        }
      }
    }
    return arr;
  }
}
