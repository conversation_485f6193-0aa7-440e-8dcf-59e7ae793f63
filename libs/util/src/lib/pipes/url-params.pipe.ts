import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'urlParams',
    standalone: false
})
export class UrlParamsPipe implements PipeTransform {
  transform(params: Record<string, any>): string {
    let urlParams = '';

    if (Object.keys(params).length > 0 && params.constructor === Object) {
      urlParams =
        '?' +
        Object.entries(params)
          .map(([key, val]) => `${key}=${encodeURIComponent(val)}`)
          .join('&');
    }

    return urlParams;
  }
}
