import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'objectArrayFilter',
    standalone: false
})
export class ObjectArrayFilterPipe implements PipeTransform {
  transform<T>(arr: Array<T>, filters: any): Array<T> {
    const newArr: Array<T> = new Array<T>();
    const keys = Object.keys(filters);

    if (arr) {
      for (let i = 0; i < arr.length; i++) {
        let doesMatch = true;
        for (let j = 0; j < keys.length; j++) {
          const key: string = keys[j];
          const val: any = filters[key];
          if (arr[i][key] !== val) {
            doesMatch = false;
          }
        }
        if (doesMatch) {
          newArr.push(arr[i]);
        }
      }
    }
    return newArr;
  }
}
