import {Pipe, PipeTransform} from '@angular/core';
import {Provider, ProviderSelectable} from "@oscar-pro/interfaces";

@Pipe({
    name: 'providerSelectableFilter',
    standalone: false
})
export class ProviderSelectableFilterPipe implements PipeTransform {

  transform(providers: Array<ProviderSelectable>, filter: string): Array<ProviderSelectable> {
    if (!filter || filter.trim().length === 0) {
      return providers;
    }
    return fuzzyMatchProviders(providers, filter.trim());
  }
}

function strip(value: string) {
  return !value ? value : value.replace(/[^A-Za-z]/g, '');
}

function fuzzyMatchProviders(providers: Array<Provider>, filter: string) {
  const commaPos = filter.indexOf(',');
  const spacePos = filter.indexOf(' ');
  let first;
  let last;
  if (commaPos > -1) {
    last = filter.substring(0, commaPos);
    first = filter.substring(commaPos + 1);
  } else if (spacePos > -1) {
    first = filter.substring(0, spacePos);
    last = filter.substring(spacePos + 1);
  } else {
    first = filter;
    last = null;
  }
  const single = !!first !== !!last;
  console.log('single', single, 'first', !!first, 'last', !!last);
  const firstPattern = !single || !!first ? getPattern(first) : getPattern(last);
  const lastPattern = !single || !!last ? getPattern(last) : getPattern(first);
  const results: Provider[] = [];
  providers.forEach(provider => {
    const firstTest = firstPattern.test(provider.firstName);
    const lastTest = lastPattern.test(provider.lastName);
    if ((!single && firstTest && lastTest)
        // Match on first name only if that is all that was supplied
        || (single && !!first && firstTest)
        // Match on last name only if that is all that was supplied
        || (single && !!last && lastTest)
        // Match on first or last name if only one part was supplied
        || (single && (firstTest || lastTest))) {
      results.push(provider);
    }
  });
  return results;
}

function getPattern(value: string) {
  return new RegExp('.*' + strip(value).split('').join('.*') + '.*', 'i');
}
