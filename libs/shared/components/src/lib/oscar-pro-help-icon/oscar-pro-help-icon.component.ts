import { Component, Input, OnInit } from '@angular/core';
import { ApiSystemPreferenceService, SystemPreferenceKey } from '@oscar-pro/data-access';
import { ToastrService } from 'ngx-toastr';

@Component({
    selector: 'oscar-pro-help-icon',
    templateUrl: './oscar-pro-help-icon.component.html',
    styleUrls: ['./oscar-pro-help-icon.component.scss'],
    standalone: false
})
export class OscarProHelpIconComponent implements OnInit {
  @Input() href?: string; // Optional manual URL
  @Input() systemPreferenceKey?: SystemPreferenceKey; // Allows fetching dynamic URLs

  resolvedHref = '#';

  constructor(
    private systemPreferenceService: ApiSystemPreferenceService,
    private toastrService: ToastrService
  ) {
  }

  ngOnInit(): void {
    if (this.href) {
      // If a static href is provided, use it directly
      this.resolvedHref = this.href;
    } else if (this.systemPreferenceKey) {
      // Dynamically fetch URL based on the provided SystemPreferenceKey
      this.systemPreferenceService.readStringPreference(this.systemPreferenceKey).subscribe(
        (data) => {
          this.resolvedHref = data || '#'; // Fallback if empty
        },
        (err) => {
          this.toastrService.error('An error occurred while loading the Help URL.');
        }
      );
    }
  }
}
