import {
  Component,
  Inject,
  Input,
  OnInit,
  SimpleChange,
  SimpleChanges,
} from '@angular/core';
import { AuthService } from '@oscar-pro/auth';
import { APP_CONFIG } from '@oscar-pro/config';
import {
  ApiAppointmentService,
  ApiSystemPreferenceService,
  SystemPreferenceKey,
} from '@oscar-pro/data-access';
import {
  AppConfig,
  Appointment,
  AppointmentStatus,
  Demographic,
  User,
} from '@oscar-pro/interfaces';
import {
  ObjectArrayFilterPipe,
  UrlParamsPipe,
  WindowRefService,
} from '@oscar-pro/util';

@Component({
    selector: 'oscar-pro-appointment-history',
    templateUrl: './appointment-history.component.html',
    styleUrls: ['./appointment-history.component.scss'],
    standalone: false
})
export class AppointmentHistoryComponent implements OnInit {
  @Input() demographic: Demographic = {} as Demographic;

  private nativeWindow: Window;

  public appointmentStatuses: Array<AppointmentStatus> =
    new Array<AppointmentStatus>();

  public currentUser: User | undefined;
  public demographicList: Array<Demographic> = [] as Array<Demographic>;
  public obgynShortcuts = false;
  public recentAppointments: Array<Appointment> = new Array<Appointment>();

  constructor(
    private apiAppointmentService: ApiAppointmentService,
    private apiSystemPreferenceService: ApiSystemPreferenceService,
    private authService: AuthService,
    private objectArrayFilterPipe: ObjectArrayFilterPipe,
    private urlParamsPipe: UrlParamsPipe,
    private windowRefService: WindowRefService,
    @Inject(APP_CONFIG) private appConfig: AppConfig,
  ) {
    this.currentUser = this.authService.getCurrentUser();
    this.nativeWindow = this.windowRefService.getNativeWindow();
  }

  ngOnChanges(changes: SimpleChanges): void {
    const demographic: SimpleChange = changes.demographic;
    this.demographic = demographic.currentValue;
    this.getAppointmentData(this.demographic);
  }

  ngOnInit(): void {
    if (this.demographic) {
      this.getAppointmentData(this.demographic);
      this.apiSystemPreferenceService
        .readBooleanPreference(SystemPreferenceKey.ShowObgynShortcuts, false)
        .subscribe(
          (data) => {
            this.obgynShortcuts = data;
          },
          (err) => console.error(err),
        );
    }
    this.getAppointmentStatuses();
  }

  getAppointmentData(demographic: Demographic): void {
    this.apiAppointmentService
      .getRecentAppointmentsByDemographicNo(demographic.demographicNumber)
      .subscribe(
        (data) => {
          this.recentAppointments = data;
        },
        (err) => console.error(err),
      );
  }

  getAppointmentStatuses(): void {
    this.apiAppointmentService.getActiveAppointmentStatuses().subscribe(
      (data) => {
        this.appointmentStatuses = data;
      },
      (err) => console.error(err),
    );
  }

  getAppointmentStatus(fullCode: string): string {
    const code: string = fullCode[0];
    const matchingStatus = this.objectArrayFilterPipe.transform(
      this.appointmentStatuses,
      { status: code },
    )[0];
    return matchingStatus ? matchingStatus['description'] : '';
  }

  isFutureAppointment(appointment: Appointment) {
    return (
      new Date(appointment.appointmentDate).valueOf() > new Date().valueOf()
    );
  }

  onClickPerinatal(event: MouseEvent, section = 'PR2'): void {
    event.preventDefault();
    let url = `/${this.appConfig.oscarContext}/form`;
    let features = 'height=290,width=775';
    if (section.indexOf('-') > -1) {
      const params = {
        demographic_no: this.demographic.demographicNumber,
        section: section,
      };

      url += `/formONPerinatalForm.jsp${this.urlParamsPipe.transform(params)}`;
    } else {
      const params = {
        demographic_no: this.demographic.demographicNumber,
        shortcut: true,
        update: true,
      };
      url +=
        (section === 'PR3'
          ? '/formONPerinatalRecord3.jsp'
          : '/formONPerinatalRecord2.jsp')
        + `${this.urlParamsPipe.transform(params)}`;
      features = 'height=700,width=1024';
    }

    this.nativeWindow.open(url, 'attachment', features);
  }
}
