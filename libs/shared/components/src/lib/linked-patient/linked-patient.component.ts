import {
  Component, DestroyRef,
  EventEmitter,
  Inject,
  Input, OnChanges, OnInit,
  Output,
  SimpleChange,
  SimpleChanges,
} from '@angular/core';
import { AuthService } from '@oscar-pro/auth';
import { APP_CONFIG } from '@oscar-pro/config';
import {
  ApiAppointmentService,
  ApiDemographicService, ApiPropertyService,
  ApiSystemPreferenceService,
  SystemPreferenceKey,
} from '@oscar-pro/data-access';
import {
  AppConfig,
  Appointment,
  Demographic,
  DocumentDetail,
  OptionValue,
  User,
} from '@oscar-pro/interfaces';
import {
  ObjectArrayFilterPipe,
  UrlParamsPipe,
  WindowRefService,
} from '@oscar-pro/util';
import { isNil } from 'lodash';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AttachmentManagerPrintable } from "@oscar-pro/feature/attachment-manager";

@Component({
    selector: 'oscar-pro-linked-patient',
    templateUrl: './linked-patient.component.html',
    styleUrls: ['./linked-patient.component.scss'],
    standalone: false
})
export class LinkedPatientComponent implements OnChanges, OnInit {
  @Input() demographic: Demographic = {} as Demographic;
  @Input() autoFlagPreference!: boolean;
  @Input() itemType: string | undefined;
  @Input() eChartNoteReason: string | undefined;
  @Input() document = {} as DocumentDetail;
  @Output()
  linkDemographic: EventEmitter<Demographic> = new EventEmitter<Demographic>();
  @Output() removeDemographic: EventEmitter<null> = new EventEmitter();
  @Output() reloadLabEntry: EventEmitter<null> = new EventEmitter<null>();
  @Output()
  openMessageWindow: EventEmitter<MouseEvent> = new EventEmitter<MouseEvent>();
  @Output()
  openTaskWindow: EventEmitter<MouseEvent> = new EventEmitter<MouseEvent>();
  @Output() linkMrp: EventEmitter<any> = new EventEmitter<any>();

  private nativeWindow: Window;
  private timeoutInterval = 500;
  private timeout: ReturnType<typeof setTimeout> | undefined;
  private minKeywordLength = 3;
  private isProRx = false;

  public currentUser: User | undefined;
  public demographicList: Array<Demographic> = [] as Array<Demographic>;
  public demographicOptions: Array<OptionValue> = [] as Array<OptionValue>;
  public searchActivePatientsOnly = true;
  public obgynShortcuts = false;
  public nextAppointment: Appointment | undefined;
  public selectedPrintables: AttachmentManagerPrintable[] = [];
  public isOceanEmailEnabled = false;
  public isOceanWarningDismissed = false;
  public oceanWarningMessage = '';

  constructor(
    private apiDemographicService: ApiDemographicService,
    private authService: AuthService,
    private objectArrayFilterPipe: ObjectArrayFilterPipe,
    private urlParamsPipe: UrlParamsPipe,
    private windowRefService: WindowRefService,
    private apiAppointmentService: ApiAppointmentService,
    private apiSystemPreferenceService: ApiSystemPreferenceService,
    private apiPropertyService: ApiPropertyService,
    private destroyRef: DestroyRef,
    @Inject(APP_CONFIG) private appConfig: AppConfig,
  ) {
    this.currentUser = this.authService.getCurrentUser();
    this.nativeWindow = this.windowRefService.getNativeWindow();

    this.apiPropertyService.isProEnabled('pro_prescription').pipe(
      takeUntilDestroyed(this.destroyRef),
    ).subscribe((enabled) => {
      this.isProRx = enabled;
    });
  }

  ngOnInit(): void {
    this.apiSystemPreferenceService
    .readBooleanPreference(SystemPreferenceKey.AttachmentManagerOceanEmailEnabled, false)
    .pipe(takeUntilDestroyed(this.destroyRef))
    .subscribe(
        (data) => {
          this.isOceanEmailEnabled = data;
        },
        (error) => {
          console.error('Error reading OceanEmailEnabled preference:', error);
        }
    );

    this.apiSystemPreferenceService
    .readBooleanPreference(SystemPreferenceKey.AttachmentManagerOceanWarningDismissed, false)
    .pipe(takeUntilDestroyed(this.destroyRef))
    .subscribe(
        (data) => {
          this.isOceanWarningDismissed = data;
        },
        (error) => {
          console.error('Error reading AttachmentManagerOceanWarningDismissed preference:', error);
        }
    );

    this.apiSystemPreferenceService
    .readStringPreference(SystemPreferenceKey.AttachmentManagerOceanWarningMessage)
    .pipe(takeUntilDestroyed(this.destroyRef))
    .subscribe(
        (data) => {
          this.oceanWarningMessage = data;
        },
        (error) => {
          console.error('Error reading AttachmentManagerOceanWarningMessage preference:', error);
        }
    );

    if (this.document) {
      this.selectedPrintables = [{
        id: this.document.documentNo,
        name: this.document.docDesc || 'Document',
        type: 'Document',
        date: this.document.observationDate,
        previewUrl: '',
        supported: true,
        params: {},
        printableAttachments: []
      }];
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    const demographic: SimpleChange = changes.demographic;
    if (changes.demographic?.currentValue?.length > 0) {
      this.demographic = demographic.currentValue;
    }
    if (!this.demographic) {
      this.obgynShortcuts = false;
    } else {
      this.getObgynPreference();
    }
    this.getAppointmentData(this.demographic);
  }

  onClickAppointmentHistory(event: MouseEvent): void {
    event.preventDefault();
    const params = {
      orderby: 'appttime',
      displaymode: 'appt_history',
      dboperation: 'appt_history',
      limit1: 0,
      limit2: 25,
      demographic_no: this.demographic.demographicNumber,
    };
    const url = `/${
      this.appConfig.oscarContext
    }/demographic/demographiccontrol.jsp${this.urlParamsPipe.transform(
      params,
    )}`;
    this.nativeWindow.open(url, 'AppointmentHistory', 'height=700,width=960');
  }

  onClickCreateNewDemographic(event: MouseEvent): void {
    event.preventDefault();
    const url = `/${this.appConfig.oscarContext}/demographic/demographicaddarecordhtm.jsp`;
    this.nativeWindow.open(url, 'demographic', 'height=700,width=960');
  }

  onClickEChart(event: MouseEvent): void {
    event.preventDefault();
    const params = {
      updateParent: false,
      reason: this.eChartNoteReason,
      curDate: new Date(),
      appointmentNo: '',
      appointmentDate: '',
      startTime: '',
      status: '',
      demographicNo: this.demographic.demographicNumber,
    };
    const url = `/${
      this.appConfig.oscarContext
    }/oscarEncounter/IncomingEncounter.do${this.urlParamsPipe.transform(
      params,
    )}`;
    this.nativeWindow.open(url, 'encounter', 'height=700,width=960');
  }

  onClickMasterFile(event: MouseEvent): void {
    event.preventDefault();
    const params = {
      displaymode: 'edit',
      dboperation: 'search_detail',
      demographic_no: this.demographic.demographicNumber,
    };
    const url = `/${
      this.appConfig.oscarContext
    }/demographic/demographiccontrol.jsp${this.urlParamsPipe.transform(
      params,
    )}`;
    this.nativeWindow.open(url, 'MasterFile', 'height=700,width=960');
  }

  onClickPreventions(event: MouseEvent): void {
    event.preventDefault();
    const params = {
      demographic_no: this.demographic.demographicNumber,
    };
    const url = `/${
      this.appConfig.oscarContext
    }/oscarPrevention/index.jsp${this.urlParamsPipe.transform(params)}`;
    this.nativeWindow.open(url, 'MasterFile', 'height=700,width=960');
  }

  onClickRemoveDemographic(event: MouseEvent): void {
    event.preventDefault();
    this.removeDemographic.emit();
  }

  onClickRx(event: MouseEvent): void {
    event.preventDefault();
    const params = {
      providerNo: this.currentUser?.providerNo,
      demographicNo: this.demographic.demographicNumber,
    };
    const endpoint = this.isProRx ?
      `/${this.appConfig.proContext}/app/components/rx/` :
      `/${this.appConfig.oscarContext}/oscarRx/choosePatient.do`
    const url = `${endpoint}${this.urlParamsPipe.transform(params)}`;
    this.nativeWindow.open(url, 'Rx', 'height=700,width=960');
  }

  onEventUpdateDemographic(demographicNumber: string): void {
    const demographic: Demographic = <Demographic>(
      this.objectArrayFilterPipe.transform(this.demographicList, {
        demographicNumber: +demographicNumber,
      })[0]
    );
    this.linkDemographic.emit(demographic);
    this.reloadLabEntry.emit(); // emit without passing labId, value will be entered up the chain
  }

  searchDemographics(keyword: string): void {
    if (keyword.length < this.minKeywordLength) {
      return;
    }
    clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      this.demographicOptions = [] as Array<OptionValue>;
      if (keyword && keyword !== '') {
        this.apiDemographicService
          .searchDemographics(
            keyword.trim(),
            this.searchActivePatientsOnly,
            true,
          )
          .subscribe(
            (data) => {
              this.demographicList = data;
              if (!isNil(this.demographicList)) {
                this.demographicList.forEach((demographic) =>
                  this.demographicOptions.push({
                    option: `${demographic.lastName}, ${demographic.firstName} ${demographic.dob} (${demographic.patientStatus})`,
                    value: demographic.demographicNumber.toString(),
                  }),
                );
              }
            },
            (err) => {
              console.error(err);
            },
          );
      }
    }, this.timeoutInterval);
  }

  onClickMessage(event: MouseEvent): void {
    this.openMessageWindow.emit(event);
  }

  onClickTask(event: MouseEvent): void {
    this.openTaskWindow.emit(event);
  }

  getAppointmentData(demographic: Demographic): void {
    if (demographic?.demographicNumber) {
      this.apiAppointmentService
        .getNextAppointmentsByDemographicNo(demographic.demographicNumber)
        .subscribe(
          (data) => {
            this.nextAppointment = data;
          },
          (err) => console.error(err),
        );
    }
  }

  onClickPerinatal(event: MouseEvent, section = 'PR2'): void {
    event.preventDefault();
    let url = `/${this.appConfig.oscarContext}/form`;
    let features = 'height=290,width=775';
    if (section.indexOf('-') > -1) {
      const params = {
        demographic_no: this.demographic.demographicNumber,
        section: section,
      };

      url += `/formONPerinatalForm.jsp${this.urlParamsPipe.transform(params)}`;
    } else {
      const params = {
        demographic_no: this.demographic.demographicNumber,
        shortcut: true,
        update: true,
      };
      url +=
        (section === 'PR3'
          ? '/formONPerinatalRecord3.jsp'
          : '/formONPerinatalRecord2.jsp')
        + `${this.urlParamsPipe.transform(params)}`;
      features = 'height=700,width=1024';
    }

    this.nativeWindow.open(url, 'attachment', features);
  }

  getObgynPreference() {
    if (this.demographic) {
      this.getAppointmentData(this.demographic);
      this.apiSystemPreferenceService
        .readBooleanPreference(SystemPreferenceKey.ShowObgynShortcuts, false)
        .subscribe(
          (data) => {
            this.obgynShortcuts = data;
          },
          (err) => console.error(err),
        );
    }
  }

  onClickLinkMrp(): void {
    this.linkMrp.emit(this.autoFlagPreference);
  }
}
