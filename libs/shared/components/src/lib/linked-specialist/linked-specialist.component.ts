import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AuthService } from '@oscar-pro/auth';
import {
  ApiProviderService,
  ApiSpecialistService,
} from '@oscar-pro/data-access';
import { <PERSON><PERSON>Val<PERSON>, Specialist, User } from '@oscar-pro/interfaces';
import { ObjectArrayFilterPipe } from '@oscar-pro/util';

@Component({
    selector: 'oscar-pro-linked-specialist',
    templateUrl: './linked-specialist.component.html',
    styleUrls: ['./linked-specialist.component.scss'],
    standalone: false
})
export class LinkedSpecialistComponent implements OnInit {
  @Input() selectedSpecialist!: Specialist;
  @Input() selectedSpecialists: Array<Specialist> = new Array<Specialist>();
  @Input() selectedFaxes: Array<string> = new Array<string>();
  @Output()
  updateSpecialist: EventEmitter<Specialist> = new EventEmitter<Specialist>();
  @Output()
  linkSpecialist: EventEmitter<Specialist> = new EventEmitter<Specialist>();
  @Output() addFaxNumber: EventEmitter<string> = new EventEmitter<string>();
  @Output()
  sendFax: EventEmitter<Specialist> = new EventEmitter<Specialist>();
  @Output()
  removeSpecialist: EventEmitter<Specialist> = new EventEmitter<Specialist>();
  @Output() removeFax: EventEmitter<string> = new EventEmitter<string>();

  private currentUser: User | undefined;
  private specialistList: Array<Specialist> = [] as Array<Specialist>;
  public specialistOptions: OptionValue[] = [] as Array<OptionValue>;
  public faxNo = '';
  public clearOnSelection = true;
  public specialist: Specialist | null = null;

  constructor(
    private apiProviderService: ApiProviderService,
    private authService: AuthService,
    private apiSpecialistService: ApiSpecialistService,
    private objectArrayFilter: ObjectArrayFilterPipe // private snotifyConfigService: SnotifyConfigService, // private snotifyService: SnotifyService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
  }

  onInputSearchSpecialist(keyword: string): void {
    this.specialistList = [] as Array<Specialist>;
    this.specialistOptions = [] as Array<OptionValue>;
    if (keyword && keyword.length) {
      this.apiSpecialistService.searchFaxSpecialists(keyword).subscribe(
        (data) => {
          this.specialistList = data;
          this.specialistList.forEach((specialist) =>
            this.specialistOptions.push({
              option: specialist.formattedNameFax,
              value: specialist.specialistId.toString(),
            })
          );
        },
        (error) => console.error(error)
      );
    }
  }

  onClickRemoveSpecialist(
    event: MouseEvent,
    selectedSpecialist: Specialist
  ): void {
    event.preventDefault();
    this.removeSpecialist.emit(selectedSpecialist);
  }

  onClickRemoveFax(event: MouseEvent, selectedFax: string): void {
    event.preventDefault();
    this.removeFax.emit(selectedFax);
  }

  onClickAddFax(faxNo: string) {
    const faxFormat = /^\d{10,12}$/;

    const x = this.faxNo?.replace(/[- )(]/g, '');

    if (x?.match(faxFormat)) {
      this.addFaxNumber.emit(faxNo);
    } else {
      //TODO: show a warning
    }
    this.faxNo = '';
  }

  onClickSelectSpecialist(specialistId: number): void {
    const specialist: Specialist = <Specialist>this.objectArrayFilter.transform(
      this.specialistList,
      {
        specialistId: specialistId,
      }
    )[0];
    this.specialist = specialist;
    if (specialist.fax !== '') {
      this.linkSpecialist.emit(specialist);
      this.specialist = null;
    } else {
      // this.snotifyService.warning(
      //   'Missing Fax No. Cannot attach specialist.',
      //   this.snotifyConfigService.getConfig()
      // );
    }
  }
}
