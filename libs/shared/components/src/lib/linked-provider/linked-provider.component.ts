import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AuthService } from '@oscar-pro/auth';
import { ApiProviderService } from '@oscar-pro/data-access';
import {
  Demographic,
  OptionValue,
  Provider,
  ProviderDocumentRouting,
  ProviderHrmRouting,
  User,
} from '@oscar-pro/interfaces';
import { ObjectArrayFilterPipe } from '@oscar-pro/util';
import { isNil } from 'lodash';

@Component({
    selector: 'oscar-pro-linked-provider',
    templateUrl: './linked-provider.component.html',
    styleUrls: ['./linked-provider.component.scss'],
    standalone: false
})
export class LinkedProviderComponent implements OnInit {
  @Input() displayTags = true;
  @Input() demographic: Demographic | null = null;
  @Input() displayPromptMrpRemove = true;
  @Input() itemType!: 'DOC' | 'LAB' | 'HRM';
  @Input() providerRoutings: Array<
    ProviderHrmRouting | ProviderDocumentRouting
  > = new Array<ProviderHrmRouting | ProviderDocumentRouting>();

  @Output()
  linkProvider: EventEmitter<Provider> = new EventEmitter<Provider>();
  @Output() removeProvider: EventEmitter<
    ProviderHrmRouting | ProviderDocumentRouting
  > = new EventEmitter<ProviderHrmRouting | ProviderDocumentRouting>();
  @Output() saveComment: EventEmitter<string> = new EventEmitter<string>();

  private currentUser: User | undefined;

  public commentText!: string;
  public providerList: Array<Provider> = new Array<Provider>();
  public providerOptions: Array<OptionValue> = [] as Array<OptionValue>;
  public showCommentInput = false;

  constructor(
    private apiProviderService: ApiProviderService,
    private authService: AuthService,
    private objectArrayFilter: ObjectArrayFilterPipe
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
  }

  hasDemographic(): boolean {
    return !isNil(this.demographic);
  }

  isMRP(providerNumber : string) : string {
    return this.demographic?.provider?.providerNo == providerNumber
      ? '(MRP)'
        : '';
  }

  isMatchedProvider(
    providerRouting: ProviderHrmRouting | ProviderDocumentRouting
  ): boolean {
    return !isNil(providerRouting.provider);
  }

  getTitle(
    providerRouting: ProviderHrmRouting | ProviderDocumentRouting
  ): string {
    let title = '';
    if (!this.isMatchedProvider(providerRouting)) {
      title =
        this.itemType === 'HRM'
          ? 'From the HRM document'
          : 'Unmatched Provider';
    }
    return title;
  }

  onClickCancelComment(event: MouseEvent): void {
    event.preventDefault();
    this.showCommentInput = false;
  }

  onClickComment(event: MouseEvent): void {
    event.preventDefault();
    this.showCommentInput = !this.showCommentInput;
  }

  onClickRemoveProvider(
    event: MouseEvent,
    providerRouting: ProviderHrmRouting | ProviderDocumentRouting
  ): void {
    event.preventDefault();
    if (
      !isNil(this.demographic?.provider?.providerNo)
      && this.displayPromptMrpRemove
      && providerRouting.provider.providerNo == this.demographic?.provider?.providerNo
    ) {
      if (confirm('Are you sure you want to remove "' + providerRouting.provider.formattedName + '" (MRP) from this document?')) {
        this.removeProvider.emit(providerRouting);
      }
    } else {
      this.removeProvider.emit(providerRouting);
    }
  }

  onClickSaveComment(event: MouseEvent, comment: string): void {
    event.preventDefault();
    this.showCommentInput = false;
    this.saveComment.emit(comment);
  }

  onSelectProvider(providerNo: string): void {
    const provider: Provider = <Provider>this.objectArrayFilter.transform(
      this.providerList,
      {
        providerNo: providerNo,
      }
    )[0];
    this.linkProvider.emit(provider);
  }

  searchProviders(keyword: string): void {
    this.providerList = [] as Array<Provider>;
    this.providerOptions = [] as Array<OptionValue>;

    if (keyword && keyword !== '') {
      this.apiProviderService
        .searchProviders(keyword.trim())
        .subscribe(
          (data) => {
            this.providerList = data;
            if (!isNil(this.providerList)) {
              this.providerList.forEach((provider) =>
                this.providerOptions.push({
                  option: `${provider.lastName}, ${provider.firstName}`,
                  value: provider.providerNo,
                })
              );
            }
          },
          (err) => console.error(err)
        );
    }
  }
}
