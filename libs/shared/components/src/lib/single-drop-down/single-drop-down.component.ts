import {
  Component, Input, Output,
  EventEmitter,
  OnInit, ViewEncapsulation, ViewChild, ElementRef,
} from '@angular/core';
import {ObjectArrayFilterPipe} from "@oscar-pro/util";
import { isNil } from 'lodash';

@Component({
    encapsulation: ViewEncapsulation.None,
    selector: 'oscar-pro-single-drop-down',
    templateUrl: './single-drop-down.component.html',
    styleUrls: ['./single-drop-down.component.sass'],
    standalone: false
})
export class SingleDropDownComponent implements OnInit {

  @Input() alwaysUnselected = false;
  @Input() classes = '';
  @Input() defaultOption: any = null;
  @Input() notSelectedText = ''; // | null = null;
  @Input() options: Array<OptionValue> = [];
  @Input() selectedText = '';
  @Input() size = '';
  @Input() tabIndex = -1;
  @Input() title: string | undefined;
  @Input() emitOnUpdate = false;

  @Output() updateOption: EventEmitter<string> = new EventEmitter<string>();

  @ViewChild('dm') dropDownListEl: ElementRef | undefined;

  public arrowKeyIndex = 0;
  public isFocused = false;
  public isOpen = false;
  public selectedOptionVal = '';

  constructor(private objectArrayFilterPipe: ObjectArrayFilterPipe) {}

  ngDoCheck(): void {
    // watch variable changed elsewhere to update selected highlighting
    const watchOption = this.objectArrayFilterPipe.transform(this.options, { value: this.defaultOption })[0];
    const selectedOption = this.objectArrayFilterPipe.transform(this.options, { value: this.selectedOptionVal })[0];
    if (!isNil(watchOption) && !isNil(selectedOption) && watchOption !== selectedOption) {
      this.updateSelected(this.defaultOption, this.emitOnUpdate);
    }
  }

  ngOnInit(): void {
    if (!this.notSelectedText) {
      this.notSelectedText = `Select ${ this.title }`;
    }

    this.updateSelected(this.defaultOption ? this.defaultOption : this.notSelectedText, this.emitOnUpdate);
  }

  updateSelected(optionVal: string, emit = true): void {
    const isOptionSelected: boolean = this.isOptionSelected(optionVal);
    if (this.alwaysUnselected || optionVal === this.notSelectedText) {
      this.selectedOptionVal = '';
      this.arrowKeyIndex = 0;
      this.selectedText = this.notSelectedText;
    } else {
      this.selectedOptionVal = optionVal;
      const selectedOption = this.objectArrayFilterPipe.transform(this.options, { value: optionVal })[0];
      this.arrowKeyIndex = this.options.indexOf(selectedOption);
      this.selectedText = !isNil(selectedOption) ? selectedOption['option'] : this.notSelectedText;
    }
    this.isOpen = false;
    this.isFocused = false;

    if (emit && !isOptionSelected) {
      this.updateOption.emit(optionVal);
    }
  }

  isOptionSelected(option: string): boolean {
    return option === this.selectedOptionVal;
  }

  onBlur() {
    this.isFocused = false;
  }

  onChangeOption(event: UIEvent, option: string): void {
    event.preventDefault();
    this.updateSelected(option);
  }

  onClickSingleSelect(event: MouseEvent): void {
    event.preventDefault();
    this.isOpen = !this.isOpen;
  }

  // todo: emit click outside event?
  onClickOutsideSingleSelect(event: Event): void {
    if (this.isOpen) {
      event.preventDefault();
      this.isOpen = false;
      this.isFocused = false;
    }
  }

  onFocus() {
    this.isFocused = true;
  }

  onKeydown(e: KeyboardEvent): void {
    const key = e.keyCode;
    if (this.isOpen && [13, 38, 40].includes(key)) {
      e.preventDefault();

      switch (key) {
        case 13: {
          // enter
          this.updateSelected(this.options[this.arrowKeyIndex].value);
          break;
        }
        case 38: {
          // up arrow
          this.arrowKeyIndex == 0 ? this.arrowKeyIndex = (this.options.length - 1) : this.arrowKeyIndex--;
          break;
        }
        case 40: {
          // down arrow
          this.arrowKeyIndex == (this.options.length - 1) ? this.arrowKeyIndex = 0 : this.arrowKeyIndex++;
          break;
        }
      }
      // updates scrollbar location based on selected index
      // todo
      if (this.dropDownListEl) {
        this.dropDownListEl.nativeElement.scrollTop = this.arrowKeyIndex < 4 ? 0 : (this.arrowKeyIndex-4)*(this.dropDownListEl.nativeElement.scrollHeight/(this.options.length));
      }
    }
  }
}

export interface OptionValue {
  option: string;
  value: string;
  color?: string;
}

