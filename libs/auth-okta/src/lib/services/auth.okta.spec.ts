import { TestBed, waitForAsync } from '@angular/core/testing';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { RouterTestingModule } from '@angular/router/testing';
import { APP_CONFIG } from '@oscar-pro/config';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { AuthOktaService } from "@oscar-pro/auth-okta";
import { EnvironmentVarsPipe, LogService, UrlParamsPipe } from "@oscar-pro/util";
import { systemPropertiesFixture} from "@oscar-pro/testing";
import { AuthInterceptor, InitPropertyService } from "@oscar-pro/auth";
import { Observable, of } from "rxjs";
import { OktaSignIn } from "@okta/okta-signin-widget";

describe('AuthOktaService', () => {
  let service: AuthOktaService;
  let httpMock: HttpTestingController;
  const oscarContext = 'oscar';
  const proContext = 'kaiemr';
  const apiPrefix = 'api';

  beforeEach( () => {
    TestBed.configureTestingModule({
      imports: [HttpClientModule, RouterTestingModule, HttpClientTestingModule],
      providers: [
        {
          provide: APP_CONFIG,
          useValue: {
            proContext: proContext,
            apiPrefix: apiPrefix,
            oscarContext: oscarContext,
          },
        },
        {
          provide: InitPropertyService,
          useValue: {
            pollSystemProperties$: jest.fn(),
          }
        },
        AuthInterceptor,
        {
          provide: HTTP_INTERCEPTORS,
          useClass: AuthInterceptor,
          multi: true,
        },
        UrlParamsPipe,
        EnvironmentVarsPipe,
        {
          provide: LogService,
          useValue: {
            warn: jest.fn(),
            debug1: jest.fn(),
            debug2: jest.fn(),
          }
        }
      ],
    });
    service = TestBed.inject(AuthOktaService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
    jest.clearAllMocks();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it(
    'should call getSignInWidget$ without providing oscar_okta_authorization_server_issuer, returning null',
    waitForAsync(() => {
      const initPropertyServiceReference = TestBed.inject(InitPropertyService) as any;
      const properties = systemPropertiesFixture();
      properties['oscar_okta_authorization_server_issuer'] = null;
      initPropertyServiceReference.pollSystemProperties$.mockReturnValueOnce(of(new Map(Object.entries(properties))));

      const loginResult = service.getSignInWidget$() as Observable<OktaSignIn>;
      loginResult.subscribe((results) => {
        expect(results).toBe(null);
      });
    })
  );

  it(
    'should call getSignInWidget$ without providing okta_authorization_code_redirect_fullurl, returning null',
    waitForAsync(() => {
      const initPropertyServiceReference = TestBed.inject(InitPropertyService) as any;
      const properties = systemPropertiesFixture();
      properties['okta_authorization_code_redirect_fullurl'] = null;
      initPropertyServiceReference.pollSystemProperties$.mockReturnValueOnce(of(new Map(Object.entries(properties))));

      const loginResult = service.getSignInWidget$() as Observable<OktaSignIn>;
      loginResult.subscribe((results) => {
        expect(results).toBe(null);
      });
    })
  );
});
