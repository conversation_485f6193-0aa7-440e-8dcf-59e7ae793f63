import {Component, Input} from '@angular/core';
import {ButtonBaseComponent} from "../button-base/button-base.component";

@Component({
    selector: 'weg-button-icon-isolated',
    templateUrl: './button-icon-isolated.component.html',
    styleUrls: ['./button-icon-isolated.component.scss'],
    standalone: false
})
export class ButtonIconIsolatedComponent extends ButtonBaseComponent {
  @Input() icon = 'fa-solid fa-check';
}
