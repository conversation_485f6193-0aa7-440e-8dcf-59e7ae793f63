import {Component, EventEmitter, Input, Output} from '@angular/core';

@Component({
    selector: 'weg-button-base',
    templateUrl: './button-base.component.html',
    styleUrls: ['./button-base.component.scss'],
    standalone: false
})
export class ButtonBaseComponent {
  @Input() colour: 'primary' | 'secondary' | 'secondary-legacy' | 'tertiary' | 'destructive' = 'primary';
  @Input() size: 'x-small' | 'small' | 'medium' = 'medium';
  @Input() width: 'fit' | 'full' = 'fit';
  @Input() isDisabled = false;
  @Output() clicked: EventEmitter<void> = new EventEmitter();

  handleClick() {
    this.clicked.emit();
  }
}
