import {Component, EventEmitter, Input, Output} from '@angular/core';
import {CheckboxState} from "../checkbox-state.enum";
import {CheckboxBaseComponent} from "../checkbox-base/checkbox-base.component";

@Component({
    selector: 'weg-checkbox-basic',
    templateUrl: './checkbox-basic.component.html',
    styleUrls: ['./checkbox-basic.component.scss'],
    standalone: false
})
export class CheckboxBasicComponent extends CheckboxBaseComponent {
  @Input() override checkboxState: CheckboxState.Checked | CheckboxState.Unchecked = CheckboxState.Unchecked;
  @Output() override checkboxClicked: EventEmitter<CheckboxState> = new EventEmitter();

  protected readonly CheckboxState = CheckboxState;

  override handleCheckboxClick(): void {
    if (this.checkboxState === CheckboxState.Unchecked) {
      this.checkboxState = CheckboxState.Checked;
    } else {
      this.checkboxState = CheckboxState.Unchecked;
    }
    this.checkboxClicked.emit(this.checkboxState);
  }

  handleFocusChange(event: boolean) {
    this.focusChanged.emit(event);
  }
}
