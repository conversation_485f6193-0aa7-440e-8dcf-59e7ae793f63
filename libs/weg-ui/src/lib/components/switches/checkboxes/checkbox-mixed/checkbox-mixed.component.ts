import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges
} from '@angular/core';
import {CheckboxState} from "../checkbox-state.enum";
import {CheckboxBaseComponent} from "../checkbox-base/checkbox-base.component";

@Component({
    selector: 'weg-checkbox-mixed',
    templateUrl: './checkbox-mixed.component.html',
    styleUrls: ['./checkbox-mixed.component.scss'],
    standalone: false
})
export class CheckboxMixedComponent extends CheckboxBaseComponent implements AfterViewInit, OnChanges {
  @Input() isIndeterminate = false;
  @Output() override checkboxClicked: EventEmitter<CheckboxState> = new EventEmitter();

  protected readonly CheckBoxState = CheckboxState;

  override ngAfterViewInit(): void {
    if (this.isIndeterminate) {
      this.checkboxState = CheckboxState.Indeterminate;
    }
  }

  override ngOnChanges(changes: SimpleChanges): void {
    if (changes['isIndeterminate']) {
      this.checkboxState = CheckboxState.Indeterminate;
    }
  }

  override handleCheckboxClick(): void {
    if (this.checkboxState === CheckboxState.Checked) {
      this.checkboxState = CheckboxState.Unchecked;
    } else {
      this.checkboxState = CheckboxState.Checked;
    }
    this.checkboxClicked.emit(this.checkboxState);
  }

  handleFocusChange(event: boolean) {
    this.focusChanged.emit(event);
  }
}
