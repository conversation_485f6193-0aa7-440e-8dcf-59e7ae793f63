<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<bindings xmlns="http://java.sun.com/xml/ns/jaxb" if-exists="true" version="2.1">
      
    <!--

This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.3.0 
See <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
Any modifications to this file will be lost upon recompilation of the source schema. 
Generated on: 2021.02.09 at 11:11:14 AM PST 

  -->
      
    <bindings xmlns:tns="cds" if-exists="true" scd="x-schema::tns">
            
        <schemaBindings map="false">
                  
            <package name="cds.v5_0"/>
                
        </schemaBindings>
            
        <bindings if-exists="true" scd="tns:Demographics">
                  
            <class ref="cds.v5_0.Demographics"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:MedicationsAndTreatments">
                  
            <class ref="cds.v5_0.MedicationsAndTreatments"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:LaboratoryResults">
                  
            <class ref="cds.v5_0.LaboratoryResults"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:Appointments">
                  
            <class ref="cds.v5_0.Appointments"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:ClinicalNotes">
                  
            <class ref="cds.v5_0.ClinicalNotes"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:Reports">
                  
            <class ref="cds.v5_0.Reports"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:OmdCds">
                  
            <class ref="cds.v5_0.OmdCds"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:PatientRecord">
                  
            <class ref="cds.v5_0.PatientRecord"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:PersonalHistory">
                  
            <class ref="cds.v5_0.PersonalHistory"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:FamilyHistory">
                  
            <class ref="cds.v5_0.FamilyHistory"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:PastHealth">
                  
            <class ref="cds.v5_0.PastHealth"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:ProblemList">
                  
            <class ref="cds.v5_0.ProblemList"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:RiskFactors">
                  
            <class ref="cds.v5_0.RiskFactors"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:AllergiesAndAdverseReactions">
                  
            <class ref="cds.v5_0.AllergiesAndAdverseReactions"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:Immunizations">
                  
            <class ref="cds.v5_0.Immunizations"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:CareElements">
                  
            <class ref="cds.v5_0.CareElements"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:AlertsAndSpecialNeeds">
                  
            <class ref="cds.v5_0.AlertsAndSpecialNeeds"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="tns:NewCategory">
                  
            <class ref="cds.v5_0.NewCategory"/>
                
        </bindings>
          
    </bindings>
      
    <bindings xmlns:tns="cds_dt" if-exists="true" scd="x-schema::tns">
            
        <schemaBindings map="false">
                  
            <package name="cds.v5_0"/>
                
        </schemaBindings>
            
        <bindings if-exists="true" scd="~tns:residualInformationForAlerts">
                  
            <class ref="cds.v5_0.ResidualInformationForAlerts"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:residualInformation">
                  
            <class ref="cds.v5_0.ResidualInformation"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:personNameStandard">
                  
            <class ref="cds.v5_0.PersonNameStandard"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:healthCard">
                  
            <class ref="cds.v5_0.HealthCard"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:address">
                  
            <class ref="cds.v5_0.Address"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:phoneNumber">
                  
            <class ref="cds.v5_0.PhoneNumber"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:personNameSimple">
                  
            <class ref="cds.v5_0.PersonNameSimple"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:dateFullOrPartial">
                  
            <class ref="cds.v5_0.DateFullOrPartial"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:standardCoding">
                  
            <class ref="cds.v5_0.StandardCoding"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:DrugCode">
                  
            <class ref="cds.v5_0.DrugCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:dateTimeFullOrPartial">
                  
            <class ref="cds.v5_0.DateTimeFullOrPartial"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:drugMeasure">
                  
            <class ref="cds.v5_0.DrugMeasure"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ynIndicator">
                  
            <class ref="cds.v5_0.YnIndicator"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:code">
                  
            <class ref="cds.v5_0.Code"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:resultNormalAbnormalFlag">
                  
            <class ref="cds.v5_0.ResultNormalAbnormalFlag"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:reportContent">
                  
            <class ref="cds.v5_0.ReportContent"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:smokingStatus">
                  
            <class ref="cds.v5_0.SmokingStatus"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:smokingPacks">
                  
            <class ref="cds.v5_0.SmokingPacks"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:weight">
                  
            <class ref="cds.v5_0.Weight"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:height">
                  
            <class ref="cds.v5_0.Height"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:waistCircumference">
                  
            <class ref="cds.v5_0.WaistCircumference"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:bloodPressure">
                  
            <class ref="cds.v5_0.BloodPressure"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:diabetesComplicationScreening">
                  
            <class ref="cds.v5_0.DiabetesComplicationScreening"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:diabetesMotivationalCounselling">
                  
            <class ref="cds.v5_0.DiabetesMotivationalCounselling"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:diabetesSelfManagementCollaborative">
                  
            <class ref="cds.v5_0.DiabetesSelfManagementCollaborative"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:diabetesSelfManagementChallenges">
                  
            <class ref="cds.v5_0.DiabetesSelfManagementChallenges"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:diabetesEducationalSelfManagement">
                  
            <class ref="cds.v5_0.DiabetesEducationalSelfManagement"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:hypoglycemicEpisodes">
                  
            <class ref="cds.v5_0.HypoglycemicEpisodes"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:selfMonitoringBloodGlucose">
                  
            <class ref="cds.v5_0.SelfMonitoringBloodGlucose"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:address.structured">
                  
            <class ref="cds.v5_0.AddressStructured"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:enrollmentInfo">
                  
            <class ref="cds.v5_0.EnrollmentInfo"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:personNameSimpleWithMiddleName">
                  
            <class ref="cds.v5_0.PersonNameSimpleWithMiddleName"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:purposeEnumOrPlainText">
                  
            <class ref="cds.v5_0.PurposeEnumOrPlainText"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:postalZipCode">
                  
            <class ref="cds.v5_0.PostalZipCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:ynIndicatorAndBlank">
                  
            <class ref="cds.v5_0.YnIndicatorAndBlank"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:gender">
                  
            <typesafeEnumClass ref="cds.v5_0.Gender"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:officialSpokenLanguageCode">
                  
            <typesafeEnumClass ref="cds.v5_0.OfficialSpokenLanguageCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:lifeStage">
                  
            <typesafeEnumClass ref="cds.v5_0.LifeStage"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:propertyOfOffendingAgent">
                  
            <typesafeEnumClass ref="cds.v5_0.PropertyOfOffendingAgent"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:adverseReactionType">
                  
            <typesafeEnumClass ref="cds.v5_0.AdverseReactionType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:adverseReactionSeverity">
                  
            <typesafeEnumClass ref="cds.v5_0.AdverseReactionSeverity"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:immunizationType">
                  
            <typesafeEnumClass ref="cds.v5_0.ImmunizationType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:yIndicator">
                  
            <typesafeEnumClass ref="cds.v5_0.YIndicator"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:reportMedia">
                  
            <typesafeEnumClass ref="cds.v5_0.ReportMedia"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:reportFormat">
                  
            <typesafeEnumClass ref="cds.v5_0.ReportFormat"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:reportClass">
                  
            <typesafeEnumClass ref="cds.v5_0.ReportClass"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:addressType">
                  
            <typesafeEnumClass ref="cds.v5_0.AddressType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:medicalSurgicalFlag">
                  
            <typesafeEnumClass ref="cds.v5_0.MedicalSurgicalFlag"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:personNamePrefixCode">
                  
            <typesafeEnumClass ref="cds.v5_0.PersonNamePrefixCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:personNamePartTypeCode">
                  
            <typesafeEnumClass ref="cds.v5_0.PersonNamePartTypeCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:personNamePartQualifierCode">
                  
            <typesafeEnumClass ref="cds.v5_0.PersonNamePartQualifierCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:personNamePurposeCode">
                  
            <typesafeEnumClass ref="cds.v5_0.PersonNamePurposeCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:personNameSuffixCode">
                  
            <typesafeEnumClass ref="cds.v5_0.PersonNameSuffixCode"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:personStatus">
                  
            <typesafeEnumClass ref="cds.v5_0.PersonStatus"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:phoneNumberType">
                  
            <typesafeEnumClass ref="cds.v5_0.PhoneNumberType"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:preferredMethodOfContact">
                  
            <typesafeEnumClass ref="cds.v5_0.PreferredMethodOfContact"/>
                
        </bindings>
            
        <bindings if-exists="true" scd="~tns:auditFormat">
                  
            <typesafeEnumClass ref="cds.v5_0.AuditFormat"/>
                
        </bindings>
          
    </bindings>
    
</bindings>
