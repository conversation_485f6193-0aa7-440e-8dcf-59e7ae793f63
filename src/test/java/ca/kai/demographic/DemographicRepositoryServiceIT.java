package ca.kai.demographic;

import ca.kai.fhir.subscription.FhirSubscriptionManager;
import ca.oscarpro.base.IntegrationTestBase;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import javax.persistence.EntityManager;
import javax.transaction.Transactional;
import lombok.val;
import lombok.var;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.DirtiesContext.ClassMode;

@DataJpaTest
@DatabaseSetup(value = "DemographicRepositoryServiceTest.xml")
@DirtiesContext(classMode = ClassMode.BEFORE_EACH_TEST_METHOD)
@Transactional
public class DemographicRepositoryServiceIT extends IntegrationTestBase {

  public static final Integer DEMOGRAPHIC_NO_VALUE = 1;
  public static final String OLD_CITY = "Surrey";
  public static final String NEW_CITY = "Penticton";

  @Autowired private DemographicRepository demographicRepository;
  @Autowired private DemographicArchiveRepository demographicArchiveRepository;
  @Autowired private DemographicExtRepository demographicExtRepository;
  @Autowired private DemographicExtArchiveRepository demographicExtArchiveRepository;
  @Autowired private EntityManager entityManager;
  @Mock private FhirSubscriptionManager fhirSubscriptionManager;

  private DemographicRepositoryService demographicRepositoryService;

  @Before
  public void before() throws ParseException {
    MockitoAnnotations.initMocks(this);

    demographicRepositoryService = new DemographicRepositoryService(
        demographicRepository,
        demographicArchiveRepository,
        demographicExtRepository,
        demographicExtArchiveRepository,
        fhirSubscriptionManager,
        entityManager
    );

    val archives =
        demographicArchiveRepository.getByDemographicNumber(1, new PageRequest(0, 10));
    Assert.assertEquals(0, archives.getTotalElements());

    val archivedExtensions =
        demographicExtArchiveRepository.getByArchiveId(1);
    Assert.assertEquals(0, archivedExtensions.size());
  }

  @Test
  public void givenPositiveControl_whenQueueDemographicSubscription_thenDelegateFunctionCalled() {
    demographicRepositoryService.queueDemographicSubscription(DEMOGRAPHIC_NO_VALUE.toString());
    Mockito.verify(fhirSubscriptionManager, Mockito.times(1))
        .checkResourceSubscriptionSafe(Mockito.any(String.class), Mockito.any(String.class));
  }

  @Test
  public void givenDemographicWithExtensions_whenArchiveDemographic_thenDemographicArchivedProperly() {
    val demographic = demographicRepository.getByDemographicNumber(1);
    // Change the address to verify its previous value was archived properly
    demographic.setCity(NEW_CITY);
    demographicRepositoryService.archiveDemographicAndExtensions(demographic);
    assertDemographicArchivedProperly(1);
  }

  @Test
  public void givenMultipleDemographics_whenSaveDemographics_thenBothDemographicsArchivedProperly() {
    val demographics = new ArrayList<>(Arrays.asList(
        demographicRepository.getByDemographicNumber(1),
        demographicRepository.getByDemographicNumber(2)
    ));
    demographics.get(0).setCity(NEW_CITY);
    demographics.get(1).setCity(NEW_CITY);
    demographicRepositoryService.saveDemographics(demographics);
    assertDemographicArchivedProperly(1);
    assertDemographicArchivedProperly(2);
  }

  @Test
  public void givenDemographic_whenSaveDemographic_thenDemographicArchivedProperly() {
    val demographic = demographicRepository.getByDemographicNumber(DEMOGRAPHIC_NO_VALUE);
    demographic.setCity(NEW_CITY);
    demographicRepositoryService.saveDemographic(demographic);
    assertDemographicArchivedProperly(1);
  }

  @Test
  public void givenNotArchiving_whenSaveDemographic_thenDemographicIsNotArchived() {
    val demographic = demographicRepository.getByDemographicNumber(DEMOGRAPHIC_NO_VALUE);
    Assert.assertNotEquals("NEW_NAME", demographic.getFirstName());

    demographic.setFirstName("NEW_NAME");
    demographicRepositoryService.saveDemographic(demographic, true);

    val saved =
        demographicRepository.getByDemographicNumber(demographic.getDemographicNumber());
    assertDemographicSavedAndSkippedArchiving(saved);
    Assert.assertEquals("NEW_NAME", saved.getFirstName());
  }

  @Test
  public void givenDemographicWithExtensions_whenSaveDemographicExtension_thenExtensionsArchived() {
    val extensions = new ArrayList<DemographicExt>();
    val activeExtensions = new ArrayList<DemographicExt>();
    val archivedExtensions = new ArrayList<DemographicExtArchive>();
    processDemographicExtSaving(extensions, activeExtensions, archivedExtensions, false);

    Assert.assertEquals(extensions.size() + 1, activeExtensions.size());
    Assert.assertEquals(extensions.size() + 1, archivedExtensions.size());
  }

  @Test
  public void givenNotArchiving_whenSaveDemographicExtension_thenExtensionsNotArchived() {
    val extensions = new ArrayList<DemographicExt>();
    val activeExtensions = new ArrayList<DemographicExt>();
    val archivedExtensions = new ArrayList<DemographicExtArchive>();
    processDemographicExtSaving(extensions, activeExtensions, archivedExtensions, true);

    Assert.assertEquals(extensions.size() + 1, activeExtensions.size());
    Assert.assertEquals(0, archivedExtensions.size());
  }

  @Test
  public void givenDemographicWithExtensions_whenDeleteDuplicateDemographicExtKeys_thenExtensionsAreDeleted() {
    val demographicNumber = 1;
    val demographic = demographicRepository.getByDemographicNumber(demographicNumber);
    val extension = new DemographicExt();
    extension.setDemographicNumber(1);
    extension.setProviderNo("999998");
    extension.setKey("UUID");
    extension.setValue(UUID.randomUUID().toString());
    extension.setHidden('0');
    demographicRepositoryService.saveDemographicExtension(demographic, extension, true);

    val initialExtensions = demographicExtRepository.getAllByDemographicNumber(DEMOGRAPHIC_NO_VALUE);
    Assert.assertEquals(3, initialExtensions.size());

    for (int i = 0; i < 5; i++){
      val duplicate = new DemographicExt();
      duplicate.setDemographicNumber(1);
      duplicate.setProviderNo("999998");
      duplicate.setKey("UUID");
      duplicate.setValue(UUID.randomUUID().toString());
      duplicate.setHidden('0');
      demographicRepositoryService.saveDemographicExtension(demographic, duplicate, true);
    }

    var duplicateExtensions = demographicExtRepository.getAllByDemographicNumber(DEMOGRAPHIC_NO_VALUE);
    Assert.assertEquals(8, duplicateExtensions.size());

    demographicRepositoryService.saveDemographic(demographic, false);

    val filteredExtensions = demographicExtRepository.getAllByDemographicNumber(DEMOGRAPHIC_NO_VALUE);
    Assert.assertEquals(3, filteredExtensions.size());
  }

  @Test
  public void givenMultipleDemographics_whenArchiveDemographicsAndExtensions_thenDemographicsArchivedProperly() {
    val demographics = new ArrayList<>(Arrays.asList(
        demographicRepository.getByDemographicNumber(1),
        demographicRepository.getByDemographicNumber(2)
    ));
    demographicRepositoryService.archiveDemographicsAndExtensions(demographics);
    assertDemographicArchivedProperly(1);
    assertDemographicArchivedProperly(2);
  }

  @Test
  public void givenGenderAndPronounIdsChanged_whenSaveDemographic_thenOldValuesArchivedProperly() {
    // Get the demographic record
    val demographic = demographicRepository.getByDemographicNumber(DEMOGRAPHIC_NO_VALUE);

    // Record the current values of genderId and pronounId
    Integer oldGenderId = demographic.getGenderId();
    Integer oldPronounId = demographic.getPronounId();

    demographic.setGenderId(oldGenderId == null ? 1 : oldGenderId + 1); // Change genderId
    demographic.setPronounId(oldPronounId == null ? 1 : oldPronounId + 1); // Change pronounId

    // Save the demographic record
    demographicRepositoryService.saveDemographic(demographic);

    // Verify that the old values are properly archived
    assertGenderAndPronounIdsArchivedProperly(DEMOGRAPHIC_NO_VALUE, oldGenderId, oldPronounId);
  }

  /* HELPERS */

  private void assertDemographicArchivedProperly(Integer demographicNo) {
    Pageable pageable = new PageRequest(0, 10);
    Page<DemographicArchive> archives =
        demographicArchiveRepository.getByDemographicNumber(demographicNo, pageable);
    DemographicArchive archivedDemographic = archives.getContent().get(0);
    List<DemographicExt> extensions = demographicExtRepository.getAllByDemographicNumber(demographicNo);
    val archivedExtensions =
        demographicExtArchiveRepository.getByArchiveId(archivedDemographic.getId());
    Assert.assertEquals(1, archives.getTotalElements());
    Assert.assertEquals(extensions.size(), archivedExtensions.size());
    Assert.assertEquals(OLD_CITY, archivedDemographic.getCity());
  }

  private void assertDemographicSavedAndSkippedArchiving(Demographic demographic) {
    Integer demographicNumber = demographic.getDemographicNumber();
    Pageable pageable = new PageRequest(0, 10);
    Page<DemographicArchive> archives =
        demographicArchiveRepository.getByDemographicNumber(demographicNumber, pageable);
    Assert.assertEquals(0, archives.getTotalElements());
  }

  private void assertGenderAndPronounIdsArchivedProperly(Integer demographicNo, Integer expectedGenderId, Integer expectedPronounId) {
    Pageable pageable = new PageRequest(0, 10);
    Page<DemographicArchive> archives =
        demographicArchiveRepository.getByDemographicNumber(demographicNo, pageable);
    Assert.assertEquals(1, archives.getTotalElements());

    DemographicArchive archivedDemographic = archives.getContent().get(0);
    Assert.assertEquals(expectedGenderId, archivedDemographic.getGenderId());
    Assert.assertEquals(expectedPronounId, archivedDemographic.getPronounId());
  }

  private void processDemographicExtSaving(
      List<DemographicExt> extensions,
      List<DemographicExt> activeExtensions,
      List<DemographicExtArchive> archivedExtensions,
      boolean skipArchiving
  ) {
    val demographicNumber = 1;
    val demographic = demographicRepository.getByDemographicNumber(demographicNumber);
    extensions.addAll(demographicExtRepository.getAllByDemographicNumber(demographicNumber));

    val extension = new DemographicExt();
    extension.setDemographicNumber(1);
    extension.setProviderNo("999998");
    extension.setKey("UUID");
    extension.setValue(UUID.randomUUID().toString());
    extension.setHidden('0');

    demographicRepositoryService.saveDemographicExtension(demographic, extension, skipArchiving);
    activeExtensions.addAll(demographicExtRepository.getAllByDemographicNumber(demographicNumber));
    archivedExtensions.addAll(
        demographicExtArchiveRepository.getByDemographicNumber(demographicNumber)
    );
  }
}
