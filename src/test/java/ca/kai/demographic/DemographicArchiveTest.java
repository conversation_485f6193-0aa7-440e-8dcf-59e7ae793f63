package ca.kai.demographic;

import static org.junit.Assert.*;

import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import lombok.val;
import lombok.var;
import org.junit.Test;

public class DemographicArchiveTest {

  @Test
  public void testDemographicArchiveConstructor() throws IllegalAccessException {
    val demographic = new Demographic();
    // Set demographic fields...
    val demographicClass = Demographic.class;
    val fields = demographicClass.getDeclaredFields();
    val ignoredFields = new HashSet<>(Arrays.asList("extensions", "gender", "pronoun"));
    for (var field : fields) {
      field.setAccessible(true);
      val fieldName = field.getName();
      Object value;
      // Generate value based on field type
      if (field.getType().equals(String.class)) {
        value = fieldName;
      } else if (field.getType().equals(int.class) || field.getType().equals(Integer.class)) {
        value = 1;
      } else if (field.getType().equals(boolean.class) || field.getType().equals(Boolean.class)) {
        value = true;
      } else if (field.getType().equals(Date.class)) {
        value = new Date();
      } else if (field.getType().equals(DemographicGender.class)) {
        val demographicGender = new DemographicGender();
        demographicGender.setId(1);
        demographicGender.setValue("Male");
        demographicGender.setEditable(false);
        value = demographicGender;
      } else if (field.getType().equals(DemographicPronoun.class)) {
        val demographicPronoun = new DemographicPronoun();
        demographicPronoun.setId(1);
        demographicPronoun.setValue("She/Her");
        demographicPronoun.setEditable(false);
        value = demographicPronoun;
      } else {
        value = null; // Set to null for any other types
      }
      // Set the generated value using the setter method for demographic
      val setterMethodName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
      try {
        val setterMethod = demographicClass.getMethod(setterMethodName, field.getType());
        setterMethod.invoke(demographic, value);
      } catch (NoSuchMethodException e) {
        // Handle the case when the setter method is not found
        System.err.println("Setter method not found for field: " + fieldName);
      } catch (InvocationTargetException e) {
        // Handle the case when the setter method invocation fails
        System.err.println("Error invoking setter method for field: " + fieldName);
      }
    }

    // Set genderId
    Integer expectedGenderId = 2;
    demographic.setGenderId(expectedGenderId);

    // Set pronounId
    Integer expectedPronounId = 3;
    demographic.setPronounId(expectedPronounId);

    val demographicArchive = new DemographicArchive(demographic);

    assertEquals(expectedGenderId, demographicArchive.getGenderId());
    assertEquals(expectedPronounId, demographicArchive.getPronounId());

    // Loop through fields of demographic and demographicArchive for assertions
    for (var field : fields) {
      field.setAccessible(true);
      val fieldName = field.getName();
      if (ignoredFields.contains(fieldName)) {
        continue; // Skip extensions field
      }
      if (fieldName.equals("lastUpdateDate")) {
        assertTrue(demographicArchive.getLastUpdateDate().getTime() >= demographic.getLastUpdateDate().getTime());
        continue;
      }
      try {
        val getterMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        val getterMethod = demographicArchive.getClass().getMethod(getterMethodName);
        val demographicValue = field.get(demographic);
        val demographicArchiveValue = getterMethod.invoke(demographicArchive);
        assertEquals(demographicValue, demographicArchiveValue);
      } catch (IllegalAccessException e) {
        // Handle the case when accessing field values fails
        System.err.println("Error accessing field value for field: " + fieldName);
      } catch (NoSuchMethodException | InvocationTargetException e) {
        // Handle the case when the getter method is not found or invocation fails
        System.err.println("Error invoking getter method for field: " + fieldName);
      }
    }
  }
}
