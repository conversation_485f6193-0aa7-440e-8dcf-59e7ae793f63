package ca.kai.hrm;

import ca.kai.OscarProperties;
import ca.kai.document.DocumentService;
import ca.kai.hrm.comment.HRMComment;
import ca.kai.hrm.comment.HRMCommentRepository;
import ca.kai.hrm.subClass.HRMSubClassRepository;
import ca.kai.hrm.util.HRMPDFCreator;
import ca.kai.incomingLabRules.IncomingLabRuleRepository;
import ca.kai.incomingLabRules.IncomingLabRuleService;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderRepository;
import ca.kai.inbox.InboxItem;
import ca.kai.inbox.InboxItemPK;
import ca.kai.providerLabRouting.ProviderHRMRouting;
import ca.kai.providerLabRouting.ProviderHRMRoutingRepository;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import lombok.val;
import lombok.var;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.util.ReflectionTestUtils;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Parameterized.class)
public class HRMServiceTest {

  @Mock ProviderHRMRoutingRepository providerHrmRoutingRepository;
  @Mock IncomingLabRuleService incomingLabRuleService;
  @Mock ProviderRepository providerRepository;
  @Mock DocumentService documentService;
  @Mock HRMCommentRepository hrmCommentRepository;
  @Mock HRMDocumentRepository hrmDocumentRepository;
  @Mock HRMSubClassRepository hrmSubClassRepository;
  @Mock IncomingLabRuleRepository incomingLabRuleRepository;
  @Mock HRMPDFCreator hrmPdfCreator;
  @Mock OscarProperties oscarProperties;

  private static final String UNCLAIMED_PROVIDER_NUMBER = "-1";
  private static final String PROVIDER_NUMBER = "1";
  private static final Integer HRM_DOCUMENT_ID = 123;

  private HRMService hrmService = new HRMService(
      providerHrmRoutingRepository,
      incomingLabRuleService,
      providerRepository,
      documentService,
      hrmCommentRepository,
      hrmDocumentRepository,
      hrmSubClassRepository,
      incomingLabRuleRepository,
      hrmPdfCreator,
      oscarProperties
  );
  private static InboxItem inboxItem = new InboxItem();
  private String inboxItemStatus;
  private ProviderHRMRouting providerHRMRouting;

  public HRMServiceTest(final String inboxItemStatus, final ProviderHRMRouting providerHRMRouting) {
    this.inboxItemStatus = inboxItemStatus;
    this.providerHRMRouting = providerHRMRouting;
  }

  @Parameterized.Parameters
  public static Collection parameters() {
    val inboxItemPK = new InboxItemPK(HRM_DOCUMENT_ID, "HRM");
    inboxItem.setId(inboxItemPK);
    val archivedHRMRouting = new ProviderHRMRouting(PROVIDER_NUMBER, HRM_DOCUMENT_ID);
    archivedHRMRouting.setDeleted(true);
    val filedHRMRouting = new ProviderHRMRouting(PROVIDER_NUMBER, HRM_DOCUMENT_ID);
    filedHRMRouting.setFiled(true);
    val acknowledgedHRMRouting = new ProviderHRMRouting(PROVIDER_NUMBER, HRM_DOCUMENT_ID);
    acknowledgedHRMRouting.setSignedOff(1);

    return Arrays.asList(new Object[][]{
        {"X", archivedHRMRouting},
        {"F", filedHRMRouting},
        {"A", acknowledgedHRMRouting}
    });
  }

  @Before
  public void before() {
    MockitoAnnotations.openMocks(this);

    ReflectionTestUtils.setField(hrmService, "providerHrmRoutingRepository", providerHrmRoutingRepository);
    ReflectionTestUtils.setField(
        hrmService,
        "hrmCommentRepository",
        hrmCommentRepository
    );
    ReflectionTestUtils.setField(hrmService, "hrmDocumentRepository", hrmDocumentRepository);
  }

  @Test
  public void removeProvider_noActiveLinkedProviders() {
    val providerHrmRouting = new ProviderHRMRouting(PROVIDER_NUMBER, HRM_DOCUMENT_ID);
    val argument = ArgumentCaptor.forClass(ProviderHRMRouting.class);
    Mockito.when(providerHrmRoutingRepository.getFirstByHrmDocumentIdAndDeleted(
        HRM_DOCUMENT_ID, false)).thenReturn(null);
    hrmService.removeProvider(providerHrmRouting);
    Mockito.verify(providerHrmRoutingRepository, Mockito.times(2)).save(argument.capture());

    // updated entry to deleted status
    assertProviderHrmRouting(argument.getAllValues().get(0),
        HRM_DOCUMENT_ID, PROVIDER_NUMBER, true);

    // generated unclaimed entry
    assertProviderHrmRouting(argument.getAllValues().get(1),
        HRM_DOCUMENT_ID, UNCLAIMED_PROVIDER_NUMBER, false);

  }

  @Test
  public void removeProvider_existingLinkedProvider() {
    val providerHrmRouting = new ProviderHRMRouting(PROVIDER_NUMBER, HRM_DOCUMENT_ID);
    val argument = ArgumentCaptor.forClass(ProviderHRMRouting.class);
    Mockito.when(providerHrmRoutingRepository.getFirstByHrmDocumentIdAndDeleted(
        HRM_DOCUMENT_ID, false)).thenReturn(new ProviderHRMRouting());
    hrmService.removeProvider(providerHrmRouting);
    Mockito.verify(providerHrmRoutingRepository, Mockito.times(1)).save(argument.capture());

    // updated entry to deleted status
    assertProviderHrmRouting(argument.getValue(),
        HRM_DOCUMENT_ID, PROVIDER_NUMBER, true);
  }

  @Test
  public void removeProvider_unmatchedProvider() {
    val provider = new Provider();
    provider.setLastName("oscardoc");
    provider.setFirstName("doctor");
    val providerHrmRouting = new ProviderHRMRouting(null, HRM_DOCUMENT_ID);
    providerHrmRouting.setProvider(provider);
    val hrmDocument = new HRMDocument();
    hrmDocument.setId(123);
    hrmDocument.setUnmatchedProviders("|oscardoc, doctor");
    Mockito.when(providerHrmRoutingRepository.getFirstByHrmDocumentIdAndDeleted(
        HRM_DOCUMENT_ID, false)).thenReturn(new ProviderHRMRouting());
    Mockito.when(hrmDocumentRepository.findById(HRM_DOCUMENT_ID))
        .thenReturn(hrmDocument);
    hrmService.removeProvider(providerHrmRouting);
    //Assert that the unmatched provider has been removed from the document
    Assert.assertEquals("", hrmDocument.getUnmatchedProviders());
  }

  @Test
  public void removeProvider_unmatchedProviderNoLastName() {
    val provider = new Provider();
    provider.setLastName("");
    provider.setFirstName("doctor");
    val providerHrmRouting = new ProviderHRMRouting(null, HRM_DOCUMENT_ID);
    providerHrmRouting.setProvider(provider);
    val hrmDocument = new HRMDocument();
    hrmDocument.setId(123);
    hrmDocument.setUnmatchedProviders("|, doctor");
    Mockito.when(providerHrmRoutingRepository.getFirstByHrmDocumentIdAndDeleted(
        HRM_DOCUMENT_ID, false)).thenReturn(new ProviderHRMRouting());
    Mockito.when(hrmDocumentRepository.findById(HRM_DOCUMENT_ID))
        .thenReturn(hrmDocument);
    hrmService.removeProvider(providerHrmRouting);
    //Assert that the unmatched provider has been removed from the document
    Assert.assertEquals("", hrmDocument.getUnmatchedProviders());
  }

  @Test
  public void removeProvider_unmatchedProviderNoFirstName() {
    val provider = new Provider();
    provider.setLastName("oscardoc");
    provider.setFirstName("");
    val providerHrmRouting = new ProviderHRMRouting(null, HRM_DOCUMENT_ID);
    providerHrmRouting.setProvider(provider);
    val hrmDocument = new HRMDocument();
    hrmDocument.setId(123);
    hrmDocument.setUnmatchedProviders("|oscardoc,");
    Mockito.when(providerHrmRoutingRepository.getFirstByHrmDocumentIdAndDeleted(
        HRM_DOCUMENT_ID, false)).thenReturn(new ProviderHRMRouting());
    Mockito.when(hrmDocumentRepository.findById(HRM_DOCUMENT_ID))
        .thenReturn(hrmDocument);
    hrmService.removeProvider(providerHrmRouting);
    //Assert that the unmatched provider has been removed from the document
    Assert.assertEquals("", hrmDocument.getUnmatchedProviders());
  }

  @Test
  public void hrmStatusIsUpdated() {
    providerHrmRoutingRepository.save(this.providerHRMRouting);
    Mockito.when(providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(HRM_DOCUMENT_ID, PROVIDER_NUMBER))
        .thenReturn(this.providerHRMRouting);

    hrmService.updateHrmInboxItemsStatus(Arrays.asList(inboxItem), PROVIDER_NUMBER, this.inboxItemStatus);
    Assert.assertEquals(this.inboxItemStatus, inboxItem.getStatus());
  }

  @Test
  public void hrmStatusIsUpdated_allProvidersAllStatusSearch() {
    val providerHrmRouting = new ProviderHRMRouting();
    val allHrmRoutingList = new ArrayList<ProviderHRMRouting>();
    allHrmRoutingList.add(this.providerHRMRouting);
    Mockito.when(providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(HRM_DOCUMENT_ID, PROVIDER_NUMBER))
        .thenReturn(providerHrmRouting);
    Mockito.when(providerHrmRoutingRepository.getRoutesByHrmDocumentId(any())).thenReturn(allHrmRoutingList);

    hrmService.updateHrmInboxItemsStatus(Arrays.asList(inboxItem), "", "L");
    Assert.assertEquals(this.inboxItemStatus, inboxItem.getStatus());
  }

  @Test
  public void hrmStatusIsUpdated_allProvidersCustomStatusSearch() {
    val providerHrmRouting = new ProviderHRMRouting();
    val allHrmRoutingList = new ArrayList<ProviderHRMRouting>();
    allHrmRoutingList.add(this.providerHRMRouting);
    Mockito.when(providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(HRM_DOCUMENT_ID, PROVIDER_NUMBER))
        .thenReturn(providerHrmRouting);
    Mockito.when(providerHrmRoutingRepository.getRoutesByHrmDocumentIdAndStatus(any(), anyInt(), anyBoolean(), anyBoolean()))
        .thenReturn(allHrmRoutingList);

    hrmService.updateHrmInboxItemsStatus(Arrays.asList(inboxItem), "", this.inboxItemStatus);
    Assert.assertEquals(this.inboxItemStatus, inboxItem.getStatus());
  }

  @Test
  public void routeProvider_noLinkedProvider() {
    val unclaimedEntry = new ProviderHRMRouting(UNCLAIMED_PROVIDER_NUMBER, HRM_DOCUMENT_ID);
    unclaimedEntry.setId(1);
    Mockito.when(providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(any(), any()))
        .thenReturn(unclaimedEntry, null);

    val savedRouting = hrmService.routeProvider(new ProviderHRMRouting(PROVIDER_NUMBER, HRM_DOCUMENT_ID));
    Assert.assertEquals((Integer) 1, savedRouting.getId());
  }

  @Test
  public void routeProvider_providerWithPreviousRouting() {
    val previousEntry = new ProviderHRMRouting(PROVIDER_NUMBER, HRM_DOCUMENT_ID);
    previousEntry.setId(1);
    previousEntry.setDeleted(true);
    Mockito.when(providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(any(), any()))
        .thenReturn(new ProviderHRMRouting(), previousEntry);

    val savedRouting = hrmService.routeProvider(new ProviderHRMRouting(PROVIDER_NUMBER, HRM_DOCUMENT_ID));
    Assert.assertFalse(savedRouting.getDeleted());
  }

  private void assertProviderHrmRouting(
      final ProviderHRMRouting providerHRMRouting,
      final Integer hrmDocumentId,
      final String providerNo,
      final boolean isDeleted
  ) {
    Assert.assertEquals(hrmDocumentId, providerHRMRouting.getHrmDocumentId());
    Assert.assertEquals(providerNo, providerHRMRouting.getProviderNo());
    Assert.assertEquals(isDeleted, providerHRMRouting.getDeleted());
    Assert.assertNull(providerHRMRouting.getSignedOffTimestamp());
    Assert.assertEquals((Integer) 0, providerHRMRouting.getSignedOff());
    Assert.assertFalse(providerHRMRouting.isFiled());
  }

  @Test
  public void updateComment() {
    var hrmCommentOld = new HRMComment();
    hrmCommentOld.setDeleted(false);
    val argument = ArgumentCaptor.forClass(HRMComment.class);
    Mockito.when(hrmCommentRepository.save(hrmCommentOld)).thenReturn(hrmCommentOld);
    var hrmCommentNew = hrmService.updateComment(hrmCommentOld);
    Mockito.verify(hrmCommentRepository, Mockito.times(1)).save(argument.capture());
    // test method returning same comment as its input
    Assert.assertEquals(hrmCommentNew, hrmCommentOld);
    // test save method is called and saving the same comment as input
    Assert.assertEquals(argument.getValue(), hrmCommentOld);
  }

  @Test
  public void processSubclassDescription(){


    HRMDocumentSubClass hrmDocumentSubClassCT = new HRMDocumentSubClass();
    hrmDocumentSubClassCT.setId(1);
    hrmDocumentSubClassCT.setSubClass("CT");
    hrmDocumentSubClassCT.setSubClassDescription("CT ABDOMEN WITHOUT CONTRAST");

    HRMDocumentSubClass hrmDocumentSubClassXRAY = new HRMDocumentSubClass();
    hrmDocumentSubClassXRAY.setId(2);
    hrmDocumentSubClassXRAY.setSubClass("XRAY");
    hrmDocumentSubClassXRAY.setSubClassDescription("X224_Knee (3 to 4 views)[L]");

    HRMDocumentSubClass hrmDocumentSubClassCRDCN4 = new HRMDocumentSubClass();
    hrmDocumentSubClassCRDCN4.setId(3);
    hrmDocumentSubClassCRDCN4.setSubClass("CRDCN4^CRD Consult Note");

    HRMDocumentSubClass hrmDocumentSubClassRAD = new HRMDocumentSubClass();
    hrmDocumentSubClassRAD.setId(4);
    hrmDocumentSubClassRAD.setSubClass("RAD");
    hrmDocumentSubClassRAD.setSubClassDescription("Radiology studies (set)");

    List<HRMDocumentSubClass> list = new ArrayList<HRMDocumentSubClass>();
    list.add(hrmDocumentSubClassCT);

    Assert.assertEquals("CT ABDOMEN WITHOUT CONTRAST",hrmService.getSubClassDisplayString(list));
    list.clear();

    list.add(hrmDocumentSubClassXRAY);
    Assert.assertEquals("X224_Knee (3 to 4 views)[L]",hrmService.getSubClassDisplayString(list));
    list.clear();

    list.add(hrmDocumentSubClassCRDCN4);
    Assert.assertEquals("CRD Consult Note",hrmService.getSubClassDisplayString(list));
    list.clear();

    list.add(hrmDocumentSubClassRAD);
    Assert.assertEquals("Radiology studies (set)",hrmService.getSubClassDisplayString(list));

  }

}
