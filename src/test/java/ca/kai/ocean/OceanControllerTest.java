package ca.kai.ocean;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import ca.kai.OscarProperties;
import ca.kai.applicationcontext.ApplicationContextProvider;
import ca.kai.systemPreference.SystemPreferenceService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

@ExtendWith(MockitoExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
public class OceanControllerTest {

  @Mock OscarProperties oscarProperties;
  @Mock SystemPreferenceService systemPreferenceService;
  @Mock RestTemplate restTemplate;

  private OceanController oceanController;

  protected static MockedStatic<ApplicationContextProvider> mockedApplicationContextProvider;

  @BeforeAll
  public static void baseInit() {
    mockedApplicationContextProvider = mockStatic(ApplicationContextProvider.class);
  }

  @BeforeEach
  public void beforeEach() {
    mockedApplicationContextProvider
        .when(() -> ApplicationContextProvider.getBean(OscarProperties.class))
        .thenReturn(oscarProperties);
    oceanController = new OceanController(restTemplate, systemPreferenceService);
    oceanController.postConstruct();
  }

  @AfterAll
  public static void baseClose() {
    mockedApplicationContextProvider.close();
  }

  @Test
  public void givenSystemPreferenceIsOcean_whenIsOceanEnabled_thenReturnTrue() {
    when(systemPreferenceService.readString("echart_toolbar_type", "")).thenReturn("ocean");
    assertTrue(oceanController.isOceanEnabled());
  }

  @Test
  public void givenSystemPreferenceIsNotOcean_whenIsOceanEnabled_thenReturnFalse() {
    when(systemPreferenceService.readString("echart_toolbar_type", "")).thenReturn("not ocean");
    assertFalse(oceanController.isOceanEnabled());
  }

  @Test
  public void givenSystemPreferenceIsEmpty_andPropertyIsOcean_whenIsOceanEnabled_thenReturnTrue() {
    when(systemPreferenceService.readString("echart_toolbar_type", "")).thenReturn("");
    when(oscarProperties.getProperty("cme_js", "")).thenReturn("ocean");
    assertTrue(oceanController.isOceanEnabled());
  }

  @Test
  public void givenSystemPreferenceIsEmpty_andPropertyIsNotOcean_whenIsOceanEnabled_thenReturnFalse() {
    when(systemPreferenceService.readString("echart_toolbar_type", "")).thenReturn("");
    when(oscarProperties.getProperty("cme_js", "")).thenReturn("not ocean");
    assertFalse(oceanController.isOceanEnabled());
  }
}
