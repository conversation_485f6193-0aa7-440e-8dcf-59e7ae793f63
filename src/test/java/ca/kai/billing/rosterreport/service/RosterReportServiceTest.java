/*
 * Copyright (c) 2024 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package ca.kai.billing.rosterreport.service;

import static com.helger.commons.mock.CommonsAssert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import ca.kai.OscarProperties;
import ca.kai.authentication.AuthenticationService;
import ca.kai.billing.rosterreport.exception.RosterReportException;
import ca.kai.billing.rosterreport.model.RosterPatientData;
import ca.kai.billing.rosterreport.model.RosterReport;
import ca.kai.demographic.Demographic;
import ca.kai.demographic.DemographicExt;
import ca.kai.demographic.DemographicExtRepository;
import ca.kai.demographic.DemographicRepository;
import ca.kai.demographic.DemographicRepositoryService;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderRepository;
import ca.kai.util.DateUtil;
import ca.kai.util.UnauthorizedException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import lombok.val;
import lombok.var;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(value = MockitoExtension.class)
public class RosterReportServiceTest {

  private static final String PROVIDER_NUMBER = "providerNo";
  private static final long MILLISECONDS_IN_A_DAY = 86400000L;
  private static final String TEST_OHIP_NUMBER = "123456";
  private static final String TEST_PROVIDER_NUMBER = "999998";
  private static final String TEST_DATE = "2024-01-01";
  private static final String OLD_DATE = "2020-01-01";
  private static final String TEST_TERMINATION_CODE = "TC";
  private static final String TEST_REASON_CODE = "RC";
  private static final String TEST_FOLDER_PATH = "/test/path";
  private static final String TEST_FILE_NAME = "testFile.xml";

  @Mock
  RosterReport report;
  @Mock
  HttpServletRequest request;
  @Mock
  ProviderRepository providerRepository;
  @Mock
  RosterReportService rosterReportService;
  @Mock
  Provider provider;
  @Mock
  DemographicRepositoryService demographicRepositoryService;
  @Mock
  OscarProperties oscarProperties;
  @Mock
  DemographicRepository demographicRepository;
  @Mock
  DemographicExtRepository demographicExtRepository;
  private MockedStatic<AuthenticationService> authenticationService;

  @BeforeEach
  public void before() {
    authenticationService = Mockito.mockStatic(AuthenticationService.class);
  }

  @AfterEach
  public void after() {
    authenticationService.close();
  }

  @Test
  public void givenValidInputs_whenGetRosterReport_thenReturnsRosterReport() throws Exception {
    val provider = createProvider();
    when(AuthenticationService.getAuthenticatedProviderFromRequest(request)).thenReturn(provider);

    doReturn(report).when(rosterReportService)
        .getRosterReport(any(), any(), any());
    RosterReport result = rosterReportService.getRosterReport(TEST_FOLDER_PATH, TEST_FILE_NAME,
        request);

    assertNotNull(result);
    assertSame(report, result);
  }

  @Test
  public void givenUnprocessedReport_whenCheckAndUpdateProvider_thenReturnsTrue() {
    when(provider.getOnMohLastRosterReportDate()).thenReturn(null);
    when(report.getReportDate()).thenReturn(new Date());
    when(providerRepository.save(any(Provider.class))).thenReturn(provider);

    val result = createTestRosterReportService().
        checkIfRosterReportIsUnprocessedAndUpdateProvider
            (
                provider,
                report
            );

    assertTrue(result);
    verify(provider).setOnMohLastRosterReportDate(report.getReportDate());
    verify(providerRepository).save(provider);
  }

  @Test
  public void givenPatientWithAllFieldsNotEmpty_whenIsRosterStatusTerminated_thenReturnsTrue() {
    var patient = new RosterPatientData();
    patient.setRosterEnd(TEST_DATE);
    patient.setTerminationCode(TEST_TERMINATION_CODE);

    assertTrue(createTestRosterReportService().isRosterStatusTerminated(patient));
  }

  @Test
  public void givenPatientWithRosterEndNotEmpty_whenIsRosterStatusTerminated_thenReturnsTrue() {
    var patient = new RosterPatientData();
    patient.setRosterEnd(TEST_DATE);

    assertTrue(createTestRosterReportService().isRosterStatusTerminated(patient));
  }

  @Test
  public void givenTerminationCodeNotEmpty_whenIsRosterStatusTerminated_returnsTrue() {
    var patient = new RosterPatientData();
    patient.setTerminationCode(TEST_TERMINATION_CODE);

    assertTrue(createTestRosterReportService().isRosterStatusTerminated(patient));
  }

  @Test
  public void givenPatientWithReasonCodeNotEmpty_whenIsRosterStatusTerminated_thenReturnsFalse() {
    var patient = new RosterPatientData();
    patient.setReasonCode(TEST_REASON_CODE);

    assertFalse(createTestRosterReportService().isRosterStatusTerminated(patient));
  }

  @Test
  public void givenPatientWithAllFieldsEmpty_whenIsRosterStatusTerminated_thenReturnsFalse() {
    val patient = new RosterPatientData();

    assertFalse(createTestRosterReportService().isRosterStatusTerminated(patient));
  }

  @Test
  public void givenPatientRosterDateAfterProviderLastRosterDate_whenIsNewPatient_thenReturnsTrue() {
    val patientRosterDate = new Date();
    val providerLastRosterDate = new Date(
        patientRosterDate.getTime() - MILLISECONDS_IN_A_DAY);

    assertTrue(createTestRosterReportService().isNewPatientRosterReport(patientRosterDate,
        providerLastRosterDate));
  }

  @Test
  public void givenPatientRosterDateBeforeProviderDate_whenIsNewPatient_thenReturnsFalse() {
    val patientRosterDate = new Date();
    val providerLastRosterDate = new Date(
        patientRosterDate.getTime() + MILLISECONDS_IN_A_DAY);

    assertFalse(createTestRosterReportService().isNewPatientRosterReport(patientRosterDate,
        providerLastRosterDate));
  }

  @Test
  public void givenPatientRosterDateSameAsProviderDate_whenIsNewPatient_thenReturnsTrue() {
    val patientRosterDate = new Date();
    val providerLastRosterDate = new Date(patientRosterDate.getTime());

    assertTrue(createTestRosterReportService().isNewPatientRosterReport(patientRosterDate,
        providerLastRosterDate));
  }

  @Test
  public void givenProviderLastRosterDateIsNull_whenIsNewPatientRosterReport_thenReturnsTrue() {
    val patientRosterDate = new Date();

    assertTrue(createTestRosterReportService().isNewPatientRosterReport(patientRosterDate, null));
  }

  @Test
  public void givenExistingOhipNumber_whenGetProviderFromRepository_thenReturnsProvider()
      throws RosterReportException {
    val ohipNumber = TEST_OHIP_NUMBER;
    var expectedProvider = new Provider();
    expectedProvider.setOhipNo(ohipNumber);

    when(providerRepository.findFirstByOhipNo(ohipNumber)).thenReturn(expectedProvider);

    Provider actualProvider = createTestRosterReportService().
        getFirstProviderFromRepositoryByOhipNumber(ohipNumber);

    assertNotNull(actualProvider);
    assertEquals(expectedProvider, actualProvider);
  }

  @Test
  public void givenNonExistingOhipNumber_whenGetProviderFromRepository_thenThrowsException() {
    val ohipNumber = TEST_OHIP_NUMBER;

    when(providerRepository.findFirstByOhipNo(ohipNumber)).thenReturn(null);

    assertThrows(RosterReportException.class,
        () -> createTestRosterReportService().getFirstProviderFromRepositoryByOhipNumber(
            ohipNumber));
  }

  @Test
  public void givenRequest_whenGetLoggedInProviderFromRequestCalled_thenProviderIsReturned() {
    var expectedProvider = new Provider();
    expectedProvider.setProviderNo(TEST_PROVIDER_NUMBER);

    when(AuthenticationService.getAuthenticatedProviderFromRequest(request)).thenReturn(
        expectedProvider);

    val rosterReportService = createTestRosterReportService();

    Provider actualProvider = rosterReportService.getLoggedInProviderFromRequest(request);

    assertEquals(expectedProvider, actualProvider);
  }

  @Test
  public void givenRequestWithoutLoggedInProvider_whenGetLoggedInProvider_thenThrowsException() {
    when(AuthenticationService.getAuthenticatedProviderFromRequest(request)).thenReturn(null);

    val rosterReportService = createTestRosterReportService();

    assertThrows(UnauthorizedException.class,
        () -> rosterReportService.getLoggedInProviderFromRequest(request));
  }

  @Test
  public void givenNewPatient_whenAddPatientToList_thenAddedToNewlyRosteredList() {
    var patient = new RosterPatientData();
    patient.setRosterStart(TEST_DATE);

    var provider = createProvider();
    provider.setOnMohLastRosterReportDate(DateUtil.parseDateOrReturnNull(OLD_DATE));

    var report = new RosterReport();
    report.setProvider(provider);

    createTestRosterReportService().addPatientToList(patient, report);

    assertTrue(report.getNewlyRosteredDemographics().contains(patient));
  }

  @Test
  public void givenTerminatedPatient_whenAddPatientToList_thenAddedToTerminatedList() {
    var patient = new RosterPatientData();
    patient.setRosterEnd(TEST_DATE);
    patient.setTerminationCode(TEST_TERMINATION_CODE);

    val report = new RosterReport();

    createTestRosterReportService().addPatientToList(patient, report);

    assertTrue(report.getTerminatedDemographics().contains(patient));
  }

  @Test
  public void givenExistingPatient_whenAddPatientToList_thenAddedToPreviouslyRosteredList() {
    var patient = new RosterPatientData();
    patient.setRosterStart(OLD_DATE);

    var provider = createProvider();
    provider.setOnMohLastRosterReportDate(DateUtil.parseDateOrReturnNull(TEST_DATE));

    var report = new RosterReport();
    report.setProvider(provider);

    createTestRosterReportService().addPatientToList(patient, report);

    assertTrue(report.getPreviouslyRosteredDemographics().contains(patient));
  }

  @Test
  public void givenPatientData_whenUpdateDemographicData_thenDemographicDataIsUpdated() {
    var patient = new RosterPatientData();
    patient.setRosterStart("2024-01-01");
    patient.setRosterEnd("2025-01-01");
    patient.setTerminationCode("TC");
    patient.setReasonCode("RC");

    var demographic = new Demographic();
    demographic.setRosterStatus("RO");

    var report = new RosterReport();
    report.setLoggedInUser("testUser");
    report.setProvider(createProvider());

    createTestRosterReportService().updateDemographicData(demographic, patient, report.getLoggedInUser());

    assertEquals(DateUtil.parseDateOrReturnNull(patient.getRosterStart()),
        demographic.getRosterDate());
    assertEquals(DateUtil.parseDateOrReturnNull(patient.getRosterEnd()),
        demographic.getRosterTerminationDate());
    assertEquals(patient.getTerminationCode(), demographic.getRosterTerminationReason());
    assertEquals(report.getLoggedInUser(), demographic.getLastUpdateUser());
    assertNotNull(demographic.getLastUpdateDate());
    assertEquals("TE", demographic.getRosterStatus());
  }

  private RosterReportService createTestRosterReportService() {
    val realRosterReportService = new RosterReportService(
        demographicRepository,
        providerRepository,
        oscarProperties,
        demographicRepositoryService,
        demographicExtRepository
    );

    return spy(realRosterReportService);
  }

  private Provider createProvider() {
    val provider = new Provider();
    provider.setProviderNo(PROVIDER_NUMBER);
    return provider;
  }
}

@ExtendWith(value = MockitoExtension.class)
class RosterReportServiceReconcileDemographicsTest {

  @Mock
  DemographicRepository demographicRepository;
  @Mock
  DemographicExtRepository demographicExtRepository;
  @Mock
  ProviderRepository providerRepository;
  @Mock
  DemographicRepositoryService demographicRepositoryService;
  @Mock
  OscarProperties oscarProperties;

  List<Demographic> demographics;
  List<RosterPatientData> patientData;
  RosterReportService rosterReportService;

  @BeforeEach
  void before() {
    // Simulate patient records from an RCX report and the corresponding demographics in the database

    patientData = new ArrayList<>();
    demographics = new ArrayList<>();
    when(demographicRepository.getByHinIn(any(List.class)))
        .thenReturn(demographics);

    for (int i = 0; i < 3; i++) {
      val patient = new RosterPatientData();
      patient.setRosterStart("2024-0"+(i+1)+"-0"+(i+1));
      patient.setRosterEnd("");
      patient.setHealthNumber("123456789" + i);
      patientData.add(patient);
      val demographic = new Demographic();
      demographic.setDemographicNumber(1000 + i);
      demographic.setHin(patient.getHealthNumber());
      demographics.add(demographic);
    }
    rosterReportService = new RosterReportService(
        demographicRepository,
        providerRepository,
        oscarProperties,
        demographicRepositoryService,
        demographicExtRepository
    );

  }

  @Test
  public void givenNoPatientHasEnrollmentProvider_whenReconcileDemographics_thenAllExtensionsCreated() {
    when(demographicExtRepository.getAllByKeyAndDemographicNumberIn(
        any(String.class), any(List.class)
    )).thenReturn(Collections.emptyList());

    rosterReportService.reconcileDemographics(patientData, "999998", "Dr. Coolstuff");

    val captor = ArgumentCaptor.forClass(Iterable.class);

    verify(demographicExtRepository).save(captor.capture());
    List<DemographicExt> savedExtensions = (List<DemographicExt>) captor.getValue();
    assertEquals(3, savedExtensions.size());

    // To prove that the extensions were created and not updated, show that they do not contain IDs
    for (DemographicExt ext: savedExtensions) {
      assertNull(ext.getId());
      assertEquals("enrollmentProvider", ext.getKey());
      assertEquals("999998", ext.getValue());
    }

  }

  @Test
  public void givenSomePatientsHaveEnrollmentProvider_whenReconcileDemographics_thenExtensionsCreatedAsNeeded() {

    // Simulate existing extension for demographic 1000
    val demographicExt = new DemographicExt("enrollmentProvider", "oldEnrollmentProvider", new Date(), 1000, "999998", '0');
    demographicExt.setId(1);
    when(demographicExtRepository.getAllByKeyAndDemographicNumberIn(eq("enrollmentProvider"), any(List.class)))
        .thenReturn(Collections.singletonList(demographicExt));

    rosterReportService.reconcileDemographics(patientData, "999998", "Dr. Coolstuff");

    val captor = ArgumentCaptor.forClass(Iterable.class);

    verify(demographicExtRepository).save(captor.capture());
    List<DemographicExt> savedExtensions = (List<DemographicExt>) captor.getValue();
    assertEquals(3, savedExtensions.size());

    val expectedValues = new Integer[3][2];
    expectedValues[0] = new Integer[]{1000, 1}; // Existing extension for demographic 1000
    expectedValues[1] = new Integer[]{1001, null}; // New extension for demographic 1001
    expectedValues[2] = new Integer[]{1002, null}; // New extension for demographic 1002
    
    for (int i = 0; i < savedExtensions.size(); i++) {
      DemographicExt ext = savedExtensions.get(i);
      assertEquals("enrollmentProvider", ext.getKey());
      assertEquals("999998", ext.getValue());
      assertEquals(expectedValues[i][0], ext.getDemographicNumber());
      assertEquals(expectedValues[i][1], ext.getId());
    }
  }

  @Test
  public void givenAllPatientsHaveEnrollmentProvider_whenReconcileDemographics_thenNoExtensionsCreated() {

    val demographicExts = new ArrayList<DemographicExt>();
    for (int i = 0; i < 3; i++) {
      val demographicExt = new DemographicExt("enrollmentProvider", "oldEnrollmentProvider", new Date(), 1000 + i, "999998", '0');
      demographicExt.setId(i + 1);
      demographicExts.add(demographicExt);
    }
    when(demographicExtRepository.getAllByKeyAndDemographicNumberIn(eq("enrollmentProvider"), any(List.class)))
        .thenReturn(demographicExts);

    rosterReportService.reconcileDemographics(patientData, "999998", "Dr. Coolstuff");

    val captor = ArgumentCaptor.forClass(Iterable.class);

    verify(demographicExtRepository).save(captor.capture());
    List<DemographicExt> savedExtensions = (List<DemographicExt>) captor.getValue();
    assertEquals(3, savedExtensions.size());

    val expectedValues = new Integer[3][2];
    expectedValues[0] = new Integer[]{1000, 1};  // Existing extension for demographic 1000
    expectedValues[1] = new Integer[]{1001, 2};  // Existing extension for demographic 1001
    expectedValues[2] = new Integer[]{1002, 3};  // Existing extension for demographic 1002

    for (int i = 0; i < savedExtensions.size(); i++) {
      DemographicExt ext = savedExtensions.get(i);
      assertEquals("enrollmentProvider", ext.getKey());
      assertEquals("999998", ext.getValue());
      assertEquals(expectedValues[i][0], ext.getDemographicNumber());
      assertEquals(expectedValues[i][1], ext.getId());
    }

  }

  @Test
  public void givenDemographicsNeedsChange_whenReconcileDemographics_thenDemographicUpdated() {

    for (Demographic demographic : demographics) {
      assertNull(demographic.getRosterStatus());
      assertNull(demographic.getRosterDate());
      assertNull(demographic.getRosterTerminationDate());
      assertNull(demographic.getRosterTerminationReason());
      assertNull(demographic.getLastUpdateUser());
      assertNull(demographic.getLastUpdateDate());
    }

    rosterReportService.reconcileDemographics(patientData, "999998", "Dr. Coolstuff");

    for (Demographic demographic : demographics) {
      assertEquals("RO", demographic.getRosterStatus());
      assertNotNull(demographic.getRosterDate());
      assertNull(demographic.getRosterTerminationDate());
      assertNull(demographic.getRosterTerminationReason());
      assertEquals("Dr. Coolstuff", demographic.getLastUpdateUser());
      assertNotNull(demographic.getLastUpdateDate());
    }
  }
}