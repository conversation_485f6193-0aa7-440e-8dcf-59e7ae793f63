package ca.kai.attachmentmanager.attachmenttype;

import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

public class AttachmentTypeTest {

  @Test
  public void givenAttachableTypes_whenGetSearchableTypes_thenIncludeConsultations() {
    List<String> searchableTypes = Arrays.asList(AttachmentType.getSearchableTypes());
    assertTrue(searchableTypes.contains(AttachmentType.Consultations.toString()));
  }

}