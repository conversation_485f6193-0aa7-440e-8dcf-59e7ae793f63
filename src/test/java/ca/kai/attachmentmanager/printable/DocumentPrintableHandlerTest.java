package ca.kai.attachmentmanager.printable;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import ca.kai.OscarProperties;
import ca.kai.applicationcontext.ApplicationContextProvider;
import ca.kai.document.Document;
import ca.kai.document.DocumentRepository;
import ca.kai.document.DocumentService;
import java.util.Arrays;
import java.util.Date;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
public class DocumentPrintableHandlerTest extends PrintableHandlerTestBase {
  private final DocumentPrintableHandler documentPrintableHandler = new DocumentPrintableHandler();

  @Mock
  private OscarProperties oscarProperties;
  @Mock
  private DocumentRepository documentRepository;
  @Mock
  private DocumentService documentService;

  @BeforeEach
  public void beforeEach() {
    mockedApplicationContextProvider
        .when(() -> ApplicationContextProvider.getBean(OscarProperties.class))
        .thenReturn(oscarProperties);
    mockedApplicationContextProvider
        .when(() -> ApplicationContextProvider.getBean(DocumentRepository.class))
        .thenReturn(documentRepository);
    mockedApplicationContextProvider
        .when(() -> ApplicationContextProvider.getBean(DocumentService.class))
        .thenReturn(documentService);
    documentPrintableHandler.postConstruct();
  }

  @Test
  public void testSearch_success() {
    when(documentRepository.getActiveDocumentsByDemographicNo(1)).thenReturn(
        Arrays.asList(
            createDocument(1, "pdf"),
            createDocument(2, "html")));
    val results = documentPrintableHandler.search(1, "999998");
    val documentOne = results.stream()
        .filter(printable -> printable.getId().equals("1"))
        .findFirst()
        .orElse(null);
    val documentTwo = results.stream()
        .filter(printable -> printable.getId().equals("2"))
        .findFirst()
        .orElse(null);
    assertNotNull(documentOne);
    assertNotNull(documentTwo);
  }

  @Test
  public void testGetById_success() {
    when(documentRepository.getDocumentById(1)).thenReturn(createDocument(1, "pdf"));
    val printable = documentPrintableHandler.getById("1");
    assertNotNull(printable);
    assertEquals("1", printable.getId());
  }

  @Test
  public void testGetAttachments_success() {
    val attachments = documentPrintableHandler.getAttachments(new Printable());
    assertTrue(attachments.isEmpty());
  }

  @Test
  public void testGetPreviewUrl_success() {
    when(oscarProperties.getKaiemrDeployedContext()).thenReturn("kaiemr");
    val url = documentPrintableHandler.getPreviewUrl("1");
    assertEquals("/kaiemr/api/document/getAttachmentManagerPreview/1?pageCount=5", url);
  }

  private Document createDocument(final int id, final String fileExtension) {
    val document = new Document();
    document.setDocumentNo(id);
    document.setDocFileName("testdocument" + id);
    document.setDocDesc("Test document " + id);
    document.setObservationDate(new Date());
    document.setDocFileName("test" + id + "." + fileExtension);
    return document;
  }
}
