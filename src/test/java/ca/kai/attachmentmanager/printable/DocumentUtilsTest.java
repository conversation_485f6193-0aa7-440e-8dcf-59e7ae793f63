package ca.kai.attachmentmanager.printable;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import ca.kai.document.Document;
import lombok.val;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

public class DocumentUtilsTest {

  @ParameterizedTest
  @ValueSource(strings = {".pdf", ".jpg", ".jpeg", ".jpe", ".png", ".pdf.pdf"})
  public void givenSupportedExtension_whenIsSupported_thenReturnTrue(String extension) {
    val document = new Document();
    document.setDocFileName("docFileName" + extension);
    assertTrue(DocumentUtils.isSupported(document));
  }

  @ParameterizedTest
  @ValueSource(strings = {".PDF", ".JPG", ".JPEG", ".JPE", ".PNG", ".PDF.PDF"})
  public void givenSupportedUpperCaseExtension_whenIsSupported_thenReturnTrue(String extension) {
    val document = new Document();
    document.setDocFileName("docFileName" + extension);
    assertTrue(DocumentUtils.isSupported(document));
  }

  @ParameterizedTest
  @ValueSource(
      strings = {
        ".doc",
        ".docx",
        ".txt",
        ".xml",
        ".html",
        ".xls",
        ".xlsx",
        ".ppt",
        ".pptx",
        ".zip",
        ".rar",
        ".mp4",
        ".avi",
        ".mp3",
        ".wav",
        ".unsupported"
      })
  public void givenUnsupportedExtension_whenIsSupported_thenReturnFalse(String extension) {
    val document = new Document();
    document.setDocFileName("docFileName" + extension);
    assertFalse(DocumentUtils.isSupported(document));
  }

}
