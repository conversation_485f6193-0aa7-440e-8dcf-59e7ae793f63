package ca.kai.attachmentmanager.printable;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import ca.kai.OscarProperties;
import ca.kai.applicationcontext.ApplicationContextProvider;
import ca.kai.attachmentmanager.attachmenttype.AttachmentType;
import ca.kai.document.Document;
import ca.kai.document.DocumentRepository;
import ca.kai.document.DocumentService;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
public class EdocPrintableHandlerTest extends PrintableHandlerTestBase{

  private static final int PROVIDER_NUMBER = 999998;

  private final EdocPrintableHandler edocPrintableHandler = new EdocPrintableHandler();

  @InjectMocks
  private DocumentService documentService;
  @Mock
  private DocumentRepository documentRepository;
  @Mock
  private OscarProperties oscarProperties;

  @BeforeEach
  public void beforeEach() {
    mockedApplicationContextProvider
        .when(() -> ApplicationContextProvider.getBean(OscarProperties.class))
        .thenReturn(oscarProperties);
    mockedApplicationContextProvider
        .when(() -> ApplicationContextProvider.getBean(DocumentRepository.class))
        .thenReturn(documentRepository);
    mockedApplicationContextProvider
        .when(() -> ApplicationContextProvider.getBean(DocumentService.class))
        .thenReturn(documentService);
    edocPrintableHandler.postConstruct();
  }
  @Test
  public void givenDocumentsExist_whenSearch_thenReturnDocuments() {
    when(documentRepository.getActiveEdocByProviderNumber(PROVIDER_NUMBER))
        .thenReturn(Arrays.asList(
            createDocument(1, "1"),
            createDocument(2, "0"),
            createDocument(3, "1")));
    val results = edocPrintableHandler.search(1, "999998");
    val docOne = results.stream()
        .filter(printable -> printable.getId().equals("1"))
        .findFirst()
        .orElse(null);
    val docTwo = results.stream()
        .filter(printable -> printable.getId().equals("2"))
        .findFirst()
        .orElse(null);
    val docThree = results.stream()
        .filter(printable -> printable.getId().equals("3"))
        .findFirst()
        .orElse(null);
    assertNotNull(docOne);
    assertEquals(AttachmentType.Edocs, docOne.getType());
    assertNotNull(docTwo);
    assertEquals(AttachmentType.Edocs, docTwo.getType());
    assertNotNull(docThree);
    assertEquals(AttachmentType.Edocs, docThree.getType());
  }
  
  @Test
  public void givenNoDocumentsExist_whenSearch_thenReturnEmptyList() {
    when(documentRepository.getActiveEdocByProviderNumber(PROVIDER_NUMBER))
        .thenReturn(Collections.emptyList());
    val results = edocPrintableHandler.search(1, "999998");
    assertTrue(results.isEmpty());
  }

  @Test
  public void testGetById_success() {
    when(documentRepository.getDocumentById(1)).thenReturn(createDocument(1, "1"));
    val printable = edocPrintableHandler.getById("1");
    assertNotNull(printable);
    assertEquals("1", printable.getId());
  }

  @Test
  public void testGetAttachments_success() {
    val attachments = edocPrintableHandler.getAttachments(new Printable());
    assertTrue(attachments.isEmpty());
  }

  @Test
  public void testGetPreviewUrl_success() {
    when(oscarProperties.getKaiemrDeployedContext()).thenReturn("kaiemr");
    val url = edocPrintableHandler.getPreviewUrl("1");
    assertEquals("/kaiemr/api/document/getAttachmentManagerPreview/1?pageCount=5", url);
  }

  private Document createDocument(final int id, final String isPublic) {
    val date = new Date();
    val document = new Document();
    document.setDocumentNo(id);
    document.setDocDesc("Test eDoc " + id);
    document.setObservationDate(date);
    document.setDocFileName("test" + id + ".pdf");
    document.setDocumentPublic(isPublic);
    return document;
  }
}
