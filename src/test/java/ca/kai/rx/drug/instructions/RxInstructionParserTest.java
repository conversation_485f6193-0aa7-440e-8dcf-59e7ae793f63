package ca.kai.rx.drug.instructions;

import ca.kai.rx.drug.DrugInstruction;
import ca.kai.rx.drug.Drug;
import java.util.Arrays;
import java.util.Collection;
import lombok.val;
import org.junit.Test;
import org.junit.experimental.runners.Enclosed;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

/**
 * Unit test for {@link RxInstructionParser}
 */
@RunWith(Enclosed.class)
public class RxInstructionParserTest {

  @RunWith(Parameterized.class)
  public static class RxInstructionParserParamTest {
    private String frequency;
    private String instructionString;

    public RxInstructionParserParamTest(String frequency, String instructionString) {
      this.frequency = frequency;
      this.instructionString = instructionString;
    }

    @Parameters
    public static Collection<Object[]> frequencies() {
      return Arrays.asList(new Object[][]{
          {"Q3W", "Take 2 Q3W for 2 weeks"},
          {"Q4W", "Take 2 Q4W for 2 weeks"},
          {"Q5W", "Take 2 Q5W for 2 weeks"},
          {"Q6W", "Take 2 Q6W for 2 weeks"},
          {"Q7W", "Take 2 Q7W for 2 weeks"},
          {"Q8W", "Take 2 Q8W for 2 weeks"},
          {"Q9W", "Take 2 Q9W for 2 weeks"},
          {"Q10W", "Take 2 Q10W for 2 weeks"},
          {"Q11W", "Take 2 Q11W for 2 weeks"},
          {"Q12W", "Take 2 Q12W for 2 weeks"},
      });
    }

    @Test
    public void testFrequencies_shouldReturnFrequencyCode() {
      val parser = InstructionParser.getDefaultInstructionParser();
      val drug = new Drug();
      drug.setSpecial(instructionString);
      parser.setValuesFromInstructions(drug);
      assertEquals(frequency, drug.getFreqCode());
    }
  }

  public static class RxInstructionParserSingleTests {

    private final RxInstructionParser parser = new RxInstructionParser();

    /**
     * Test for setValuesForInstructions
     * Outcome 1: The drug is a null object
     * Expected: Drug is a null object
     */
    @Test
    public void setValuesFromInstructions_nullDrug_returnNullDrug () {
      Drug drug = null;
      parser.setValuesFromInstructions(drug);
      assertNull(drug);
    }

    /**
     * Test for setValuesForInstructions
     * Outcome 2: The special contains the instruction string
     * Expected: The values in drug are set based on the instruction string
     */
    @Test
    public void setValuesForInstructions_drugSpecialSet_setDrugValues() {
      Drug drug = new Drug();
      drug.setSpecial("Take 2 BID for 10 days");
      parser.setValuesFromInstructions(drug);
      assertEquals(drug.getMethod(), "Take");
      assertEquals(drug.getTakeMin(), 2, 0.1);
      assertEquals(drug.getTakeMax(), 2, 0.1);
      assertEquals(drug.getDuration(), "10");
      assertEquals(drug.getDurationUnit(), "D");
    }

    @Test
    public void setValuesFromInstructions_drugSpecialNotSet_setDrugValues() {
      Drug drug = new Drug();
      drug.setSpecial("");
      parser.setValuesFromInstructions(drug);
      assertEquals(drug.getMethod(), "");
      assertEquals(drug.getTakeMin(), 0, 0.1);
      assertEquals(drug.getTakeMax(), 0, 0.1);
      assertEquals(drug.getDuration(), "");
      assertEquals(drug.getDurationUnit(), "");
    }

    @Test
    public void setValuesFromInstructions_routeIsSet_routeNotAppended() {
      val drug = new Drug();
      drug.setRoute("TestRoute");
      drug.setSpecial("Take 2 BID");
      parser.setValuesFromInstructions(drug);
      assertEquals(drug.getSpecial(), "Take 2 BID");
      assertEquals(drug.getRoute(), "TestRoute");
    }
    @Test
    public void setValuesFromInstructions_allParamsSet_totalQuantityUpdated() {
      val drug = new Drug();
      drug.setSpecial("Take 2 tablets BID for 5 days");
      drug.setQuantity("0");
      parser.setValuesFromInstructions(drug);
      assertNotEquals("0", drug.getQuantity());
      assertEquals("20", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_missingFrequency_totalQuantityChangedWithDefaultFrequency() {
      val drug = new Drug();
      drug.setSpecial("Take 2 tablets for 5 days");
      drug.setQuantity("50");
      parser.setValuesFromInstructions(drug);
      assertEquals("10", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_missingDuration_totalQuantityUnchanged() {
      val drug = new Drug();
      drug.setSpecial("Take 2 tablets BID");
      drug.setQuantity("50");
      parser.setValuesFromInstructions(drug);
      assertEquals("50", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_missingTakeMax_totalQuantityNotChanged() {
      val drug = new Drug();
      drug.setSpecial("Take BID for 5 days");
      drug.setQuantity("50");
      parser.setValuesFromInstructions(drug);
      assertEquals("10", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_withTakeMax_totalQuantityChanged() {
      val drug = new Drug();
      drug.setSpecial("Take 1 BID for 5 days");
      drug.setQuantity("50");
      parser.setValuesFromInstructions(drug);
      assertEquals("10", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_QTY_totalQuantityChanged() {
      val drug = new Drug();
      drug.setSpecial("QTY");
      drug.setQuantity("50");
      parser.setValuesFromInstructions(drug);
      assertEquals("50", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_specialInstructionBID_totalQuantityChanged() {
      val drug = new Drug();
      drug.setSpecial("Apply to affected areas bid as directed");
      drug.setQuantity("50");
      parser.setValuesFromInstructions(drug);
      assertEquals("50", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_specialInstructionTID_totalQuantityChanged() {
      val drug = new Drug();
      drug.setSpecial("25mg po tid prn every day");
      drug.setQuantity("50");
      parser.setValuesFromInstructions(drug);
      assertEquals("50", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_specialNotSet_totalQuantityUnchanged() {
      val drug = new Drug();
      drug.setQuantity("50");
      parser.setValuesFromInstructions(drug);
      assertEquals("50", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_siteIsSet_siteNotAppended() {
      val drug = new Drug();
      drug.setSite("TestSite");
      drug.setSpecial("Take 2 BID");
      parser.setValuesFromInstructions(drug);
      assertEquals(drug.getSpecial(), "Take 2 BID");
      assertEquals(drug.getSite(), "TestSite");
    }

    /**
     * Test for checkForMethod
     * Outcome 1: The method is present in the instruction string, and is the first word
     * Expected: The function returns the method present in the instruction string
     */
    @Test
    public void checkForMethod_methodPresent_returnMethod() {
      val instruction = "Take 2 BID for 10 days";
      val result =  RxInstructionParser.checkForMethod(instruction);

      assertEquals("Take", result);
    }

    /**
     * Test for checkForMethod
     * Outcome 2: The method is not present in the instruction string
     * Expected: The function returns an empty string
     */
    @Test
    public void checkForMethod_methodNotPresent_returnEmptyString() {
      val instruction = "2 BID for 10 days";
      val result = RxInstructionParser.checkForMethod(instruction);

      assertEquals("", result);
    }

    /**
     * Test for checkForMethod
     * Outcome 3: The method is present, but it is not the first word in the instruction string
     * Expected: The function returns an empty string
     */
    @Test
    public void checkForMethod_methodNotFirst_returnEmptyString() {
      val instruction = "2 BID Take for 10 days";
      val result = RxInstructionParser.checkForMethod(instruction);

      assertEquals("", result);
    }

    /**
     * Test for checkForDurationAndDurationUnit
     * Outcome 1: The duration and duration unit is present in the instruction string
     * Expected: The function returns an array containing the duration and duration unit
     */
    @Test
    public void checkForDurationAndDurationUnit_durationAndDurationUnitPresent_returnDurationAndDurationUnit() {
      val instruction = "Take 2 BID for 10 days";
      val result = RxInstructionParser.checkForDurationAndDurationUnit(instruction);

      assertEquals("10", result[0]);
      assertEquals("DAYS", result[1]);
    }

    /**
     * Test for checkForDurationAndDurationUnit
     * Outcome 2: The duration and duration unit is not present in the instruction string
     * Expected: Returns an array of empty strings
     */
    @Test
    public void checkForDurationAndDurationUnit_noDurationAndDurationUnit_returnEmptyStringArray() {
      val instruction = "Take 2 BID";
      val result = RxInstructionParser.checkForDurationAndDurationUnit(instruction);

      assertEquals("", result[0]);
      assertEquals("", result[1]);
    }

    /**
     * Test for checkForDurationAndDurationUnit
     * Outcome 3: The duration is present, but not the duration unit
     * Expected: Returns an array of empty strings
     */
    @Test
    public void checkForDurationAndDurationUnit_durationPresentDurationUnitNotPresent_returnEmptyStringArray() {
      val instruction = "Take 2 BID for 10";
      val result = RxInstructionParser.checkForDurationAndDurationUnit(instruction);

      assertEquals("", result[0]);
      assertEquals("", result[1]);
    }

    /**
     * Test for checkForDurationAndDurationUnit
     * Outcome 4: The duration is not present, but the duration unit is present in the instruction string
     * Expected: Returns the string array containing the duration unit
     */
    @Test
    public void checkForDurationAndDurationUnit_durationNotPresentDurationUnitPresent_returnDurationUnit() {
      val instruction = "Take 2 BID for days";
      val result = RxInstructionParser.checkForDurationAndDurationUnit(instruction);

      assertEquals("", result[0]);
      assertEquals("DAYS", result[1]);
    }

    /**
     * Test for checkForDurationAndDurationUnit
     * Outcome 5: The duration amount has no whitespace after for
     * Expected: Returns the duration and duration unit in a String array
     */
    @Test
    public void testForCheckForDurationAndDurationUnit_noWhitespaceAfterFor_returnDurationAndDurationUnit() {
      val instruction = "Take 2 BID for10 days";
      val result = RxInstructionParser.checkForDurationAndDurationUnit(instruction);

      assertEquals("10", result[0]);
      assertEquals("DAYS", result[1]);
    }

    /**
     * Test for checkForDurationAndDurationUnit
     * Outcome 6: The word "for" does not appear before the duration
     * Expected: Returns the duration and duration unit in a String array
     */
    @Test
    public void testForCheckForDurationAndDurationUnit6_forNotPresent_returnDurationAndDurationUnit(){
      val instruction = "Take 2 BID 10 weeks";
      val result = RxInstructionParser.checkForDurationAndDurationUnit(instruction);

      assertEquals("10", result[0]);
      assertEquals("WEEKS", result[1]);
    }

    /**
     * Test for checkForDurationAndDurationUnit
     * Outcome 7: The duration unit is contained in the duration amount (e.g. 10w)
     * Expected: Returns the duration and duration unit in a String array
     */
    @Test
    public void checkForDurationAndDurationUnit_durationUnitContainedInDuration_returnDurationAndDurationUnit() {
      val instruction = "Take 2 BID 5w";
      val result = RxInstructionParser.checkForDurationAndDurationUnit(instruction);

      assertEquals("5", result[0]);
      assertEquals("W", result[1]);
    }

    /**
     * Test for checkForRoute
     * Outcome 1: Route is not present in the instructions
     * Expected: Returns an empty string
     */
    @Test
    public void checkForRoute_routeNotPresent_returnEmptyString() {
      val instruction = "Take 2 BID for 10 days";
      val result = RxInstructionParser.checkForRoute(instruction);

      assertEquals("", result);
    }

    /**
     * Test for checkRoute
     * Outcome 2: Route is present in the instructions
     * Expected: Returns the route string
     */
    @Test
    public void checkForRoute_routePresent_returnRouteString() {
      val instruction = "Take 2 BID for 10 days PO";
      val result = RxInstructionParser.checkForRoute(instruction);

      assertEquals("PO", result);
    }

    /**
     * Test for checkForSite:
     * Outcome 1: No "Site:" label or site present in the instruction string
     * Expected: Returns an empty string
     */
    @Test
    public void checkForSite_noSiteLabelNoSite_returnEmptyString() {
      val instruction = "Take 2 BID for 10 days";
      val result = RxInstructionParser.checkForSite(instruction);

      assertEquals("", result);
    }

    /**
     * Test for checkForSite
     * Outcome 2: "Site:" label present and site string present in instruction string
     * Expected: Returns the site string
     */
    @Test
    public void checkForSite_siteLabelWithSite_returnSiteString() {
      val instruction = "Take 2 BID for 10 days Site: Arm";
      val result = RxInstructionParser.checkForSite(instruction);

      assertEquals("Arm", result);
    }

    /**
     * Test for checkForSite
     * Outcome 3: "Site:" label present but no site string after label
     * Expected: Returns an empty string
     */
    @Test
    public void checkForSite_siteLabelWithSiteNotPresent_returnEmptyString() {
      val instruction = "Take 2 BID for 10 days Site:";
      val result = RxInstructionParser.checkForSite(instruction);

      assertEquals("", result);
    }

    /**
     * Test for checkForSite
     * Outcome 4: "Site:" label present and site string present but before other fields
     * Expected: Returns the string in front of "Site:"
     */
    @Test
    public void checkForSite_siteLabelWithSiteExtraStrings_returnSiteWithExtraStrings() {
      val instruction = "Take 2 BID for Site: Arm 10 days";
      val result = RxInstructionParser.checkForSite(instruction);

      assertEquals("Arm 10 days", result);
    }

    /**
     * Test for checkForSite
     * Outcome 5: "Site:" label not present but site is present
     * Expected: Returns empty string
     */
    @Test
    public void checkForSite_siteLabelNotPresentSitePresent_returnEmptyString() {
      val instruction = "Take 2 BID for 10 days Arm";
      val result = RxInstructionParser.checkForSite(instruction);

      assertEquals("", result);
    }
    /**
     * Test for checkForFrequency
     * Outcome 1: Frequency is present in the instruction string
     * Expected: Returns the frequency string
     */
    @Test
    public void checkForFrequency_frequencyPresent_returnFrequencyString() {
      val drug = new Drug();
      val instruction = "Take 2 BID for 10 days";
      val result = RxInstructionParser.checkForFrequency(drug, instruction);

      assertEquals("BID", result);
    }
    /**
     * Test for checkForFrequency
     * Outcome 2: Frequency is not present in the instruction string
     * Expected: Returns an empty string
     */
    @Test
    public void checkForFrequency_frequencyNotPresent_returnEmptyString() {
      val drug = new Drug();
      val instruction = "Take 2 for 10 days";
      val result = RxInstructionParser.checkForFrequency(drug, instruction);

      assertEquals("", result);
    }
    /**
     * Test for checkForFrequency
     * Outcome 3: Frequency is a non-standard value (such as daily)
     * Expected: Returns the converted standard frequency value
     */
    @Test
    public void checkForFrequency_frequencyNonStandard_returnConvertedFrequency() {
      val drug = new Drug();
      val instruction = "Take 2 daily for 10 days";
      val result = RxInstructionParser.checkForFrequency(drug, instruction);

      assertEquals("OD", result);
    }
    /**
     * Test for checkForDrugForm
     * Outcome 1: Drug form is present in instructions, but not in drug object
     * Expected: Returns the drug form in the instructions
     */
    @Test
    public void checkForDrugForm_drugFormPresentInInstructions_returnDrugFormFromInstructions() {
      val instruction = "Take 2 capsules BID for 10 days";
      val drug = new Drug();
      val result = RxInstructionParser.checkForDrugForm(drug, instruction);

      assertEquals("capsules", result);
    }
    /**
     * Test for checkForDrugForm
     * Outcome 2: Drug form is present in drug object, but not in instruction string
     * Expected: Return the drug form present in drug object
     */
    @Test
    public void checkForDrugForm_drugFormPresentInObject_returnDrugFormFromObject() {
      val instruction = "Take 2 BID for 10 days";
      val drug = new Drug();
      drug.setDrugForm("VIAL");
      val result = RxInstructionParser.checkForDrugForm(drug, instruction);

      assertEquals("VIAL", result);
    }
    /**
     * Test for checkForDrugForm
     * Outcome 3: Drug form is present in drug object, and is present in instruction string
     * Expected: Return the drug form present in drug object
     */
    @Test
    public void checkForDrugForm_drugFormPresentInObjectAndString_returnDrugFormFromObject() {
      val instruction = "Take 2 capsules BID for 10 days";
      val drug = new Drug();
      drug.setDrugForm("VIAL");
      val result = RxInstructionParser.checkForDrugForm(drug, instruction);

      assertEquals("VIAL", result);
    }

    /**
     * Test for checkForAmount
     * Outcome 1: The amount is a single number with digits
     * Expected: Return takeMin and takeMax as that single number
     */
    @Test
    public void checkForAmount_singleNumberWithDigits_returnSingleNumber() {
      val instruction = "Take 2 BID for 10 days";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);

      assertEquals("2", result[0]);
      assertEquals("2", result[1]);
    }

    /**
     * Test for checkForAmount
     * Outcome 2: The amount is a single number word
     * Expected: Return takeMin and takeMax as that single number (as a digit)
     */
    @Test
    public void checkForAmount_singleNumberWord_returnSingleNumberAsDigit() {
      val instruction = "Take five BID for 10 days";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);

      assertEquals("5", result[0]);
      assertEquals("5", result[1]);
    }
    /**
     * Test for checkForAmount
     * Outcome 3: The amount is a range between two numbers (both digit numbers, no spaces)
     * Expected: Return takeMin as the lower bound of range and takeMax as the upper bound of range
     */
    @Test
    public void checkForAmount_numberRange_returnNumberRange() {
      val instruction = "Take 4-5 TID for 2 weeks";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);

      assertEquals("4", result[0]);
      assertEquals("5", result[1]);
    }
    /**
     * Test for checkForAmount
     * Outcome 4: The amount is a range between two numbers (both number words, no spaces)
     * Expected: Return takeMin as the lower bound of range and takeMax as the upper bound of range (as digits)
     */
    @Test
    public void checkForAmount_numberWordRange_returnNumberRangeAsDigits() {
      val instruction = "Take four-five TID for 2 weeks";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);

      assertEquals("4", result[0]);
      assertEquals("5", result[1]);
    }
    /**
     * Test for checkForAmount
     * Outcome 5: The amount is a range between two numbers (mixed types, no spaces)
     * Expected: Return takeMin as the lower bound of range and takeMax as the upper bound of range (as digits)
     */
    @Test
    public void checkForAmount_numberRangeMixedTypes_returnNumberRangeAsDigits() {
      val instruction = "Take 3-five TID for 2 weeks";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);

      assertEquals("3", result[0]);
      assertEquals("5", result[1]);
    }
    /**
     * Test for checkForAmount
     * Outcome 6: The amount is a range between two numbers (both digit numbers, with a space
     * Expected: Return takeMin as the lower bound of range and takeMax as the upper bound of range (as digits)
     */
    @Test
    public void checkForAmount_numberDigitRangeWithSpace_returnNumberRange() {
      val instruction = "Take 5 - 6 BID for 3 weeks";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);

      assertEquals("5", result[0]);
      assertEquals("6", result[1]);
    }
    /**
     * Test for checkForAmount
     * Outcome 7: The amount is a range between two numbers (both number words, with a space)
     * Expected: Return takeMin as the lower bound of range and takeMax as the upper bound (as digits)
     */
    @Test
    public void checkForAmount_numberWordRangeWithSpace_returnNumberRangeAsDigits() {
      val instruction = "Take seven - eight BID for 3 weeks";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);

      assertEquals("7", result[0]);
      assertEquals("8", result[1]);
    }
    /**
     * Test for checkForAmount
     * Outcome 8: The amount is a range between two numbers (mixed types, with spaces)
     * Expected: Return takeMin as the lower bound of range and takeMax as the upper bound (as digits)
     */
    @Test
    public void checkForAmount_numberRangeMixedTypesWithSpace_returnNumberRangeAsDigits() {
      val instruction = "take eight to 12 daily for 2w";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);

      assertEquals("8", result[0]);
      assertEquals("12", result[1]);
    }
    /**
     * Test for calculateTotalQuantity
     * Outcome 1: Duration, duration unit, frequency, and takeMax are valid inputs
     * Expected: Return total quantity as (takeMax * frequency * duration (in days))
     */
    @Test
    public void calculateTotalQuantity_allValuesValid_returnTotalQuantity() {
      val duration = "3";
      val durationUnit = "W";
      val frequency = "TID";
      val takeMax = "5";

      val result = RxInstructionParser.calculateTotalQuantity(duration, durationUnit, frequency, takeMax);

      //Expected: 5 * 3 * 3 * 7 = 315
      assertEquals(315, result, 0.1);
    }
    /**
     * Test for calculateTotalQuantity
     * Outcome 2: Duration is empty (thus invalid), other values valid
     * Expected: Returns 0
     */
    @Test
    public void calculateTotalQuantity_durationEmpty_returnZero() {
      val duration = "";
      val durationUnit = "M";
      val frequency = "BID";
      val takeMax = "4";

      val result = RxInstructionParser.calculateTotalQuantity(duration, durationUnit, frequency, takeMax);
      assertEquals(0, result, 0.1);
    }
    /**
     * Test for calculateTotalQuantity
     * Outcome 3: Duration unit is empty (thus invalid), other values valid
     * Expected: Returns 0
     */
    @Test
    public void calculateTotalQuantity_durationUnitEmpty_returnZero() {
      val duration = "5";
      val durationUnit = "";
      val frequency = "BID";
      val takeMax = "4";

      val result = RxInstructionParser.calculateTotalQuantity(duration, durationUnit, frequency, takeMax);
      assertEquals(0, result, 0.1);
    }
    /**
     * Test for calculateTotalQuantity
     * Outcome 4: Frequency is empty (thus invalid), other values valid
     * Expected: Returns 0
     */
    @Test
    public void calculateTotalQuantity_frequencyEmpty_returnZero() {
      val duration = "5";
      val durationUnit = "W";
      val frequency = "";
      val takeMax = "4";

      val result = RxInstructionParser.calculateTotalQuantity(duration, durationUnit, frequency, takeMax);
      assertEquals(140.0, result, 0.1);
    }
    /**
     * Test for calculateTotalQuantity
     * Outcome 5: takeMax is empty (thus invalid), other values valid
     * Expected: Returns 0
     */
    @Test
    public void calculateTotalQuantity_takeMaxEmpty_returnZero() {
      val duration = "5";
      val durationUnit = "W";
      val frequency = "BID";
      val takeMax = "";

      val result = RxInstructionParser.calculateTotalQuantity(duration, durationUnit, frequency, takeMax);
      assertEquals(70, result, 0.1);
    }
    /**
     * Test for calculateTotalQuantity
     * Outcome 6: All values empty (thus invalid)
     * Expected: Returns 0
     */
    @Test
    public void calculateTotalQuantity_allValuesEmpty_returnZero() {
      val duration = "";
      val durationUnit = "";
      val frequency = "";
      val takeMax = "";

      val result = RxInstructionParser.calculateTotalQuantity(duration, durationUnit, frequency, takeMax);
      assertEquals(0, result, 0.1);
    }

    @Test
    public void givenTakeMaxIsDecimal_whenCalculateTotalQuantity_thenCalculateCorrectly() {
      val duration = "3";
      val durationUnit = "W";
      val frequency = "TID";
      val takeMax = "1.5";

      val result = RxInstructionParser.calculateTotalQuantity(duration, durationUnit, frequency, takeMax);

      //Expected: 3 * 1.5 * 3 * 7 = 94.5
      assertEquals(94.5, result, 0.1);
    }
    /**
     * Test for convertFrequencyCodeFullText
     * Outcome 1: The frequency code is a valid frequency code
     * Expected: Returns the full frequency code text
     */
    @Test
    public void convertFrequencyCodeFullText_validFrequencyCode_returnFullText() {
      val frequency = "BID";
      val result = RxInstructionParser.convertFrequencyCodeFullText(frequency);

      assertEquals("2x day", result);
    }
    /**
     * Test for convertFrequencyCodeFullText
     * Outcome 2: The frequency code is an invalid frequency code (frequency code not in RxUtils)
     * Expected: Returns the frequency code input
     */
    @Test
    public void convertFrequencyCodeFullText_invalidFrequencyCode_returnFrequencyCode() {
      val frequency = "QAM&HS";
      val result = RxInstructionParser.convertFrequencyCodeFullText(frequency);

      assertEquals("QAM&HS", result);
    }
    /**
     * Test for isParsable
     * Outcome 1: The instruction text is parsable
     * Expected: The function returns true
     */
    @Test
    public void isParsable_instructionTextIsParsable_returnTrue() {
      val instructions = new DrugInstruction();
      instructions.setText("10 caps bid for10 days PO Site: Arm");

      assertTrue(RxInstructionParser.isParsable(instructions));
    }
    /**
     * Test for isParsable
     * Outcome 2: The instruction text is missing a field (frequency)
     * Expected: The function returns false
     */
    @Test
    public void isParsable_instructionTextMissingField_returnFalse() {
      val instructions = new DrugInstruction();
      instructions.setText("10 caps for 10 days");

      assertFalse(RxInstructionParser.isParsable(instructions));
    }

    /**
     * Test for isParsable
     * Outcome 3: The instruction text is empty
     * Expected: The function returns false
     */
    @Test
    public void isParsable_instructionTextEmpty_returnFalse() {
      val instructions = new DrugInstruction();
      instructions.setText("");

      assertFalse(RxInstructionParser.isParsable(instructions));
    }
    /**
     * Test for modifyInstructionText
     * Outcome 1: The drug instruction text is not empty
     * Expected: Returns the modified instruction text
     */
    @Test
    public void modifyInstructionText_instructionText_returnModifiedInstructionText() {
      val instructions = new DrugInstruction();
      val drug = new Drug();
      instructions.setText("Take two to three caps 1x daily for 10 days");

      val result = parser.modifyInstructionText(drug, instructions);

      assertEquals("Take 2 to 3 capsule(s) once daily for 10 days", result);
    }
    /**
     * Test for modifyInstructionText
     * Outcome 2: The drug instruction object is null
     * Expected: Returns an empty string
     */
    @Test
    public void modifyInstructionText_drugInstructionNull_returnEmptyString() {
      val drug = new Drug();
      val result = parser.modifyInstructionText(drug, null);
      assertEquals("", result);
    }
    /**
     * Test for modifyInstructionText
     * Outcome 3: The drug object is null
     * Expected: Returns an empty string
     */
    @Test
    public void modifyInstructionText_drugObjectNull_returnEmptyString() {
      val drugInstruction = new DrugInstruction();
      val result = parser.modifyInstructionText(null, drugInstruction);
      assertEquals("", result);
    }
    /**
     * Test for modifyInstructionText
     * Outcome 4: The drug instruction text is empty, but the drug object has discrete values
     * Expected: Returns instructions based on drug object values
     */
    @Test
    public void modifyInstructionText_instructionTextEmpty_returnInstructionsFromDrugObject() {
      val drugInstruction = new DrugInstruction();
      val drug = new Drug();

      drug.setDrugForm("caps");
      drug.setTakeMin(2);
      drug.setTakeMax(4);
      drug.setFreqCode("BID");
      drug.setDuration("10");
      drug.setDurationUnit("D");
      drug.setMethod("Take");

      val result = parser.modifyInstructionText(drug, drugInstruction);

      assertEquals("Take 2-4 capsule(s) twice daily for 10 days", result);
    }

    @Test
    public void setValuesFromInstructions_oneBidThreeMonths_totalQuantityEqualsOneHundredEighty() {
      val drug = new Drug();
      drug.setSpecial("Take 1 bid for 3 months");
      parser.setValuesFromInstructions(drug);
      assertEquals("180", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_bidAndMonthsInInstructions_frequencyIsBid() {
      val drug = new Drug();
      drug.setSpecial("Take 1 bid for 3 months");
      parser.setValuesFromInstructions(drug);
      assertEquals("BID", drug.getFreqCode());
    }

    @Test
    public void setValuesFromInstructions_noSpecifiedQuantity_totalQuantityIsCorrect() {
      val drug = new Drug();
      drug.setSpecial("take bid 7 days");
      parser.setValuesFromInstructions(drug);
      assertEquals("14", drug.getQuantity());
    }

    @ParameterizedTest
    @ValueSource(strings = {"TAKE", "CONSUME", "RUB", "APPLY", "INJECT"})
    public void testMethods_shouldReturnMethod(String method) {
      val drug = new Drug();
      drug.setSpecial(method + " 2 QID for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals(method, drug.getMethod());
      assertEquals("QID", drug.getFreqCode());
      assertEquals("W", drug.getDurationUnit());
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "TABLETS", "TABLET", "TABS", "TAB", "CAPSULES",
        "CAPSULE", "CAPS", "CAP", "SUSPENSION", "VIAL"
    })
    public void testDrugForms_shouldReturnDrugForm(String drugForm) {
      val drug = new Drug();
      drug.setSpecial("Take 2 " + drugForm + " QID for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals(drugForm, drug.getDrugForm());
      assertEquals("Take", drug.getMethod());
      assertEquals("QID", drug.getFreqCode());
      assertEquals("W", drug.getDurationUnit());
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "OD", "1D", "QAM", "QPM", "QHS", "HS", "QAM&PM", "QAM&HS", "NOON", "QLUNCH", "QDINNER",
        "BID-TID", "BID-QID", "TID-QID", "BID", "TID", "3D", "QID", "4D", "5D", "6D", "QD-BID",
        "6D-8D", "QH", "Q1H", "Q2H", "Q3H", "Q4H", "Q6H", "Q8H", "Q12H", "Q4-6H", "Q6-8H", "Q24H",
        "Q72H", "Q5MIN", "Q2D", "QOTHERD", "Q2NDD", "Q3D", "Q1W", "Q1TW", "QW", "Q2W", "Q1M", "QM",
        "Q2M", "Q3M", "2TW", "2TPW", "3TW", "3TPW", "QMWSS", "QTTF", "Q3W", "Q4W", "Q5W", "Q6W",
        "Q7W", "Q8W", "Q9W", "Q10W", "Q11W", "Q12W"
    })
    public void testFrequencies_shouldReturnFrequency(String frequency) {
      val drug = new Drug();
      drug.setSpecial("Take 2 " + frequency + " for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals(frequency, drug.getFreqCode());
      assertEquals("Take", drug.getMethod());
      assertEquals("W", drug.getDurationUnit());
    }

    @ParameterizedTest
    @ValueSource(strings = {"ONCE DAILY", "1X DAILY", "1X DAY", "DAILY"})
    public void testNonStandardFrequencies1x_shouldReturnFrequency(String frequency) {
      val drug = new Drug();
      drug.setSpecial("Take 2 " + frequency + " for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals("OD", drug.getFreqCode());
      assertEquals("Take", drug.getMethod());
      assertEquals("W", drug.getDurationUnit());
    }

    @ParameterizedTest
    @ValueSource(strings = {"TWICE DAILY", "2X DAILY", "2X DAY"})
    public void testNonStandardFrequencies2xday_shouldReturnFrequency(String frequency) {
      val drug = new Drug();
      drug.setSpecial("Take 2 " + frequency + " for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals("BID", drug.getFreqCode());
      assertEquals("Take", drug.getMethod());
      assertEquals("W", drug.getDurationUnit());
    }

    @ParameterizedTest
    @ValueSource(strings = {"3X DAILY", "3X DAY"})
    public void testNonStandardFrequencies3xday_shouldReturnFrequency(String frequency) {
      val drug = new Drug();
      drug.setSpecial("Take 2 " + frequency + " for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals("TID", drug.getFreqCode());
      assertEquals("Take", drug.getMethod());
      assertEquals("W", drug.getDurationUnit());
    }

    @ParameterizedTest
    @ValueSource(strings = {"4X DAILY", "4X DAY"})
    public void testNonStandardFrequencies4xday_shouldReturnFrequency(String frequency) {
      val drug = new Drug();
      drug.setSpecial("Take 2 " + frequency + " for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals("QID", drug.getFreqCode());
      assertEquals("Take", drug.getMethod());
      assertEquals("W", drug.getDurationUnit());
    }

    @ParameterizedTest
    @ValueSource(strings = {"WEEKLY", "1X WEEK"})
    public void testNonStandardFrequenciesWeekly_shouldReturnFrequency(String frequency) {
      val drug = new Drug();
      drug.setSpecial("Take 2 " + frequency + " for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals("Q1W", drug.getFreqCode());
      assertEquals("Take", drug.getMethod());
      assertEquals("W", drug.getDurationUnit());
    }

    @ParameterizedTest
    @ValueSource(strings = {"MONTHLY", "1X MONTH"})
    public void testNonStandardFrequenciesMonthly_shouldReturnFrequency(String frequency) {
      val drug = new Drug();
      drug.setSpecial("Take 2 " + frequency + " for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals("Q1M", drug.getFreqCode());
      assertEquals("Take", drug.getMethod());
      assertEquals("W", drug.getDurationUnit());
    }

    @ParameterizedTest
    @ValueSource(strings = {"DAYS", "DAY", "D", "DAYS\n"})
    public void testDurationUnitsDays_shouldReturnDurationUnit(String durationUnit) {
      val drug = new Drug();
      drug.setSpecial("Take 2 QID for 2 " + durationUnit);
      parser.setValuesFromInstructions(drug);
      assertEquals("D", drug.getDurationUnit());
      assertEquals("Take", drug.getMethod());
      assertEquals("QID", drug.getFreqCode());
    }


    @ParameterizedTest
    @ValueSource(strings = {"WEEKS", "WEEK", "W", "WEEKS\n"})
    public void testDurationUnitsWeeks_shouldReturnDurationUnit(String durationUnit) {
      val drug = new Drug();
      drug.setSpecial("Take 2 QID for 2 " + durationUnit);
      parser.setValuesFromInstructions(drug);
      assertEquals("W", drug.getDurationUnit());
      assertEquals("Take", drug.getMethod());
      assertEquals("QID", drug.getFreqCode());
    }

    @ParameterizedTest
    @ValueSource(strings = {"MONTHS", "MONTH", "M", "MONTHS\n"})
    public void testDurationUnitsMonths_shouldReturnDurationUnit(String durationUnit) {
      val drug = new Drug();
      drug.setSpecial("Take 2 QID for 2 " + durationUnit);
      parser.setValuesFromInstructions(drug);
      assertEquals("M", drug.getDurationUnit());
      assertEquals("Take", drug.getMethod());
      assertEquals("QID", drug.getFreqCode());
    }

    @ParameterizedTest
    @ValueSource(strings = {"YEARS", "YEAR", "Y", "YEARS\n"})
    public void testDurationUnitsYears_shouldReturnDurationUnit(String durationUnit) {
      val drug = new Drug();
      drug.setSpecial("Take 2 QID for 2 " + durationUnit);
      parser.setValuesFromInstructions(drug);
      assertEquals("Y", drug.getDurationUnit());
      assertEquals("Take", drug.getMethod());
      assertEquals("QID", drug.getFreqCode());
    }

    @ParameterizedTest
    @ValueSource(strings = {"PO", "SL", "IM", "SC", "PATCH", "TOP.", "INH", "SUPP"})
    public void testRoutes_shouldReturnRoute(String route) {
      val drug = new Drug();
      drug.setSpecial("Take 2 QID " + route + " for 2 weeks");
      parser.setValuesFromInstructions(drug);
      assertEquals(route, drug.getRoute());
      assertEquals("Take", drug.getMethod());
      assertEquals("QID", drug.getFreqCode());
      assertEquals("W", drug.getDurationUnit());
    }

    @Test
    public void setValuesFromInstructions_noFrequencyTerm_quantityNotZero() {
      val drug = new Drug();
      drug.setSpecial("Take 1 tab for 90 days");
      parser.setValuesFromInstructions(drug);
      assertEquals("90", drug.getQuantity());
    }

    @Test
    public void setValuesFromInstructions_specificInstruction_noArrayOutOfBoundsException() {
      val drug = new Drug();
      drug.setSpecial("Q2W x1m");
      parser.setValuesFromInstructions(drug);
      assertEquals("Q2W", drug.getFreqCode());
      assertEquals("1", drug.getDuration());
      assertEquals("M", drug.getDurationUnit());
    }

    @Test
    public void setValuesFromInstructions_onlyDurationUnit_noArrayOutOfBoundsException() {
      val drug = new Drug();
      drug.setSpecial("month");
      parser.setValuesFromInstructions(drug);
      Assertions.assertDoesNotThrow(() -> parser.setValuesFromInstructions(drug));
    }

    @Test
    public void givenValidRouteInInstruction_whenCalculateAmount_thenReturnValidAmount() {
      val instruction = "1 Tab PO/ HS for 30 days";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);
      assertEquals("1", result[0]);
      assertEquals("1", result[1]);
    }

    @Test
    public void givenValidFractionInInstruction_whenCalculateAmount_thenReturnValidAmount() {
      val instruction = "Take 1/4 Tablet BID for 10 Days";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);
      assertEquals("0.25", result[0]);
      assertEquals("0.25", result[1]);
    }

    @Test
    public void
        givenValidFractionAndValidRouteInInstruction_whenCalculateAmount_thenReturnValidAmount() {
      val instruction = "1/2 Tab BID PO/ HS for 30 days";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);
      assertEquals("0.5", result[0]);
      assertEquals("0.5", result[1]);
    }

    @Test
    public void givenValidInstruction_whenSetValuesFromInstructions_thenReturnSameInstruction() {
      val instruction = "do not take medication X";
      val drug = new Drug();
      drug.setSpecial(instruction);
      parser.setValuesFromInstructions(drug);
      assertEquals(instruction, drug.getSpecial());
    }

    @Test
    public void givenInstructionEndWithTo_whenCalculateAmount_thenReturnValidAmount() {
      val instruction = "Empty contents of 1 packet into a glass of 30-60mL";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);
      assertEquals("1", result[0]);
      assertEquals("30", result[1]);
    }

    @Test
    public void givenInstructionEndWithDash_whenCalculateAmount_thenReturnValidAmount() {
      val instruction = "take 1 bid 10- days duration";
      val method = RxInstructionParser.checkForMethod(instruction);
      val result = RxInstructionParser.checkForAmount(method, instruction);
      assertEquals("1", result[0]);
      assertEquals("1", result[1]);
    }

    @Test
    public void givenStringWithNoFrequency_whenSetValuesFromInstructions_thenSeEmptytFreqCode() {
      val drug = new Drug();
      drug.setSpecial("Take 1 tablet for 10 days");
      parser.setValuesFromInstructions(drug);
      assertEquals("", drug.getFreqCode());
    }

    @Test
    public void givenNullDrugAndInstruction_whenCheckForFrequency_thenReturnEmptyDrug() {
      val instruction = "Take 1 tablet for 10 days";
      val result = RxInstructionParser.checkForFrequency(null, instruction);
      assertEquals("", result);
    }

    @Test
    public void givenExistingFrequencyInDrug_whenCheckForFrequency_thenReturnExistingFrequency() {
      val drug = new Drug();
      drug.setFreqCode("BID");
      val instruction = "Take 2 for 10 days";
      val result = RxInstructionParser.checkForFrequency(drug, instruction);
      assertEquals("BID", result);
    }

    @Test
    public void givenConflictingFrequency_whenCheckForFrequency_thenInstructionTakesPrecedence() {
      val drug = new Drug();
      drug.setFreqCode("BID");
      val instruction = "Take 2 TID for 5 days";
      val result = RxInstructionParser.checkForFrequency(drug, instruction);
      assertEquals("TID", result);
    }
  }
}