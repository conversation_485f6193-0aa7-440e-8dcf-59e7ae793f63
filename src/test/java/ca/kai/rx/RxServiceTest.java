/**
 * Copyright (c) 2021 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package ca.kai.rx;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import ca.kai.OscarProperties;
import ca.kai.integration.eRx.ClinicConfig;
import ca.kai.rx.drug.Drug;
import ca.kai.rx.drug.DrugArchiveReason;
import ca.kai.rx.drug.DrugRepository;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxInteractionResponseRepository;
import ca.kai.rx.externalPrescriptions.prescribeIT.RxFillRequestService.RXTaskRequest;
import ca.kai.rx.externalPrescriptions.prescribeIT.Task;
import ca.kai.rx.externalPrescriptions.prescribeIT.TaskRepository;
import ca.kai.rx.externalPrescriptions.prescribeIT.TaskService;
import ca.kai.rx.prescription.Prescription;
import ca.kai.rx.prescription.PrescriptionRepository;
import ca.kai.site.SiteRepository;
import ca.kai.systemPreference.SystemPreferenceService;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.val;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class RxServiceTest {

  @Mock
  DrugRepository drugRepository;
  @Mock
  SystemPreferenceService systemPreferenceService;
  @Mock
  private TaskRepository taskRepository;
  @Mock
  private PrescriptionRepository prescriptionRepository;
  @Mock
  private SiteRepository siteRepository;
  @Mock
  ErxInteractionResponseRepository erxInteractionResponseRepository;
  @Mock
  private OscarProperties oscarProperties;
  private MockedStatic<ClinicConfig> clinicConfigMockedStatic;
  private static ClinicConfig clinicConfig = mock(ClinicConfig.class);

  private final RxService rxService = new RxService();
  private static final Integer DEMO_NUMBER = 1;
  private final String REASON = "Cost";
  private final String COMMENT = "Too expensive";
  private static final Date MAIN_WRITTEN_DATE = new Date();
  private static final Date FIRST_WRITTEN_DATE = new Date(2023, Calendar.JUNE, 4);
  private static final Date RX_CANCEL_DISPENCE_DATE = new Date(2024, Calendar.JUNE, 3);
  RxParserLogger mockLogger = mock(RxParserLogger.class);

  @BeforeEach
  public void setup() {
    initializeMocks();
    ReflectionTestUtils.setField(rxService, "rxParserLogger", mockLogger);
    clinicConfigMockedStatic = mockStatic(ClinicConfig.class);
  }

  @AfterEach
  public void tearDown() {
    clinicConfigMockedStatic.close();
  }

  @Test
  public void testGenerateProInstructions() throws Exception {
    val drug = new Drug();
    drug.setMethod("SampleMethod");
    drug.setTakeMin(1.5f);
    drug.setTakeMax(3.5f);
    drug.setFreqCode("SampleFreqCode");
    drug.setDuration("SampleDuration");
    drug.setDurationUnit("SampleDurationUnit");
    drug.setQuantity("SampleQuantity");
    val method = RxService.class.getDeclaredMethod("generateProInstructions", Drug.class);
    method.setAccessible(true);
    val result = (Map<String, String>) method.invoke(rxService, drug);
    assertEquals("SampleMethod", result.get("Method"));
    assertEquals("1.5", result.get("TakeMin"));
    assertEquals("3.5", result.get("TakeMax"));
    assertEquals("SampleFreqCode", result.get("FreqCode"));
    assertEquals("SampleDuration", result.get("Duration"));
    assertEquals("SampleDurationUnit", result.get("DurationUnit"));
    assertEquals("SampleQuantity", result.get("Quantity"));
  }

  @Test
  public void givenSameValues_whenLogDiscrepancyIfAny_thenNoDiscrepancyLogged() throws Exception {
    val proValue = "value";
    val classicValue = "value";
    val method = RxService.class.getDeclaredMethod("logDiscrepancyIfAny", String.class, String.class, String.class);
    method.setAccessible(true);
    val result = (boolean) method.invoke(rxService, "SampleKey", proValue, classicValue);
    assertFalse(result, "Expected no discrepancy to be logged when values are the same");
    verify(mockLogger, never()).logDiscrepancy(any(), any(), any());
  }

  @Test
  public void givenDifferentValues_whenLogDiscrepancyIfAny_thenDiscrepancyLogged() throws Exception {
    val proValue = "proValue";
    val classicValue = "classicValue";
    val method = RxService.class.getDeclaredMethod("logDiscrepancyIfAny", String.class, String.class, String.class);
    method.setAccessible(true);
    val result = (boolean) method.invoke(rxService, "SampleKey", proValue, classicValue);
    assertTrue(result, "Expected a discrepancy to be logged when values are different");
    verify(mockLogger).logDiscrepancy("SampleKey", proValue, classicValue);
  }

  @Test
  public void givenCaseDifferentValues_whenLogDiscrepancyIfAny_thenNoDiscrepancyLogged() throws Exception {
    val proValue = "VALUE";
    val classicValue = "value";
    val method = RxService.class.getDeclaredMethod("logDiscrepancyIfAny", String.class, String.class, String.class);
    method.setAccessible(true);
    val result = (boolean) method.invoke(rxService, "SampleKey", proValue, classicValue);
    assertFalse(result, "Expected no discrepancy to be logged when values are the same");
    verify(mockLogger, never()).logDiscrepancy(any(), any(), any());
  }

  @Test
  public void givenProValueNull_whenLogDiscrepancyIfAny_thenDiscrepancyLogged() throws Exception {
    String proValue = null;
    val classicValue = "classicValue";
    val method = RxService.class.getDeclaredMethod("logDiscrepancyIfAny", String.class, String.class, String.class);
    method.setAccessible(true);
    val result = (boolean) method.invoke(rxService, "SampleKey", proValue, classicValue);
    assertTrue(result, "Expected a discrepancy to be logged when proValue is null");
    verify(mockLogger).logDiscrepancy("SampleKey", proValue, classicValue);
  }

  @Test
  public void givenClassicValueNull_whenLogDiscrepancyIfAny_thenDiscrepancyLogged() throws Exception {
    val proValue = "proValue";
    String classicValue = null;
    val method = RxService.class.getDeclaredMethod("logDiscrepancyIfAny", String.class, String.class, String.class);
    method.setAccessible(true);
    val result = (boolean) method.invoke(rxService, "SampleKey", proValue, classicValue);
    assertTrue(result, "Expected a discrepancy to be logged when classicValue is null");
    verify(mockLogger).logDiscrepancy("SampleKey", proValue, classicValue);
  }

  @Test
  public void givenBothValuesNull_whenLogDiscrepancyIfAny_thenNoDiscrepancyLogged() throws Exception {
    String proValue = null;
    String classicValue = null;
    val method = RxService.class.getDeclaredMethod("logDiscrepancyIfAny", String.class, String.class, String.class);
    method.setAccessible(true);
    val result = (boolean) method.invoke(rxService, "SampleKey", proValue, classicValue);
    assertFalse(result, "Expected no discrepancy to be logged when both values are null");
    verify(mockLogger, never()).logDiscrepancy(any(), any(), any());
  }

  @Test
  public void testArchiveDrug() {
    val drug = new Drug();
    Mockito.when(drugRepository.save(any(Drug.class))).thenReturn(drug);
    assertDrugIsArchived(rxService.archiveDrug(drug, REASON, COMMENT));
  }

  @Test
  public void test_listPreviousInstruction_nullInstruction() {
    //Test data
    val dataArray = new Object[]{null, null, null};
    val instructionList = new ArrayList<Object[]>();
    instructionList.add(dataArray);
    //Initialize mocks
    val drugRepository = mock(DrugRepository.class);
    ReflectionTestUtils.setField(rxService, "drugRepository", drugRepository);
    when(drugRepository.getPreviousInstructionsForBrandDrug("brandName"))
        .thenReturn(instructionList);
    //Initialize drug
    val drug = new Drug();
    drug.setBrandName("brandName");
    //Test result
    val result = rxService.listPreviousInstructions(drug);
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("", result.get(0).get("instruction"));
    assertEquals("", result.get(0).get("pharmacy_instruction"));
  }

  @Test
  public void test_listPreviousInstructions_customDrug() {
    //Test data
    val dataArray = new Object[]{"test instruction", "test pharmacy instruction", ""};
    val instructionList = new ArrayList<Object[]>();
    instructionList.add(dataArray);
    //Initialize mocks
    val drugRepository = mock(DrugRepository.class);
    ReflectionTestUtils.setField(rxService, "drugRepository", drugRepository);
    when(drugRepository.getPreviousInstructionsForCustomDrug("customName"))
        .thenReturn(instructionList);
    //Initialize drug
    val drug = new Drug();
    drug.setCustomName("customName");
    //Test result
    val result = rxService.listPreviousInstructions(drug);
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("test instruction", result.get(0).get("instruction"));
    assertEquals("test pharmacy instruction", result.get(0).get("pharmacy_instruction"));
  }

  @Test
  public void test_listPreviousInstruction_regionalDrug() {
    //Test data
    val dataArray = new Object[]{"test instruction", "test pharmacy instruction", ""};
    val instructionList = new ArrayList<Object[]>();
    instructionList.add(dataArray);
    //Initialize mocks
    val drugRepository = mock(DrugRepository.class);
    ReflectionTestUtils.setField(rxService, "drugRepository", drugRepository);
    when(drugRepository.getPreviousInstructionsForRegionalDrug("regionalIdentifier"))
        .thenReturn(instructionList);
    //Initialize drug
    val drug = new Drug();
    drug.setRegionalIdentifier("regionalIdentifier");
    //Test result
    val result = rxService.listPreviousInstructions(drug);
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("test instruction", result.get(0).get("instruction"));
    assertEquals("test pharmacy instruction", result.get(0).get("pharmacy_instruction"));
  }

  @Test
  public void test_listPreviousInstruction_brandDrug() {
    //Test data
    val dataArray = new Object[]{"test instruction", "test pharmacy instruction", ""};
    val instructionList = new ArrayList<Object[]>();
    instructionList.add(dataArray);
    //Initialize mocks
    val drugRepository = mock(DrugRepository.class);
    ReflectionTestUtils.setField(rxService, "drugRepository", drugRepository);
    when(drugRepository.getPreviousInstructionsForBrandDrug("brandName"))
        .thenReturn(instructionList);
    //Initialize drug
    val drug = new Drug();
    drug.setBrandName("brandName");
    //Test result
    val result = rxService.listPreviousInstructions(drug);
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("test instruction", result.get(0).get("instruction"));
    assertEquals("test pharmacy instruction", result.get(0).get("pharmacy_instruction"));
  }

  @Test
  public void test_listPreviousInstruction_nullPharmacyInstruction() {
    //Test data
    val dataArray = new Object[]{"test instruction", null, "test special instruction"};
    val instructionList = new ArrayList<Object[]>();
    instructionList.add(dataArray);
    //Initialize mocks
    val drugRepository = mock(DrugRepository.class);
    ReflectionTestUtils.setField(rxService, "drugRepository", drugRepository);
    when(drugRepository.getPreviousInstructionsForBrandDrug("brandName"))
        .thenReturn(instructionList);
    //Initialize drug
    val drug = new Drug();
    drug.setBrandName("brandName");
    //Test result
    val result = rxService.listPreviousInstructions(drug);
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("test instruction", result.get(0).get("instruction"));
    assertEquals("test special instruction", result.get(0).get("pharmacy_instruction"));
  }

  @Test
  public void test_listPreviousInstruction_emptyPharmacyInstruction() {
    //Test data
    val dataArray = new Object[]{"test instruction", "", "test special instruction"};
    val instructionList = new ArrayList<Object[]>();
    instructionList.add(dataArray);
    //Initialize mocks
    val drugRepository = mock(DrugRepository.class);
    ReflectionTestUtils.setField(rxService, "drugRepository", drugRepository);
    when(drugRepository.getPreviousInstructionsForBrandDrug("brandName"))
        .thenReturn(instructionList);
    //Initialize drug
    val drug = new Drug();
    drug.setBrandName("brandName");
    //Test result
    val result = rxService.listPreviousInstructions(drug);
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("test instruction", result.get(0).get("instruction"));
    assertEquals("test special instruction", result.get(0).get("pharmacy_instruction"));
  }

  private void initializeMocks() {
    drugRepository = Mockito.mock(DrugRepository.class);
    ReflectionTestUtils.setField(rxService, "drugRepository", drugRepository);
    systemPreferenceService = Mockito.mock(SystemPreferenceService.class);
    ReflectionTestUtils.setField(rxService, "systemPreferenceService", systemPreferenceService);
  }

  private void assertDrugIsArchived(final Drug drug) {
    assertTrue(drug.getArchived());
    assertEquals(REASON, drug.getArchivedReason());
    assertEquals(COMMENT, drug.getComment());
  }

  @Test
  public void testModifyMainWrittenDateForReprint_ReprintFalse() {
    val prescription = createPrescription();
    val mainWrittenDateTest = rxService.modifyMainWrittenDateForReprint(prescription, false, MAIN_WRITTEN_DATE);
    assertEquals(MAIN_WRITTEN_DATE, mainWrittenDateTest);
  }

  @Test
  public void testModifyMainWrittenDateForReprint_ReprintTrue() {
    val prescription = createPrescription();
    val mainWrittenDateTest = rxService.modifyMainWrittenDateForReprint(prescription, true, MAIN_WRITTEN_DATE);
    assertEquals(FIRST_WRITTEN_DATE, mainWrittenDateTest);
  }

  @Test
  public void givenArchivedDrug_whenCreateRePrescribeDrug_thenNewDrugIsUnarchived() {
    //Initialize drug data
    val drug = new Drug();
    drug.archiveDrug(REASON);
    drug.setQuantity("1");

    val represcribedDrug = rxService.createRePrescribeDrug(drug);

    assertFalse(represcribedDrug.getArchived());
    assertNull(represcribedDrug.getArchivedReason());
    assertNull(represcribedDrug.getArchivedDate());
  }

  @Test
  public void givenArchivedDrugNotForReprescribed_whenCreateRePrescribeDrug_thenNewDrugIsAlsoArchived() {
    //Initialize drug data
    val drug = new Drug();
    drug.archiveDrug(REASON);
    drug.setArchivedReason(DrugArchiveReason.UNKNOWN);
    drug.setArchivedDate(FIRST_WRITTEN_DATE);
    drug.setQuantity("1");
    drug.setDemographicNo(1);
    drug.setAtc("ATC");
    drug.setRegionalIdentifier("regionalIdentifier");
    //Initialize mocks
    when(drugRepository.findLatestArchivedForDemographicByRegionalIdentifierAndAtc(any(Integer.class), any(String.class), any(String.class)))
        .thenReturn(drug);

    val represcribedDrug = rxService.createRePrescribeDrug(drug);

    assertTrue(represcribedDrug.getArchived());
    assertNotNull(represcribedDrug.getArchivedReason());
    assertNotNull(represcribedDrug.getArchivedDate());
  }

  @Test
  public void givenDrugs_whenParseInstructionsDisabled_thenRawDataIsCopiedOver(){
    val oldDrug = new Drug();
    oldDrug.setSpecial("take 30 caps each day for 8days");
    oldDrug.setParseInstructions(false);
    oldDrug.setQuantity("40");
    oldDrug.setQuantityUnit("Pills");
    oldDrug.setDuration("40");
    oldDrug.setDurationUnit("Days");

    val newDrug = new Drug();

    rxService.parseInstructions(oldDrug, newDrug);

    assertEquals(newDrug.getQuantity(), "40");
    assertEquals(newDrug.getQuantityUnit(), "Pills");
    assertEquals(newDrug.getSpecial(), oldDrug.getSpecial());
    assertEquals(newDrug.getDuration(), "40");
    assertEquals(newDrug.getDurationUnit(), "Days");
  }

  @Test
  public void givenDrugs_whenParseInstructiions_thenBothDrugsAreParsed() {
    //Initialize drug data
    val oldDrug = new Drug();
    oldDrug.setSpecial("take 30 caps each day for 8days");

    val newDrug = new Drug();
    Mockito.when(systemPreferenceService.readBooleanPreference(any(), any())).thenReturn(false);

    rxService.parseInstructions(oldDrug, newDrug);

    assertEquals(oldDrug.getQuantity(), newDrug.getQuantity());
    assertEquals(oldDrug.getTakeMin(), newDrug.getTakeMin(), 0);
    assertEquals(oldDrug.getTakeMax(), newDrug.getTakeMax(), 0);
    assertEquals(oldDrug.getFreqCode(), newDrug.getFreqCode());
    assertEquals(oldDrug.getDuration(), newDrug.getDuration());
    assertEquals(oldDrug.getDurationUnit(), newDrug.getDurationUnit());
    assertEquals(oldDrug.getSpecial(), newDrug.getSpecial());
    assertEquals(oldDrug.getMethod(), newDrug.getMethod());
    assertEquals(oldDrug.getDrugForm(), newDrug.getDrugForm());
    assertEquals(newDrug.getSpecial(), "take 30 caps each day for 8days");
    assertEquals(newDrug.getQuantity(), "240");
    assertEquals(newDrug.getTakeMin(), 30.0f,0 );
    assertEquals(newDrug.getTakeMax(), 30.0f, 0);
    assertEquals(newDrug.getFreqCode(), "");
    assertEquals(newDrug.getDuration(), "8");
    assertEquals(newDrug.getDurationUnit(), "D");
    assertEquals(newDrug.getMethod(), "take");
    assertEquals(newDrug.getDrugForm(), "caps");
  }

  @ParameterizedTest
  @ValueSource(strings = {"D", "W", "M", "day", "week", "month", "days", "weeks", "months", "mo"})
  public void givenDrugWithUnitName_whenCreateSpecial_returnSpecialWithoutMitte(String unitName) {
    val drug = new Drug();
    drug.setUnitName(unitName);

    val special = rxService.createSpecial(drug);

    assertFalse(special.contains("Mitte"));
    assertTrue(special.contains("Qty"));
    assertFalse(special.contains(unitName));
  }

  @Test
  public void givenDemoWithDrugsAndErxTasks_whenGetAllRxItemsByDemographicNumber_thenQueryForDrugsAndTasks() {
    clinicConfigMockedStatic
        .when(ClinicConfig::getInstance)
        .thenReturn(clinicConfig);
    when(clinicConfig.isMultisiteEnabled()).thenReturn(false);
    initializeMocksGetAllRxMocks();

    val drugs = createDrugTestData();
    val tasks = createTaskTestData(drugs);

    when(drugRepository.findAllByDemographicNo(DEMO_NUMBER)).thenReturn(drugs);
    when(taskRepository.findAllByDemographicNo(DEMO_NUMBER)).thenReturn(tasks);
    when(oscarProperties.getBooleanProperty("indivicare_link_enabled")).thenReturn(false);

    // first three drugs are canceled
    for (int i = 1; i <= 3; i++) {
      Integer drugIndex = i;
      when(taskRepository.findTasksByDrugIdAndTypeInOrderByCreateDateDesc(eq(i),
              eq(Collections.singletonList(RXTaskRequest.CANCEL.getCode()))))
          .thenReturn(tasks.stream().filter(task -> task.getType().equals(RXTaskRequest.CANCEL.getCode())
              && task.getDrugId().equals(drugIndex)).collect(
                  Collectors.toList()));
    }
    // drug 4 has no cancel tasks
    when(taskRepository.findTasksByDrugIdAndTypeInOrderByCreateDateDesc(eq(4),
        eq(Collections.singletonList(RXTaskRequest.CANCEL.getCode())))).thenReturn(new ArrayList<>());

    // drug 1 has a CANCEL_APPROVED cancel response
    when(taskRepository.findByDrugIdAndTypeIn(eq(1), any())).thenReturn(
        tasks.stream().filter(task -> task.getType().equals(
                TaskService.RXTaskRequest.CANCEL_APPROVED.getCode())).findFirst()
            .get());
    // drug 2 has a CANCEL_DENIED cancel response
    when(taskRepository.findByDrugIdAndTypeIn(eq(2), any())).thenReturn(
        tasks.stream().filter(task -> task.getType().equals(
                TaskService.RXTaskRequest.CANCEL_DENIED.getCode())).findFirst()
            .get());
    // drug 3 has a CANCEL_REMAINING_REVOKED cancel response
    when(taskRepository.findByDrugIdAndTypeIn(eq(3), any())).thenReturn(
        tasks.stream().filter(task -> task.getType().equals(
                TaskService.RXTaskRequest.CANCEL_REMAINING_REVOKED.getCode())).findFirst()
            .get());

    // Dispense notification and responses
    // all 4 drugs have dispense notifications
    for (int i = 1; i <= 4; i++) {
      Integer drugIndex = i;
      when(taskRepository.findTasksByDrugIdAndTypeInOrderByCreateDateDesc(eq(i),
          eq(Collections.singletonList(TaskService.RXTaskRequest.DISPENSE_NOTIFICATION.getCode())))).thenReturn(
          tasks.stream().filter(task -> task.getType().equals(
              TaskService.RXTaskRequest.DISPENSE_NOTIFICATION.getCode())
              && task.getDrugId().equals(drugIndex)).collect(
              Collectors.toList()));
    }
    // drug 1 has a cancel dispense task
    when(taskRepository.findFirstByCancelDispenseTaskByDispenseTaskId(eq(1))).thenReturn(
        tasks.stream().filter(task -> task.getType().equals(
                TaskService.RXTaskRequest.DISPENSE_CANCEL.getCode())).findFirst()
            .get());

    val prescription = createPrescription();
    prescription.setDeliveryMethod("ERX");

    when(prescriptionRepository.findOne(any(Integer.class))).thenReturn(prescription);

    when(erxInteractionResponseRepository.findErxInteractionResponsesByScriptNo(any())).thenReturn(new ArrayList<>());
    when(erxInteractionResponseRepository.findErxInteractionResponseByDrugId(any())).thenReturn(
        new ArrayList<>());

    // When
    val results = rxService.getAllRxItemsByDemographicNumber(DEMO_NUMBER);

    // Then
    assertEquals(4, results.size());

    // erx cancel status
    for (int i = 0; i < 3; i++) {
      assertEquals(RXTaskRequest.CANCEL.getCode(),
          results.get(i).getCancelStatus().getRequestType());
    }
    // drug 4 has no cancel status
    assertEquals("NONE", results.get(3).getCancelStatus().getStatus());

    // erx cancel response
    assertEquals(TaskService.RXTaskRequest.CANCEL_APPROVED.getCode(), results.get(0).getCancelStatus().getResponseType());
    assertEquals(TaskService.RXTaskRequest.CANCEL_DENIED.getCode(),
        results.get(1).getCancelStatus().getResponseType());
    assertEquals(TaskService.RXTaskRequest.CANCEL_REMAINING_REVOKED.getCode(),
        results.get(2).getCancelStatus().getResponseType());


    // dispense notification and responses
    for (int i = 0; i < 4; i++) {
      assertTrue(!results.get(i).getErxNotificationDisplay().getDispenses().isEmpty());
    }
    // drug 1 has a cancel dispense task
    assertEquals(RX_CANCEL_DISPENCE_DATE,
        results.get(0).getErxNotificationDisplay().getDispenses().get(0).getCancelledDate());

  }

  @Test
  public void givenDemoWithDrugsAndErxTasks_whenGetAllRxItemsByDemographicNumberOptimized_thenQueryForDrugsAndTasks() {
    clinicConfigMockedStatic
        .when(ClinicConfig::getInstance)
        .thenReturn(clinicConfig);
    when(clinicConfig.isMultisiteEnabled()).thenReturn(false);
    initializeMocksGetAllRxMocks();

    val drugs = createDrugTestData();
    val tasks = createTaskTestData(drugs);

    when(drugRepository.findAllByDemographicNo(DEMO_NUMBER)).thenReturn(drugs);
    when(taskRepository.findByDemographicNoAndDrugIdIn(eq(DEMO_NUMBER), any())).thenReturn(tasks);

    val prescription = createPrescription();
    prescription.setDeliveryMethod("ERX");

    val prescriptionMap = new HashMap<Integer, Prescription>() {{
      put(1, prescription);
    }};
    when(prescriptionRepository.findAllByScriptNoInAsMap(any())).thenReturn(prescriptionMap);
    when(oscarProperties.getBooleanProperty("indivicare_link_enabled")).thenReturn(false);

    // drug 1 has a cancel dispense task
    when(taskRepository.findFirstByCancelDispenseTaskByDispenseTaskId(eq(1))).thenReturn(
        tasks.stream().filter(task -> task.getType().equals(
                TaskService.RXTaskRequest.DISPENSE_CANCEL.getCode())).findFirst()
            .get());

    // When
    val results = rxService.getAllRxItemsByDemographicNumberOptimized(DEMO_NUMBER);

    // Then
    assertEquals(4, results.size());

    // erx cancel status
    for (int i = 0; i < 3; i++) {
      assertEquals(RXTaskRequest.CANCEL.getCode(),
          results.get(i).getCancelStatus().getRequestType());
    }
    // drug 4 has no cancel status
    assertEquals("NONE", results.get(3).getCancelStatus().getStatus());

    // erx cancel response
    assertEquals(TaskService.RXTaskRequest.CANCEL_APPROVED.getCode(),
        results.get(0).getCancelStatus().getResponseType());
    assertEquals(TaskService.RXTaskRequest.CANCEL_DENIED.getCode(),
        results.get(1).getCancelStatus().getResponseType());
    assertEquals(TaskService.RXTaskRequest.CANCEL_REMAINING_REVOKED.getCode(),
        results.get(2).getCancelStatus().getResponseType());

    // dispense notification and responses
    for (int i = 0; i < 4; i++) {
      assertTrue(!results.get(i).getErxNotificationDisplay().getDispenses().isEmpty());
    }
    // drug 1 has a cancel dispense task
    assertEquals(RX_CANCEL_DISPENCE_DATE,
        results.get(0).getErxNotificationDisplay().getDispenses().get(0).getCancelledDate());

  }

  private void initializeMocksGetAllRxMocks() {
    ReflectionTestUtils.setField(rxService, "drugRepository", drugRepository);
    ReflectionTestUtils.setField(rxService, "taskRepository", taskRepository);
    ReflectionTestUtils.setField(rxService, "prescriptionRepository", prescriptionRepository);
    ReflectionTestUtils.setField(rxService, "siteRepository", siteRepository);
    ReflectionTestUtils.setField(rxService, "oscarProperties", oscarProperties);
    ReflectionTestUtils.setField(rxService, "erxInteractionResponseRepository",
        erxInteractionResponseRepository);
  }


  private List<Task> createTaskTestData(List<Drug> demographicErxDrugs) {
    val tasks = new ArrayList<Task>();

    // create renewal request
    val renewalRequest = new Task();
    renewalRequest.setType(TaskService.RXTaskRequest.CREATE_RENEWAL.getCode());
    renewalRequest.setReason("RENEWAL");
    renewalRequest.setReasonText("Test Renewal Request");
    renewalRequest.setCreateDate(new Date());
    renewalRequest.setDrugId(demographicErxDrugs.get(0).getId());
    renewalRequest.setDemographicNo(DEMO_NUMBER);
    tasks.add(renewalRequest);

    // Create a 3 cancel requests to assign to the tasks
    for (int i = 0; i < 3; i++) {
      val cancelRequest = new Task();
      cancelRequest.setType(RXTaskRequest.CANCEL.getCode());
      cancelRequest.setReason("CANCEL_REQUEST");
      cancelRequest.setReasonText("Test Cancel Request " + i + 1);
      cancelRequest.setCreateDate(new Date());
      cancelRequest.setDrugId(demographicErxDrugs.get(i).getId());
      cancelRequest.setDemographicNo(DEMO_NUMBER);
      tasks.add(cancelRequest);
    }

    // create responses
    Task cancelApprovedResponse = new Task();
    cancelApprovedResponse.setType(TaskService.RXTaskRequest.CANCEL_APPROVED.getCode());
    cancelApprovedResponse.setReason("CANCEL_APPROVED");
    cancelApprovedResponse.setReasonText("Test Cancel Approved Response");
    cancelApprovedResponse.setCreateDate(new Date());
    cancelApprovedResponse.setDrugId(1);
    tasks.add(cancelApprovedResponse);
    Task cancelDeniedResponse = new Task();
    cancelDeniedResponse.setType(TaskService.RXTaskRequest.CANCEL_DENIED.getCode());
    cancelDeniedResponse.setReason("CANCEL_DENIED");
    cancelDeniedResponse.setReasonText("Test Cancel Denied Response");
    cancelDeniedResponse.setCreateDate(new Date());
    cancelDeniedResponse.setDrugId(2);
    tasks.add(cancelDeniedResponse);
    Task cancelRevokedResponse = new Task();
    cancelRevokedResponse.setType(TaskService.RXTaskRequest.CANCEL_REMAINING_REVOKED.getCode());
    cancelRevokedResponse.setReason("CANCEL_REMAINING_REVOKED");
    cancelRevokedResponse.setReasonText("Test Cancel Revoked Response");
    cancelRevokedResponse.setCreateDate(new Date());
    cancelRevokedResponse.setDrugId(3);
    tasks.add(cancelRevokedResponse);

    // create dispense notification for all drugs
    for (int i = 0; i < 4; i++) {
      val dispenseNotification = new Task();
      dispenseNotification.setId(i + 1);
      dispenseNotification.setType(TaskService.RXTaskRequest.DISPENSE_NOTIFICATION.getCode());
      dispenseNotification.setReason("DISPENSE_NOTIFICATION");
      dispenseNotification.setReasonText("Test Dispense Notification for Drug "
          + demographicErxDrugs.get(i).getId());
      dispenseNotification.setCreateDate(new Date());
      dispenseNotification.setDrugId(demographicErxDrugs.get(i).getId());
      dispenseNotification.setDemographicNo(DEMO_NUMBER);
      tasks.add(dispenseNotification);
    }

    // add a cancel dispense task for drug 1
    val cancelDispenseTask = new Task();
    cancelDispenseTask.setType(TaskService.RXTaskRequest.DISPENSE_CANCEL.getCode());
    cancelDispenseTask.setReason("DISPENSE_CANCEL");
    cancelDispenseTask.setReasonText("Test Cancel Dispense Task for Drug 1");
    cancelDispenseTask.setCreateDate(RX_CANCEL_DISPENCE_DATE);
    cancelDispenseTask.setDrugId(1);
    cancelDispenseTask.setDemographicNo(DEMO_NUMBER);
    tasks.add(cancelDispenseTask);


    return tasks;
  }

  private List<Drug> createDrugTestData() {
    val drugs = new ArrayList<Drug>();
    for (int i = 1; i <= 4; i++) {
      val drug = new Drug();
      drug.setId(i);
      drug.setScriptNo(1);
      drug.setDemographicNo(DEMO_NUMBER);
      drug.setRemoteProviderNo("remoteProvider" + i);
      drug.setRemoteDrug(true);
      drugs.add(drug);
    }
    return drugs;
  }


  public Prescription createPrescription() {
    val drug = new Drug();
    drug.setWrittenDate(FIRST_WRITTEN_DATE);
    val drugList = new ArrayList<Drug>();
    drugList.add(drug);
    val prescription = new Prescription();
    prescription.setScriptNo(1);
    prescription.setItems(drugList);
    return prescription;
  }
}
