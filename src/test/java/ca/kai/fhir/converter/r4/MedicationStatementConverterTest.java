package ca.kai.fhir.converter.r4;

import static ca.kai.fhir.converter.r4.MedicationRequestConverter.CREATE_DATE_URL;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.when;

import ca.kai.fhir.converter.LanguageConstants;
import ca.kai.rx.drug.Drug;
import ca.kai.rx.drug.DrugExt;
import ca.kai.rx.drug.DrugExtKeyNames;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.val;
import lombok.var;
import org.hl7.fhir.exceptions.FHIRException;
import org.hl7.fhir.r4.model.BooleanType;
import org.hl7.fhir.r4.model.CodeableConcept;
import org.hl7.fhir.r4.model.Dosage;
import org.hl7.fhir.r4.model.MedicationStatement.MedicationStatementStatus;
import org.hl7.fhir.r4.model.Period;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class MedicationStatementConverterTest {

  private static final Integer DRUG_ID = 6;
  private static final Integer DEMOGRAPHIC_NO = 8;
  private static final String PROVIDER_NO = "1234";
  private static final String COMMENT = "TEST COMMENT";
  private static final String SPECIAL = "TEST SPECIAL";
  private static final Date WRITTEN_DATE = new Date(**********);
  private static final Date START_DATE = new Date(**********);
  private static final Date END_DATE = new Date(**********);
  private static final String GUID = "GUID123";
  private static final Date CREATE_DATE = new Date();

  @InjectMocks
  private MedicationStatementConverter converter;

  @Mock
  private DosageConverter dosageConverter;

  @Mock
  private MedicationCodeConverter medicationCodeConverter;

  @Before
  public void setUp() throws Exception {
    MockitoAnnotations.openMocks(this);

    // Mocking dosageConverter
    when(dosageConverter.toFhirObject(any(Drug.class))).thenReturn(new Dosage());

    // Mocking medicationCodeConverter
    CodeableConcept medCodeableConcept = new CodeableConcept();
    when(medicationCodeConverter.toFhirObject(any(Drug.class), anyList())).thenReturn(medCodeableConcept);
  }

  @Test
  public void testToFhirObject_null() throws FHIRException {
    assertNull(converter.toFhirObject(null, null));
  }

  @Test
  public void testToFhirObject_success() throws FHIRException {
    var drug = createDrug(DRUG_ID, DEMOGRAPHIC_NO, PROVIDER_NO, WRITTEN_DATE, Boolean.TRUE,
        Boolean.TRUE, END_DATE, COMMENT, null, null);
    drug.setLongTerm(Boolean.TRUE);

    var medicationStatement = converter.toFhirObject(drug,
        createDrugExts(DrugExtKeyNames.RX_NORM_CODE, "TEST_DRUG_CODE"));

    assertEquals("MedicationStatement/" + DRUG_ID, medicationStatement.getId());
    assertEquals(LanguageConstants.ENGLISH_CA, medicationStatement.getLanguage());
    assertEquals("Patient/" + DEMOGRAPHIC_NO,
        medicationStatement.getSubject().getReference());
    assertEquals(WRITTEN_DATE, medicationStatement.getDateAsserted());
    assertEquals(MedicationStatementStatus.COMPLETED, medicationStatement.getStatus());

    var effectivePeriod = (Period) medicationStatement.getEffective();
    assertEquals(START_DATE, effectivePeriod.getStart());

    // Note assertions remain as is
    assertEquals(COMMENT, medicationStatement.getNote().get(0).getText());

    val longTermExtension = medicationStatement.getExtensionByUrl(
        MedicationStatementConverter.LONG_TERM_MED_EXTENSION_URL);
    assertEquals(Boolean.TRUE, ((BooleanType) longTermExtension.getValue()).getValue());
    assertEquals(
        CREATE_DATE,
        medicationStatement.getExtensionByUrl(CREATE_DATE_URL).getValueAsPrimitive().getValue()
    );
  }

  @Test
  public void givenGuidAndDosage_WhenToFhirObject_ThenExtractsCorrectly() throws FHIRException {
    var drug = createDrug(DRUG_ID, DEMOGRAPHIC_NO, PROVIDER_NO, WRITTEN_DATE, Boolean.TRUE,
        Boolean.TRUE, END_DATE, COMMENT, SPECIAL, GUID);

    var medicationStatement = converter.toFhirObject(drug,
        createDrugExts(DrugExtKeyNames.RX_NORM_CODE, "TEST_DRUG_CODE"));

    // Verify GUID
    assertEquals(GUID, medicationStatement.getIdentifier().get(0).getValue());

    // Comment and Special field assertions remain as is
    assertEquals(COMMENT, medicationStatement.getNote().get(0).getText());
    assertEquals(SPECIAL, medicationStatement.getNote().get(1).getText());
  }

  private List<DrugExt> createDrugExts(String keyValue, String value) {
    List<DrugExt> drugExts = new ArrayList<>();
    DrugExt drugExt = new DrugExt();
    drugExt.setDrugId(DRUG_ID);
    drugExt.setKeyVal(keyValue);
    drugExt.setValue(value);
    drugExts.add(drugExt);
    return drugExts;
  }

  private Drug createDrug(
      final Integer drugId,
      final Integer demographicNo,
      final String providerNo,
      final Date writtenDate,
      final Boolean isArchived,
      final Boolean patientCompliance,
      final Date endDate,
      final String comment,
      final String special,
      final String guid
  ) {
    val drug = new Drug();
    drug.setId(drugId);
    drug.setDemographicNo(demographicNo);
    drug.setProviderNo(providerNo);
    drug.setWrittenDate(writtenDate);
    drug.setCreateDate(CREATE_DATE);
    drug.setRxDate(START_DATE);
    drug.setArchived(isArchived);
    drug.setPatientCompliance(patientCompliance);
    drug.setEndDate(endDate);
    drug.setComment(comment);
    drug.setSpecial(special);
    drug.setGuid(guid);
    return drug;
  }
}
