package ca.kai.fhir.converter.r4;

import static ca.kai.fhir.converter.CodeSystemUrlConstants.IDENTIFIER_TYPE_SYSTEM_URL;
import static ca.kai.fhir.converter.ConverterConstants.URL_HC_TYPE;
import static ca.kai.fhir.converter.r4.BaseConverter.GUID_SYSTEM;
import static ca.kai.fhir.converter.r4.BaseConverter.createCodeableConcept;
import static ca.kai.fhir.converter.r4.DocumentReferenceConverter.ABNORMAL;
import static ca.kai.fhir.converter.r4.DocumentReferenceConverter.CATEGORY_SYSTEM;
import static ca.kai.fhir.converter.r4.DocumentReferenceConverter.DISCRIMINATOR;
import static ca.kai.fhir.converter.r4.DocumentReferenceConverter.DOCUMENTS_CODE;
import static ca.kai.fhir.converter.r4.DocumentReferenceConverter.DOC_CLASS;
import static ca.kai.fhir.converter.r4.DocumentReferenceConverter.DOC_SUB_CLASS;
import static ca.kai.fhir.converter.r4.DocumentReferenceConverter.DOC_TYPE;
import static ca.kai.fhir.converter.r4.DocumentReferenceConverter.SOURCE;
import static ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls.HRM_SUBCLASS_URL;
import static ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls.START_DATE;
import static ca.kai.fhir.converter.r4.helper.ConverterTestHelper.assertCodeableConcept;
import static ca.kai.fhir.converter.r4.helper.ConverterTestHelper.assertExtension;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockConstruction;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import ca.kai.attachmentmanager.printable.Printable;
import ca.kai.attachmentmanager.printable.PrintableService;
import ca.kai.caseManagementNote.CaseManagementIssue;
import ca.kai.caseManagementNote.CaseManagementIssueRepository;
import ca.kai.caseManagementNote.CaseManagementNote;
import ca.kai.caseManagementNote.CaseManagementNoteExt;
import ca.kai.caseManagementNote.CaseManagementNoteExtKey;
import ca.kai.caseManagementNote.CaseManagementNoteExtRepository;
import ca.kai.caseManagementNote.CaseManagementNoteLink;
import ca.kai.caseManagementNote.CaseManagementNoteLinkRepository;
import ca.kai.caseManagementNote.Issue;
import ca.kai.caseManagementNote.IssueRepository;
import ca.kai.ctlDocument.CtlDocumentRepository;
import ca.kai.demographic.Demographic;
import ca.kai.demographic.DemographicRepository;
import ca.kai.document.Document;
import ca.kai.document.DocumentRepository;
import ca.kai.document.DocumentService;
import ca.kai.eForm.EForm;
import ca.kai.eForm.EFormRepository;
import ca.kai.fhir.converter.CodeSystemUrlConstants;
import ca.kai.fhir.converter.DemographicIdentifierData;
import ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls;
import ca.kai.fhir.exception.UnmatchedRemoteRecordException;
import ca.kai.hrm.HRMDocument;
import ca.kai.hrm.HRMReport;
import ca.kai.hrm.util.HRMParser;
import ca.kai.patientLabRouting.PatientDocumentRouting;
import ca.kai.patientLabRouting.PatientDocumentRoutingRepository;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderRepository;
import health.apps.gateway.common.configuration.GWConfigurationService;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import lombok.val;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.hl7.fhir.r4.model.Attachment;
import org.hl7.fhir.r4.model.BooleanType;
import org.hl7.fhir.r4.model.CodeableConcept;
import org.hl7.fhir.r4.model.DateTimeType;
import org.hl7.fhir.r4.model.DateType;
import org.hl7.fhir.r4.model.DocumentReference;
import org.hl7.fhir.r4.model.DocumentReference.ReferredDocumentStatus;
import org.hl7.fhir.r4.model.Encounter;
import org.hl7.fhir.r4.model.Enumerations;
import org.hl7.fhir.r4.model.Enumerations.DocumentReferenceStatus;
import org.hl7.fhir.r4.model.Extension;
import org.hl7.fhir.r4.model.Group;
import org.hl7.fhir.r4.model.Identifier;
import org.hl7.fhir.r4.model.IntegerType;
import org.hl7.fhir.r4.model.Organization;
import org.hl7.fhir.r4.model.Patient;
import org.hl7.fhir.r4.model.Reference;
import org.hl7.fhir.r4.model.StringType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(value = MockitoExtension.class)
class DocumentReferenceConverterTest {

    private static final Long NOTE_ID = 451L;
    private static final Date UPDATE_DATE = new Date();
    private static final Date OBSERVATION_DATE = new Date();
    private static final Integer DEMOGRAPHIC_NO = 123;
    private static final String DEMOGRAPHIC_GUID = "1234-5678";
    private static final String PROVIDER_NO = "199998";
    private static final String TOILET_ROLL_NOTE_1st_REV = "[12-May-2022 : ]\nTest note, saved";
    private static final String TOILET_ROLL_NOTE_2nd_REV = "[12-May-2022 : ]\nTest note, saved\nTaking good care";
    private static final String TOILET_ROLL_NOTE_2nd_REV_HISTORY =
            String.format(CaseManagementNote.HISTORY_RECORD_PATTERN, TOILET_ROLL_NOTE_2nd_REV, TOILET_ROLL_NOTE_1st_REV);
    private static final Boolean SIGNED = true;
    private static final Boolean INCLUDE_ISSUE_IN_NOTE = false;
    private static final String SIGNING_PROVIDER_NO = "23";
    private static final String ENCOUNTER_TYPE = "face to face encounter with client";
    private static final String BILLING_CODE = "";
    private static final String PROGRAM_NO = "10034";
    private static final String REPORTER_CAISI_ROLE = "3";
    private static final String REPORTER_PROGRAM_TEAM = "0";
    private static final String PASSWORD = null;
    private static final Boolean LOCKED = false;
    private static final Boolean ARCHIVED = false;
    private static final Integer POSITION = 0;
    private static final String NOTE_UUID = UUID.randomUUID().toString();
    private static final Integer APPOINTMENT_NO = 19;
    private static final Integer HOUR_OF_ENCOUNTER_TIME = null;
    private static final Integer MINUTE_OF_ENCOUNTER_TIME = null;
    private static final Integer HOUR_OF_ENC_TRANSPORTATION_TIME = null;
    private static final Integer MINUTE_OF_ENC_TRANSPORTATION_TIME = null;
    private static final Boolean IS_AVAILABLE = false;
    private static final Date AUTO_SYNC_DATE = new Date();
    private static final Date LAST_SYNCED_DATE = new Date();

    private static final String SOCIAL_HISTORY_NOTE = "Social History CPP Note";
    private static final String MEDICAL_HISTORY_NOTE = "Medical History CPP Note";
    private static final String ONGOING_CONCERNS_NOTE = "Ongoing Concerns CPP Note";
    private static final String REMINDERS_NOTE = "Reminders CPP Note";
    public static final byte[] DOCUMENT_BYTES = "DOCUMENT_BYTES".getBytes();
    public static final int ARBITRY_DATE_OFFSET_3 = 3;
    public static final int EXPECTED_EXTENSION_COUNT = 10;
    public static final String EFORM_NAME = "eFormName";
    public static final String EFORM_SUBJECT = "subject";
    public static final boolean ACTIVE_STATUS = true;
    public static final String HIN = "**********";
    public static final String HC_TYPE = "ON";
    public static final Date HRM_DATE = new Date();
    public static final String DUMMY_PDF_CONTENT = "dummy pdf content";

    @Mock DemographicRepository demographicRepository;
    @Mock DocumentRepository documentRepository;
    @Mock ProviderRepository providerRepository;
    @Mock CtlDocumentRepository ctlDocumentRepository;
    @Mock DocumentService documentService;
    @Mock CaseManagementNoteExtRepository caseManagementNoteExtRepository;
    @Mock CaseManagementIssueRepository caseManagementIssueRepository;
    @Mock CaseManagementNoteLinkRepository caseManagementNoteLinkRepository;
    @Mock IssueRepository issueRepository;
    @Mock EncounterConverter encounterConverter;
    @Mock PatientDocumentRoutingRepository patientDocumentRoutingRepository;
    @Mock GWConfigurationService gwConfigurationService;
    @Mock EFormRepository eFormRepository;
    @Mock PrintableService printableService;
    @Mock
    private HRMReport hrmReport;

    @InjectMocks
    private DocumentReferenceConverter converter;

    void mockDemographicById(Integer id, String guid) {
        val demographic = new Demographic();
        demographic.setDemographicNumber(id);
        demographic.setGuid(guid);
        when(demographicRepository.findOne(id)).thenReturn(demographic);
    }

    void mockDemographicByGuid(Integer id, String guid) {
        val demographic = new Demographic();
        demographic.setDemographicNumber(id);
        demographic.setGuid(guid);
        when(demographicRepository.getByGuid(guid)).thenReturn(demographic);
    }

    private CaseManagementNote createCaseManagementNotes() {
        val note = new CaseManagementNote();
        note.setId(NOTE_ID);
        note.setUpdateDate(UPDATE_DATE);
        note.setObservationDate(OBSERVATION_DATE);
        note.setDemographicNo(DEMOGRAPHIC_NO);
        note.setProviderNo(PROVIDER_NO);
        note.setNote(TOILET_ROLL_NOTE_2nd_REV);
        note.setHistory(TOILET_ROLL_NOTE_2nd_REV_HISTORY);
        note.setSigned(SIGNED);
        note.setIncludeIssueInNote(INCLUDE_ISSUE_IN_NOTE);
        note.setSigningProviderNo(SIGNING_PROVIDER_NO);
        note.setEncounterType(ENCOUNTER_TYPE);
        note.setBillingCode(BILLING_CODE);
        note.setProgramNo(PROGRAM_NO);
        note.setReporterCaisiRole(REPORTER_CAISI_ROLE);
        note.setReporterProgramTeam(REPORTER_PROGRAM_TEAM);
        note.setPassword(PASSWORD);
        note.setLocked(LOCKED);
        note.setArchived(ARCHIVED);
        note.setPosition(POSITION);
        note.setUuid(NOTE_UUID);
        note.setAppointmentNo(APPOINTMENT_NO);
        note.setHourOfEncounterTime(HOUR_OF_ENCOUNTER_TIME);
        note.setMinuteOfEncounterTime(MINUTE_OF_ENCOUNTER_TIME);
        note.setHourOfEncTransportationTime(HOUR_OF_ENC_TRANSPORTATION_TIME);
        note.setMinuteOfEncTransportationTime(MINUTE_OF_ENC_TRANSPORTATION_TIME);
        note.setAvailable(IS_AVAILABLE);
        note.setAutoSyncDate(AUTO_SYNC_DATE);
        note.setLastSyncedDate(LAST_SYNCED_DATE);

        return note;
    }

    private List<CaseManagementIssue> createNoteIssueList() {
        val issueList = new ArrayList<CaseManagementIssue>();
        var issue = new CaseManagementIssue();
        issue.setId(2L);
        issue.setDemographicNo(Long.valueOf(DEMOGRAPHIC_NO));
        issue.setIssueId(66L);
        issue.setAcute(false);
        issue.setCertain(false);
        issue.setMajor(false);
        issue.setResolved(false);
        issue.setUpdateDate(new Date());
        issue.setType("nurse");
        issueList.add(issue);

        issue = new CaseManagementIssue();
        issue.setId(1L);
        issue.setDemographicNo(Long.valueOf(DEMOGRAPHIC_NO));
        issue.setIssueId(1L);
        issue.setAcute(ACTIVE_STATUS);
        issue.setCertain(ACTIVE_STATUS);
        issue.setMajor(ACTIVE_STATUS);
        issue.setResolved(ACTIVE_STATUS);
        issue.setUpdateDate(new Date());
        issue.setType("nurse");
        issueList.add(issue);

        return issueList;
    }

    @Nested
    class CaseManagementNoteTest {

        @Test
        public void testNoteToFhirObject_null() {
            assertNull(converter.toFhirObject((CaseManagementNote) null));
        }

        @Test
        public void testNoteToOscarCaseManagementNote_null() throws UnmatchedRemoteRecordException {
            assertNull(converter.toOscarCaseManagementNote(null));
        }

        @Test
        public void testNoteToFhirObject_empty() {
            when(encounterConverter.toFhirObject(any())).thenReturn(new Encounter());

            val note = converter.toFhirObject(new CaseManagementNote());
            assertNotNull(note);
            // fhir document category
            assertNotNull(note.getCategoryFirstRep());
            assertCodeableConcept(note.getCategoryFirstRep(),
                CATEGORY_SYSTEM, DocumentReferenceConverter.NOTES_CODE);

            // note id
            assertNull(note.getIdElement().getIdPartAsLong());
            // note
            assertNotNull(note.getContentFirstRep());
            assertNull(note.getContentFirstRep().getAttachment().getData());
            // note history
            assertNull(
                note.getContentFirstRep()
                    .getExtensionByUrl(DocumentReferenceExtensionUrls.NOTE_HISTORY));
            // provider
            assertNull(BaseConverter.getId(note.getAuthorFirstRep()));
            // observation date
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.OBSERVATION_DATE));
            // update date
            assertNull(note.getMeta().getLastUpdated());
            // status
            assertEquals(Enumerations.DocumentReferenceStatus.CURRENT, note.getStatus());
            // demographicNo
            val demographicNumber = BaseConverter.getId(note.getSubject());
            assertNull(demographicNumber);
            // encounterType
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.ENCOUNTER_TYPE));
            // observationDate
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.OBSERVATION_DATE));
            // position
            assertExtension(note, DocumentReferenceExtensionUrls.POSITION, POSITION);
            // programNo
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.PROGRAM_ID));
            // reporterCaisiRole
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.REPORTER_CAISI_ROLE));
            // reporterProgramTeam
            assertNull(
                note.getExtensionByUrl(DocumentReferenceExtensionUrls.REPORTER_PROGRAM_TEAM));
            // signed
            assertExtension(note, DocumentReferenceExtensionUrls.IS_SIGNED, false);
            // includeIssueInNote
            assertExtension(note, DocumentReferenceExtensionUrls.IS_INCLUDE_ISSUE_IN_NOTE,
                ACTIVE_STATUS);
            // updateDate
            assertNotNull(note.getMeta());
            assertNull(note.getMeta().getLastUpdated());
            // uuid
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.UUID));

            // start date
            assertNull(note.getExtensionByUrl(START_DATE));
            // resolution date
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.RESOLUTION_DATE));
            // procedure date
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.PROCEDURE_DATE));
            // age at onset
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.AGE_AT_ONSET));
            // treatment
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.TREATMENT));
            // problem status
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.PROBLEM_STATUS));
            // exposure detail
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.EXPOSURE_DETAIL));
            // relationship
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.RELATIONSHIP));
            // hide from cpp
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.HIDE_CPP));
            // problem description
            assertNull(note.getExtensionByUrl(DocumentReferenceExtensionUrls.PROBLEM_DESC));

            // review all items
        }

        @Test
        public void testNoteToOscarCaseManagementNote_empty()
            throws UnmatchedRemoteRecordException {
            when(encounterConverter.toFhirObject(any())).thenReturn(new Encounter());
            val fhirNote = converter.toFhirObject(new CaseManagementNote());
            val note = converter.toOscarCaseManagementNote(fhirNote);
            assertEmptyNote(note);
        }

        @Test
        public void testNoteToFhirObject_success() {

            when(caseManagementIssueRepository.findByNoteId(NOTE_ID)).thenReturn(
                createNoteIssueList());
            setupMocks(false);
            when(gwConfigurationService.getSystemId()).thenReturn("1");
            mockDemographicById(DEMOGRAPHIC_NO, DEMOGRAPHIC_GUID);

            val cmnNote = createCaseManagementNotes();
            val note = converter.toFhirObject(cmnNote);

            assertNotNull(note);

            // note id
            assertEquals(NOTE_ID, note.getIdElement().getIdPartAsLong());
            // note
            assertNotNull(note.getContentFirstRep());
            assertEquals(TOILET_ROLL_NOTE_2nd_REV,
                new String(note.getContentFirstRep().getAttachment().getData()));
            // note history
            Extension historyExt = note.getContentFirstRep()
                .getExtensionByUrl(DocumentReferenceExtensionUrls.NOTE_HISTORY);
            assertEquals(TOILET_ROLL_NOTE_2nd_REV_HISTORY,
                new String(((Attachment) historyExt.getValue()).getData()));
            // provider
            assertEquals(PROVIDER_NO, BaseConverter.getId(note.getAuthorFirstRep()));
            // observation date
            assertExtension(note, DocumentReferenceExtensionUrls.OBSERVATION_DATE,
                OBSERVATION_DATE);
            // update date
            assertEquals(UPDATE_DATE, note.getMeta().getLastUpdated());
            // status
            assertEquals(Enumerations.DocumentReferenceStatus.CURRENT, note.getStatus());
            // demographicNo
            val demographicNumber = BaseConverter.getId(note.getSubject());
            assertNotNull(demographicNumber);
            assertEquals(DEMOGRAPHIC_NO, (Integer) Integer.parseInt(demographicNumber));
            // encounterType
            assertExtension(note, DocumentReferenceExtensionUrls.ENCOUNTER_TYPE, ENCOUNTER_TYPE);
            // observationDate
            assertExtension(note, DocumentReferenceExtensionUrls.OBSERVATION_DATE,
                OBSERVATION_DATE);
            // position
            assertExtension(note, DocumentReferenceExtensionUrls.POSITION, POSITION);
            // programNo
            assertExtension(note, DocumentReferenceExtensionUrls.PROGRAM_ID, PROGRAM_NO);
            // providerNo
            val authorReference = note.getAuthorFirstRep();
            assertNotNull(authorReference);
            assertEquals(PROVIDER_NO, BaseConverter.getId(authorReference));
            // reporterCaisiRole
            assertExtension(note, DocumentReferenceExtensionUrls.REPORTER_CAISI_ROLE,
                REPORTER_CAISI_ROLE);
            // reporterProgramTeam
            assertExtension(note, DocumentReferenceExtensionUrls.REPORTER_PROGRAM_TEAM,
                REPORTER_PROGRAM_TEAM);
            // signed
            assertExtension(note, DocumentReferenceExtensionUrls.IS_SIGNED, SIGNED);
            // includeIssueInNote
            assertExtension(note, DocumentReferenceExtensionUrls.IS_INCLUDE_ISSUE_IN_NOTE,
                INCLUDE_ISSUE_IN_NOTE);
            // updateDate
            assertNotNull(note.getMeta());
            assertEquals(UPDATE_DATE, note.getMeta().getLastUpdated());
            // uuid
            val identifier = note.getIdentifierFirstRep();
            assertNotNull(identifier);
            assertEquals(NOTE_UUID, identifier.getValue());
            assertEquals("Organization/1", identifier.getAssigner().getReference());

            // fhir document category
            assertNotNull(note.getCategoryFirstRep());
            val noteCodeableConcept = note.getCategory().stream()
                .filter(cc -> cc.getCodingFirstRep().getSystem().equals(CATEGORY_SYSTEM)
                    && cc.getCodingFirstRep().getCode()
                    .equals(DocumentReferenceConverter.NOTES_CODE))
                .findFirst();
            assertTrue(noteCodeableConcept.isPresent());
            assertCodeableConcept(noteCodeableConcept.get(),
                CATEGORY_SYSTEM, DocumentReferenceConverter.NOTES_CODE);

            // note casemanagement issues
            val issues = note.getCategory().stream()
                .filter(cc -> cc.getCodingFirstRep().getSystem()
                    .equals(DocumentReferenceConverter.CATEGORY_ISSUE))
                .collect(Collectors.toList());
            assertCodeableConcept(note.getCategoryFirstRep(),
                CATEGORY_SYSTEM, DocumentReferenceConverter.NOTES_CODE);
            assertEquals(2, issues.size());
            val first = issues.get(0);
            assertCodeableConcept(first, DocumentReferenceConverter.CATEGORY_ISSUE, "MedHistory");
            assertExtension(first, DocumentReferenceExtensionUrls.ISSUE_ACUTE, false);
            assertExtension(first, DocumentReferenceExtensionUrls.ISSUE_CERTAIN, false);
            assertExtension(first, DocumentReferenceExtensionUrls.ISSUE_MAJOR, false);
            assertExtension(first, DocumentReferenceExtensionUrls.ISSUE_RESOLVED, false);
            assertExtension(first, DocumentReferenceExtensionUrls.ISSUE_TYPE, "nurse");

            val second = issues.get(1);
            assertCodeableConcept(second, DocumentReferenceConverter.CATEGORY_ISSUE,
                "PastOcularHistory");
            assertExtension(second, DocumentReferenceExtensionUrls.ISSUE_ACUTE, ACTIVE_STATUS);
            assertExtension(second, DocumentReferenceExtensionUrls.ISSUE_CERTAIN, ACTIVE_STATUS);
            assertExtension(second, DocumentReferenceExtensionUrls.ISSUE_MAJOR, ACTIVE_STATUS);
            assertExtension(second, DocumentReferenceExtensionUrls.ISSUE_RESOLVED, ACTIVE_STATUS);
            assertExtension(second, DocumentReferenceExtensionUrls.ISSUE_TYPE, "nurse");
        }

    @ParameterizedTest
    @ArgumentsSource(ParametersForToOscarCaseManagementNote.class)
    public void testNoteToOscarCaseManagementNote_success(
        boolean isGatewayReference,
        Long expectedId,
        String expectedProviderNumber,
        String expectedSigningProviderNumber,
        int expectedAppointmentNumber)
        throws UnmatchedRemoteRecordException {
            val issueList = createNoteIssueList();
            when(caseManagementIssueRepository.findByNoteId(NOTE_ID)).thenReturn(issueList);
            setupMocks(ACTIVE_STATUS);

            when(caseManagementNoteExtRepository.findByNoteId(NOTE_ID)).thenReturn(noteExtList());
            mockDemographicByGuid(DEMOGRAPHIC_NO, DEMOGRAPHIC_GUID);
            mockDemographicById(DEMOGRAPHIC_NO, DEMOGRAPHIC_GUID);
            if (isGatewayReference) {
              when(gwConfigurationService.getSystemId()).thenReturn("1");
            }
            val fhirNote = converter.toFhirObject(createCaseManagementNotes());
            val note = converter.toOscarCaseManagementNote(fhirNote);


            if (isGatewayReference) {
            assertEquals(expectedId, note.getId());
              assertEquals(1, note.getRemoteSystemId());
            }
            assertEquals(UPDATE_DATE, note.getUpdateDate());
            assertEquals(OBSERVATION_DATE, note.getObservationDate());
            assertEquals(DEMOGRAPHIC_NO, note.getDemographicNo());
            assertEquals(expectedProviderNumber, note.getProviderNo());
            assertEquals(TOILET_ROLL_NOTE_2nd_REV, note.getNote());
            assertEquals(TOILET_ROLL_NOTE_2nd_REV_HISTORY, note.getHistory());
            assertEquals(SIGNED, note.getSigned());
            assertEquals(INCLUDE_ISSUE_IN_NOTE, note.getIncludeIssueInNote());
            assertEquals(expectedSigningProviderNumber, note.getSigningProviderNo());
            assertEquals(ENCOUNTER_TYPE, note.getEncounterType());
            assertEquals(BILLING_CODE, note.getBillingCode());
            assertEquals(PROGRAM_NO, note.getProgramNo());
            assertEquals(REPORTER_CAISI_ROLE, note.getReporterCaisiRole());
            assertEquals(REPORTER_PROGRAM_TEAM, note.getReporterProgramTeam());
            assertEquals(PASSWORD, note.getPassword());
            assertEquals(LOCKED, note.getLocked());
            assertEquals(ARCHIVED, note.getArchived());
            assertEquals(POSITION, note.getPosition());
            assertEquals(NOTE_UUID, note.getUuid());
            assertEquals(expectedAppointmentNumber, note.getAppointmentNo());
            assertEquals(HOUR_OF_ENCOUNTER_TIME, note.getHourOfEncounterTime());
            assertEquals(MINUTE_OF_ENCOUNTER_TIME, note.getMinuteOfEncounterTime());
            assertEquals(HOUR_OF_ENC_TRANSPORTATION_TIME, note.getHourOfEncTransportationTime());
            assertEquals(MINUTE_OF_ENC_TRANSPORTATION_TIME,
                note.getMinuteOfEncTransportationTime());
            assertEquals(IS_AVAILABLE, note.isAvailable());
            assertEquals(AUTO_SYNC_DATE, note.getAutoSyncDate());
            assertEquals(LAST_SYNCED_DATE, note.getLastSyncedDate());
            assertEquals(note.getIssues().size(), 2);
            assertEquals(note.getIssues().get(0).getIssueId(), Long.valueOf(66L));
            assertFalse(note.getIssues().get(0).isAcute());
            assertFalse(note.getIssues().get(0).isCertain());
            assertFalse(note.getIssues().get(0).isMajor());
            assertFalse(note.getIssues().get(0).isResolved());
            assertEquals("nurse", note.getIssues().get(0).getType());
            assertEquals(note.getIssues().get(1).getIssueId(), Long.valueOf(1L));
            assertTrue(note.getIssues().get(1).isAcute());
            assertTrue(note.getIssues().get(1).isCertain());
            assertTrue(note.getIssues().get(1).isMajor());
            assertTrue(note.getIssues().get(1).isResolved());
            assertEquals("nurse", note.getIssues().get(1).getType());
            val extensions = note.getExtensions();
            assertEquals(EXPECTED_EXTENSION_COUNT, extensions.size());
            val iterator = extensions.iterator();

            var item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.AGE_AT_ONSET.getKey(), item.getKeyVal());
            assertEquals("age", item.getValue());

            item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.EXPOSURE_DETAIL.getKey(), item.getKeyVal());
            assertEquals("exposure", item.getValue());

            item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.HIDE_CPP.getKey(), item.getKeyVal());
            assertEquals("hide", item.getValue());

            item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.PROBLEM_DESC.getKey(), item.getKeyVal());
            assertEquals("desc", item.getValue());

            item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.PROBLEM_STATUS.getKey(), item.getKeyVal());
            assertEquals("status", item.getValue());

            item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.PROCEDURE_DATE.getKey(), item.getKeyVal());
            assertEquals(new Date(ARBITRY_DATE_OFFSET_3), item.getDateValue());

            item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.RELATIONSHIP.getKey(), item.getKeyVal());
            assertEquals("relationship", item.getValue());

            item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.RESOLUTION_DATE.getKey(), item.getKeyVal());
            assertEquals(new Date(2), item.getDateValue());

            item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.START_DATE.getKey(), item.getKeyVal());
            assertEquals(new Date(1), item.getDateValue());

            item = iterator.next();
            assertEquals(CaseManagementNoteExtKey.TREATMENT.getKey(), item.getKeyVal());
            assertEquals("treatment", item.getValue());
        }

    @Test
    public void givenNote_whenToFhirObject_thenEncounterIsContained() {
      when(encounterConverter.toFhirObject(any())).thenReturn(new Encounter());
      val cmnNote = createCaseManagementNotes();
      mockDemographicById(DEMOGRAPHIC_NO, DEMOGRAPHIC_GUID);
      val note = converter.toFhirObject(cmnNote);
      assertNotNull(note);
      assertInstanceOf(Encounter.class, note.getContained().get(0));
    }

    @Test
    public void givenRelatedObjectToNote_whenToFhirObject_thenFhirObjectContainsRelatedInfo() {

      val caseManagementNoteLink = new CaseManagementNoteLink();
      caseManagementNoteLink.setTableName(CaseManagementNoteLink.DOCUMENT);
      caseManagementNoteLink.setTableId(1L);
      when(caseManagementNoteLinkRepository.getByNoteIdOrOrderByIdDesc(any()))
          .thenReturn(caseManagementNoteLink);

      val document = new Document();
      document.setGuid("Test Guid");
      when(documentRepository.getDocumentById(caseManagementNoteLink.getTableId().intValue()))
          .thenReturn(document);

      when(encounterConverter.toFhirObject(any())).thenReturn(new Encounter());

      val note = new CaseManagementNote();
      val documentReference = converter.toFhirObject(note);

      val actualLinkedModel =
          ((IntegerType)
                  converter.getExtensionValue(
                      DocumentReferenceExtensionUrls.ATTACHED_MODEL_LINK, documentReference))
              .getValue();
      assertEquals(CaseManagementNoteLink.DOCUMENT, actualLinkedModel);
      val actualDocumentGuid =
          ((StringType)
                  converter.getExtensionValue(
                      DocumentReferenceExtensionUrls.ATTACHED_MODEL_GUID, documentReference))
              .getValue();
      assertEquals(document.getGuid(), actualDocumentGuid);
    }

    private List<CaseManagementNoteExt> noteExtList() {
      return asList(
          new CaseManagementNoteExt(CaseManagementNoteExtKey.START_DATE.getKey(), new Date(1)),
          new CaseManagementNoteExt(CaseManagementNoteExtKey.RESOLUTION_DATE.getKey(), new Date(2)),
          new CaseManagementNoteExt(
              CaseManagementNoteExtKey.PROCEDURE_DATE.getKey(), new Date(ARBITRY_DATE_OFFSET_3)),
          new CaseManagementNoteExt(CaseManagementNoteExtKey.AGE_AT_ONSET.getKey(), "age"),
          new CaseManagementNoteExt(CaseManagementNoteExtKey.TREATMENT.getKey(), "treatment"),
          new CaseManagementNoteExt(CaseManagementNoteExtKey.PROBLEM_STATUS.getKey(), "status"),
          new CaseManagementNoteExt(CaseManagementNoteExtKey.EXPOSURE_DETAIL.getKey(), "exposure"),
          new CaseManagementNoteExt(CaseManagementNoteExtKey.RELATIONSHIP.getKey(), "relationship"),
          new CaseManagementNoteExt(CaseManagementNoteExtKey.HIDE_CPP.getKey(), "hide"),
          new CaseManagementNoteExt(CaseManagementNoteExtKey.PROBLEM_DESC.getKey(), "desc"));
    }
  }

  @Nested
  class DocumentReferenceToCaseManagementNoteTest {

    @Test
    public void testNoteToOscarCaseManagementNote_empty() throws UnmatchedRemoteRecordException {
      when(encounterConverter.toFhirObject(any())).thenReturn(new Encounter());
      val fhirNote = converter.toFhirObject(new CaseManagementNote());
      val note = converter.toOscarCaseManagementNote(fhirNote);
      assertEmptyNote(note);
    }

        @Test
        void givenDemographicIdDiffersLocally_whenToOscarObject_thenOscarObjectFetchesCorrectDemographic()
            throws UnmatchedRemoteRecordException {
            val fhir = new DocumentReference();
            val subject = new Reference("Patient/1");
            subject.setIdentifier(new Identifier().setValue("uuid-2").setType(
                createCodeableConcept(
                    CodeSystemUrlConstants.APPS_HEALTH_CODE_SYSTEM_URL,
                    DemographicIdentifierData.getCode(DemographicIdentifierData.LINK_GUID),
                    DemographicIdentifierData.getCode(DemographicIdentifierData.LINK_GUID)
                )));
            fhir.setSubject(subject);

            val demographic = new Demographic();
            demographic.setDemographicNumber(2);
            demographic.setGuid("uuid-2");

            when(demographicRepository.getByGuid(demographic.getGuid())).thenReturn(demographic);

            val oscar = converter.toOscarCaseManagementNote(fhir);
            assertEquals(demographic.getDemographicNumber(), oscar.getDemographicNo());
        }

        @Test
        void givenNoLocalDemographicWithUuid_whenToOscarObject_thenExceptionIsThrown()
            throws UnmatchedRemoteRecordException {
            val fhir = new DocumentReference();
            val subject = new Reference("Patient/1");
            subject.setIdentifier(new Identifier().setValue("uuid-2").setType(
                createCodeableConcept(
                    CodeSystemUrlConstants.APPS_HEALTH_CODE_SYSTEM_URL,
                    DemographicIdentifierData.getCode(DemographicIdentifierData.LINK_GUID),
                    DemographicIdentifierData.getCode(DemographicIdentifierData.LINK_GUID)
                )));
            fhir.setSubject(subject);

            when(demographicRepository.getByGuid("uuid-2")).thenReturn(null);

            try {
                converter.toOscarCaseManagementNote(fhir);
                fail();
            } catch (UnmatchedRemoteRecordException e) {
                assertEquals("Unknown demographic with uuid: uuid-2", e.getMessage());
            }
        }

        @Test
        public void givenNoteIsNotRemote_whenToOscarObject_thenFhirObjectContainsCurrentSystemId()
            throws UnmatchedRemoteRecordException {
            val noteSystemId = 2;
            val currentSystemId = 1;
            when(encounterConverter.toFhirObject(any())).thenReturn(new Encounter());

            val note = new CaseManagementNote();
            note.setUuid("1234");
            note.setRemoteSystemId(noteSystemId);

            val fhirNote = converter.toFhirObject(note);
            val oscarNote = converter.toOscarCaseManagementNote(fhirNote);

            assertEquals(noteSystemId, oscarNote.getRemoteSystemId());

        }

        @Test
        public void givenNoteIsRemote_whenToOscarObject_thenFhirObjectContainsNotesSystemId()
            throws UnmatchedRemoteRecordException {
            Integer noteSystemId = null;
            val currentSystemId = 1;
            when(encounterConverter.toFhirObject(any())).thenReturn(new Encounter());
            when(gwConfigurationService.getSystemId()).thenReturn(String.valueOf(currentSystemId));

            val note = new CaseManagementNote();
            note.setUuid("1234");
            note.setRemoteSystemId(noteSystemId);

            val fhirNote = converter.toFhirObject(note);
            val oscarNote = converter.toOscarCaseManagementNote(fhirNote);

            assertEquals(currentSystemId, oscarNote.getRemoteSystemId());

        }
    }

    // ArgumentsProvider for @ArgumentsSource annotated unit test since
    // static methods cannot be declared in a nested class
    static class ParametersForToOscarCaseManagementNote implements ArgumentsProvider {
      @Override
      public Stream<Arguments> provideArguments(ExtensionContext context) {
      return Stream.of(
          Arguments.of(false, NOTE_ID, PROVIDER_NO, SIGNING_PROVIDER_NO, APPOINTMENT_NO),
          Arguments.of(true, null, "-1", "-1", -1));
      }
    }

    @Nested
    class OscarDocumentTest {

        @Test
        void givenNull_whenToFhirObject_thenNull() {
            assertNull(converter.toFhirObject((ca.kai.document.Document) null));
        }

        @Test
        void givenNull_whenToOscarDocument_thenNull() {
            assertNull(converter.toOscarObject(null));
        }

        @Test
        void givenEmpty_whenToFhirObject_thenEmpty() {
            val document = new ca.kai.document.Document();
            when(documentService.getDocumentBytes(document, null)).thenReturn(DOCUMENT_BYTES);
            val fhir = converter.toFhirObject(document);
            assertNotNull(fhir);
            assertNull(fhir.getIdElement().getIdPartAsLong());
            assertNull(fhir.getMeta().getLastUpdated());
            assertTrue(fhir.getSubject().isEmpty());
            assertTrue(fhir.getAuthorFirstRep().isEmpty());
            val discriminator = fhir.getCategoryFirstRep();
            assertEquals(DISCRIMINATOR, discriminator.getText());
            assertEquals(DOCUMENTS_CODE, discriminator.getCodingFirstRep().getCode());
            val content = fhir.getContentFirstRep();
            assertNotNull(content);
            val attachment = content.getAttachment();
            assertNotNull(attachment);
            assertNull(attachment.getUrl());
            assertNull(attachment.getContentType());
            assertNull(attachment.getTitle());
            assertNull(attachment.getCreation());
            assertEquals(DOCUMENT_BYTES, attachment.getData());
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.OBSERVATION_DATE));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.PROGRAM_ID));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.REPORTER_CAISI_ROLE));
            assertNull(
                fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.REPORTER_PROGRAM_TEAM));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.IS_SIGNED));
            assertNull(
                fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.IS_INCLUDE_ISSUE_IN_NOTE));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.UUID));
            assertNull(fhir.getExtensionByUrl(START_DATE));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.RESOLUTION_DATE));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.PROCEDURE_DATE));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.AGE_AT_ONSET));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.TREATMENT));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.PROBLEM_STATUS));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.EXPOSURE_DETAIL));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.RELATIONSHIP));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.HIDE_CPP));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.PROBLEM_DESC));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.DOC_CLASS));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.DOC_SUB_CLASS));
            assertExtension(fhir, DocumentReferenceExtensionUrls.ABNORMAL, false);
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.SOURCE));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.SOURCE_FACILITY));
            assertNull(fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.OBSERVATION_DATE));
        }

        @Test
        void givenEmpty_whenToOscarDocument_thenEmpty() {
            val fhir = new org.hl7.fhir.r4.model.DocumentReference();
            val oscar = converter.toOscarObject(fhir);
            assertNotNull(oscar);
            assertNull(oscar.getDocumentNo());
            assertNull(oscar.getDocType());
            assertNull(oscar.getDocClass());
            assertNull(oscar.getDocSubClass());
            assertNull(oscar.getDocDesc());
            assertNull(oscar.getDocXml());
            assertNull(oscar.getDocFileName());
            assertNull(oscar.getDocCreator());
            assertNull(oscar.getResponsible());
            assertNull(oscar.getSource());
            assertNull(oscar.getSourceFacility());
            assertNull(oscar.getProgramId());
            assertNull(oscar.getUpdateDateTime());
            assertNull(oscar.getStatus());
            assertNull(oscar.getContentType());
            assertNull(oscar.getContentDateTime());
            assertNull(oscar.getReportMedia());
            assertEquals("0", oscar.getDocumentPublic());
            assertNull(oscar.getObservationDate());
            assertNull(oscar.getReviewer());
            assertNull(oscar.getReviewDateTime());
            assertEquals(1, oscar.getNumberOfPages());
            assertNull(oscar.getAppointmentNo());
            assertNull(oscar.getRestrictToProgram());
            assertFalse(oscar.getAbnormal());
            assertTrue(oscar.getComments().isEmpty());
        }

        @Test
        void givenDocument_whenToFhirObject_thenSuccess() {
            val oscar = new ca.kai.document.Document();
            val provider = new Provider();
            provider.setProviderNo("provider#");
            provider.setFirstName("first");
            provider.setLastName("last");
            oscar.setDocumentNo(70);
            oscar.setGuid("guid");
            oscar.setDocType("type");
            oscar.setDocClass("class");
            oscar.setDocSubClass("subclass");
            oscar.setDocDesc("desc");
            oscar.setDocXml("xml");
            oscar.setDocFileName("file");
            oscar.setDocCreator(provider);
            oscar.setResponsible("responsible");
            oscar.setSource("source");
            oscar.setSourceFacility("facility");
            oscar.setProgramId(80);
            oscar.setUpdateDateTime(new Date());
            oscar.setStatus("status");
            oscar.setContentType("content");
            oscar.setContentDateTime(new Date());
            oscar.setReportMedia("media");
            oscar.setDocumentPublic("public");
            oscar.setObservationDate(new Date());
            oscar.setReviewer("reviewer");
            oscar.setReviewDateTime(new Date());
            oscar.setNumberOfPages(2);
            oscar.setAppointmentNo(3);
            oscar.setRestrictToProgram(5);
            oscar.setAbnormal(ACTIVE_STATUS);
            oscar.setComments(new ArrayList<>());

            when(documentService.getDocumentBytes(oscar, null)).thenReturn(DOCUMENT_BYTES);

            val demographic = mock(ca.kai.demographic.Demographic.class);
            when(demographic.getDemographicNumber()).thenReturn(3);
            when(demographic.getFormattedName()).thenReturn("doe, john");
            val patientDocumentRouting = mock(PatientDocumentRouting.class);
            when(patientDocumentRouting.getDemographic()).thenReturn(demographic);
            when(patientDocumentRoutingRepository.getAllPatientDocumentRoutingsByDocumentId(70))
                .thenReturn(singletonList(patientDocumentRouting));

            val fhir = converter.toFhirObject(oscar);

            assertNotNull(fhir);
            assertEquals(70, fhir.getIdElement().getIdPartAsLong());
            assertEquals("guid", fhir.getIdentifierFirstRep().getValue());
            assertEquals(oscar.getUpdateDateTime(), fhir.getMeta().getLastUpdated());
            val category = fhir.getCategory();
            assertEquals(DISCRIMINATOR, category.get(0).getText());
            assertEquals(DOCUMENTS_CODE, category.get(0).getCodingFirstRep().getCode());
            assertEquals(oscar.getDocType(), category.get(1).getText());
            assertEquals(oscar.getDocClass(), category.get(2).getText());
            assertEquals(oscar.getDocSubClass(), category.get(3).getText());
            assertEquals(oscar.getSource(), category.get(4).getText());
            assertEquals("true", category.get(5).getText());

            assertEquals(oscar.getDocDesc(), fhir.getDescription());
            // assert subject
            assertReference(fhir.getSubject(), "Patient", "doe, john", "3");
            //assertEquals(oscar.getDocXml(), new String(fhir.getContentFirstRep().getAttachment().getData()));
            assertReference(fhir.getAuthorFirstRep(), "Practitioner", "last, first", "provider#");
            assertReference(fhir.getAuthenticator(), "Practitioner", "responsible", null);
            assertReference(fhir.getCustodian(), "Organization", "facility", null);
            assertReference(fhir.getContext().getEncounterFirstRep(), "Encounter", "3", "3");
            val content = fhir.getContentFirstRep();
            assertNotNull(content);
            val attachment = content.getAttachment();
            assertNotNull(attachment);
            assertNull(attachment.getUrl());
            assertEquals(oscar.getContentType(), attachment.getContentType());
            assertEquals(oscar.getDocFileName(), attachment.getTitle());
            assertEquals(oscar.getContentDateTime(), attachment.getCreation());
            assertEquals(DOCUMENT_BYTES, attachment.getData());
            assertExtension(fhir, DocumentReferenceExtensionUrls.REPORT_MEDIA,
                oscar.getReportMedia());
            assertExtension(fhir, DocumentReferenceExtensionUrls.PUBLIC1,
                oscar.getDocumentPublic());
            assertEquals(fhir.getDate(), oscar.getObservationDate());
            assertExtension(fhir, DocumentReferenceExtensionUrls.REVIEWER, oscar.getReviewer());
            assertTrue(fhir.hasExtension(DocumentReferenceExtensionUrls.PROGRAM_ID));
            assertTrue((fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.PROGRAM_ID)
                .getValue() instanceof Reference));
            assertEquals(
                "Group/80",
                ((Reference) fhir.getExtensionByUrl(DocumentReferenceExtensionUrls.PROGRAM_ID)
                    .getValue()).getReference()
            );
            assertExtension(fhir, DocumentReferenceExtensionUrls.NUMBER_OF_PAGES,
                oscar.getNumberOfPages());
            //assertExtension(fhir, DocumentReferenceUrls.RESTRICT_TO_PROGRAM, oscar.getRestrictToProgram());
            assertExtension(fhir, DocumentReferenceExtensionUrls.REVIEW_DATE_TIME,
                oscar.getReviewDateTime());
            assertExtension(fhir, DocumentReferenceExtensionUrls.RESPONSIBLE,
                oscar.getResponsible());
            assertExtension(fhir, DocumentReferenceExtensionUrls.ABNORMAL, oscar.getAbnormal());
            assertExtension(fhir, DocumentReferenceExtensionUrls.DOC_CLASS, oscar.getDocClass());
            assertExtension(fhir, DocumentReferenceExtensionUrls.DOC_SUB_CLASS,
                oscar.getDocSubClass());
            assertExtension(fhir, DocumentReferenceExtensionUrls.SOURCE, oscar.getSource());
            assertExtension(fhir, DocumentReferenceExtensionUrls.SOURCE_FACILITY,
                oscar.getSourceFacility());
            assertExtension(
                fhir, DocumentReferenceExtensionUrls.OBSERVATION_DATE, oscar.getObservationDate());
        }

        @Test
        void givenFhirDocument_whenToOscarDocument_thenSuccess() {
            // an arbitrary date
            val DT = new Date(0);
            val DT2 = new Date(1);
            val DT3 = new Date(2);
            val DT4 = new Date(ARBITRY_DATE_OFFSET_3);

            val fhir = new org.hl7.fhir.r4.model.DocumentReference();
            val provider = new Provider();
            provider.setProviderNo("provider#");
            provider.setFirstName("first");
            provider.setLastName("last");
            fhir.setId("70");
            fhir.addIdentifier().setValue("guid").setSystem(GUID_SYSTEM);
            fhir.setMeta(new org.hl7.fhir.r4.model.Meta().setLastUpdated(DT4));
            fhir.addCategory().setText(DISCRIMINATOR)
                .addCoding().setSystem(CATEGORY_SYSTEM).setCode(DOCUMENTS_CODE);
            fhir.addCategory().setText("type")
                .addCoding().setSystem(CATEGORY_SYSTEM).setCode(DOC_TYPE);
            fhir.addCategory().setText("class")
                .addCoding().setSystem(CATEGORY_SYSTEM).setCode(DOC_CLASS);
            fhir.addCategory().setText("subclass")
                .addCoding().setSystem(CATEGORY_SYSTEM).setCode(DOC_SUB_CLASS);
            fhir.addCategory().setText("source")
                .addCoding().setSystem(CATEGORY_SYSTEM).setCode(SOURCE);
            fhir.addCategory().setText("true")
                .addCoding().setSystem(CATEGORY_SYSTEM).setCode(ABNORMAL);
            fhir.setStatus(Enumerations.DocumentReferenceStatus.CURRENT);
            fhir.setDescription("desc");
            fhir.getContentFirstRep().getAttachment().setContentType("content");
            fhir.getContentFirstRep().getAttachment().setTitle("file");
            fhir.getContentFirstRep().getAttachment().setCreation(DT);
            fhir.getContentFirstRep().getAttachment().setData(DOCUMENT_BYTES);
            fhir.addAuthor().setReference("Practitioner/provider#").setDisplay("last, first");
            fhir.getAuthenticator().setReference("Practitioner/responsible")
                .setDisplay("responsible");
            fhir.getCustodian().setReference("Organization/facility").setDisplay("facility");
            fhir.getContext().getEncounterFirstRep().setReference("Encounter/3").setDisplay("3");
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.REPORT_MEDIA)
                .setValue(new StringType("media"));
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.PUBLIC1)
                .setValue(new StringType("public"));
            fhir.setDate(DT2);
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.REVIEWER)
                .setValue(new StringType("reviewer"));
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.PROGRAM_ID)
                .setValue(new Reference(Group.class.getSimpleName() + "/" + 80));
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.NUMBER_OF_PAGES)
                .setValue(new IntegerType(2));
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.REVIEW_DATE_TIME)
                .setValue(new DateTimeType(DT3));
            // the following extensions are for testing if values are saved from the directly
            // mapped fields and not the extensions when both are present in FHIR resource
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.OBSERVATION_DATE)
                .setValue(new DateTimeType(DT));
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.DOC_CLASS)
                .setValue(new StringType("Second Doc class"));
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.DOC_SUB_CLASS)
                .setValue(new StringType("Second Doc sub class"));
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.SOURCE)
                .setValue(new StringType("Second Source"));
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.SOURCE_FACILITY)
                .setValue(new StringType("Second Source Facility"));
            fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.ABNORMAL)
                .setValue(new BooleanType(false));

//          assertEquals("", FhirContext.forR4().newJsonParser().setPrettyPrint(true).encodeResourceToString(fhir));

            val oscar = converter.toOscarObject(fhir);

            assertNotNull(oscar);
            assertEquals(70, oscar.getDocumentNo());
            assertEquals("guid", oscar.getGuid());
            assertEquals("type", oscar.getDocType());
            assertEquals("class", oscar.getDocClass());
            assertEquals("subclass", oscar.getDocSubClass());
            assertEquals("desc", oscar.getDocDesc());
            assertEquals("content", oscar.getContentType());
            assertEquals("file", oscar.getDocFileName());
            assertEquals("provider#", oscar.getDocCreator().getProviderNo());
            assertEquals("last, first", oscar.getDocCreator().getFormattedName());
            assertEquals("responsible", oscar.getResponsible());
            assertEquals("source", oscar.getSource());
            assertEquals("facility", oscar.getSourceFacility());
            assertEquals(80, oscar.getProgramId());
            assertEquals(DT4, oscar.getUpdateDateTime());
            assertEquals("A", oscar.getStatus());
            assertEquals(DT, oscar.getContentDateTime());
            assertEquals("media", oscar.getReportMedia());
            assertEquals("public", oscar.getDocumentPublic());
            assertEquals(DT2, oscar.getObservationDate());
            assertEquals("reviewer", oscar.getReviewer());
            assertEquals(DT3, oscar.getReviewDateTime());
            assertEquals(2, oscar.getNumberOfPages());
            assertEquals(ARBITRY_DATE_OFFSET_3, oscar.getAppointmentNo());
            //assertEquals(5, oscar.getRestrictToProgram());
            assertTrue(oscar.getAbnormal());
            assertTrue(oscar.getComments().isEmpty());
        }

        @Test
        void givenAuthenticatorAndExtension_whenToOscarObject_thenGetFromAuthenticator() {
          val fhir = new DocumentReference();
          fhir.getAuthenticator().setReference("Practitioner/responsible")
              .setDisplay("responsible");
          fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.RESPONSIBLE)
              .setValue(new StringType("responsible-extension"));

          val oscar = converter.toOscarObject(fhir);
          assertEquals("responsible", oscar.getResponsible());
        }

        @Test
        void givenOnlyResponsibleExtension_whenToOscarObject_thenGetFromExtension() {
          val fhir = new DocumentReference();
          fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.RESPONSIBLE)
              .setValue(new StringType("responsible-extension"));

          val oscar = converter.toOscarObject(fhir);
          assertEquals("responsible-extension", oscar.getResponsible());
        }

        @Test
        void givenReferenceTypeProgramId_whenToOscarObject_thenGetFromReferenceType() {
          val fhir = new DocumentReference();
          fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.PROGRAM_ID)
              .setValue(new Reference("Group/80"));

          val oscar = converter.toOscarObject(fhir);
          assertEquals(80, oscar.getProgramId());
        }

        @Test
        void givenAuthorWithoutDisplay_whenToOscarObject_thenDoNotSetDocumentCreator() {
          val fhir = new DocumentReference();
          fhir.addAuthor().setReference("Practitioner/provider#");

          val oscar = converter.toOscarObject(fhir);
          assertNull(oscar.getDocCreator());
        }

        @Test
        void givenDateExtensionAndField_whenToOscarObject_thenUseField() {
          val fhir = new DocumentReference();
          val fieldDate = new Date();
          fhir.setDate(fieldDate);
          fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.OBSERVATION_DATE)
              .setValue(new DateType("2021-02-02"));

          val oscar = converter.toOscarObject(fhir);
          assertEquals(fieldDate, oscar.getObservationDate());
        }

        @Test
        void givenObservationDateExtensionOnly_whenToOscarObject_thenGetValueFromExtension() {
          val fhir = new DocumentReference();
          val extensionDate = new Date(1);
          fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.OBSERVATION_DATE)
              .setValue(new DateType(extensionDate));

          val oscar = converter.toOscarObject(fhir);
          assertEquals(extensionDate, oscar.getObservationDate());
        }

        @Test
        void givenDocClassExtension_whenToOscarObject_thenGetValueFromExtension() {
          val fhir = new DocumentReference();
          fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.DOC_CLASS)
              .setValue(new StringType("Second Doc class"));

          val oscar = converter.toOscarObject(fhir);
          assertEquals("Second Doc class", oscar.getDocClass());
        }

        @Test
        void givenDocSubClassExtension_whenToOscarObject_thenGetValueFromExtension() {
          val fhir = new DocumentReference();
          fhir.addExtension()
              .setUrl(DocumentReferenceExtensionUrls.DOC_SUB_CLASS)
              .setValue(new StringType("Second Doc sub class"));

          val oscar = converter.toOscarObject(fhir);
          assertEquals("Second Doc sub class", oscar.getDocSubClass());
        }

        @Test
        void givenSourceExtension_whenToOscarObject_thenGetValueFromExtension() {
          val fhir = new DocumentReference();
          fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.SOURCE)
              .setValue(new StringType("Second Source"));

          val oscar = converter.toOscarObject(fhir);
          assertEquals("Second Source", oscar.getSource());
        }

        @Test
        void givenAbnormalExtension_whenToOscarObject_thenGetValueFromExtension() {
          val fhir = new DocumentReference();
          fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.ABNORMAL)
              .setValue(new BooleanType(false));

          val oscar = converter.toOscarObject(fhir);
          assertFalse(oscar.getAbnormal());
        }

        @Test
        void givenSourceFacilityExtension_whenToOscarObject_thenGetValueFromExtension() {
          val fhir = new DocumentReference();
          fhir.addExtension().setUrl(DocumentReferenceExtensionUrls.SOURCE_FACILITY)
              .setValue(new StringType("Second Source Facility"));

          val oscar = converter.toOscarObject(fhir);
          assertEquals("Second Source Facility", oscar.getSourceFacility());
        }

      @Test
      void givenDocRefWithGuid_whenToOscarObject_thenGuidIsSetCorrectly() {
        // Arrange
        val fhir = new DocumentReference();
        fhir.addIdentifier()
            .setValue("test-guid")
            .setSystem(GUID_SYSTEM);
        // Act
        val oscar = converter.toOscarObject(fhir);
        // Assert
        assertNotNull(oscar);
        assertEquals("test-guid", oscar.getGuid());
      }

      @Test
      void givenDocRefWithoutGuid_whenToOscarObject_thenGuidIsGenerated() {
        // Arrange
        val fhir = new DocumentReference();
        fhir.setId("1");
        // Act
        val oscar = converter.toOscarObject(fhir);
        // Assert
        assertNotNull(oscar);
        assertNotNull(oscar.getGuid());
        assertFalse(oscar.getGuid().isEmpty());
        assertEquals(36, oscar.getGuid().length());
      }
    }

  @Nested
  class OscarEformTest {
    private static final String EFORM_GUID = "465a6ebd-1e50-4f60-9f48-e0651ae23235";
    private static final String HC_VER = "KP";

      @Test
    void givenNull_whenToFhirObject_thenNull() {
      assertNull(converter.toFhirObject((EForm) null, false));
    }

    @Test
    void givenNull_whenToOscarEform_thenNull() {
      assertNull(converter.toOscarObject(null));
    }

    @Test
    void givenEform_whenToFhirObject_thenCreateFhirObjectCorrectly() {
      val eform = new EForm();
      val eformDate = new Date();
      eform.setId(1L);
      eform.setFormName(EFORM_NAME);
      eform.setFormDate(eformDate);
      eform.setSubject(EFORM_SUBJECT);
      eform.setGuid(EFORM_GUID);
      eform.setStatus(ACTIVE_STATUS);
      eform.setDemographicNo(DEMOGRAPHIC_NO);
      eform.setProviderNo(PROVIDER_NO);
      val fhir = converter.toFhirObject(eform, false);
      assertNotNull(fhir);
      assertEquals(1, fhir.getIdElement().getIdPartAsLong());
      val identifier = fhir.getIdentifierFirstRep();
      assertNotNull(identifier);
      assertEquals(EFORM_GUID, identifier.getValue());
      assertEquals(DocumentReferenceStatus.CURRENT, fhir.getStatus());
      assertEquals(EFORM_NAME, fhir.getDescription());
      assertEquals(eformDate, fhir.getDate());
      assertEquals(EFORM_SUBJECT, fhir.getType().getText());
      assertEquals("Patient/123", fhir.getSubject().getReference());
      assertEquals("Practitioner/199998", fhir.getAuthorFirstRep().getReference());
    }

    @Test
    void givenFhirObject_whenToOscarEform_thenCreateEformCorrectly() {
      val fhir = new DocumentReference();
      val demographic = new Demographic();
      demographic.setDemographicNumber(DEMOGRAPHIC_NO);
      when(eFormRepository.findFirstByGuid(anyString())).thenReturn(null);
      when(demographicRepository.getFirstByHinAndHcType(anyString(), anyString()))
          .thenReturn(demographic);
      val eformDate = new Date();
      val testData = "Test Eform Data".getBytes();
      fhir.setId("1");
      fhir.setDescription(EFORM_NAME);
      fhir.setDate(eformDate);
      fhir.setType(new CodeableConcept().setText(EFORM_SUBJECT));
      fhir.addIdentifier()
          .setSystem(GUID_SYSTEM)
          .setValue(EFORM_GUID)
          .setAssigner(new Reference(Organization.class.getSimpleName() + "/" + 1));
      fhir.setStatus(DocumentReferenceStatus.CURRENT);
      val patient = new Patient();
      patient.addIdentifier(
          new Identifier()
              .setType(
                  BaseConverter.createCodeableConcept(
                      IDENTIFIER_TYPE_SYSTEM_URL,
                      PatientConverter.JHN_CODE,
                      PatientConverter.JHN_DISPLAY
                  )
              )
              .setValue(String.format("%s::%s", HIN, HC_VER)));
      patient.addExtension(new Extension().setUrl(URL_HC_TYPE).setValue(new StringType(HC_TYPE)));
      fhir.setSubject(new Reference(patient));
      val attachment = new Attachment().setData(testData);
      fhir.addContent().setAttachment(attachment);

      val eform = converter.toOscarEform(fhir);

      assertNotNull(eform);
      assertEquals(1, eform.getId());
      assertFalse(eform.isShowLatestFormOnly());
      assertEquals(EFORM_NAME, eform.getFormName());
      assertEquals(eformDate, eform.getFormDate());
      assertEquals(eformDate.getTime(), eform.getFormTime().getTime());
      assertEquals(EFORM_SUBJECT, eform.getSubject());
      // assert identifier with guid_system has correct guid and assigner
      assertEquals(EFORM_GUID, eform.getGuid());
      assertEquals(1, eform.getRemoteSystemId());
      assertTrue(eform.isStatus());
      assertEquals(DEMOGRAPHIC_NO, eform.getDemographicNo());
      assertEquals(Base64.getEncoder().encodeToString(testData), eform.getFormData());
    }

    // printable service is used when attaching local pdf content
    @SneakyThrows
    @Test
    void givenLocalEform_whenToFhirObject_thenAttachmentIsAdded() {
      try (val ignored = mockConstruction(Printable.class);
          val ignored1 = mockStatic(FileUtils.class)) {

        val dummyPdfContentBytes = DUMMY_PDF_CONTENT.getBytes();
        val eform = new EForm();
        eform.setId(1L);
        eform.setFormName("Sample Form");
        eform.setFormDate(new Date());
        eform.setFormData(DUMMY_PDF_CONTENT);
        eform.setRemoteSystemId(null);

        when(FileUtils.readFileToByteArray(any())).thenReturn(dummyPdfContentBytes);
        when(printableService.expandAndPrintPrintables(any())).thenReturn("tempPath");

        val fhir = converter.toFhirObject(eform, true);

        assertNotNull(fhir.getContent());
        assertFalse(fhir.getContent().isEmpty());
        val attachment = fhir.getContentFirstRep().getAttachment();
        assertNotNull(attachment);
        assertEquals(dummyPdfContentBytes, attachment.getData());
        assertEquals("application/pdf", attachment.getContentType());
        assertEquals(eform.getFormName(), attachment.getTitle());
        assertEquals(eform.getFormDate(), attachment.getCreation());
        assertEquals(dummyPdfContentBytes.length, attachment.getSize());
      }

    }

    // printable service is not used when attaching remote pdf content (uses base64 encoded data)
    @SneakyThrows
    @Test
    void givenRemoteEform_whenToFhirObject_thenAttachmentIsAdded() {
      val dummyPdfContentBytes = Base64.getEncoder().encodeToString(DUMMY_PDF_CONTENT.getBytes());
      val eform = new EForm();
      eform.setId(1L);
      eform.setFormName("Sample Form");
      eform.setFormDate(new Date());
      eform.setFormData(dummyPdfContentBytes);
      eform.setRemoteSystemId(1);

      val fhir = converter.toFhirObject(eform, true);

      assertNotNull(fhir.getContent());
      assertFalse(fhir.getContent().isEmpty());
      val attachment = fhir.getContentFirstRep().getAttachment();
      assertNotNull(attachment);
      assertEquals(DUMMY_PDF_CONTENT, new String(attachment.getData()));
      assertEquals("application/pdf", attachment.getContentType());
      assertEquals(eform.getFormName(), attachment.getTitle());
      assertEquals(eform.getFormDate(), attachment.getCreation());
      assertEquals(DUMMY_PDF_CONTENT.length(), attachment.getSize());
    }

    @Test
    void givenPdfDataAndEform_whenAddPdfAttachment_thenAttachmentIsAdded() {
      val fhir = new DocumentReference();
      val pdfData = DUMMY_PDF_CONTENT.getBytes();

      val eform = new EForm();
      eform.setId(1L);
      eform.setFormName("Sample Form");
      eform.setFormDate(new Date());
      eform.setSubject("Sample Subject");

      // Call the extracted helper method
      converter.addPdfAttachment(fhir, pdfData, eform);

      // Verify that the attachment was added correctly
      assertNotNull(fhir.getContent());
      assertFalse(fhir.getContent().isEmpty());
      val attachment = fhir.getContentFirstRep().getAttachment();
      assertNotNull(attachment);
      assertArrayEquals(pdfData, attachment.getData());
      assertEquals("application/pdf", attachment.getContentType());
      assertEquals(eform.getFormName(), attachment.getTitle());
      assertEquals(eform.getFormDate(), attachment.getCreation());
      assertEquals(pdfData.length, attachment.getSize());
    }
  }

  @Nested
  class OscarHrmTest {

    public static final String HRM_GUID = "guid";
    public static final String HRM_STATUS = "S";
    public static final String HRM_DESCRIPTION = "description";
    public static final String HRM_REPORT_FILE = "test-report.xml";
    public static final String HRM_REPORT_DATA = "test-report-data";
    private static final String HRM_REPORT_TYPE = "test-report-type";
    public static final String TEST_REPORT_SUB_CLASS = "Test^Report-Sub-Class";
    private MockedStatic<HRMParser> hrmParserMockedStatic;

    @AfterEach
    void tearDown() {
      if (hrmParserMockedStatic != null) {
        hrmParserMockedStatic.close();
      }
    }

    @Test
    void givenNullHrm_whenToFhirObject_thenReturnNull() {
      assertNull(converter.toFhirObject((HRMDocument) null));
    }

    @Test
    void givenNullDocumentReference_whenToOscarObject_thenReturnNull() {
      assertNull(converter.toOscarObject(null));
    }

    @Test
    void givenHrmDocument_whenToFhirObject_thenReturnDocumentReference() {
      val hrmDocument = createHrmDocument();
      hrmParserMockedStatic = Mockito.mockStatic(HRMParser.class);
      hrmParserMockedStatic.when(() -> HRMParser.parseReport(hrmDocument.getReportFile()))
          .thenReturn(hrmReport);
      when(hrmReport.getFileData()).thenReturn(HRM_REPORT_DATA);
      when(hrmReport.getFirstReportClass()).thenReturn(StringUtils.EMPTY);
      when(hrmReport.getFirstReportSubClass()).thenReturn(TEST_REPORT_SUB_CLASS);
      val result = converter.toFhirObject(hrmDocument);

      assertNotNull(result);
      assertEquals(String.valueOf(hrmDocument.getId()), result.getIdElement().getIdPart());
      assertEquals(HRM_GUID, result.getIdentifierFirstRep().getValue());
      // HRM_STATUS "S" maps to FINAL
      assertEquals(ReferredDocumentStatus.FINAL, result.getDocStatus());
      assertEquals(HRM_DATE, result.getDate());
      assertEquals(HRM_DESCRIPTION, result.getDescription());
      val resultAttachment = result.getContentFirstRep().getAttachment();
      assertEquals(HRM_REPORT_FILE, resultAttachment.getTitle());
      assertEquals(HRM_REPORT_DATA, new String(resultAttachment.getData()));
      assertEquals(HRM_DATE, resultAttachment.getCreation());
      assertEquals(HRM_REPORT_TYPE, result.getType().getText());
      assertEquals(
          "Report-Sub-Class",
          result.getExtensionByUrl(HRM_SUBCLASS_URL).getValueAsPrimitive().getValueAsString()
      );
    }

    @Test
    void givenHrmDocumentWithAccompanyingSubClass_whenToFhirObject_thenReturnDocumentReference() {
      val hrmDocument = createHrmDocument();
      hrmParserMockedStatic = Mockito.mockStatic(HRMParser.class);
      hrmParserMockedStatic.when(() -> HRMParser.parseReport(hrmDocument.getReportFile()))
          .thenReturn(hrmReport);
      when(hrmReport.getFileData()).thenReturn(HRM_REPORT_DATA);
      when(hrmReport.getFirstReportClass()).thenReturn("Diagnostic Imaging Report");
      when(hrmReport.getFirstAccompanyingSubClass()).thenReturn(TEST_REPORT_SUB_CLASS);
      val result = converter.toFhirObject(hrmDocument);

      assertNotNull(result);
      assertEquals(String.valueOf(hrmDocument.getId()), result.getIdElement().getIdPart());
      assertEquals(
          TEST_REPORT_SUB_CLASS,
          result.getExtensionByUrl(HRM_SUBCLASS_URL).getValueAsPrimitive().getValueAsString()
      );
    }

    @Test
    void givenDocumentReference_whenToHrmObject_thenReturnHrmDocument() {
      val fhir = new DocumentReference();
      fhir.setId("1");
      fhir.addIdentifier().setValue(HRM_GUID).setSystem(GUID_SYSTEM);
      fhir.setDate(HRM_DATE);
      fhir.setDocStatus(ReferredDocumentStatus.FINAL);
      fhir.setDescription(HRM_DESCRIPTION);
      val attachment = new Attachment();
      attachment.setTitle(HRM_REPORT_FILE);
      attachment.setData(HRM_REPORT_DATA.getBytes());
      fhir.addContent().setAttachment(attachment);

      val result = converter.toHrmDocument(fhir);

      assertNotNull(result);
      assertEquals(1, result.getId());
      assertEquals(HRM_GUID, result.getGuid());
      assertEquals(HRM_STATUS, result.getReportStatus());
      assertEquals(HRM_DATE, result.getReportDate());
      assertEquals(HRM_DESCRIPTION, result.getDescription());
      assertEquals(HRM_REPORT_FILE, result.getReportFile());
      assertEquals(HRM_REPORT_DATA, result.getReport().getFileData());
    }

    @Test
    void givenDocRefWithGuid_whenToHrmDoc_thenGuidIsSetCorrectly() {
      // Arrange
      val fhir = new DocumentReference();
      fhir.addIdentifier()
          .setValue(HRM_GUID)
          .setSystem(GUID_SYSTEM);
      // Act
      val hrmDocument = converter.toHrmDocument(fhir);
      // Assert
      assertNotNull(hrmDocument);
      assertEquals(HRM_GUID, hrmDocument.getGuid());
    }

    @Test
    void givenDocRefWithoutGuid_whenToHrmDoc_thenGuidIsGenerated() {
      // Arrange
      val fhir = new DocumentReference();
      fhir.setId("1");
      // Act
      val hrmDocument = converter.toHrmDocument(fhir);
      // Assert
      assertNotNull(hrmDocument);
      assertNotNull(hrmDocument.getGuid());
      assertFalse(hrmDocument.getGuid().isEmpty());
      assertEquals(36, hrmDocument.getGuid().length());
    }

    private HRMDocument createHrmDocument() {
      val hrmDocument = new HRMDocument();
      hrmDocument.setId(1);
      hrmDocument.setGuid(HRM_GUID);
      hrmDocument.setReportStatus(HRM_STATUS);
      hrmDocument.setReportDate(HRM_DATE);
      hrmDocument.setDescription(HRM_DESCRIPTION);
      hrmDocument.setReportFile(HRM_REPORT_FILE);
      hrmDocument.setTimeReceived(HRM_DATE);
      hrmDocument.setReportType(HRM_REPORT_TYPE);
      return hrmDocument;
    }
  }

    private void assertReference(Reference reference, String type, String display, String id) {
        assertNotNull(reference);
        assertEquals(type, reference.getType());
        assertEquals(display, reference.getDisplay());
        assertEquals(id, reference.getId());
    }

    public void setupMocks(boolean setupCode) {
        setupIssueMocks(66L, "MedHistory", "Medical History as part of cpp", setupCode);
        setupIssueMocks(1L, "PastOcularHistory", "Past Ocular History", setupCode);
    }

    private void setupIssueMocks(
        long id,
        String code,
        String description,
        boolean mockCodeSearch
    ) {
        val issue = new Issue();
        issue.setId(id);
        issue.setCode(code);
        issue.setDescription(description);
        issue.setRole("nurse");
        issue.setUpdateDate(new Date());
        issue.setType("system");
        issue.setSortOrderId(0);

        when(issueRepository.findOne(id)).thenReturn(issue);

        if (mockCodeSearch) {
            when(issueRepository.findByCode(code)).thenReturn(issue);
        }

        when(encounterConverter.toFhirObject(any())).thenReturn(new Encounter());
    }

  private static void assertEmptyNote(CaseManagementNote note) {
    assertNotNull(note);
    assertNull(note.getId());
    assertNull(note.getUpdateDate());
    assertNull(note.getObservationDate());
    assertNull(note.getDemographicNo());
    assertNull(note.getProviderNo());
    assertNull(note.getNote());
    assertFalse(note.getSigned());
    assertTrue(note.getIncludeIssueInNote());
    assertNull(note.getSigningProviderNo());
    assertEquals("", note.getEncounterType());
    assertEquals("", note.getBillingCode());
    assertNull(note.getProgramNo());
    assertNull(note.getReporterCaisiRole());
    assertNull(note.getReporterProgramTeam());
    assertNull(note.getHistory());
    assertNull(note.getPassword());
    assertFalse(note.getLocked());
    assertFalse(note.getArchived());
    assertEquals(Integer.valueOf(0), note.getPosition());
    assertEquals(Integer.valueOf(0), note.getAppointmentNo());
    assertNull(note.getUuid());
    assertNull(note.getHourOfEncounterTime());
    assertNull(note.getMinuteOfEncounterTime());
    assertNull(note.getHourOfEncTransportationTime());
    assertNull(note.getMinuteOfEncTransportationTime());
    assertFalse(note.isAvailable());
    assertNull(note.getAutoSyncDate());
    assertNull(note.getLastSyncedDate());
    assertEquals(note.getIssues().size(), 0);
  }
}
