package ca.kai.document;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import ca.kai.OscarProperties;
import ca.kai.caseManagementNote.CaseManagementNoteLinkRepository;
import ca.kai.caseManagementNote.CaseManagementNoteRepository;
import ca.kai.ctlDocument.CtlDocumentRepository;
import ca.kai.demographic.DemographicExtRepository;
import ca.kai.demographic.DemographicRepository;
import ca.kai.incomingLabRules.IncomingLabRuleRepository;
import ca.kai.incomingLabRules.IncomingLabRuleService;
import ca.kai.patientLabRouting.PatientDocumentRoutingRepository;
import ca.kai.program.ProgramService;
import ca.kai.provider.ProviderRepository;
import ca.kai.providerLabRouting.ProviderDocumentRoutingRepository;
import ca.kai.secRole.SecRoleRepository;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import javax.imageio.ImageIO;
import lombok.val;
import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DocumentServiceTest {

  @InjectMocks
  private DocumentService documentService;
  private DocumentService spyDocumentService;

  @Mock
  private DocumentRepository documentRepository;
  @Mock
  private DocumentCommentRepository documentCommentRepository;
  @Mock
  private QueueDocumentLinkRepository queueDocumentLinkRepository;
  @Mock
  private DemographicRepository demographicRepository;
  @Mock
  private ProviderRepository providerRepository;
  @Mock
  private ProviderDocumentRoutingRepository providerDocumentRoutingRepository;
  @Mock
  private PatientDocumentRoutingRepository patientDocumentRoutingRepository;
  @Mock
  private CtlDocumentRepository ctlDocumentRepository;
  @Mock
  private SecRoleRepository secRoleRepository;
  @Mock
  private CaseManagementNoteRepository caseManagementNoteRepository;
  @Mock
  private CaseManagementNoteLinkRepository caseManagementNoteLinkRepository;
  @Mock
  private IncomingLabRuleRepository incomingLabRuleRepository;
  @Mock
  private DemographicExtRepository demographicExtRepository;
  @Mock
  private IncomingLabRuleService incomingLabRuleService;
  @Mock
  private ProgramService programService;
  @Mock
  private OscarProperties oscarProperties;
  @Mock
  private PDDocument pdDocument;
  @Mock
  private Document document;
  @Mock
  private File mockFile;

  private MockedStatic<FileUtils> fileUtilsMockedStatic;

  private static final byte BYTE_ZERO = 0;
  private static final byte BYTE_ONE = 1;
  private static final byte BYTE_TWO = 2;
  private static final byte BYTE_THREE = 3;
  private static final byte BYTE_FOUR = 4;

  @Before
  public void setUp() throws IOException {
    documentService = new DocumentService(
        documentRepository,
        documentCommentRepository,
        queueDocumentLinkRepository,
        demographicRepository,
        providerRepository,
        providerDocumentRoutingRepository,
        patientDocumentRoutingRepository,
        ctlDocumentRepository,
        secRoleRepository,
        caseManagementNoteRepository,
        caseManagementNoteLinkRepository,
        incomingLabRuleRepository,
        demographicExtRepository,
        incomingLabRuleService,
        programService,
        oscarProperties
    );
    spyDocumentService = spy(documentService);
    when(oscarProperties.getProperty("kaiemr_deployed_context", "kaiemr")).thenReturn("kaiemr");

    val date = new Date();
    val testDocumentOne = new Document();
    testDocumentOne.setDocumentNo(1);
    testDocumentOne.setDocFileName("testdocument1");
    testDocumentOne.setDocDesc("Test document 1");
    testDocumentOne.setObservationDate(date);
    testDocumentOne.setDocFileName("test.pdf");
    testDocumentOne.setContentType("application/pdf");

    when(documentRepository.getDocumentById(1)).thenReturn(testDocumentOne);
    byte[] byteArray;
    try (val testPdf = createTestPdf()) {
      val byteArrayOutputStream = new ByteArrayOutputStream();
      testPdf.save(byteArrayOutputStream);
       byteArray = byteArrayOutputStream.toByteArray();
    }
    fileUtilsMockedStatic = mockStatic(FileUtils.class);
    fileUtilsMockedStatic.when(() -> FileUtils.readFileToByteArray(any())).thenReturn(byteArray);
  }

  @After
  public void tearDown() {
    fileUtilsMockedStatic.close();
  }

  @Test
  public void testDocumentPagePreviewUrlGeneratorWithDocument() {
    // Given
    Document document = new Document();
    document.setDocumentNo(123);
    // When
    String result = documentService.documentPagePreviewUrlGenerator(document);
    // Then
    assertEquals("/kaiemr/api/document/getPagePreview/123?page=1", result);
  }

  @Test
  public void testDocumentPagePreviewUrlGeneratorWithDocumentNumberAndPage() {
    // Given
    Integer documentNumber = 456;
    Integer page = 2;
    // When
    String result = documentService.documentPagePreviewUrlGenerator(documentNumber, page);
    // Then
    assertEquals("/kaiemr/api/document/getPagePreview/456?page=2", result);
  }

  @Test
  public void testPrintWithTempFile_success() throws IOException {
    val path = documentService.printWithTempFile(1);
    assertNotNull(path);
  }

  private PDDocument createTestPdf() throws IOException {
    val testPdf = new PDDocument();
    val page = new PDPage();
    testPdf.addPage(page);
    val contentStream = new PDPageContentStream(testPdf, page);
    contentStream.setFont(PDType1Font.HELVETICA, 12);
    contentStream.beginText();
    contentStream.showText("Test PDF");
    contentStream.endText();
    contentStream.close();
    return testPdf;
  }

  @Test
  public void givenInvalidImage_whenConvertImageToPdf_thenRuntimeExceptionIsThrown() {
    val invalidBytes = new byte[]{BYTE_ZERO, BYTE_ONE, BYTE_TWO, BYTE_THREE, BYTE_FOUR};
    Assertions.assertThrows(RuntimeException.class, () -> {
      documentService.convertImageToPdf(invalidBytes);
    }, "Should throw RuntimeException for invalid bytes");
  }

  @Test
  public void givenNullImage_whenConvertImageToPdf_thenRuntimeExceptionIsThrown() {
    Assertions.assertThrows(RuntimeException.class, () -> {
      documentService.convertImageToPdf(null);
    }, "Should throw RuntimeException for null bytes");
  }

  @Test
  public void givenNullDocument_whenGetAMPreview_thenReturnEmptyByteArray() {
    val result = documentService.getDocumentForAttachmentManagerPreview(null, 5);

    assertNotNull(result);
    assertEquals(0, result.length);
  }

  @Test
  public void givenNullPageCount_whenGetAMPreview_thenDefaultToFive() {
    when(document.getContentType()).thenReturn("application/pdf");
    doReturn(mockFile).when(spyDocumentService).readDocumentFile(document);

    val expectedBytes = new byte[] {1, 2, 3, 4, 5};
    doReturn(expectedBytes).when(spyDocumentService).renderPdfPages(any(PDDocument.class), eq(5));

    try (val pdDocumentMock = mockStatic(PDDocument.class)) {
      val mockPDDocument = mock(PDDocument.class);
      pdDocumentMock.when(() -> PDDocument.load(mockFile)).thenReturn(mockPDDocument);

      val result = spyDocumentService.getDocumentForAttachmentManagerPreview(document, null);

      assertArrayEquals(expectedBytes, result);
      verify(spyDocumentService).renderPdfPages(mockPDDocument, 5);
    }
  }

  @Test
  public void givenPdfDocument_whenGetAMPreview_thenReturnPreviewByteArray() {
    when(document.getContentType()).thenReturn("application/pdf");
    doReturn(mockFile).when(spyDocumentService).readDocumentFile(document);

    val expectedBytes = new byte[] {80, 68, 70, 45, 49, 46, 52};
    doReturn(expectedBytes).when(spyDocumentService).renderPdfPages(any(PDDocument.class), eq(3));

    try (val pdDocumentMock = mockStatic(PDDocument.class)) {
      val mockPDDocument = mock(PDDocument.class);
      pdDocumentMock.when(() -> PDDocument.load(mockFile)).thenReturn(mockPDDocument);

      val result = spyDocumentService.getDocumentForAttachmentManagerPreview(document, 3);

      assertArrayEquals(expectedBytes, result);
      verify(spyDocumentService).renderPdfPages(mockPDDocument, 3);
    }
  }

  @Test
  public void givenImageDocument_whenGetAMPreview_thenDefaultToFive() throws IOException {
    when(document.getContentType()).thenReturn("image/jpeg");
    doReturn(mockFile).when(spyDocumentService).readDocumentFile(document);

    val expectedBytes = new byte[] {-1, -40, -1, -32, 0, 16, 74, 70, 73, 70};
    doReturn(expectedBytes)
        .when(spyDocumentService)
        .renderImage(eq(null), eq(mockFile), eq("jpeg"));

    try (val imageIOMock = mockStatic(ImageIO.class)) {
      String[] supportedFormats = {"jpeg", "jpg", "png", "gif", "bmp"};
      when(ImageIO.getReaderFormatNames()).thenReturn(supportedFormats);

      val result = spyDocumentService.getDocumentForAttachmentManagerPreview(document, 5);

      assertArrayEquals(expectedBytes, result);
      verify(spyDocumentService).renderImage(null, mockFile, "jpeg");
      verify(spyDocumentService).readDocumentFile(document);
    }
  }

  @Test
  public void givenUnsupportedDocumentType_whenGetAMPreview_thenReturnEmptyByteArray()
      throws IOException {
    when(document.getContentType()).thenReturn("application/msword");

    try (MockedStatic<ImageIO> imageIOMock = mockStatic(ImageIO.class)) {
      String[] supportedFormats = {"jpeg", "jpg", "png", "gif", "bmp"};
      imageIOMock.when(ImageIO::getReaderFormatNames).thenReturn(supportedFormats);

      byte[] result = spyDocumentService.getDocumentForAttachmentManagerPreview(document, 5);

      assertNotNull(result);
      assertEquals(0, result.length);
      verify(spyDocumentService, never()).renderPdfPages(any(), any());
      verify(spyDocumentService, never()).renderImage(any(), any(), any());
    }
  }

  @Test
  public void voidGivenNullDocumentType_whenGetAMPreview_thenReturnEmptyByteArray()
      throws IOException {
    when(document.getContentType()).thenReturn(null);

    try (MockedStatic<ImageIO> imageIOMock = mockStatic(ImageIO.class)) {
      String[] supportedFormats = {"jpeg", "jpg", "png", "gif", "bmp"};
      imageIOMock.when(ImageIO::getReaderFormatNames).thenReturn(supportedFormats);

      byte[] result = spyDocumentService.getDocumentForAttachmentManagerPreview(document, 5);

      assertNotNull(result);
      assertEquals(0, result.length);
      verify(spyDocumentService, never()).renderPdfPages(any(), any());
      verify(spyDocumentService, never()).renderImage(any(), any(), any());
    }
  }
}
