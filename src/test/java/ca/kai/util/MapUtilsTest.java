package ca.kai.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertFalse;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.oscarehr.common.model.DemographicExtKey;

public class MapUtilsTest {

  private static final String DEFAULT = "DEFAULT";
  private final HashMap<Object, Object> map = new HashMap<Object, Object>() {{
    put(DemographicExtKey.NURSE, DemographicExtKey.NURSE.getKey());
    put(DemographicExtKey.NURSE.getKey(), DemographicExtKey.NURSE.getKey());
    put("display_former_name_true", true);
    put("display_former_name_false", false);
    put("null_value", null);
  }};

  @Test
  public void testGetOrDefault_demographicExtKey_returnsValue() {
    assertEquals(
        DemographicExtKey.NURSE.getKey(),
        MapUtils.getOrDefault(map, DemographicExtKey.NURSE, DEFAULT)
    );
  }

  @Test
  public void testGetOrDefault_demographicExtKeyGetKey_returnsValue() {
    assertEquals(
        DEFAULT,
        MapUtils.getOrDefault(map, DemographicExtKey.MIDWIFE.getKey(), DEFAULT)
    );
  }

  @Test
  public void testGetOrDefault_demographicExtKeyNull_returnsDefault() {
    assertEquals(
        DemographicExtKey.NURSE.getKey(),
        MapUtils.getOrDefault(map, DemographicExtKey.NURSE, DEFAULT)
    );
  }

  @Test
  public void testGetOrDefault_stringBooleanTrue_returnsValue() {
    assertEquals(
        true,
        MapUtils.getOrDefault(map, "display_former_name_true", false)
    );
  }

  @Test
  public void testGetOrDefault_stringBooleanFalse_returnsValue() {
    assertEquals(
        false,
        MapUtils.getOrDefault(map, "display_former_name_false", true)
    );
  }

  @Test
  public void testGetOrDefault_stringBooleanNull_returnsDefault() {
    assertEquals(
        false,
        MapUtils.getOrDefault(map, "display_former_name_null", false)
    );
  }

  @Test
  public void testGetOrDefault_nullValue_returnsNull() {
    assertNull(MapUtils.getOrDefault(map, "null_value", "bad_value"));
  }

  @Test
  public void givenNullList_whenConvertQueryListToMap_thenReturnEmptyMap() {
    Map<String, String> result = MapUtils.convertQueryListToMap(null, String.class);
    assertTrue(result.isEmpty());
  }

  @Test
  public void givenEmptyList_whenConvertQueryListToMap_thenReturnEmptyMap() {
    Map<String, String> result = MapUtils.convertQueryListToMap(Collections.emptyList(),
        String.class);
    assertTrue(result.isEmpty());
  }

  @Test
  public void givenNullValueClass_whenConvertQueryListToMap_thenReturnEmptyMap() {
    List<Object[]> list = new ArrayList<>();
    list.add(new Object[]{"key1", "value1"});
    Map<String, Object> result = MapUtils.convertQueryListToMap(list, null);
    assertTrue(result.isEmpty());
  }

  @Test
  public void givenValidStringList_whenConvertQueryListToMap_thenReturnCorrectMap() {
    List<Object[]> list = new ArrayList<>();
    list.add(new Object[]{"key1", "value1"});
    list.add(new Object[]{"key2", "value2"});

    Map<String, String> result = MapUtils.convertQueryListToMap(list, String.class);

    assertEquals(2, result.size());
    assertEquals("value1", result.get("key1"));
    assertEquals("value2", result.get("key2"));
  }

  @Test
  public void givenMixedTypeList_whenConvertQueryListToMap_thenReturnCorrectMap() {
    List<Object[]> list = new ArrayList<>();
    list.add(new Object[]{"key1", 1});
    list.add(new Object[]{"key2", 2});

    Map<String, Integer> result = MapUtils.convertQueryListToMap(list, Integer.class);

    assertEquals(2, result.size());
    assertEquals(Integer.valueOf(1), result.get("key1"));
    assertEquals(Integer.valueOf(2), result.get("key2"));
  }

  @Test
  public void givenNumericTypes_whenConvertQueryListToMap_thenReturnCorrectMap() {
    List<Object[]> list = new ArrayList<>();
    list.add(new Object[]{"key1", 1L});  // Long value
    list.add(new Object[]{"key2", 2.0});  // Double value

    Map<String, Integer> result = MapUtils.convertQueryListToMap(list, Integer.class);

    assertEquals(2, result.size());
    assertEquals(Integer.valueOf(1), result.get("key1"));
    assertEquals(Integer.valueOf(2), result.get("key2"));
  }

  @Test
  public void givenInvalidEntries_whenConvertQueryListToMap_thenSkipsInvalidEntries() {
    List<Object[]> list = new ArrayList<>();
    list.add(new Object[]{"key1", "value1"});
    list.add(new Object[]{"key2"});  // Only one element
    list.add(new Object[]{"key3", "value3", "extra"});  // Three elements

    Map<String, String> result = MapUtils.convertQueryListToMap(list, String.class);

    assertEquals(1, result.size());
    assertEquals("value1", result.get("key1"));
    assertFalse(result.containsKey("key2"));
    assertFalse(result.containsKey("key3"));
  }

  @Test
  public void givenTypeMismatch_whenConvertQueryListToMap_thenSkipsInvalidType() {
    List<Object[]> list = new ArrayList<>();
    list.add(new Object[]{"key1", "value1"});
    list.add(new Object[]{"key2", 2});  // Integer instead of String

    Map<String, String> result = MapUtils.convertQueryListToMap(list, String.class);

    assertEquals(1, result.size());
    assertEquals("value1", result.get("key1"));
    assertFalse(result.containsKey("key2"));
  }

  @Test
  public void givenNullKey_whenConvertQueryListToMap_thenSkipsEntry() {
    List<Object[]> list = new ArrayList<>();
    list.add(new Object[]{"key1", "value1"});
    list.add(new Object[]{null, "value2"});  // Null key

    Map<String, String> result = MapUtils.convertQueryListToMap(list, String.class);

    assertEquals(1, result.size());
    assertEquals("value1", result.get("key1"));
  }
}
