package ca.kai.util.fhir;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.lenient;

import ca.kai.OscarProperties;
import ca.kai.clinic.Clinic;
import ca.kai.clinic.ClinicService;
import ca.kai.demographic.DemographicRepository;
import ca.kai.demographic.DemographicService;
import ca.kai.demographic.PatientExternalPharmacyIdRepository;
import ca.kai.integration.eRx.AppConfig;
import ca.kai.integration.eRx.ClinicConfig;
import ca.kai.integration.infoway.TerminologyGateway;
import ca.kai.pharmacy.PharmacyInfoRepository;
import ca.kai.property.PropertyService;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderService;
import ca.kai.rx.drug.DrugRepository;
import ca.kai.rx.drugDatabases.fdb.FdbService;
import ca.kai.rx.externalPrescriptions.prescribeIT.TaskRepository;
import ca.kai.systemPreference.SystemPreferenceService;
import ca.kai.util.LocationsService;
import lombok.val;
import org.hl7.fhir.dstu2016may.model.MedicationOrder;
import org.hl7.fhir.dstu2016may.model.Organization;

import org.hl7.fhir.dstu2016may.model.Quantity;
import org.hl7.fhir.dstu2016may.model.SimpleQuantity;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;


@ExtendWith(MockitoExtension.class)
public class FhirConversionServiceTest {

  @Mock
  private ClinicService clinicService;
  @Mock
  private DemographicService demographicService;
  @Mock
  private DrugRepository drugRepository;
  @Mock
  private FdbService fdbService;
  @Mock
  private LocationsService locationsService;
  @Mock
  private OscarProperties oscarProperties;
  @Mock
  private PharmacyInfoRepository pharmacyInfoRepository;
  @Mock
  private PropertyService propertyService;
  @Mock
  private ProviderService providerService;
  @Mock
  private TaskRepository taskRepository;
  @Mock
  private PatientExternalPharmacyIdRepository patientExternalPharmacyIdRepository;
  @Mock
  private DemographicRepository demographicRepository;
  @Mock
  private SystemPreferenceService systemPreferenceService;
  @Mock
  private TerminologyGateway terminologyGateway;
  @Mock
  private static AppConfig appConfig;
  @Mock
  private static ClinicConfig clinicConfig;

  private FhirConversionService fhirConversionService;

  private MockedStatic<AppConfig> appConfigMockedStatic;

  private MockedStatic<ClinicConfig> clinicConfigMockedStatic;

  private Clinic clinic;

  private Provider provider;

  private static final String RX_PHONE = "rxPhone";
  private static final String RX_ADDRESS = "rxAddress";
  private static final String RX_CITY = "rxCity";
  private static final String RX_PROVINCE = "rxProvince";
  private static final String RX_POSTAL = "rxPostal";
  private static final String RX_FAX = "faxnumber";


  @BeforeEach
  public void init() throws Exception {
    initConfigInstances();
    MockitoAnnotations.openMocks(this);
    createFhirConversionService();
    initBaseClasses();
    initBaseMocks();
  }

  @AfterEach
  public void tearDown() {
    appConfigMockedStatic.close();
    clinicConfigMockedStatic.close();
  }

  @Test
  public void givenDisabledPreference_whenClinicToOrganization_thenReturnClinicInformation() {
    when(systemPreferenceService.readBooleanPreference(anyString())).thenReturn(false);
    val results = fhirConversionService.clinicToOrganization(clinic, provider);
    assertUsingClinicInformation(results);
  }

  @Test
  public void givenNullProvider_whenClinicToOrganization_thenReturnClinicInformation() {
    val results = fhirConversionService.clinicToOrganization(clinic, null);
    assertUsingClinicInformation(results);
  }

  @Test
  public void givenProviderWithNoProperties_whenClinicToOrganization_thenReturnClinicInformation() {
    val results = fhirConversionService.clinicToOrganization(clinic, provider);
    assertUsingClinicInformation(results);
  }

  @Test
  public void givenProviderWithEmptyProperties_whenClinicToOrganization_thenReturnClinicInformation() {
    setEmptyProviderProperties(provider);
    val results = fhirConversionService.clinicToOrganization(clinic, provider);
    assertUsingClinicInformation(results);
  }

  @Test
  public void givenProviderWithProperties_whenClinicToOrganization_thenReturnProviderProperties() {
    when(systemPreferenceService.readBooleanPreference(anyString())).thenReturn(true);
    setProviderProperties(provider);
    val results = fhirConversionService.clinicToOrganization(clinic, provider);
    assertUsingProviderProperties(results);
  }

  @Test
  public void givenProviderAddress_whenClinicToOrganization_thenOnlyUseProviderPropertyAddress() {
    setEmptyProviderProperties(provider);
    when(systemPreferenceService.readBooleanPreference(anyString())).thenReturn(true);
    when(propertyService.readProperty("rxAddress", provider.getProviderNo()))
        .thenReturn("456 Provider St");
    val results = fhirConversionService.clinicToOrganization(clinic, provider);
    assertEquals("456 Provider St", results.getAddress().get(0).getLine().get(0).getValue());
    assertNotEquals("Toronto", results.getAddress().get(0).getCity());
    assertNotEquals("ON", results.getAddress().get(0).getState());
    assertNotEquals("M1M 1M1", results.getAddress().get(0).getPostalCode());
  }

  // ********************************* HELPER METHODS ********************************* //

  private void initConfigInstances() {
    appConfigMockedStatic = mockStatic(AppConfig.class);
    clinicConfigMockedStatic = mockStatic(ClinicConfig.class);
    appConfigMockedStatic
        .when(AppConfig::getInstance)
        .thenReturn(appConfig);
    clinicConfigMockedStatic
        .when(ClinicConfig::getInstance)
        .thenReturn(clinicConfig);
  }

  private void initBaseClasses() {
    clinic = new Clinic();
    provider = new Provider();
    setClinicInformation(clinic);
  }

  private void initBaseMocks() {
    lenient().when(locationsService.getSubdivisionShortNameFromCode(anyString())).thenReturn("ON");
  }

  private void createFhirConversionService() {
    fhirConversionService = new FhirConversionService(
        terminologyGateway,
        fdbService,
        clinicService,
        demographicService,
        drugRepository,
        locationsService,
        oscarProperties,
        pharmacyInfoRepository,
        propertyService,
        providerService,
        taskRepository,
        patientExternalPharmacyIdRepository,
        demographicRepository,
        systemPreferenceService
    );
  }

  private void setClinicInformation(Clinic clinic) {
    clinic.setAddress("123 Clinic St");
    clinic.setCity("Toronto");
    clinic.setProvince("ON");
    clinic.setPostalCode("M1M 1M1");
    clinic.setPhone("************");
    clinic.setFax("************");
  }

  private void assertUsingClinicInformation(Organization organization) {
    assertEquals("123 Clinic St", organization.getAddress().get(0).getLine().get(0).getValue());
    assertEquals("Toronto", organization.getAddress().get(0).getCity());
    assertEquals("ON", organization.getAddress().get(0).getState());
    assertEquals("M1M 1M1", organization.getAddress().get(0).getPostalCode());
    assertEquals("(*************", organization.getTelecom().get(0).getValue());
    assertEquals("(*************", organization.getTelecom().get(1).getValue());
  }

  private void assertUsingProviderProperties(Organization organization) {
    assertEquals("(*************", organization.getTelecom().get(0).getValue());
    assertEquals("(*************", organization.getTelecom().get(1).getValue());
    assertEquals("456 Provider St", organization.getAddress().get(0).getLine().get(0).getValue());
    assertEquals("Oshawa", organization.getAddress().get(0).getCity());
    assertEquals("ON", organization.getAddress().get(0).getState());
    assertEquals("V8V 1V1", organization.getAddress().get(0).getPostalCode());
  }

  private void setProviderProperties(
      Provider provider,
      String phone,
      String fax,
      String address,
      String city,
      String province,
      String postal
  ) {
    when(propertyService.readProperty(RX_PHONE, provider.getProviderNo()))
        .thenReturn(phone);
    when(propertyService.readProperty(RX_FAX, provider.getProviderNo()))
        .thenReturn(fax);
    when(propertyService.readProperty(RX_ADDRESS, provider.getProviderNo()))
        .thenReturn(address);
    lenient().when(propertyService.readProperty(RX_CITY, provider.getProviderNo()))
        .thenReturn(city);
    lenient().when(propertyService.readProperty(RX_PROVINCE, provider.getProviderNo()))
        .thenReturn(province);
    lenient().when(propertyService.readProperty(RX_POSTAL, provider.getProviderNo()))
        .thenReturn(postal);
  }

  private void setProviderProperties(Provider provider) {
    setProviderProperties(provider,
        "************",
        "************",
        "456 Provider St",
        "Oshawa",
        "ON",
        "V8V 1V1");
  }

  private void setEmptyProviderProperties(Provider provider) {
    setProviderProperties(provider, "", "", "", "", "", "");
  }


  private MedicationOrder createBaseMedicationOrder() {
    val medicationOrder = new MedicationOrder();
    // Set status
    medicationOrder.setStatus(MedicationOrder.MedicationOrderStatus.ACTIVE);

    return medicationOrder;
  }

  @Test
  void givenMedicalOrderHasDispenseRequest_whenMedicationOrderToDrug_thenDrugParsesCorrectData() {

    val expectedQuantity = 10;
    val expectedUnit = "mg";
    val expectedRepeat = 3;

    val medicationOrder = createBaseMedicationOrder();
    val quantity = new SimpleQuantity();
    quantity.setValue(expectedQuantity);
    quantity.setUnit(expectedUnit);
    medicationOrder.getDispenseRequest()
        .setQuantity(quantity)
        .setNumberOfRepeatsAllowed(expectedRepeat);

    val actualDrug = fhirConversionService.medicationOrderToDrug(medicationOrder);

    assertEquals(expectedQuantity, Integer.parseInt(actualDrug.getQuantity()));
    assertEquals(expectedUnit, actualDrug.getQuantityUnit());
    assertEquals(expectedUnit, actualDrug.getDispensingUnits());
    assertEquals(expectedRepeat, actualDrug.getRepeat());

  }
}