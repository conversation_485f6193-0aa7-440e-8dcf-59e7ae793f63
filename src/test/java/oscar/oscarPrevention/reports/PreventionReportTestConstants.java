/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package oscar.oscarPrevention.reports;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;

/**
 * Common constants for prevention report tests
 */
public class PreventionReportTestConstants {
    // Date format constants
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String AS_OF_DATE_STRING = "2025-04-01";
    public static final Date AS_OF_DATE;
    public static final LocalDate AS_OF_LOCAL_DATE;

    // Basic test constants
    public static final String DEMOGRAPHIC_NO = "1";
    public static final String ZERO_RESULTS = "0";

    // Field name constants
    public static final String FIELD_REFUSED = "refused";
    public static final String FIELD_PREVENTION_DATE = "prevention_date";
    public static final String FIELD_PREVENTION_DATE_NO_TIME = "prevention_date_no_time";
    public static final String FIELD_NEXT_DATE = "nextDate";
    public static final String FIELD_ID = "id";
    public static final String FIELD_RESULT = "result";
    public static final String FIELD_INELIGIBLE = "inEligible";
    public static final String FIELD_UP2DATE = "up2date";
    public static final String FIELD_PERCENT = "percent";
    public static final String FIELD_PERCENT_WITH_GRACE = "percentWithGrace";
    public static final String FIELD_RETURN_REPORT = "returnReport";
    public static final String FIELD_BILL_CODE = "BillCode";
    
    // Bonus and bill status constants
    public static final String BONUS_STATUS_YES = "Y";
    public static final String BONUS_STATUS_NO = "N";
    public static final String BILL_STATUS_YES = "Y";
    public static final String BILL_STATUS_NO = "N";

    // Refused status constants
    public static final String REFUSED_STATUS_ELIGIBLE_0 = "0"; // Patient is eligible and has not refused
    public static final String REFUSED_STATUS_ELIGIBLE_1 = "1"; // Patient is eligible but has refused
    public static final String REFUSED_STATUS_INELIGIBLE = "2"; // Patient is ineligible for this prevention

    // Result value constants
    public static final String RESULT_PENDING = "pending";
    public static final String RESULT_EMPTY = "";

    // State constants
    public static final String STATE_UP_TO_DATE = "Up to date";
    public static final String STATE_DUE = "due";
    public static final String STATE_OVERDUE = "Overdue";
    public static final String STATE_REFUSED = "Refused";
    public static final String STATE_INELIGIBLE = "Ineligible";
    public static final String STATE_PENDING = "Pending";
    public static final String STATE_NO_INFO = "No Info";
    public static final String STATE_NO_DATA = "------";

    // Color constants
    public static final String COLOR_GREEN = "green";
    public static final String COLOR_YELLOW = "yellow";
    public static final String COLOR_RED = "red";
    public static final String COLOR_ORANGE = "orange";
    public static final String COLOR_PINK = "pink";
    public static final String COLOR_MAGENTA = "Magenta";
    public static final String COLOR_GREY = "grey";

    // Prevention type constants
    public static final String PAP_TYPE = "PAP";
    public static final String HPV_TYPE = "HPV Test";
    public static final String MAM_TYPE = "MAM";
    public static final String FLU_TYPE = "Flu";
    public static final String FIT_TYPE = "FIT";
    public static final String COLONOSCOPY_TYPE = "COLONOSCOPY";
    
    // Child immunization constants
    public static final String DTAP_IPV_HIB_TYPE = "DTaP-IPV-Hib";
    public static final String PNEU_C_TYPE = "Pneu-C";
    public static final String ROT_TYPE = "Rot";
    public static final String MENC_C_TYPE = "MenC-C";
    public static final String MMR_TYPE = "MMR";

    // Time interval constants
    public static final int THREE_YEARS = 3;
    public static final int FIVE_YEARS = 5;
    public static final int SIX_MONTHS = 6;

    public static final String TEST_ID = "123";
    public static final String TEST_ID_2 = "456";
    public static final String TEST_ID_3 = "789";

    static {
        Date asOfDate;
        try {
            asOfDate = new SimpleDateFormat(DATE_FORMAT).parse(AS_OF_DATE_STRING);
        } catch (ParseException e) {
            throw new IllegalStateException("Failed to parse AS_OF_DATE_STRING", e);
        }
        AS_OF_DATE = asOfDate;
        AS_OF_LOCAL_DATE = LocalDate.parse(AS_OF_DATE_STRING);
    }
}
