/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package oscar.oscarPrevention.reports;

import org.junit.Before;
import org.junit.Test;
import org.oscarehr.util.LoggedInInfo;
import oscar.oscarPrevention.pageUtil.PreventionReportDisplay;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.AS_OF_DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.AS_OF_LOCAL_DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_GREEN;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_MAGENTA;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_ORANGE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_PINK;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_RED;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_YELLOW;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.DEMOGRAPHIC_NO;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_BILL_CODE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_ID;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_INELIGIBLE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_PERCENT;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_PERCENT_WITH_GRACE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_PREVENTION_DATE_NO_TIME;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_REFUSED;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_RETURN_REPORT;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_UP2DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.REFUSED_STATUS_ELIGIBLE_0;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.REFUSED_STATUS_ELIGIBLE_1;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.REFUSED_STATUS_INELIGIBLE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.RESULT_EMPTY;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.RESULT_PENDING;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_DUE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_NO_INFO;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_OVERDUE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_PENDING;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_REFUSED;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_UP_TO_DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.TEST_ID;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.TEST_ID_2;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.TEST_ID_3;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.ZERO_RESULTS;

public class MammogramReportTest {
    private TestMammogramReport mammogramReport;
    private LoggedInInfo loggedInInfo;
    
    // Test date constants
    private static final String TEST_DATE_2023_01_01 = "2023-01-01";
    private static final String TEST_DATE_2024_01_01 = "2024-01-01";
    private static final String TEST_DATE_2025_01_01 = "2025-01-01";
    private static final String TEST_DATE_2026_01_01 = "2026-01-01";

    @Before
    public void setUp() {
        mammogramReport = new TestMammogramReport();
        loggedInInfo = new LoggedInInfo();
    }

    // Test double class that extends MammogramReport to override static method calls
    private static class TestMammogramReport extends MammogramReport {
        private static final String MAMMOGRAM_BILL_CODE = "Q002A";
        private static final String MAMMOGRAM_EFORM_SEARCH = "Mam";
        private static final String MAMMOGRAM_FOLLOW_UP_TYPE = "MAMF";
        private static final String TEST_LETTER_PROCESSING_RESULT = "Test letter processing result";
        private static final int MAMMOGRAM_DUE_YEARS = 2;
        private static final int MAMMOGRAM_BONUS_MONTHS = 30;
        
        private final Map<String, ArrayList<Map<String, Object>>> preventionDataMap = new HashMap<>();
        private final Map<String, String> extValueMap = new HashMap<>();
        
        public void setPreventionData(final String demographicNo, final ArrayList<Map<String, Object>> data) {
            preventionDataMap.put(demographicNo, data);
        }
        
        public void setExtValue(final String id, final String result) {
            extValueMap.put(id, result);
        }
        
        @Override
        public Hashtable runReport(final LoggedInInfo loggedInInfo, final ArrayList<ArrayList<String>> list, final Date asofDate) {
            final BasePreventionReportTestHelper.PreventionTestConfig config = new BasePreventionReportTestHelper.PreventionTestConfig() {
                @Override
                public ArrayList<Map<String, Object>> getPreventionData(final String demographicNo) {
                    return preventionDataMap.getOrDefault(demographicNo, new ArrayList<>());
                }
                
                @Override
                public String getExtValue(final String id) {
                    return extValueMap.getOrDefault(id, RESULT_EMPTY);
                }
                
                @Override
                public boolean isIneligible(final ArrayList<Map<String, Object>> prevs, final Integer demo, final LocalDate asofDate) {
                    return ineligible(prevs);
                }
                
                @Override
                public LocalDate[] calculateDueDates(final LocalDate preventionDate, final LocalDate asofDate) {
                    final LocalDate dueDate = asofDate.minusYears(MAMMOGRAM_DUE_YEARS);
                    final LocalDate cutoffDate = dueDate.minusMonths(SIX_MONTHS);
                    return new LocalDate[]{dueDate, cutoffDate};
                }
                
                @Override
                public boolean isBonusEligible(final LocalDate preventionDate, final LocalDate asofDate, final boolean refused, final String result) {
                    final LocalDate bonusStartDate = asofDate.minusMonths(MAMMOGRAM_BONUS_MONTHS);
                    return !refused && preventionDate != null && 
                           bonusStartDate.isBefore(preventionDate) && 
                           asofDate.isAfter(preventionDate) && 
                           !RESULT_PENDING.equalsIgnoreCase(result);
                }
                
                @Override
                public boolean isDue(final LocalDate dueDate, final LocalDate preventionDate, final LocalDate cutoffDate) {
                    return due(dueDate, preventionDate, cutoffDate);
                }
                
                @Override
                public boolean isOverdue(final LocalDate cutoffDate, final LocalDate preventionDate) {
                    return overdue(cutoffDate, preventionDate);
                }
                
                @Override
                public boolean hasSpecialUpToDateLogic(final LocalDate preventionDate, final LocalDate dueDate, final LocalDate cutoffDate) {
                    return false; // Mammogram doesn't have special logic
                }
                
                @Override
                public String getBillCode() { 
                    return MAMMOGRAM_BILL_CODE; 
                }
                
                @Override
                public String getEformSearch() { 
                    return MAMMOGRAM_EFORM_SEARCH; 
                }
                
                @Override
                public String getFollowUpType() { 
                    return MAMMOGRAM_FOLLOW_UP_TYPE; 
                }
                
                @Override
                public String getLetterProcessingResult() { 
                    return TEST_LETTER_PROCESSING_RESULT; 
                }
            };
            
            return BasePreventionReportTestHelper.runBasicReportLogic(list, asofDate, config);
        }
        
        @Override
        protected String letterProcessing(final PreventionReportDisplay prd, final Date cuttoffDate) {
            // Override to avoid dependencies in test environment
            return TEST_LETTER_PROCESSING_RESULT;
        }
        
        @Override
        public String letterProcessing(final PreventionReportDisplay prd, final String followUpType, final LocalDate asofLocalDate, final LocalDate preventionLocalDate) {
            // Override to avoid dependencies in test environment
            return TEST_LETTER_PROCESSING_RESULT;
        }
    }

    @Test
    public void givenEmptyList_whenRunReport_thenReturnsZeroResults() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();

        Hashtable<String, Object> result = mammogramReport.runReport(loggedInInfo, list, AS_OF_DATE);

        assertNotNull(result);
        assertEquals(ZERO_RESULTS, result.get(FIELD_UP2DATE));
        assertEquals(ZERO_RESULTS, result.get(FIELD_PERCENT));
        assertEquals(ZERO_RESULTS, result.get(FIELD_PERCENT_WITH_GRACE));
        assertTrue(((ArrayList<?>) result.get(FIELD_RETURN_REPORT)).isEmpty());
        assertEquals("Q002A", result.get(FIELD_BILL_CODE));
    }

    @Test
    public void givenIneligibleItem_whenRunReport_thenReturnsIneligibleCount() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "true"));
        list.add(fieldList);

        ArrayList<Map<String, Object>> preventionList = new ArrayList<>();
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_INELIGIBLE);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2024_01_01);
        preventionList.add(preventionMap);

        mammogramReport.setPreventionData(DEMOGRAPHIC_NO, preventionList);

        Hashtable<String, Object> result = mammogramReport.runReport(loggedInInfo, list, AS_OF_DATE);

        assertNotNull(result);
        assertEquals("1", result.get(FIELD_INELIGIBLE));
    }

    @Test
    public void givenRecentPreventionData_whenRunReport_thenReturnsUpToDateStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);

        // Create a date 1 year ago (should be up to date)
        LocalDate recentDate = AS_OF_LOCAL_DATE.minusYears(1);
        String recentDateStr = recentDate.toString();

        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, recentDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);

        mammogramReport.setPreventionData(DEMOGRAPHIC_NO, new ArrayList<>(Collections.singletonList(preventionMap)));
        mammogramReport.setExtValue(TEST_ID, RESULT_EMPTY);

        Hashtable<String, Object> result = mammogramReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);

        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_UP_TO_DATE, returnReport.get(0).state);
        assertEquals(COLOR_GREEN, returnReport.get(0).color);
    }

    @Test
    public void givenRefusedPreventionData_whenRunReport_thenReturnsRefusedStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);

        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_1);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2025_01_01);
        preventionMap.put(FIELD_ID, TEST_ID);

        mammogramReport.setPreventionData(DEMOGRAPHIC_NO, new ArrayList<>(Collections.singletonList(preventionMap)));
        mammogramReport.setExtValue(TEST_ID, RESULT_EMPTY);

        Hashtable<String, Object> result = mammogramReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);

        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_REFUSED, returnReport.get(0).state);
        assertEquals(COLOR_ORANGE, returnReport.get(0).color);
    }

    @Test
    public void givenPendingResultPreventionData_whenRunReport_thenReturnsPendingStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);

        // Create a date 1 year ago
        LocalDate recentDate = AS_OF_LOCAL_DATE.minusYears(1);
        String recentDateStr = recentDate.toString();

        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, recentDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);

        mammogramReport.setPreventionData(DEMOGRAPHIC_NO, new ArrayList<>(Collections.singletonList(preventionMap)));
        mammogramReport.setExtValue(TEST_ID, RESULT_PENDING);

        Hashtable<String, Object> result = mammogramReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);

        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_PENDING, returnReport.get(0).state);
        assertEquals(COLOR_PINK, returnReport.get(0).color);
    }

    @Test
    public void givenDuePreventionData_whenRunReport_thenReturnsDueStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);

        // Create a date 2 years and 3 months ago (should be due)
        LocalDate dueDate = AS_OF_LOCAL_DATE.minusYears(2).minusMonths(3);
        String dueDateStr = dueDate.toString();

        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, dueDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);

        mammogramReport.setPreventionData(DEMOGRAPHIC_NO, new ArrayList<>(Collections.singletonList(preventionMap)));
        mammogramReport.setExtValue(TEST_ID, RESULT_EMPTY);

        Hashtable<String, Object> result = mammogramReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);

        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_DUE, returnReport.get(0).state);
        assertEquals(COLOR_YELLOW, returnReport.get(0).color);
    }

    @Test
    public void givenOverduePreventionData_whenRunReport_thenReturnsOverdueStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);

        // Create a date 3 years ago (should be overdue)
        LocalDate overdueDate = AS_OF_LOCAL_DATE.minusYears(3);
        String overdueDateStr = overdueDate.toString();

        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, overdueDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);

        mammogramReport.setPreventionData(DEMOGRAPHIC_NO, new ArrayList<>(Collections.singletonList(preventionMap)));
        mammogramReport.setExtValue(TEST_ID, RESULT_EMPTY);

        Hashtable<String, Object> result = mammogramReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);

        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_OVERDUE, returnReport.get(0).state);
        assertEquals(COLOR_RED, returnReport.get(0).color);
    }

    @Test
    public void givenNoPreventionData_whenRunReport_thenReturnsNoInfoStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);

        mammogramReport.setPreventionData(DEMOGRAPHIC_NO, new ArrayList<>());

        Hashtable<String, Object> result = mammogramReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);

        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_NO_INFO, returnReport.get(0).state);
        assertEquals(COLOR_MAGENTA, returnReport.get(0).color);
    }

    @Test
    public void givenMultiplePreventionsWithFutureDate_whenRunReport_thenReturnsMostRecentValidDate() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);

        Map<String, Object> oldMap = new HashMap<>();
        oldMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        oldMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2023_01_01);
        oldMap.put(FIELD_ID, TEST_ID);

        Map<String, Object> recentMap = new HashMap<>();
        recentMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        recentMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2024_01_01);
        recentMap.put(FIELD_ID, TEST_ID_2);

        Map<String, Object> futureMap = new HashMap<>();
        futureMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        futureMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2026_01_01);
        futureMap.put(FIELD_ID, TEST_ID_3);

        ArrayList<Map<String, Object>> preventionList = new ArrayList<>();
        preventionList.add(oldMap);
        preventionList.add(recentMap);
        preventionList.add(futureMap);

        mammogramReport.setPreventionData(DEMOGRAPHIC_NO, preventionList);
        mammogramReport.setExtValue(TEST_ID_2, RESULT_EMPTY);

        Hashtable<String, Object> result = mammogramReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);

        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(TEST_DATE_2024_01_01, returnReport.get(0).lastDate);
    }

    @Test
    public void givenMammogramReport_whenDisplayNumShots_thenReturnsFalse() {
        assertFalse(mammogramReport.displayNumShots());
    }
}
