/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package oscar.oscarPrevention.reports;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.oscarehr.managers.DemographicManager;
import org.oscarehr.prevention.dao.PreventionDao;
import org.oscarehr.prevention.dao.PreventionExtDao;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;
import oscar.oscarPrevention.PreventionData;
import oscar.oscarPrevention.pageUtil.PreventionReportDisplay;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mockStatic;
import static oscar.oscarPrevention.reports.PreventionReport.KEY_CUTOFF_DATE;
import static oscar.oscarPrevention.reports.PreventionReport.KEY_DUE_DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.AS_OF_DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.AS_OF_LOCAL_DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_GREEN;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_GREY;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_MAGENTA;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_ORANGE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_PINK;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_RED;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.COLOR_YELLOW;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.DEMOGRAPHIC_NO;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_ID;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_INELIGIBLE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_NEXT_DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_PERCENT;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_PERCENT_WITH_GRACE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_PREVENTION_DATE_NO_TIME;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_REFUSED;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_RESULT;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_RETURN_REPORT;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.FIELD_UP2DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.HPV_TYPE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.PAP_TYPE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.REFUSED_STATUS_ELIGIBLE_0;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.REFUSED_STATUS_ELIGIBLE_1;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.REFUSED_STATUS_INELIGIBLE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.RESULT_EMPTY;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.RESULT_PENDING;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.SIX_MONTHS;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_DUE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_INELIGIBLE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_NO_DATA;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_NO_INFO;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_OVERDUE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_PENDING;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_REFUSED;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.STATE_UP_TO_DATE;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.TEST_ID;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.TEST_ID_2;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.TEST_ID_3;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.THREE_YEARS;
import static oscar.oscarPrevention.reports.PreventionReportTestConstants.ZERO_RESULTS;

@RunWith(MockitoJUnitRunner.class)
public class PapReportTest {

    private static MockedStatic<SpringUtils> springUtilsMock;
    private MockedStatic<PreventionData> preventionDataMock;


    @InjectMocks
    private PapReport papReport;
    
    @Mock
    private LoggedInInfo loggedInInfo;

    // Test date constants
    private static final String TEST_DATE_2023_01_01 = "2023-01-01";
    private static final String TEST_DATE_2024_01_01 = "2024-01-01";
    private static final String TEST_DATE_2025_01_01 = "2025-01-01";
    private static final String TEST_DATE_2026_01_01 = "2026-01-01";

    @BeforeClass
    public static void init() {
        springUtilsMock = mockStatic(SpringUtils.class);

        springUtilsMock
            .when(() -> SpringUtils.getBean(ArgumentMatchers.eq(PreventionDao.class)))
            .thenReturn(Mockito.mock(PreventionDao.class));
        springUtilsMock
            .when(() -> SpringUtils.getBean(ArgumentMatchers.eq(PreventionExtDao.class)))
            .thenReturn(Mockito.mock(PreventionExtDao.class));
        springUtilsMock
            .when(() -> SpringUtils.getBean(ArgumentMatchers.eq(DemographicManager.class)))
            .thenReturn(Mockito.mock(DemographicManager.class));
    }

    @Before
    public void setUp() {
        preventionDataMock = mockStatic(PreventionData.class);
    }

    @After
    public void tearDown() {
        if (preventionDataMock != null) {
            preventionDataMock.close();
        }
    }

    @AfterClass
    public static void cleanUp() {
        if (springUtilsMock != null) {
            springUtilsMock.close();
        }
    }

    @Test
    public void givenEmptyList_whenRunReport_thenReturnsZeroResults() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        
        assertNotNull(result);
        assertEquals(ZERO_RESULTS, result.get(FIELD_UP2DATE));
        assertEquals(ZERO_RESULTS, result.get(FIELD_PERCENT));
        assertEquals(ZERO_RESULTS, result.get(FIELD_PERCENT_WITH_GRACE));
        assertTrue(((ArrayList<?>) result.get(FIELD_RETURN_REPORT)).isEmpty());
    }

    @Test
    public void givenIneligibleItem_whenRunReport_thenReturnsIneligibleCount() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "true"));
        list.add(fieldList);
        
        ArrayList<Map<String, Object>> preventionList = new ArrayList<>();
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_INELIGIBLE);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2024_01_01);
        preventionList.add(preventionMap);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(preventionList);
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        
        assertNotNull(result);
        assertEquals("1", result.get(FIELD_INELIGIBLE));
    }

    @Test
    public void givenRecentPreventionData_whenRunReport_thenReturnsUpToDateStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date 1 year ago (should be up to date)
        LocalDate recentDate = AS_OF_LOCAL_DATE.minusYears(1);
        String recentDateStr = recentDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, recentDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_UP_TO_DATE, returnReport.get(0).state);
        assertEquals(COLOR_GREEN, returnReport.get(0).color);
    }

    @Test
    public void givenRefusedPreventionData_whenRunReport_thenReturnsRefusedStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_1);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2025_01_01);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_REFUSED, returnReport.get(0).state);
        assertEquals(COLOR_ORANGE, returnReport.get(0).color);
    }

    @Test
    public void givenPendingResultPreventionData_whenRunReport_thenReturnsPendingStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date 1 year ago
        LocalDate recentDate = AS_OF_LOCAL_DATE.minusYears(1);
        String recentDateStr = recentDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, recentDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_PENDING);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_PENDING, returnReport.get(0).state);
        assertEquals(COLOR_PINK, returnReport.get(0).color);
    }

    @Test
    public void givenDuePreventionData_whenRunReport_thenReturnsDueStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date 3 years and 3 months ago (should be due)
        LocalDate dueDate = AS_OF_LOCAL_DATE.minusYears(3).minusMonths(3);
        String dueDateStr = dueDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, dueDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_DUE, returnReport.get(0).state);
        assertEquals(COLOR_YELLOW, returnReport.get(0).color);
    }

    @Test
    public void givenOverduePreventionData_whenRunReport_thenReturnsOverdueStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date 4 years ago (should be overdue)
        LocalDate overdueDate = AS_OF_LOCAL_DATE.minusYears(4);
        String overdueDateStr = overdueDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, overdueDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_OVERDUE, returnReport.get(0).state);
        assertEquals(COLOR_RED, returnReport.get(0).color);
    }

    @Test
    public void givenNoPreventionData_whenRunReport_thenReturnsNoInfoStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>());
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_NO_INFO, returnReport.get(0).state);
        assertEquals(COLOR_MAGENTA, returnReport.get(0).color);
    }

    @Test
    public void givenMultiplePreventionsWithFutureDate_whenRunReport_thenReturnsMostRecentValidDate() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        Map<String, Object> oldMap = new HashMap<>();
        oldMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        oldMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2023_01_01);
        oldMap.put(FIELD_ID, TEST_ID);
        
        Map<String, Object> recentMap = new HashMap<>();
        recentMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        recentMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2024_01_01);
        recentMap.put(FIELD_ID, TEST_ID_2);
        
        Map<String, Object> futureMap = new HashMap<>();
        futureMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        futureMap.put(FIELD_PREVENTION_DATE_NO_TIME, TEST_DATE_2026_01_01);
        futureMap.put(FIELD_ID, TEST_ID_3);
        
        ArrayList<Map<String, Object>> preventionList = new ArrayList<>();
        preventionList.add(oldMap);
        preventionList.add(recentMap);
        preventionList.add(futureMap);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID_2), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(preventionList);
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(TEST_DATE_2024_01_01, returnReport.get(0).lastDate);
    }

    @Test
    public void givenPreventionWithCustomNextDate_whenRunReport_thenReturnsOverdueStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date 3 years and 3 months ago (would be 'due')
        LocalDate prevDate = AS_OF_LOCAL_DATE.minusYears(3).minusMonths(3);
        String prevDateStr = prevDate.toString();
        
        // Custom next date 1 month ago (should make it 'overdue')
        LocalDate nextDate = AS_OF_LOCAL_DATE.minusMonths(1);
        String nextDateStr = nextDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, prevDateStr);
        preventionMap.put(FIELD_NEXT_DATE, nextDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_OVERDUE, returnReport.get(0).state);
        assertEquals(COLOR_RED, returnReport.get(0).color);
    }
    
    @Test
    public void givenPreventionWithFutureNextDate_whenRunReport_thenReturnsUpToDateStatus() throws Exception {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date 3 years and 3 months ago (would normally be 'due')
        LocalDate prevDate = AS_OF_LOCAL_DATE.minusYears(3).minusMonths(3);
        String prevDateStr = prevDate.toString();
        
        // Create a next date 6 months in the future (should make it 'up to date')
        LocalDate nextDate = AS_OF_LOCAL_DATE.plusMonths(6);
        String nextDateStr = nextDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, prevDateStr);
        preventionMap.put(FIELD_NEXT_DATE, nextDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_UP_TO_DATE, returnReport.get(0).state);
        assertEquals(COLOR_GREEN, returnReport.get(0).color);
    }
    
    @Test
    public void givenPreventionWithNextDateBeforePrevDate_whenRunReport_thenReturnsUpToDateStatus() throws Exception {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date 1 year ago
        LocalDate prevDate = AS_OF_LOCAL_DATE.minusYears(1);
        String prevDateStr = prevDate.toString();
        
        // Create a next date before the prevention date (should be ignored)
        LocalDate nextDate = prevDate.minusMonths(6);
        String nextDateStr = nextDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, prevDateStr);
        preventionMap.put(FIELD_NEXT_DATE, nextDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_UP_TO_DATE, returnReport.get(0).state);
        assertEquals(COLOR_GREEN, returnReport.get(0).color);
    }
    
    @Test
    public void givenHpvPreventionData_whenRunReport_thenUses5YearIntervalAndUpToDateStatus() throws Exception {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date 4 years ago (would be overdue for PAP but up-to-date for HPV)
        LocalDate prevDate = AS_OF_LOCAL_DATE.minusYears(4);
        String prevDateStr = prevDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, prevDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        // Return empty PAP list but populated HPV list
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>());
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(HPV_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_UP_TO_DATE, returnReport.get(0).state);
        assertEquals(COLOR_GREEN, returnReport.get(0).color);
    }
    @Test
    public void givenNullState_whenLetterProcessing_thenReturnsNull() {
        PreventionReportDisplay prd = new PreventionReportDisplay();
        prd.state = null;
        
        String result = papReport.letterProcessing(prd, new Date());
        
        assertNull(result);
    }

    @Test
    public void givenNullPreventionDate_whenCalculateDueDates_thenReturnsCorrectDates() {
        // Test the calculateDueDates method with null preventionDate
        Map<String, LocalDate> dates = papReport.calculateDueDates(null, null, AS_OF_LOCAL_DATE, PAP_TYPE);
        
        assertNotNull(dates);
        assertEquals(AS_OF_LOCAL_DATE.minusYears(THREE_YEARS), dates.get(KEY_DUE_DATE));
        assertEquals(AS_OF_LOCAL_DATE.minusYears(THREE_YEARS).minusMonths(SIX_MONTHS), dates.get(KEY_CUTOFF_DATE));
    }

    @Test
    public void givenHpvPreventionWithFutureNextDate_whenRunReport_thenReturnsUpToDateStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date 4 years ago
        LocalDate prevDate = AS_OF_LOCAL_DATE.minusYears(4);
        String prevDateStr = prevDate.toString();
        
        // Next date 1 year in the future
        LocalDate nextDate = AS_OF_LOCAL_DATE.plusYears(1);
        String nextDateStr = nextDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, prevDateStr);
        preventionMap.put(FIELD_NEXT_DATE, nextDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(org.mockito.ArgumentMatchers.eq(TEST_ID), org.mockito.ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        // Return empty PAP list but populated HPV list
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(org.mockito.ArgumentMatchers.any(), org.mockito.ArgumentMatchers.eq(PAP_TYPE), org.mockito.ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>());
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(org.mockito.ArgumentMatchers.any(), org.mockito.ArgumentMatchers.eq(HPV_TYPE), org.mockito.ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_UP_TO_DATE, returnReport.get(0).state);
        assertEquals(COLOR_GREEN, returnReport.get(0).color);
    }
    
    @Test
    public void givenHpvPreventionExactlyAtDueDate_whenRunReport_thenReturnsDueStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date exactly 5 years ago (HPV due date)
        LocalDate prevDate = AS_OF_LOCAL_DATE.minusYears(5);
        String prevDateStr = prevDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, prevDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(org.mockito.ArgumentMatchers.eq(TEST_ID), org.mockito.ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        // Return empty PAP list but populated HPV list
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(org.mockito.ArgumentMatchers.any(), org.mockito.ArgumentMatchers.eq(PAP_TYPE), org.mockito.ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>());
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(org.mockito.ArgumentMatchers.any(), org.mockito.ArgumentMatchers.eq(HPV_TYPE), org.mockito.ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_DUE, returnReport.get(0).state);
        assertEquals(COLOR_YELLOW, returnReport.get(0).color);
    }
    
    @Test
    public void givenPapPreventionExactlyAtDueDate_whenRunReport_thenReturnsDueStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a date exactly 3 years ago (PAP due date)
        LocalDate prevDate = AS_OF_LOCAL_DATE.minusYears(3);
        String prevDateStr = prevDate.toString();
        
        Map<String, Object> preventionMap = new HashMap<>();
        preventionMap.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        preventionMap.put(FIELD_PREVENTION_DATE_NO_TIME, prevDateStr);
        preventionMap.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(org.mockito.ArgumentMatchers.eq(TEST_ID), org.mockito.ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(org.mockito.ArgumentMatchers.any(), org.mockito.ArgumentMatchers.eq(PAP_TYPE), org.mockito.ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(preventionMap)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_DUE, returnReport.get(0).state);
        assertEquals(COLOR_YELLOW, returnReport.get(0).color);
    }

    @Test
    public void givenRefusedStatusEligible0_whenRunReport_thenReturnsAppropriateStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a prevention with REFUSED_STATUS_ELIGIBLE_0 (eligible, not refused)
        Map<String, Object> prevention = new HashMap<>();
        prevention.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        prevention.put(FIELD_PREVENTION_DATE_NO_TIME, AS_OF_LOCAL_DATE.minusYears(1).toString());
        prevention.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(prevention)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_UP_TO_DATE, returnReport.get(0).state);
        assertEquals(COLOR_GREEN, returnReport.get(0).color);
    }
    
    @Test
    public void givenRefusedStatusEligible1_whenRunReport_thenReturnsRefusedStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a prevention with REFUSED_STATUS_ELIGIBLE_1 (eligible but refused)
        Map<String, Object> prevention = new HashMap<>();
        prevention.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_1);
        prevention.put(FIELD_PREVENTION_DATE_NO_TIME, AS_OF_LOCAL_DATE.minusYears(1).toString());
        prevention.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(prevention)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_REFUSED, returnReport.get(0).state);
        assertEquals(COLOR_ORANGE, returnReport.get(0).color);
    }
    
    @Test
    public void givenRefusedStatusIneligible_whenRunReport_thenReturnsIneligibleStatus() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a prevention with REFUSED_STATUS_INELIGIBLE (ineligible)
        Map<String, Object> prevention = new HashMap<>();
        prevention.put(FIELD_REFUSED, REFUSED_STATUS_INELIGIBLE);
        prevention.put(FIELD_PREVENTION_DATE_NO_TIME, AS_OF_LOCAL_DATE.minusYears(1).toString());
        prevention.put(FIELD_ID, TEST_ID);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.eq(TEST_ID), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(new ArrayList<>(Collections.singletonList(prevention)));
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals("Ineligible", returnReport.get(0).state);
        assertEquals(COLOR_GREY, returnReport.get(0).color);
    }
    
    @Test
    public void givenMultiplePreventionsWithMostRecentRefused_whenRunReport_thenUsesLastNonRefused() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create a valid prevention from 2 years ago
        Map<String, Object> validPrevention = new HashMap<>();
        validPrevention.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0);
        validPrevention.put(FIELD_PREVENTION_DATE_NO_TIME, AS_OF_LOCAL_DATE.minusYears(2).toString());
        validPrevention.put(FIELD_ID, TEST_ID);
        
        // Create a refused prevention from 1 year ago
        Map<String, Object> refusedPrevention = new HashMap<>();
        refusedPrevention.put(FIELD_REFUSED, REFUSED_STATUS_INELIGIBLE);
        refusedPrevention.put(FIELD_PREVENTION_DATE_NO_TIME, AS_OF_LOCAL_DATE.minusYears(1).toString());
        refusedPrevention.put(FIELD_ID, TEST_ID_2);
        
        ArrayList<Map<String, Object>> preventions = new ArrayList<>();
        preventions.add(validPrevention);
        preventions.add(refusedPrevention);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(org.mockito.ArgumentMatchers.eq(TEST_ID), org.mockito.ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(org.mockito.ArgumentMatchers.any(), org.mockito.ArgumentMatchers.eq(PAP_TYPE), org.mockito.ArgumentMatchers.anyInt()))
            .thenReturn(preventions);
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        assertEquals(STATE_INELIGIBLE, returnReport.get(0).state);
        assertEquals(COLOR_GREY, returnReport.get(0).color);
        assertEquals(STATE_NO_DATA, returnReport.get(0).lastDate);
    }
    
    @Test
    public void givenMultiplePreventionsWithDifferentRefusedStatuses_whenRunReport_thenHandlesCorrectly() {
        ArrayList<ArrayList<String>> list = new ArrayList<>();
        ArrayList<String> fieldList = new ArrayList<>(Arrays.asList(DEMOGRAPHIC_NO, "false", "false"));
        list.add(fieldList);
        
        // Create three preventions with different refused statuses
        Map<String, Object> oldestPrevention = new HashMap<>();
        oldestPrevention.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_0); // Eligible, not refused
        oldestPrevention.put(FIELD_PREVENTION_DATE_NO_TIME, AS_OF_LOCAL_DATE.minusYears(3).toString());
        oldestPrevention.put(FIELD_ID, TEST_ID);
        
        Map<String, Object> middlePrevention = new HashMap<>();
        middlePrevention.put(FIELD_REFUSED, REFUSED_STATUS_ELIGIBLE_1); // Eligible but refused
        middlePrevention.put(FIELD_PREVENTION_DATE_NO_TIME, AS_OF_LOCAL_DATE.minusYears(2).toString());
        middlePrevention.put(FIELD_ID, TEST_ID_2);
        
        Map<String, Object> newestPrevention = new HashMap<>();
        newestPrevention.put(FIELD_REFUSED, REFUSED_STATUS_INELIGIBLE); // Ineligible
        newestPrevention.put(FIELD_PREVENTION_DATE_NO_TIME, AS_OF_LOCAL_DATE.minusYears(1).toString());
        newestPrevention.put(FIELD_ID, TEST_ID_3);
        
        ArrayList<Map<String, Object>> preventions = new ArrayList<>();
        preventions.add(oldestPrevention);
        preventions.add(middlePrevention);
        preventions.add(newestPrevention);
        
        preventionDataMock
            .when(() -> PreventionData.getExtValue(ArgumentMatchers.anyString(), ArgumentMatchers.eq(FIELD_RESULT)))
            .thenReturn(RESULT_EMPTY);
        
        preventionDataMock
            .when(() -> PreventionData.getPreventionData(ArgumentMatchers.any(), ArgumentMatchers.eq(PAP_TYPE), ArgumentMatchers.anyInt()))
            .thenReturn(preventions);
        
        Hashtable<String, Object> result = papReport.runReport(loggedInInfo, list, AS_OF_DATE);
        ArrayList<PreventionReportDisplay> returnReport = (ArrayList<PreventionReportDisplay>) result.get(FIELD_RETURN_REPORT);
        
        assertNotNull(returnReport);
        assertEquals(1, returnReport.size());
        // Since the most recent prevention has REFUSED_STATUS_INELIGIBLE, the patient should be marked as ineligible
        assertEquals(STATE_INELIGIBLE, returnReport.get(0).state);
        assertEquals(COLOR_GREY, returnReport.get(0).color);
    }
}
