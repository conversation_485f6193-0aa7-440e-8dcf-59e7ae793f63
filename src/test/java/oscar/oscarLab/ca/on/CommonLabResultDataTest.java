package oscar.oscarLab.ca.on;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.common.dao.Hl7TextInfoDao;
import org.oscarehr.common.dao.IncomingLabRulesDao;
import org.oscarehr.common.dao.ProviderLabRoutingDao;
import org.oscarehr.common.dao.SystemPreferencesDao;
import org.oscarehr.common.dao.TableModificationDao;
import org.oscarehr.common.model.ProviderLabRoutingModel;
import org.oscarehr.managers.DemographicManager;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;
import oscar.oscarLab.ca.all.Hl7textResultsData;
import oscar.oscarLab.ca.all.LabResultsParameters;
import utils.OscarPropertiesUtil;

@ExtendWith(MockitoExtension.class)
public class CommonLabResultDataTest {

  // Constants for test data
  private static final String PROVIDER_NO = "999998";
  private static final String DEMOGRAPHIC_NO = "1";
  private static final String PATIENT_FIRST_NAME = "John";
  private static final String PATIENT_LAST_NAME = "Doe";
  private static final String PATIENT_HEALTH_NUMBER = "*********";
  private static final String LAB_STATUS = "A";
  private static final int DEFAULT_LAB_COUNT = 9;
  private static final int SMALL_LAB_COUNT = 3;

  @Mock private IncomingLabRulesDao incomingLabRulesDao;
  @Mock private ProviderLabRoutingDao providerLabRoutingDao;
  @Mock private SystemPreferencesDao systemPreferencesDao;
  @Mock private TableModificationDao tableModificationDao;
  @Mock private Hl7TextInfoDao hl7TextInfoDao;
  @Mock private DemographicManager demographicManager;
  @Mock private LoggedInInfo loggedInInfo;
  private MockedStatic<SpringUtils> springUtils;
  private MockedStatic<Hl7textResultsData> hl7textResultsData;

  /**
   * Sets up the test environment before each test.
   */
  @BeforeEach
  public void init() {
    springUtils = mockStatic(SpringUtils.class);
    hl7textResultsData = mockStatic(Hl7textResultsData.class);
    OscarPropertiesUtil.getOscarProperties();
    springUtils
        .when(() -> SpringUtils.getBean(SystemPreferencesDao.class))
        .thenReturn(systemPreferencesDao);
    springUtils
        .when(() -> SpringUtils.getBean(ProviderLabRoutingDao.class))
        .thenReturn(providerLabRoutingDao);
    springUtils
        .when(() -> SpringUtils.getBean(IncomingLabRulesDao.class))
        .thenReturn(incomingLabRulesDao);
    springUtils
        .when(() -> SpringUtils.getBean(TableModificationDao.class))
        .thenReturn(tableModificationDao);
    springUtils
        .when(() -> SpringUtils.getBean(Hl7TextInfoDao.class))
        .thenReturn(hl7TextInfoDao);
    springUtils
        .when(() -> SpringUtils.getBean(DemographicManager.class))
        .thenReturn(demographicManager);
  }

  @AfterEach
  public void close() {
    springUtils.close();
    hl7textResultsData.close();
  }

  @Test
  public void givenUpdateReportStatus_whenWithComment_thenUpdate() {
    // Mock the provider lab routing to return a valid model
    when(providerLabRoutingDao.findByLabNoAndLabTypeAndProviderNo(
        675, "HL7", "999998")).
        thenReturn(Arrays.asList(new ProviderLabRoutingModel(
            "999998", 675, "N", "aaaaaa", new Date(), "HL7")));

    ReportStatusUpdateResponse reportStatusUpdateResponse =
        CommonLabResultData.updateReportStatus(
            675, "999998", 'N', "test", "HL7", "");
    assertTrue(reportStatusUpdateResponse.isSuccess());
  }

  /**
   * Creates a list of test lab results with the specified size.
   *
   * @param size The number of lab results to create
   * @return A list of LabResultData objects
   */
  private List<LabResultData> createTestLabResults(final int size) {
    List<LabResultData> labResults = new ArrayList<>();
    for (int i = 0; i < size; i++) {
      LabResultData labResult = new LabResultData(LabResultData.HL7TEXT);
      labResult.segmentID = String.valueOf(i + 1);
      labResult.dateTime = "2023-01-0" + (i + 1) + " 10:00:00";
      labResults.add(labResult);
    }
    return labResults;
  }

  /**
   * Tests that when labs are requested for eChart display with a specified number to display, and
   * the number of labs exceeds that number, the returned list is limited to the specified number.
   *
   * @param numberToDisplay The number of labs to display
   */
  @ParameterizedTest
  @ValueSource(ints = {1, 2, 5})
  public void givenLabsExceedLimit_whenPopulateLabResultsDataForEChartDisplay_thenLimitApplied(
      final int numberToDisplay) {
    // Create test data
    List<LabResultData> testLabResults = createTestLabResults(DEFAULT_LAB_COUNT);

    // Mock SystemPreferencesDao to enable HL7TEXT_LABS_ENABLED
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_hl7text_labs", true))
        .thenReturn(true);
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_cml_labs", false))
        .thenReturn(false);
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_mds_labs", false))
        .thenReturn(false);
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_pathnet_labs", false))
        .thenReturn(false);
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_epsilon_labs", false))
        .thenReturn(false);

    CommonLabResultData commonLabResultData = new CommonLabResultData();

    // Mock Hl7textResultsData.populateHl7ResultsData to return our test data
    hl7textResultsData.when(
            () -> Hl7textResultsData.populateHl7ResultsData(any(), anyString(), any()))
        .thenReturn(testLabResults);

    // Create LabResultsParameters with isForEChartDisplay=true and numberToDisplay set
    LabResultsParameters labResultsParameters = new LabResultsParameters(
        loggedInInfo, PROVIDER_NO, DEMOGRAPHIC_NO, PATIENT_FIRST_NAME, PATIENT_LAST_NAME,
        PATIENT_HEALTH_NUMBER, LAB_STATUS, true, numberToDisplay);

    List<LabResultData> result = commonLabResultData.populateLabResultsDataForEChartDisplay(
        labResultsParameters);

    assertEquals(numberToDisplay, result.size(),
        "The number of lab results should be limited to " + numberToDisplay);

    // Verify the labs are in the correct order (most recent first)
    for (int i = 0; i < numberToDisplay; i++) {
      assertEquals(String.valueOf(DEFAULT_LAB_COUNT - i), result.get(i).segmentID,
          "Lab results should be sorted with most recent first");
    }
  }

  /**
   * Tests that when labs are requested for eChart display with a specified number to display, and
   * the number of labs is less than that number, all labs are returned.
   */
  @Test
  public void givenLabsUnderLimit_whenPopulateLabResultsDataForEChartDisplay_thenAllLabsReturned() {
    // Create test data - small number of lab results
    List<LabResultData> testLabResults = createTestLabResults(SMALL_LAB_COUNT);

    // Mock SystemPreferencesDao to enable HL7TEXT_LABS_ENABLED
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_hl7text_labs", true))
        .thenReturn(true);
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_cml_labs", false))
        .thenReturn(false);
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_mds_labs", false))
        .thenReturn(false);
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_pathnet_labs", false))
        .thenReturn(false);
    when(systemPreferencesDao.isReadBooleanPreferenceWithDefault("enable_epsilon_labs", false))
        .thenReturn(false);

    CommonLabResultData commonLabResultData = new CommonLabResultData();

    // Mock Hl7textResultsData.populateHl7ResultsData to return our test data
    hl7textResultsData.when(
            () -> Hl7textResultsData.populateHl7ResultsData(any(), anyString(), any()))
        .thenReturn(testLabResults);

    // Create LabResultsParameters with isForEChartDisplay=true and numberToDisplay set to 5
    LabResultsParameters labResultsParameters = new LabResultsParameters(
        loggedInInfo, PROVIDER_NO, DEMOGRAPHIC_NO, PATIENT_FIRST_NAME, PATIENT_LAST_NAME,
        PATIENT_HEALTH_NUMBER, LAB_STATUS, true, 5);

    ArrayList<LabResultData> result = commonLabResultData.populateLabResultsDataForEChartDisplay(
        labResultsParameters);

    assertEquals(SMALL_LAB_COUNT, result.size(),
        "All lab results should be returned when under the limit");
  }
}
