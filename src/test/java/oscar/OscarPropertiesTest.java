/**
 * Copyright (c) 2023 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package oscar;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import javax.servlet.ServletContext;
import lombok.val;
import lombok.var;
import org.apache.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.util.MiscUtils;
import org.springframework.test.util.ReflectionTestUtils;
import oscar.util.SystemPreferencesUtils;

@ExtendWith(value = MockitoExtension.class)
class OscarPropertiesTest {

  @TempDir
  File tempDir;

  private final List<String> oscarProProperties =
      Arrays.asList(
          "testProperty = proTest",
          "security.encryption.aes.password = password",
          "security.encryption.aes.salt = salt"
      );

  private static final String ECHART_TOOLBAR_TYPE = "echart_toolbar_type";

  @BeforeEach
  void beforeEach() {
    System.setProperty("user.home", tempDir.getAbsolutePath());
    // Reset the oscarProperties field so that each test can initialize it
    ReflectionTestUtils.setField(OscarProperties.class, "oscarProperties", null);
  }

  @Test
  void givenKaiemrDeployedContextNotSet_whenInitialize_thenInitializeNormally() throws IOException {
    val context = mock(ServletContext.class);
    when(context.getContextPath())
        .thenReturn("/classic");
    writeProperties("classic.properties",
        Collections.singletonList("testProperty = classicTest"));
    writeProperties("oscarpro.properties", oscarProProperties);

    try (val mockedMiscUtils = mockStatic(MiscUtils.class)) {
      val loggerSpy = spy(Logger.getLogger(OscarProperties.class.getName()));
      mockedMiscUtils
          .when(() -> MiscUtils.getLogger())
          .thenReturn(loggerSpy);
      OscarProperties.initialize(context);

      verify(loggerSpy, times(1))
          .error(
              eq("kaiemr_deployed_context is not set in the properties,"
                  + " skipping loading pro properties")
          );
      val properties = OscarProperties.getInstance();
      assertNull(properties.getProperty("kaiemr_deployed_context"));
      assertEquals("classicTest", properties.getProperty("testProperty"));
      assertNull(properties.getProEncryptionPassword());
      assertNull(properties.getProEncryptionSalt());
    }
  }

  @Test
  void givenKaiemrDeployedContextNotFound_whenInitialize_thenInitializeNormally() throws IOException {
    val context = mock(ServletContext.class);
    when(context.getContextPath())
        .thenReturn("/classic");
    writeProperties("classic.properties",
        Arrays.asList(
            "testProperty = classicTest",
            "kaiemr_deployed_context = oscarpro"
        )
    );

    try (val mockedMiscUtils = mockStatic(MiscUtils.class)) {
      val loggerSpy = spy(Logger.getLogger(OscarProperties.class.getName()));
      mockedMiscUtils
          .when(() -> MiscUtils.getLogger())
          .thenReturn(loggerSpy);
      OscarProperties.initialize(context);

      verify(loggerSpy, times(1))
          .info(eq("Could not load Oscar Pro properties"), any(IOException.class));
      val properties = OscarProperties.getInstance();
      assertEquals("oscarpro", properties.getProperty("kaiemr_deployed_context"));
      assertEquals("classicTest", properties.getProperty("testProperty"));
      assertNull(properties.getProEncryptionPassword());
      assertNull(properties.getProEncryptionSalt());
    }
  }

  @Test
  void givenProPropertiesExist_whenInitialize_thenOscarProPropertiesLoadedWithoutOverwriting()
      throws IOException {
    val context = mock(ServletContext.class);
    when(context.getContextPath())
        .thenReturn("/classic");
    writeProperties(
        "classic.properties",
        Collections.singletonList("kaiemr_deployed_context = oscarpro")
    );
    writeProperties("oscarpro.properties", oscarProProperties);

    OscarProperties.initialize(context);

    val properties = OscarProperties.getInstance();
    assertEquals("password", properties.getProEncryptionPassword());
    assertEquals("salt", properties.getProEncryptionSalt());
    // Assert properties not present in classic.properties are not written/overwritten from oscarpro.properties
    assertNull(properties.getProperty("testProperty"));
    assertNull(properties.getProperty("security.encryption.aes.password"));
    assertNull(properties.getProperty("security.encryption.aes.salt"));
  }

  private void writeProperties(final String fileName, final List<String> properties) throws IOException {
    val file = new File(tempDir, fileName);
    assertTrue(file.createNewFile());
    try (val testFileWriter = new FileWriter(file)) {
      for(var property : properties) {
        testFileWriter.write(System.lineSeparator());
        testFileWriter.write(property);
      }
    }
  }

  @Test
  void getEchartToolbarType_whenSystemPreferenceExists_thenReturnSystemPreferenceValue() throws IOException {
    val context = mock(ServletContext.class);
    when(context.getContextPath()).thenReturn("/classic");
    writeProperties("classic.properties", 
        Collections.singletonList("cme_js = default_value"));
    OscarProperties.initialize(context);

    try (val mockedSystemPreferencesUtils = mockStatic(SystemPreferencesUtils.class)) {
      mockedSystemPreferencesUtils
          .when(() -> SystemPreferencesUtils.getPreferenceValueByName(eq(ECHART_TOOLBAR_TYPE), anyString()))
          .thenReturn("eyeform3");

      val result = OscarProperties.getEchartToolbarType();
      assertEquals("eyeform3", result);
    }
  }

  @Test
  void getEchartToolbarType_whenSystemPreferenceIsBlank_thenReturnPropertyValue() throws IOException {
    val context = mock(ServletContext.class);
    when(context.getContextPath()).thenReturn("/classic");
    writeProperties("classic.properties", 
        Collections.singletonList("cme_js = eyeform_poc"));
    OscarProperties.initialize(context);

    try (val mockedSystemPreferencesUtils = mockStatic(SystemPreferencesUtils.class)) {
      mockedSystemPreferencesUtils
          .when(() -> SystemPreferencesUtils.getPreferenceValueByName(eq(ECHART_TOOLBAR_TYPE), anyString()))
          .thenReturn("");

      val result = OscarProperties.getEchartToolbarType();
      assertEquals("eyeform_poc", result);
    }
  }
}
