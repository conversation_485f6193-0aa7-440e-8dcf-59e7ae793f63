package oscar.eform;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import health.apps.gateway.LinkUtility;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.val;
import lombok.var;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.oscarehr.common.dao.EFormDao;
import org.oscarehr.common.dao.EFormDataDao;
import org.oscarehr.common.dto.EFormDataMetadata;
import org.oscarehr.common.model.EForm;
import org.oscarehr.common.model.EFormData;
import org.oscarehr.util.SpringUtils;
import org.springframework.test.util.ReflectionTestUtils;
import oscar.OscarProperties;
import oscar.oscarDB.DBHandler;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@Execution(ExecutionMode.SAME_THREAD)
public class EFormUtilTest {

  private static final String FORM_NAME = "testFormName";
  private static final String FORM_SUBJECT = "testFormSubject";
  private static final String FILE_NAME = "testFileName";
  private static final String HTML_STR = "testHtmlStr";
  private static final String CREATOR = "testCreator";
  private static final String ROLE_TYPE = "testRoleType";
  private static final boolean SHOW_LATEST_FORM_ONLY = true;
  private static final boolean PATIENT_INDEPENDENT = true;
  private static final boolean IS_ATTACHMENTS_ENABLED = true;

  private static MockedStatic<SpringUtils> utilsMock;

  private static MockedStatic<DBHandler> handlerMock;

  private static MockedStatic<OscarProperties> oscarPropertiesMock;

  @Captor
  private ArgumentCaptor<EForm> eFormCaptor;

  @Mock
  private EFormDao eFormDao;

  @Mock
  private EFormDataDao eformDataDao;

  @BeforeEach
  public void setUp() {
    openMocks(this);

    utilsMock = mockStatic(SpringUtils.class);
    handlerMock = mockStatic(DBHandler.class);
    oscarPropertiesMock = mockStatic(OscarProperties.class);

    ReflectionTestUtils.setField(EFormUtil.class, "eFormDataDao", eformDataDao);

    OscarProperties oscarProperties = mock(OscarProperties.class);
    when(oscarProperties.getProperty(eq("eform_image"))).thenReturn("fake/path/");
    when(OscarProperties.getInstance()).thenReturn(oscarProperties);
  }

  @AfterEach
  public void tearDown() {
    utilsMock.close();
    handlerMock.close();
    oscarPropertiesMock.close();
  }

  @Test
  public void givenInvalidSql_whenGetValues_thenReturnNull() {
    val listOfNames = new ArrayList<>(Arrays.asList("testName1", "testName2"));
    val result = EFormUtil.getValues(listOfNames, "testSql");
    assertNull(result);
  }

  @Test
  public void givenDefaultMethodUse_whenListImages_thenCallOverloadedMethod() {
    val result = EFormUtil.listImages();
    // just here for coverage. better tools needed to properly verify that the overloaded method was called
    assertNotNull(result);
  }

  @Test
  public void givenEmptyEFormImagesFolder_whenListImages_thenReturnEmptyList() {
    File mockFile = mock(File.class);
    when(mockFile.list()).thenReturn(null);

    val result = EFormUtil.listImages(mockFile);
    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  public void givenNonEmptyEFormImagesFolder_whenListImages_thenReturnSortedList() {
    File mockFile = mock(File.class);
    when(mockFile.list()).thenReturn(new String[]{"testfile3", "testfile1", "testfile2"});

    val result = EFormUtil.listImages(mockFile);
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals("testfile1", result.get(0));
    assertEquals("testfile2", result.get(1));
    assertEquals("testfile3", result.get(2));
  }

  @Test
  public void givenAllInput_whenSaveEForm_thenPersistEForm() {
    utilsMock.when(() -> SpringUtils.getBean(EFormDao.class)).thenReturn(eFormDao);
    try {
      EFormUtil.saveEForm(FORM_NAME, FORM_SUBJECT, FILE_NAME, HTML_STR, CREATOR,
              SHOW_LATEST_FORM_ONLY, PATIENT_INDEPENDENT, IS_ATTACHMENTS_ENABLED, ROLE_TYPE);
    } catch (NullPointerException e) {
      // result eForm does not have id, resulting in NPE
    }
    verify(eFormDao, times(1)).persist(eFormCaptor.capture());
    val eForm = eFormCaptor.getValue();
    assertEForm(eForm);
    assertEquals(eForm.getCreator(), CREATOR);
  }

  @Test
  public void givenNoCreator_whenSaveEForm_thenPersistEForm() {
    utilsMock.when(() -> SpringUtils.getBean(EFormDao.class)).thenReturn(eFormDao);
    try {
      EFormUtil.saveEForm(FORM_NAME, FORM_SUBJECT, FILE_NAME, HTML_STR,
              SHOW_LATEST_FORM_ONLY, PATIENT_INDEPENDENT, IS_ATTACHMENTS_ENABLED, ROLE_TYPE);
    } catch (NullPointerException e) {
      // result eForm does not have id, resulting in NPE
    }
    verify(eFormDao, times(1)).persist(eFormCaptor.capture());
    val eForm = eFormCaptor.getValue();
    assertEForm(eForm);
    assertNull(eForm.getCreator());
  }

  @Test
  public void givenEForm_whenSaveEForm_thenPersistEForm() {
    utilsMock.when(() -> SpringUtils.getBean(EFormDao.class)).thenReturn(eFormDao);
    try {
      EFormUtil.saveEForm(createTestEForm());
    } catch (NullPointerException e) {
      // result eForm does not have id, resulting in NPE
    }
    verify(eFormDao, times(1)).persist(eFormCaptor.capture());
    val eForm = eFormCaptor.getValue();
    assertEForm(eForm);
    assertEquals(eForm.getCreator(), CREATOR);
  }

  @Test
  public void givenNoIsAttachmentsEnabledAndCreator_whenSaveEForm_thenPersistEForm() {
    utilsMock.when(() -> SpringUtils.getBean(EFormDao.class)).thenReturn(eFormDao);
    try {
      EFormUtil.saveEForm(FORM_NAME, FORM_SUBJECT, FILE_NAME, HTML_STR,
              SHOW_LATEST_FORM_ONLY, PATIENT_INDEPENDENT, ROLE_TYPE);
    } catch (NullPointerException e) {
      // result eForm does not have id, resulting in NPE
    }
    verify(eFormDao, times(1)).persist(eFormCaptor.capture());
    val eForm = eFormCaptor.getValue();
    assertEForm(eForm);
    assertNull(eForm.getCreator());
    assertTrue(eForm.isAttachmentsEnabled());
  }

  @Test
  public void givenLocalEforms_whenFetchRemoteEformsAfterLocal_thenReturnRemoteFormsAfterOffset() {
    val demographicNo = 1;
    val offset = 5;
    val itemsToReturn = 10;
    val current = true;

    val eformDataDao = mock(EFormDataDao.class);

    ReflectionTestUtils.setField(EFormUtil.class, "eFormDataDao", eformDataDao);

    // Local eForms available in the database
    List<EFormData> localEForms = createLocalEForms(10); // 10 local eForms

    ArrayList<String> guids = new ArrayList<>();
    for (val localEForm: localEForms) {
      guids.add(localEForm.getGuid());
    }

    // Remote eForms available to fetch after local
    List<EFormData> remoteEForms = createRemoteEForms(15); // 15 remote eForms
    List<Map<String, Object>> chosenRemoteEForms = new ArrayList<>();

    for (var i = 0; i < 5; ++i) {
      chosenRemoteEForms.add(new HashMap<>());
    }

    // Mocking the DAO responses
    when(eformDataDao.countByDemographicIdCurrent(demographicNo, current)).thenReturn(10);
    when(eformDataDao.getGUIDsByDemographicIdCurrent(demographicNo, current)).thenReturn(guids);
    when(eformDataDao.fetchRemoteEformByDemographicId(demographicNo)).thenReturn(remoteEForms);
    when(eformDataDao.convertEFormDataListToMapList(any())).thenReturn(chosenRemoteEForms);

    // Calling the method under test
    val result = EFormUtil.fetchRemoteEformsAfterLocal(current, offset, itemsToReturn, demographicNo);

    // Validating the result
    assertNotNull(result);
    assertEquals(5, result.size()); // 10 local - 5 offset = 5 remaining local + 5 remote
  }

  @Test
  public void givenNoLocalEforms_whenFetchRemoteEformsAfterLocal_thenFetchRemoteEforms() {
    val demographicNo = 4;
    val offset = 0;
    val itemsToReturn = 10;
    val current = true;
    val remoteEforms = createRemoteEForms(15);

    List<Map<String, Object>> mappedRemoteEforms = new ArrayList<>();
    for (var i = 0; i < 10; ++i) {
      mappedRemoteEforms.add(new HashMap<>());
    }

    // Mocking the DAO responses
    when(eformDataDao.countByDemographicIdCurrent(demographicNo, current)).thenReturn(0);
    when(eformDataDao.getGUIDsByDemographicIdCurrent(demographicNo, current)).thenReturn(new ArrayList<>());
    when(eformDataDao.fetchRemoteEformByDemographicId(demographicNo)).thenReturn(remoteEforms);
    when(eformDataDao.convertEFormDataListToMapList(any())).thenReturn(mappedRemoteEforms);

    val result = EFormUtil.fetchRemoteEformsAfterLocal(current, offset, itemsToReturn, demographicNo);

    assertNotNull(result);
    assertEquals(10, result.size());
  }

  @Test
  public void givenInsufficientLocalEforms_whenFetchRemoteEformsAfterLocal_thenFetchAdditionalRemoteEforms() {
    val demographicNo = 3;
    val offset = 5;
    val itemsToReturn = 10;
    val current = true;

    List<Map<String, Object>> chosenRemoteEForms = new ArrayList<>();
    for (var i = 0; i < 10; ++i) {
      chosenRemoteEForms.add(new HashMap<>());
    }

    when(eformDataDao.countByDemographicIdCurrent(demographicNo, current)).thenReturn(5);
    when(eformDataDao.getGUIDsByDemographicIdCurrent(demographicNo, current)).thenReturn(new ArrayList<>());
    when(eformDataDao.fetchRemoteEformByDemographicId(demographicNo)).thenReturn(createRemoteEForms(15));
    when(eformDataDao.convertEFormDataListToMapList(any())).thenReturn(chosenRemoteEForms);

    val result = EFormUtil.fetchRemoteEformsAfterLocal(current, offset, itemsToReturn, demographicNo);

    assertNotNull(result);
    assertEquals(10, result.size());
  }

  @Test
  public void givenNoRemoteEforms_whenFetchRemoteEformsAfterLocal_thenReturnLocalEforms() {
    val demographicNo = 2;
    val offset = 5;
    val itemsToReturn = 10;
    val current = true;

    when(eformDataDao.countByDemographicIdCurrent(demographicNo, current)).thenReturn(5);
    when(eformDataDao.getGUIDsByDemographicIdCurrent(demographicNo, current)).thenReturn(new ArrayList<>());
    when(eformDataDao.fetchRemoteEformByDemographicId(demographicNo)).thenReturn(null);
    when(eformDataDao.convertEFormDataListToMapList(new ArrayList<>())).thenReturn(new ArrayList<>());

    val result = EFormUtil.fetchRemoteEformsAfterLocal(current, offset, itemsToReturn, demographicNo);

    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Nested
  class ListPatientEformsCurrent {

    @Test
    public void givenNoRemoteEforms_whenListPatientEformsCurrent_thenNoDedupe() {
      val demographicNo = 1;

      try (val linkUtilityMock = mockStatic(LinkUtility.class)) {
        val result = EFormUtil.listPatientEformsCurrent(demographicNo, true, 0, 1);

        assertNotNull(result);
        verify(eformDataDao).findByDemographicIdCurrent(demographicNo, true, 0, 1);
        verify(eformDataDao).fetchRemoteEformByDemographicId(demographicNo);

        linkUtilityMock.verify(() -> LinkUtility.deduplicateByGuid(any()), times(0));
      }
    }

    // some remote eforms should dedupe
    @Test
    public void givenRemoteEforms_whenListPatientEformsCurrent_thenDedupe() {
      val demographicNo = 1;
      val remoteEforms = createRemoteEForms(1);

      when(eformDataDao.fetchRemoteEformByDemographicId(demographicNo)).thenReturn(remoteEforms);

      try (val linkUtilityMock = mockStatic(LinkUtility.class)) {
        val result = EFormUtil.listPatientEformsCurrent(demographicNo, true, 0, 1);

        assertNotNull(result);
        verify(eformDataDao).findByDemographicIdCurrent(demographicNo, true, 0, 1);
        verify(eformDataDao).fetchRemoteEformByDemographicId(demographicNo);
        linkUtilityMock.verify(() -> LinkUtility.deduplicateByGuid(any()));
      }
    }
  }

  private void assertEForm(final EForm eForm) {
    assertEquals(FORM_NAME, eForm.getFormName());
    assertEquals(FORM_SUBJECT, eForm.getSubject());
    assertEquals(FILE_NAME, eForm.getFileName());
    assertEquals(HTML_STR, eForm.getFormHtml());
    assertEquals(ROLE_TYPE, eForm.getRoleType());
    assertEquals(SHOW_LATEST_FORM_ONLY, eForm.isShowLatestFormOnly());
    assertEquals(PATIENT_INDEPENDENT, eForm.isPatientIndependent());
    assertEquals(IS_ATTACHMENTS_ENABLED, eForm.isAttachmentsEnabled());
  }

  private oscar.eform.data.EForm createTestEForm() {
    oscar.eform.data.EForm eForm = new oscar.eform.data.EForm();
    eForm.setFormName(FORM_NAME);
    eForm.setFormSubject(FORM_SUBJECT);
    eForm.setFormFileName(FILE_NAME);
    eForm.setFormHtml(HTML_STR);
    eForm.setRoleType(ROLE_TYPE);
    eForm.setShowLatestFormOnly(SHOW_LATEST_FORM_ONLY);
    eForm.setPatientIndependent(PATIENT_INDEPENDENT);
    eForm.setAttachmentsEnabled(IS_ATTACHMENTS_ENABLED);
    eForm.setFormCreator(CREATOR);
    return eForm;
  }

  private List<EFormData> createLocalEForms(int count) {
    List<EFormData> eForms = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      EFormData eForm = new EFormData();

      eForm.setGuid(UUID.randomUUID().toString());
      // Set other required fields as needed
      eForm.setCurrent(true); // Assuming local forms are current
      eForms.add(eForm);
    }

    return eForms;
  }

  private List<EFormData> createRemoteEForms(int count) {
    List<EFormData> eForms = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      EFormData eForm = new EFormData();

      eForm.setId(i);
      eForm.setGuid(UUID.randomUUID().toString());
      // Set other required fields as needed
      eForm.setCurrent(true); // Assuming remote forms are current
      eForm.setRemoteSystemId(i + 1); // Setting a remote system ID to indicate it's a remote form

      eForms.add(eForm);
    }

    return eForms;
  }

  @Test
  void givenRemoteEforms_whenListPatientEformsCurrent_thenRemoteEformConvertedToMetadata() {
    val demographicNo = 1;
    val remoteEforms = createRemoteEForms(1);

    when(eformDataDao.fetchRemoteEformByDemographicId(demographicNo)).thenReturn(remoteEforms);

    val result = EFormUtil.listPatientEformsMetadataCurrent(demographicNo, true, 0, 1);
    assertEquals(1, result.size());

    val eFormMetadata = result.get(0);
    val expectedEForm = remoteEforms.get(0);
    assertEquals(expectedEForm.getId(), eFormMetadata.getId());
    assertEquals(expectedEForm.getGuid(), eFormMetadata.getGuid());
    assertEquals(expectedEForm.getRemoteSystemId(), eFormMetadata.getRemoteSystemId());
    assertEquals(expectedEForm.getFormName(), eFormMetadata.getFormName());
    assertEquals(expectedEForm.getSubject(), eFormMetadata.getSubject());
    assertEquals(expectedEForm.getFormDate(), eFormMetadata.getFormDate());
  }

  @Test
  void givenLocalAndRemoteEforms_whenListPatientEformsMetadataCurrent_thenReturnCombinedEforms() {
    val demographicNo = 1;
    val localEforms = createLocalEForms(1);
    val localEformMetadata = convertToMetadata(localEforms);
    val remoteEforms = createRemoteEForms(2);
    when(eformDataDao.findMetadataByDemographicIdCurrent(
        eq(demographicNo),
        eq(true),
        eq(0),
        eq(1),
        eq(null))
    ).thenReturn(localEformMetadata);
    when(eformDataDao.fetchRemoteEformByDemographicId(demographicNo)).thenReturn(remoteEforms);

    val result = EFormUtil.listPatientEformsMetadataCurrent(demographicNo, true, 0, 1);
    assertEquals(3, result.size());
  }

  @Test
  void givenLocalAndRemoteEformsWithDuplicateGuid_whenListPatientEformsMetadataCurrent_thenReturnDedupedCombinedEforms() {
    val demographicNo = 1;
    val localEforms = createLocalEForms(1);
    val localEformMetadata = convertToMetadata(localEforms);
    val remoteEforms = createRemoteEForms(2);
    // Make one remote eform duplicate local
    remoteEforms.get(0).setGuid(localEforms.get(0).getGuid());
    when(eformDataDao.findMetadataByDemographicIdCurrent(
        eq(demographicNo),
        eq(true),
        eq(0),
        eq(1),
        eq(null))
    ).thenReturn(localEformMetadata);
    when(eformDataDao.fetchRemoteEformByDemographicId(demographicNo)).thenReturn(remoteEforms);

    val result = EFormUtil.listPatientEformsMetadataCurrent(demographicNo, true, 0, 1);
    assertEquals(2, result.size());
  }

  private List<EFormDataMetadata> convertToMetadata(List<EFormData> eFormDataList) {
    List<EFormDataMetadata> metadataList = new ArrayList<>();
    for (EFormData eFormData : eFormDataList) {
      metadataList.add(eFormData.getMetadata());
    }
    return metadataList;
  }
}
