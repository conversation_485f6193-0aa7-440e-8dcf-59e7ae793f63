package oscar.oscarRx.pageUtil;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.concurrent.CopyOnWriteArrayList;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import lombok.val;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionMapping;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.oscarehr.common.model.Facility;
import org.oscarehr.managers.SecurityInfoManager;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.WebApplicationContext;
import oscar.oscarRx.data.RxPrescriptionData;
import oscar.oscarRx.data.RxPrescriptionData.Prescription;
import javax.servlet.ServletOutputStream;

class RxWriteScriptActionTest {

  private static final String TEST_SCRIPT_ID = "12345";
  private static final int STASH_SIZE_ONE = 1;
  private static final int FIRST_STASH_ITEM_INDEX = 0;
  private static final String RX_SESSION_BEAN_ATTR = "RxSessionBean";
  private static final String TEST_DRUG_ID_1 = "1";
  private static final String TEST_DRUG_ID_2 = "2";
  private static final String TEST_DRUG_ID_3 = "3";

  private static class TestMocks {

    SecurityInfoManager securityManager;
    RxSessionBean sessionBean;
    Prescription prescription;
    HttpServletRequest request;
    HttpSession session;
    ServletContext servletContext;
    RxPrescriptionData rxPrescriptionData;
    Facility facility;
    LoggedInInfo loggedInInfo;
  }

  private TestMocks setupCommonMocks() {
    val mocks = new TestMocks();
    mocks.securityManager = mock(SecurityInfoManager.class);
    mocks.sessionBean = mock(RxSessionBean.class);
    mocks.prescription = mock(Prescription.class);
    mocks.request = mock(HttpServletRequest.class);
    mocks.session = mock(HttpSession.class);
    mocks.servletContext = mock(ServletContext.class);
    mocks.rxPrescriptionData = mock(RxPrescriptionData.class);
    mocks.loggedInInfo = mock(LoggedInInfo.class);

    when(mocks.sessionBean.getStashSize()).thenReturn(STASH_SIZE_ONE);
    when(mocks.sessionBean.getStashItem(FIRST_STASH_ITEM_INDEX)).thenReturn(mocks.prescription);
    when(mocks.request.getSession()).thenReturn(mocks.session);
    when(mocks.session.getAttribute(RX_SESSION_BEAN_ATTR)).thenReturn(mocks.sessionBean);
    when(mocks.session.getServletContext()).thenReturn(mocks.servletContext);
    when(mocks.securityManager.hasPrivilege(any(), any(), any(), any())).thenReturn(true);
    when(mocks.servletContext.getAttribute(
        WebApplicationContext.ROOT_WEB_APPLICATION_CONTEXT_ATTRIBUTE))
        .thenReturn(mock(WebApplicationContext.class));
    when(mocks.rxPrescriptionData.saveScript(any(), any())).thenReturn(TEST_SCRIPT_ID);
    when(LoggedInInfo.getLoggedInInfoFromSession(mocks.request)).thenReturn(mocks.loggedInInfo);

    return mocks;
  }

  @Test
  void givenUpdateAndPrintActionAndStashItem_whenUnspecifiedInvoked_thenSavesPrescription()
      throws Exception {
    try (val springUtils = mockStatic(SpringUtils.class);
        val loggedInInfoMock = mockStatic(LoggedInInfo.class)) {

      val mocks = setupCommonMocks();

      val action = new RxWriteScriptAction(mocks.rxPrescriptionData);
      ReflectionTestUtils.setField(action, "securityInfoManager", mocks.securityManager);

      val form = getRxWriteScriptForm();

      action.unspecified(
          mock(ActionMapping.class),
          form,
          mocks.request,
          mock(HttpServletResponse.class)
      );

      verify(mocks.prescription).Save(TEST_SCRIPT_ID);
    }
  }

  private RxWriteScriptForm getRxWriteScriptForm() {
    val form = new RxWriteScriptForm();
    form.setAction("updateAndPrint");
    form.setSpecial("special");
    form.setUnit("unit");
    form.setDosage("dosage");
    return form;
  }

  @Test
  void givenValidSessionAndStashItem_whenSaveDrugInvoked_thenSavesPrescription() throws Exception {
    try (MockedStatic<SpringUtils> springUtils = mockStatic(SpringUtils.class);
        MockedStatic<LoggedInInfo> loggedInInfoMock = mockStatic(LoggedInInfo.class)) {

      val mocks = setupCommonMocks();
      mocks.loggedInInfo = mock(LoggedInInfo.class);
      mocks.facility = mock(Facility.class);

      when(mocks.sessionBean.getReRxDrugIdList()).thenReturn(new CopyOnWriteArrayList<>());
      when(mocks.facility.isEnableDigitalSignatures()).thenReturn(false);
      when(mocks.loggedInInfo.getCurrentFacility()).thenReturn(mocks.facility);

      val action = new RxWriteScriptAction(mocks.rxPrescriptionData);
      ReflectionTestUtils.setField(action, "securityInfoManager", mocks.securityManager);

      action.saveDrug(mocks.request);

      verify(mocks.prescription).Save(TEST_SCRIPT_ID);
    }
  }


  @Test
  void givenReRxDrugIdListAndStashItems_whenUpdateSaveAllDrugsInvoked_thenFiltersReRxDrugIdList() {
    try (MockedStatic<SpringUtils> springUtils = mockStatic(SpringUtils.class);
        MockedStatic<LoggedInInfo> loggedInInfoMock = mockStatic(LoggedInInfo.class)) {

      val mocks = setupCommonMocks();
      val reRxDrugIdList = new CopyOnWriteArrayList<String>();
      reRxDrugIdList.add(TEST_DRUG_ID_1);
      reRxDrugIdList.add(TEST_DRUG_ID_2);
      reRxDrugIdList.add(TEST_DRUG_ID_3);
      when(mocks.sessionBean.getReRxDrugIdList()).thenReturn(reRxDrugIdList);

      when(mocks.sessionBean.getStashSize()).thenReturn(1);
      when(mocks.sessionBean.getStashItem(0)).thenReturn(mocks.prescription);
      when(mocks.prescription.getDrugReferenceId()).thenReturn(Integer.parseInt(TEST_DRUG_ID_1));

      val updatedReRxDrugIdList = new ArrayList<String>();

      for (int i = 0; i < mocks.sessionBean.getStashSize(); i++) {
          val rx = mocks.sessionBean.getStashItem(i);
          int drugReferenceId = rx.getDrugReferenceId();
          if (drugReferenceId > 0) {
              updatedReRxDrugIdList.add(String.valueOf(drugReferenceId));
          }
      }

      reRxDrugIdList.retainAll(updatedReRxDrugIdList);
      assertEquals(1, reRxDrugIdList.size());
      assertTrue(reRxDrugIdList.contains(TEST_DRUG_ID_1));
    }
  }
}
