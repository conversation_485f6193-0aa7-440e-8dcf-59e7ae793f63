package oscar.oscarEncounter.pageUtil;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Comparator;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import lombok.val;
import org.apache.struts.util.MessageResources;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.oscarehr.common.dao.UserPropertyDAO;
import org.oscarehr.common.model.Allergy;
import org.oscarehr.common.model.Facility;
import org.oscarehr.managers.SecurityInfoManager;
import org.oscarehr.util.DateUtils;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;
import oscar.OscarProperties;
import oscar.oscarRx.data.RxPatientData;
import oscar.oscarRx.data.RxPatientData.Patient;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
class EctDisplayAllergyActionTest {

  @Mock
  EctSessionBean ectSessionBean;
  @Mock
  HttpServletRequest request;
  @Mock
  HttpSession session;
  @Mock
  MessageResources messages;
  @Mock
  SecurityInfoManager securityInfoManager;
  @Mock
  UserPropertyDAO userPropertyDAO;
  @Mock
  OscarProperties oscarProperties;
  EctDisplayAllergyAction ectDisplayAllergyAction;
  MockedStatic<LoggedInInfo> loggedInInfoMockedStatic;
  MockedStatic<SpringUtils> springUtilsMockedStatic;
  MockedStatic<RxPatientData> rxPatientDataMockedStatic;
  MockedStatic<OscarProperties> oscarPropertiesMockedStatic;

  @BeforeEach
  public void setUp() {
    springUtilsMockedStatic = mockStatic(SpringUtils.class);
    loggedInInfoMockedStatic = mockStatic(LoggedInInfo.class);
    springUtilsMockedStatic.when(() -> SpringUtils.getBean(SecurityInfoManager.class)).thenReturn(securityInfoManager);
    springUtilsMockedStatic.when(() -> SpringUtils.getBean(UserPropertyDAO.class)).thenReturn(userPropertyDAO);
    ectDisplayAllergyAction = new EctDisplayAllergyAction();
    oscarPropertiesMockedStatic = mockStatic(OscarProperties.class);
    rxPatientDataMockedStatic = mockStatic(RxPatientData.class);

    ectSessionBean.demographicNo = "123";
    ectSessionBean.appointmentNo = "42";
    when(securityInfoManager.hasPrivilege(any(), any(), any(), any())).thenReturn(true);
    when(request.getSession()).thenReturn(session);
    when(session.getAttribute("user")).thenReturn("user123");

    LoggedInInfo loggedInInfo = mock(LoggedInInfo.class);
    loggedInInfoMockedStatic.when(() -> LoggedInInfo.getLoggedInInfoFromSession(request)).thenReturn(loggedInInfo);

    Facility facility = mock(Facility.class);
    when(loggedInInfo.getCurrentFacility()).thenReturn(facility);
    when(facility.isIntegratorEnabled()).thenReturn(false);

    oscarPropertiesMockedStatic.when(() -> OscarProperties.getInstance()).thenReturn(oscarProperties);
    when(oscarProperties.getProperty("oscar.deployed.context", "oscar")).thenReturn("oscar");
    when(oscarProperties.getProperty("DATE_FORMAT")).thenReturn("yyyy-MM-dd");
    when(oscarProperties.getProperty("TIME_FORMAT")).thenReturn("");
    when(userPropertyDAO.getProp(any(), any())).thenReturn(null);
  }

  @AfterEach
  public void tearDown() {
    springUtilsMockedStatic.close();
    loggedInInfoMockedStatic.close();
    oscarPropertiesMockedStatic.close();
    rxPatientDataMockedStatic.close();
  }

  @Test
  void givenRequestWithoutNumToDisplay_whenGetInfo_thenNavBarDisplayDAOHasAllAllergies() throws ParseException {

    val patientMock = mock(Patient.class);
    rxPatientDataMockedStatic.when(() -> RxPatientData.getPatient(any(), anyInt())).thenReturn(patientMock);

    val allergies = generateAllergiesArray();
    when(patientMock.getActiveAllergies()).thenReturn(allergies);

    NavBarDisplayDAO navBarDisplayDao = new NavBarDisplayDAO();
    val result = ectDisplayAllergyAction.getInfo(ectSessionBean, request, navBarDisplayDao, messages);

    assertTrue(result);

    val expectedLeftUrl = "popupPage(755,1200,'Allergy123','/oscar/oscarRx/showAllergy.do?demographicNo=123')";
    val expectedRightUrl = "popupPage(755,1200,'Allergy123','/oscar/oscarRx/showAllergy.do?demographicNo=123'); return false;";
    assertEquals("allergies", navBarDisplayDao.getRightHeadingID());
    assertEquals(allergies.length, navBarDisplayDao.numItems());
    assertEquals(expectedLeftUrl, navBarDisplayDao.getLeftURL());
    assertEquals(expectedRightUrl, navBarDisplayDao.getRightURL());

    val navBarItem = navBarDisplayDao.getItem(0);
    assertEquals("Latex", navBarItem.getTitle());
    assertEquals(DateUtils.parseIsoDate("2024-12-16"), navBarItem.getDate());
    assertEquals("return false;", navBarItem.getURL());
    assertEquals("orange", navBarItem.getColour());
  }

  @Test
  void givenRequestWithNumToDisplay_whenGetInfo_thenNavBarDisplayDAOHasAllergiesLimited() throws ParseException {
    String numToDisplay = "3";
    when(request.getParameter("numToDisplay")).thenReturn(numToDisplay);

    val patientMock = mock(Patient.class);
    rxPatientDataMockedStatic.when(() -> RxPatientData.getPatient(any(), anyInt())).thenReturn(patientMock);

    val allergies = generateAllergiesArray();
    // Sort allergies by entry date in descending order to match with order done by db
    Arrays.sort(allergies, new Comparator<Allergy>() {
      @Override
      public int compare(Allergy a1, Allergy a2) {
        return a2.getEntryDate().compareTo(a1.getEntryDate());
      }
    });
    when(patientMock.findActiveAllergiesWithLimit(anyInt())).thenReturn(allergies);

    NavBarDisplayDAO navBarDisplayDao = new NavBarDisplayDAO();
    val result = ectDisplayAllergyAction.getInfo(ectSessionBean, request, navBarDisplayDao, messages);

    assertTrue(result);

    val expectedLeftUrl = "popupPage(755,1200,'Allergy123','/oscar/oscarRx/showAllergy.do?demographicNo=123')";
    val expectedRightUrl = "popupPage(755,1200,'Allergy123','/oscar/oscarRx/showAllergy.do?demographicNo=123'); return false;";
    assertEquals("allergies", navBarDisplayDao.getRightHeadingID());
    assertEquals(Integer.parseInt(numToDisplay) + 1, navBarDisplayDao.numItems());
    assertEquals(expectedLeftUrl, navBarDisplayDao.getLeftURL());
    assertEquals(expectedRightUrl, navBarDisplayDao.getRightURL());

    val navBarItem = navBarDisplayDao.getItem(0);
    assertEquals("Latex", navBarItem.getTitle());
    assertEquals(DateUtils.parseIsoDate("2024-12-16"), navBarItem.getDate());
    assertEquals("return false;", navBarItem.getURL());
    assertEquals("orange", navBarItem.getColour());
  }

  @Test
  void givenEmptyAllergies_whenGetInfo_thenNavBarDisplayDAOIsEmpty() {
    String numToDisplay = "3";
    when(request.getParameter("numToDisplay")).thenReturn(numToDisplay);

    val patientMock = mock(Patient.class);
    rxPatientDataMockedStatic.when(() -> RxPatientData.getPatient(any(), anyInt())).thenReturn(patientMock);
    when(patientMock.findActiveAllergiesWithLimit(anyInt())).thenReturn(new Allergy[0]);

    NavBarDisplayDAO navBarDisplayDao = new NavBarDisplayDAO();
    val result = ectDisplayAllergyAction.getInfo(ectSessionBean, request, navBarDisplayDao, messages);

    assertTrue(result);

    val expectedLeftUrl = "popupPage(755,1200,'Allergy123','/oscar/oscarRx/showAllergy.do?demographicNo=123')";
    val expectedRightUrl = "popupPage(755,1200,'Allergy123','/oscar/oscarRx/showAllergy.do?demographicNo=123'); return false;";
    assertEquals("allergies", navBarDisplayDao.getRightHeadingID());
    assertEquals(0, navBarDisplayDao.numItems());
    assertEquals(expectedLeftUrl, navBarDisplayDao.getLeftURL());
    assertEquals(expectedRightUrl, navBarDisplayDao.getRightURL());

  }

  private Allergy[] generateAllergiesArray() throws ParseException {
    Allergy allergy1 = createSampleAllergy(1, "Peanuts", "Anaphylaxis", "3", "2024-12-10", 1);
    Allergy allergy2 = createSampleAllergy(2, "Penicillin", "Rash", "2", "2024-12-11", 2);
    Allergy allergy3 = createSampleAllergy(3, "Chloroquine", "Rash", "1", "2024-12-12", null);
    Allergy allergy4 = createSampleAllergy(4, "Aspirin", "Nausea", "2", "2024-12-13", 3);
    Allergy allergy5 = createSampleAllergy(5, "Ibuprofen", "Dizziness", "1", "2024-12-14", null);
    Allergy allergy6 = createSampleAllergy(6, "Sulfa drugs", "Hives", "3", "2024-12-15", 4);
    Allergy allergy7 = createSampleAllergy(7, "Latex", "Itching", "2", "2024-12-16", null);

    return  new Allergy[]{allergy1, allergy2, allergy3, allergy4, allergy5, allergy6, allergy7};
  }

  private Allergy createSampleAllergy(int id, String description, String reaction, String severity,
      String date, Integer remoteSystemId) throws ParseException {
    Allergy allergy = new Allergy();
    allergy.setId(id);
    allergy.setDescription(description);
    allergy.setReaction(reaction);
    allergy.setSeverityOfReaction(severity);
    allergy.setEntryDate(DateUtils.parseIsoDate(date));
    allergy.setRemoteSystemId(remoteSystemId);
    return allergy;
  }
}