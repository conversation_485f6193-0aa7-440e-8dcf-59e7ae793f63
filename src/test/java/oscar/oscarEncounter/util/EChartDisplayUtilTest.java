/*
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package oscar.oscarEncounter.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collection;
import javax.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class EChartDisplayUtilTest {

  private static final String NUMBER_TO_DISPLAY_PARAM = "numToDisplay";
  private static final String VALID_NUMBER = "10";
  private static final String NON_NUMERIC_VALUE = "abc";
  private static final Integer EXPECTED_INCREMENTED_VALUE = 11; // 10 + 1

  @Mock
  private HttpServletRequest request;

  public static Collection<Object[]> nullReturnCases() {
    return Arrays.asList(new Object[][]{
        {"NullRequest", null},
        {"NullParameter", null},
        {"EmptyParameter", ""},
        {"NonNumericParameter", NON_NUMERIC_VALUE}
    });
  }

  @ParameterizedTest(name = "given{0}_whenGetNumberToDisplayWithIncrement_thenReturnsNull")
  @MethodSource("nullReturnCases")
  public void givenInvalidInput_whenGetNumberToDisplayWithIncrement_thenReturnsNull(
      String testCase, String parameterValue) {

    // Handle the null request case separately
    if ("NullRequest".equals(testCase)) {
      Integer result = EChartDisplayUtil.getNumberToDisplayWithIncrement(null);
      assertNull(result);
      return;
    }

    // For other cases, mock the request parameter
    when(request.getParameter(NUMBER_TO_DISPLAY_PARAM)).thenReturn(parameterValue);
    Integer result = EChartDisplayUtil.getNumberToDisplayWithIncrement(request);
    assertNull(result);
  }

  @Test
  public void givenValidNumericParameter_whenGetNumberToDisplayWithIncrement_thenReturnsIncrementedValue() {
    when(request.getParameter(NUMBER_TO_DISPLAY_PARAM)).thenReturn(VALID_NUMBER);
    Integer result = EChartDisplayUtil.getNumberToDisplayWithIncrement(request);
    assertEquals(EXPECTED_INCREMENTED_VALUE, result);
  }
}
