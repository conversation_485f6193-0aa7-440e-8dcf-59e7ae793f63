package unittesting.ca.oscarpro.common.http;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import ca.kai.util.SecurityUtils;
import ca.oscarpro.common.http.OscarProHttpService;
import java.io.IOException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import lombok.val;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
public class OscarProHttpServiceTest {

  private static final String URL = "http://testurl:8080";
  private static final String BODY = "testbody";
  private static final String KAI_SSO_COOKIE = "kai-sso";
  private static final String KAI_SSO_COOKIE_VALUE = "test";
  private static final String COOKIE_HEADER = KAI_SSO_COOKIE + "=" + KAI_SSO_COOKIE_VALUE;

  private final ArgumentCaptor<HttpGet> httpGetCaptor = ArgumentCaptor.forClass(HttpGet.class);

  private final ArgumentCaptor<HttpPost> httpPostCaptor = ArgumentCaptor.forClass(HttpPost.class);

  @Mock
  private HttpServletRequest request;

  @Mock
  private HttpClientBuilder httpClientBuilder;

  @Mock
  private CloseableHttpClient httpClient;

  @Spy
  private OscarProHttpService oscarProHttpService;

  private MockedStatic<HttpClientBuilder> httpClientBuilderMockedStatic;

  @BeforeEach
  public void setUp() {
    when(request.getCookies()).thenReturn(getCookieArrayWithKaiSsoCookie());
    // Mock the HttpClientBuilder to return a mocked CloseableHttpClient for request verification
    httpClientBuilderMockedStatic = mockStatic(HttpClientBuilder.class);
    httpClientBuilderMockedStatic.when(() -> HttpClientBuilder.create()).thenReturn(httpClientBuilder);
    when(httpClientBuilder.build()).thenReturn(httpClient);
  }

  @AfterEach
  public void tearDown() {
    httpClientBuilderMockedStatic.close();
  }

  @Test
  public void givenUrlAndBodyAndRequest_whenMakeLoggedInPostRequestToPro_thenReturnsHttpResponse()
      throws IOException {
    oscarProHttpService.makeLoggedInPostRequestToPro(URL, BODY, request);
    verify(httpClient, times(1)).execute(httpPostCaptor.capture());
    val httpPost = httpPostCaptor.getValue();
    assertNotNull(httpPost);
    assertNotNull(httpPost.getEntity());
    assertEquals(URL, httpPost.getURI().toString());
    assertEquals(BODY, EntityUtils.toString(httpPost.getEntity()));
    assertEquals(COOKIE_HEADER, httpPost.getFirstHeader("Cookie").getValue());
  }

  @Test
  public void givenUrlAndRequest_whenMakeLoggedInGetRequestToPro_thenReturnsHttpResponse()
      throws IOException {
    oscarProHttpService.makeLoggedInGetRequestToPro(URL, request);
    verify(httpClient, times(1)).execute(httpGetCaptor.capture());
    val httpGet = httpGetCaptor.getValue();
    assertNotNull(httpGet);
    assertEquals(URL, httpGet.getURI().toString());
    assertEquals(COOKIE_HEADER, httpGet.getFirstHeader("Cookie").getValue());
  }

  @Test
  void givenLoggedInProviderWithNoCookie_whenMakeLoggedInPostRequestToPro_thenReturnsHttpResponse()
      throws IOException {
    when(request.getCookies()).thenReturn(null);
    try (val mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
      mockedSecurityUtils.when(() -> SecurityUtils.createKaiSsoCookieForLoggedInProvider(any()))
          .thenReturn(new Cookie(KAI_SSO_COOKIE, KAI_SSO_COOKIE_VALUE));
      oscarProHttpService.makeLoggedInPostRequestToPro(URL, BODY, request);

      verify(httpClient, times(1)).execute(httpPostCaptor.capture());
      val httpPost = httpPostCaptor.getValue();
      assertNotNull(httpPost);
      assertEquals(URL, httpPost.getURI().toString());
      assertEquals(COOKIE_HEADER, httpPost.getFirstHeader("Cookie").getValue());
      mockedSecurityUtils.verify(() ->
              SecurityUtils.createKaiSsoCookieForLoggedInProvider(any()),
          times(1)
      );
    }
  }

  @Test
  void givenLoggedInProviderWithNoCookie_whenMakeLoggedInGetRequestToPro_thenReturnsHttpResponse()
      throws IOException {
    when(request.getCookies()).thenReturn(null);
    try (val mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
      mockedSecurityUtils.when(() -> SecurityUtils.createKaiSsoCookieForLoggedInProvider(any()))
          .thenReturn(new Cookie(KAI_SSO_COOKIE, KAI_SSO_COOKIE_VALUE));
      oscarProHttpService.makeLoggedInGetRequestToPro(URL, request);

      verify(httpClient, times(1)).execute(httpGetCaptor.capture());
      val httpGet = httpGetCaptor.getValue();
      assertNotNull(httpGet);
      assertEquals(URL, httpGet.getURI().toString());
      assertEquals(COOKIE_HEADER, httpGet.getFirstHeader("Cookie").getValue());
      mockedSecurityUtils.verify(() ->
          SecurityUtils.createKaiSsoCookieForLoggedInProvider(any()),
          times(1)
      );
    }
  }

  private Cookie[] getCookieArrayWithKaiSsoCookie() {
    return new Cookie[]{new Cookie(KAI_SSO_COOKIE, KAI_SSO_COOKIE_VALUE)};
  }

}