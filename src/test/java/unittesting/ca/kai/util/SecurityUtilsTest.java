/**
 * Copyright (c) 2023 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package unittesting.ca.kai.util;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import ca.kai.util.SecurityUtils;
import java.util.Base64;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.PMmodule.dao.ProviderDao;
import org.oscarehr.common.model.Provider;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;

@ExtendWith(MockitoExtension.class)
public class SecurityUtilsTest {

  private static final String PROVIDER_NO = "12345";
  private static final String EXISTING_UUID = "existing-uuid";
  private static final String EXISTING_SIGNING_KEY = "existingSigningKeyThatIsLongEnoughForHS256";

  @Mock
  private HttpServletRequest request;

  @Mock
  private LoggedInInfo loggedInInfo;

  @Mock
  private Provider provider;

  @Mock
  private ProviderDao providerDao;

  private MockedStatic<LoggedInInfo> loggedInInfoMockedStatic;
  private MockedStatic<SpringUtils> springUtilsMockedStatic;

  @BeforeEach
  public void setUp() {
    loggedInInfoMockedStatic = mockStatic(LoggedInInfo.class);
    springUtilsMockedStatic = mockStatic(SpringUtils.class);

    loggedInInfoMockedStatic.when(() -> LoggedInInfo.getLoggedInInfoFromRequest(any(HttpServletRequest.class)))
        .thenReturn(loggedInInfo);
    springUtilsMockedStatic.when(() -> SpringUtils.getBean(ProviderDao.class))
        .thenReturn(providerDao);

    when(loggedInInfo.getLoggedInProvider()).thenReturn(provider);
    when(provider.getProviderNo()).thenReturn(PROVIDER_NO);
  }

  @AfterEach
  public void tearDown() {
    loggedInInfoMockedStatic.close();
    springUtilsMockedStatic.close();
  }

  @Test
  public void givenExistingSigningKey_whenCreateKaiSsoCookieForLoggedInProvider_thenCreateCookieWithExistingInformation() {
    when(provider.getSigningKey()).thenReturn(Base64.getEncoder().encodeToString(
        EXISTING_SIGNING_KEY.getBytes()));
    when(provider.getLastUsedUuid()).thenReturn(EXISTING_UUID);
    Cookie cookie = SecurityUtils.createKaiSsoCookieForLoggedInProvider(request);
    assertNotNull(cookie);
    assertNotNull(cookie.getValue());
    // Verify provider was not updated and signing key was reused
    verify(providerDao, times(0)).updateProvider(any(Provider.class));
    verify(provider, times(2)).getSigningKey();
    verify(provider, times(1)).getLastUsedUuid();
  }

  @Test
  public void givenNoExistingSigningKey_whenCreateKaiSsoCookieForLoggedInProvider_thenUpdateProviderAndCreateCookie() {
    when(provider.getSigningKey()).thenReturn(null);
    Cookie cookie = SecurityUtils.createKaiSsoCookieForLoggedInProvider(request);
    assertNotNull(cookie);
    assertNotNull(cookie.getValue());
    // Verify provider was updated with new signing key and UUID
    verify(provider, times(1)).setSigningKey(any(String.class));
    verify(provider, times(1)).setLastUsedUuid(any(String.class));
    verify(providerDao, times(1)).updateProvider(provider);
  }
}
