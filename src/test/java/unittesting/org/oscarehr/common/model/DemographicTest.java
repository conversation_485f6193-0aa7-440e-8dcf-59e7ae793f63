/*
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package unittesting.org.oscarehr.common.model;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;
import lombok.val;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.common.model.DemographicGender;
import org.oscarehr.common.model.DemographicPronoun;

public class DemographicTest {

    @Test
    public void givenValidDemographic_whenCopyConstructor_thenReturnClonedDemographic() {
        val original = new Demographic();
        val demographicGender = new DemographicGender();
        demographicGender.setId(1);
        demographicGender.setValue("Male");
        demographicGender.setDeleted(false);
        demographicGender.setEditable(false);

        val demographicPronoun = new DemographicPronoun();
        demographicPronoun.setId(1);
        demographicPronoun.setValue("She/Her");
        demographicPronoun.setDeleted(false);
        demographicPronoun.setEditable(false);

        original.setDemographicNo(1);
        original.setActiveCount(5);
        original.setAddress("123 Main St");
        original.setAlias("John Doe");
        original.setAnonymous("Yes");
        original.setChartNo("C12345");
        original.setChildren("2");
        original.setCitizenship("US");
        original.setCity("New York");
        original.setConsentToUseEmailForCare(true);
        original.setConsentToUseEmailForEOrder(true);
        original.setCountryOfOrigin("USA");
        original.setDateJoined(new Date());
        original.setDateOfBirth("1980-01-01");
        original.setGenderIdentity(demographicGender);
        original.setPronoun(demographicPronoun);
        original.setGenderId(1);
        original.setPronounId(1);
        original.setEffDate(new Date());
        original.setEmail("<EMAIL>");
        original.setEndDate(new Date());
        original.setFirstName("John");
        original.setHcRenewDate(new Date());
        original.setHcType("Type A");
        original.setHeadRecord(1);
        original.setHin("H12345");
        original.setHsAlertCount(3);
        original.setLastName("Doe");
        original.setLastUpdateDate(new Date());
        original.setLastUpdateUser("admin");
        original.setLinks("www.example.com");
        original.setMiddleName("Middle");
        original.setMonthOfBirth("January");
        original.setMyOscarUserName("johndoe");
        original.setNewsletter("Yes");
        original.setOfficialLanguage("English");
        original.setPatientId("P12345");
        original.setPatientStatus("Active");
        original.setPatientStatusDate(new Date());
        original.setPatientType("Type 1");
        original.setPcnIndicator("Indicator 1");
        original.setPhone("**********");
        original.setPhone2("**********");
        original.setPortalUserId("portaluser");
        original.setPostal("12345");
        original.setPreferredName("Johnny");
        original.setPreviousAddress("456 Secondary St");
        original.setProvince("NY");
        original.setRosterDate(new Date());
        original.setRosterStatus("Enrolled");
        original.setRosterTerminationDate(new Date());
        original.setRosterTerminationReason("Reason");
        original.setSex("Male");
        original.setSexDesc("Male");
        original.setSin("S12345");
        original.setSourceOfIncome("Employment");
        original.setSpokenLanguage("English");
        original.setTitle("Mr.");
        original.setVer("1.0");
        original.setYearOfBirth("1980");
        original.setMiddleNames("Middle");
        original.setRosterEnrolledTo("EnrolledTo");
        original.setPrimarySystemId(123);
        original.setGuid("GUID123");
        Set<Integer> subRecords = new HashSet<>();
        subRecords.add(2);
        original.setSubRecord(subRecords);

        // Create a copy using the copy constructor
        val copy = new Demographic(original);

        // Verify each field in the copy matches the original
        assertEquals(original.getDemographicNo(), copy.getDemographicNo());
        assertEquals(original.getActiveCount(), copy.getActiveCount());
        assertEquals(original.getAddress(), copy.getAddress());
        assertEquals(original.getAlias(), copy.getAlias());
        assertEquals(original.getAnonymous(), copy.getAnonymous());
        assertEquals(original.getChartNo(), copy.getChartNo());
        assertEquals(original.getChildren(), copy.getChildren());
        assertEquals(original.getCitizenship(), copy.getCitizenship());
        assertEquals(original.getCity(), copy.getCity());
        assertEquals(original.getConsentToUseEmailForCare(), copy.getConsentToUseEmailForCare());
        assertEquals(original.getConsentToUseEmailForEOrder(), copy.getConsentToUseEmailForEOrder());
        assertEquals(original.getCountryOfOrigin(), copy.getCountryOfOrigin());
        assertEquals(original.getDateJoined(), copy.getDateJoined());
        assertEquals(original.getDateOfBirth(), copy.getDateOfBirth());
        assertEquals(original.getGenderIdentity(), copy.getGenderIdentity());
        assertEquals(original.getPronoun(), copy.getPronoun());
        assertEquals(original.getGenderId(), copy.getGenderId());
        assertEquals(original.getPronounId(), copy.getPronounId());
        assertEquals(original.getEffDate(), copy.getEffDate());
        assertEquals(original.getEmail(), copy.getEmail());
        assertEquals(original.getEndDate(), copy.getEndDate());
        assertEquals(original.getFirstName(), copy.getFirstName());
        assertEquals(original.getHcRenewDate(), copy.getHcRenewDate());
        assertEquals(original.getHcType(), copy.getHcType());
        assertEquals(original.getHeadRecord(), copy.getHeadRecord());
        assertEquals(original.getHin(), copy.getHin());
        assertEquals(original.getHsAlertCount(), copy.getHsAlertCount());
        assertEquals(original.getLastName(), copy.getLastName());
        assertEquals(original.getLastUpdateDate(), copy.getLastUpdateDate());
        assertEquals(original.getLastUpdateUser(), copy.getLastUpdateUser());
        assertEquals(original.getLinks(), copy.getLinks());
        assertEquals(original.getMiddleName(), copy.getMiddleName());
        assertEquals(original.getMonthOfBirth(), copy.getMonthOfBirth());
        assertEquals(original.getMyOscarUserName(), copy.getMyOscarUserName());
        assertEquals(original.getNewsletter(), copy.getNewsletter());
        assertEquals(original.getOfficialLanguage(), copy.getOfficialLanguage());
        assertEquals(original.getPatientId(), copy.getPatientId());
        assertEquals(original.getPatientStatus(), copy.getPatientStatus());
        assertEquals(original.getPatientStatusDate(), copy.getPatientStatusDate());
        assertEquals(original.getPatientType(), copy.getPatientType());
        assertEquals(original.getPcnIndicator(), copy.getPcnIndicator());
        assertEquals(original.getPhone(), copy.getPhone());
        assertEquals(original.getPhone2(), copy.getPhone2());
        assertEquals(original.getPortalUserId(), copy.getPortalUserId());
        assertEquals(original.getPostal(), copy.getPostal());
        assertEquals(original.getPreferredName(), copy.getPreferredName());
        assertEquals(original.getPreviousAddress(), copy.getPreviousAddress());
        assertEquals(original.getProvince(), copy.getProvince());
        assertEquals(original.getRosterDate(), copy.getRosterDate());
        assertEquals(original.getRosterStatus(), copy.getRosterStatus());
        assertEquals(original.getRosterTerminationDate(), copy.getRosterTerminationDate());
        assertEquals(original.getRosterTerminationReason(), copy.getRosterTerminationReason());
        assertEquals(original.getSex(), copy.getSex());
        assertEquals(original.getSexDesc(), copy.getSexDesc());
        assertEquals(original.getSin(), copy.getSin());
        assertEquals(original.getSourceOfIncome(), copy.getSourceOfIncome());
        assertEquals(original.getSpokenLanguage(), copy.getSpokenLanguage());
        assertEquals(original.getTitle(), copy.getTitle());
        assertEquals(original.getVer(), copy.getVer());
        assertEquals(original.getYearOfBirth(), copy.getYearOfBirth());
        assertEquals(original.getMiddleNames(), copy.getMiddleNames());
        assertEquals(original.getRosterEnrolledTo(), copy.getRosterEnrolledTo());
        assertEquals(original.getPrimarySystemId(), copy.getPrimarySystemId());
        assertEquals(original.getGuid(), copy.getGuid());
        assertEquals(original.getSubRecord(), copy.getSubRecord());
    }
}
