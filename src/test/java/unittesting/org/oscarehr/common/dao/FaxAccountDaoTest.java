/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package unittesting.org.oscarehr.common.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.oscarehr.common.dao.DaoTestFixtures;
import org.oscarehr.common.dao.FaxAccountDao;
import org.oscarehr.common.dao.utils.EntityDataGenerator;
import org.oscarehr.common.dao.utils.SchemaUtils;
import org.oscarehr.common.enums.FaxProviderType;
import org.oscarehr.common.model.FaxAccount;
import org.oscarehr.util.SpringUtils;

public class FaxAccountDaoTest extends DaoTestFixtures {

  protected FaxAccountDao dao = SpringUtils.getBean(FaxAccountDao.class);

  @BeforeEach
  public void before() throws Exception {
    SchemaUtils.restoreTable("FaxAccount");
  }

  @Test
  public void givenValidId_whenGetEnabledFaxAccount_thenReturnFaxAccount() throws Exception {
    val faxAccount = new FaxAccount();
    EntityDataGenerator.generateTestDataForModelClass(faxAccount);
    faxAccount.setAccountEnabled(true);
    faxAccount.setDeletedAt(null);
    faxAccount.setFaxProviderType(FaxProviderType.RING_CENTRAL);
    dao.persist(faxAccount);

    val retrievedAccount = dao.getEnabledFaxAccount(faxAccount.getId());
    assertNotNull(retrievedAccount);
    assertEquals(faxAccount.getId(), retrievedAccount.getId());
    assertTrue(retrievedAccount.isAccountEnabled());
    assertNull(retrievedAccount.getDeletedAt());
  }
}