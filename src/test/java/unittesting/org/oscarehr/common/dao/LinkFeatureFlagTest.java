package unittesting.org.oscarehr.common.dao;

import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import static org.oscarehr.common.enums.FeatureFlagEnum.ALLERGIES_ENABLED;
import static org.oscarehr.common.enums.FeatureFlagEnum.CPP_ENABLED;
import static org.oscarehr.common.enums.FeatureFlagEnum.EFORM_ENABLED;
import static org.oscarehr.common.enums.FeatureFlagEnum.MEDICATIONS_ENABLED;
import static org.oscarehr.common.enums.FeatureFlagEnum.PREVENTIONS_ENABLED;

import health.apps.gateway.LinkData;
import health.apps.gateway.LinkUtility;
import health.apps.gateway.converters.DocumentReferenceConverter;
import health.apps.gateway.service.GWConfigurationService;
import health.apps.gateway.service.GatewayDao;
import health.apps.gateway.service.GatewayDaoImpl;
import java.util.HashMap;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import lombok.SneakyThrows;
import lombok.val;
import org.hl7.fhir.r4.model.AllergyIntolerance;
import org.hl7.fhir.r4.model.Bundle;
import org.hl7.fhir.r4.model.DocumentReference;
import org.hl7.fhir.r4.model.Immunization;
import org.hl7.fhir.r4.model.MedicationRequest;
import org.hl7.fhir.r4.model.MedicationStatement;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.casemgmt.dao.CaseManagementNoteDAO;
import org.oscarehr.casemgmt.model.CaseManagementNote;
import org.oscarehr.common.dao.AllergyDao;
import org.oscarehr.common.dao.DemographicDao;
import org.oscarehr.common.dao.DocumentDao;
import org.oscarehr.common.dao.DrugDao;
import org.oscarehr.common.dao.EFormDataDao;
import org.oscarehr.common.dao.PreventionDao;
import org.oscarehr.common.enums.FeatureFlagEnum;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.common.model.EFormData;
import org.oscarehr.hospitalReportManager.dao.HRMDocumentDao;
import org.oscarehr.managers.DemographicManager;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;
import oscar.dms.EDoc;
import oscar.dms.EDocUtil;

@ExtendWith(MockitoExtension.class)
class LinkFeatureFlagTest {

  public static final Integer DEMOGRAPHIC_ID = 1007;
  private static MockedStatic<SpringUtils> springUtilsMockedStatic;
  // Service Mocks
  @Mock
  private GWConfigurationService configurationService;
  @Mock
  private DemographicManager demographicManager;
  @Mock
  private GatewayDaoImpl gatewayDao;
  @Mock
  private DocumentReferenceConverter converter;
  @Mock
  private DemographicDao demographicDao;
  @Mock
  private EntityManager entityManager;

  // Entity and Data Model Mocks
  @Mock
  private Demographic demographic;
  @Mock
  private Bundle bundle;
  @Mock
  private Query query;

  // DAOs to Inject
  @InjectMocks
  private EFormDataDao eformDao;
  @InjectMocks
  private PreventionDao preventionDao;
  @InjectMocks
  private DocumentDao documentDao;
  @InjectMocks
  private AllergyDao allergyDao;
  @InjectMocks
  private DrugDao drugDao;
  @InjectMocks
  private CaseManagementNoteDAO caseManagementNoteDAO;
  @InjectMocks
  private HRMDocumentDao hrmDocumentDao;

  @BeforeAll
  public static void setup() {
    springUtilsMockedStatic = mockStatic(SpringUtils.class);
  }

  @AfterAll
  public static void tearDown() {
    springUtilsMockedStatic.close();
  }

  @Nested
  class CaseManagementNoteDAOTests {

    @BeforeEach
    public void setUp() {
      when(SpringUtils.getBean(GatewayDao.class)).thenReturn(gatewayDao);
      when(SpringUtils.getBean(DemographicManager.class)).thenReturn(demographicManager);
    }

    @SneakyThrows
    @Test
    void givenFeatureDisabled_whenFindingRemoteNotes_thenReturnEmptyList() {
      // setup
      when(configurationService.isLinkFeatureEnabled(CPP_ENABLED)).thenReturn(false);

      // act
      List<CaseManagementNote> result = caseManagementNoteDAO.findAllRemoteByDemographicId(
          DEMOGRAPHIC_ID, "issueCodes");

      // assert
      assertTrue(result.isEmpty(), "Expected an empty list when feature flag is disabled.");
      verify(configurationService).isLinkFeatureEnabled(CPP_ENABLED);
      verifyNoInteractions(gatewayDao);
    }

    @SneakyThrows
    @Test
    void givenFeatureEnabled_whenFindingRemoteNotes_thenGatewayDaoCalled() {
      // setup
      when(configurationService.isLinkFeatureEnabled(CPP_ENABLED)).thenReturn(true);
      when(demographicManager.getDemographic(DEMOGRAPHIC_ID)).thenReturn(demographic);

      // act
      caseManagementNoteDAO.findAllRemoteByDemographicId(DEMOGRAPHIC_ID, "issueCodes");

      // assert
      verify(configurationService).isLinkFeatureEnabled(CPP_ENABLED);
      verify(demographicManager).getDemographic(DEMOGRAPHIC_ID);
      verify(gatewayDao).findAllRemoteByDemographicId(
          eq(DocumentReference.class), eq(demographic), any(HashMap.class));
    }
  }

  @Nested
  class DocumentDaoTests {

    @BeforeEach
    void setUp() {
      MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenFeatureDisabled_whenFindingRemoteDocs_thenReturnEmptyList() {
      // Arrange
      when(
          configurationService.isLinkFeatureEnabled(FeatureFlagEnum.DOCUMENTS_ENABLED)).thenReturn(
          false);

      // Act
      List<EDoc> result = executeFindRemoteDocuments("123", "active");

      // Assert
      assertTrue(result.isEmpty());
      verify(configurationService, times(1)).isLinkFeatureEnabled(
          FeatureFlagEnum.DOCUMENTS_ENABLED);
      verifyNoInteractions(gatewayDao);
      verifyNoInteractions(demographicManager);
    }

    @Test
    void givenFeatureEnabled_whenFindingRemoteDocs_thenReturnDocs() {
      // Arrange
      when(
          configurationService.isLinkFeatureEnabled(FeatureFlagEnum.DOCUMENTS_ENABLED)).thenReturn(
          true);

      // Act
      List<EDoc> result = executeFindRemoteDocuments("123", "active");

      // Assert
      assertNotNull(result);
      verifyInteractionsForSuccessfulCall();
    }

    @Test
    void givenDifferentStatuses_whenFindingRemoteDocs_thenHandleBoth() {
      // Arrange
      when(
          configurationService.isLinkFeatureEnabled(FeatureFlagEnum.DOCUMENTS_ENABLED)).thenReturn(
          true);

      // Act
      List<EDoc> resultActive = executeFindRemoteDocuments("123", "active");
      List<EDoc> resultDeleted = executeFindRemoteDocuments("123", "deleted");

      // Assert
      assertNotNull(resultActive);
      assertNotNull(resultDeleted);
      verifyInteractionsForSuccessfulCall(2);
    }

    private List<EDoc> executeFindRemoteDocuments(String demographicId, String status) {
      return documentDao.findRemoteDocumentsByDemographicId(
          mock(LoggedInInfo.class),
          demographicId,
          status,
          EDocUtil.EDocSort.DATE
      );
    }

    private void verifyInteractionsForSuccessfulCall() {
      verifyInteractionsForSuccessfulCall(1);
    }

    private void verifyInteractionsForSuccessfulCall(int timesCalled) {
      verify(configurationService, times(timesCalled)).isLinkFeatureEnabled(
          FeatureFlagEnum.DOCUMENTS_ENABLED);
      verify(gatewayDao, times(timesCalled)).findAllRemoteByDemographicId(any(), any(), any());
      verify(demographicManager, times(timesCalled)).getDemographic(any(), eq("123"));
    }
  }

  @Nested
  class AllergyDaoTests {

    @BeforeEach
    public void setUp() {
      when(SpringUtils.getBean(GatewayDao.class)).thenReturn(gatewayDao);
      when(entityManager.createQuery(anyString())).thenReturn(query);
    }

    @Nested
    class FindAllergies {

      @SneakyThrows
      @Test
      void givenLinkAllergiesDisabled_whenFindAllergies_thenGatewayDaoNotCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(ALLERGIES_ENABLED)).thenReturn(false);
        // act
        allergyDao.findActiveAllergies(DEMOGRAPHIC_ID);
        // assert
        verify(configurationService).isLinkFeatureEnabled(ALLERGIES_ENABLED);
        verify(demographicManager, times(0)).getDemographic(DEMOGRAPHIC_ID);
        verify(gatewayDao, times(0)).findAllRemoteByDemographicId(any(Class.class), any(LinkData.class),
            any(HashMap.class));
      }

      @SneakyThrows
      @Test
      void givenLinkAllergiesEnabled_whenFindAllergies_thenGatewayDaoCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(ALLERGIES_ENABLED)).thenReturn(true);
        when(demographicManager.getDemographic(DEMOGRAPHIC_ID)).thenReturn(demographic);
        when(gatewayDao.findAllRemoteByDemographicId(
            eq(AllergyIntolerance.class), eq(demographic), any(HashMap.class))).thenReturn(bundle);
        // act
        allergyDao.findAllergies(DEMOGRAPHIC_ID);
        // assert
        verify(configurationService).isLinkFeatureEnabled(ALLERGIES_ENABLED);
        verify(demographicManager).getDemographic(DEMOGRAPHIC_ID);
        verify(gatewayDao).findAllRemoteByDemographicId(
            eq(AllergyIntolerance.class), eq(demographic), any(HashMap.class));
      }
    }

    @Nested
    class FindActiveAllergies {

      @SneakyThrows
      @Test
      void givenLinkAllergiesDisabled_whenFindActiveAllergies_thenGatewayDaoNotCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(ALLERGIES_ENABLED)).thenReturn(false);
        // act
        allergyDao.findActiveAllergies(DEMOGRAPHIC_ID);
        // assert
        verify(configurationService).isLinkFeatureEnabled(ALLERGIES_ENABLED);
        verify(demographicManager, times(0)).getDemographic(DEMOGRAPHIC_ID);
        verify(gatewayDao, times(0)).findAllRemoteByDemographicId(
            any(Class.class), any(LinkData.class), any(HashMap.class));
      }

      @SneakyThrows
      @Test
      void givenLinkAllergiesEnabled_whenFindActiveAllergies_thenGatewayDaoCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(ALLERGIES_ENABLED)).thenReturn(true);
        when(demographicManager.getDemographic(DEMOGRAPHIC_ID)).thenReturn(demographic);
        when(gatewayDao.findAllRemoteByDemographicId(
            eq(AllergyIntolerance.class), eq(demographic), any(HashMap.class))).thenReturn(bundle);
        // act
        allergyDao.findActiveAllergies(DEMOGRAPHIC_ID);
        // assert
        verify(configurationService).isLinkFeatureEnabled(ALLERGIES_ENABLED);
        verify(demographicManager).getDemographic(DEMOGRAPHIC_ID);
        verify(gatewayDao).findAllRemoteByDemographicId(
            eq(AllergyIntolerance.class), eq(demographic), any(HashMap.class));
      }
    }
  }

  @Nested
  class DrugDaoUnitTest {

    @BeforeEach
    public void setUp() {
      when(demographicDao.getDemographic(DEMOGRAPHIC_ID)).thenReturn(demographic);
    }

    @Nested
    class FetchRemoteDrugs {

      @SneakyThrows
      @Test
      void givenLinkMedicationsDisabled_whenFetchRemoteDrugs_thenGatewayDaoNotCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(MEDICATIONS_ENABLED)).thenReturn(false);
        // act
        drugDao.fetchRemoteDrugs(DEMOGRAPHIC_ID);
        // assert
        verify(configurationService).isLinkFeatureEnabled(MEDICATIONS_ENABLED);
        verify(demographicDao).getDemographic(DEMOGRAPHIC_ID);
        verify(gatewayDao, times(0)).findAllRemoteByDemographicId(
            any(), any(), any());
      }

      @SneakyThrows
      @Test
      void givenLinkMedicationsEnabled_whenFetchRemoteDrugs_thenGatewayDaoCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(MEDICATIONS_ENABLED)).thenReturn(true);
        // act
        drugDao.fetchRemoteDrugs(DEMOGRAPHIC_ID);
        // assert
        verify(configurationService).isLinkFeatureEnabled(MEDICATIONS_ENABLED);
        verify(demographicDao).getDemographic(DEMOGRAPHIC_ID);
        verify(gatewayDao).findAllRemoteByDemographicId(
            eq(MedicationStatement.class), eq(demographic), any(HashMap.class));
      }
    }

    @Nested
    class FetchRemoteDrugByGuid {

      @SneakyThrows
      @Test
      void givenLinkMedicationsDisabled_whenFetchRemoteDrugByGuid_thenGatewayDaoNotCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(MEDICATIONS_ENABLED)).thenReturn(false);
        // act
        drugDao.fetchRemoteDrugByGuid(DEMOGRAPHIC_ID, "guid");
        // assert
        verify(configurationService).isLinkFeatureEnabled(MEDICATIONS_ENABLED);
        verify(gatewayDao, times(0)).findAllRemoteByDemographicId(
            any(), any(), any());
      }

      @SneakyThrows
      @Test
      void givenLinkMedicationsEnabled_whenFetchRemoteDrugByGuid_thenGatewayDaoCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(MEDICATIONS_ENABLED)).thenReturn(true);
        // act
        drugDao.fetchRemoteDrugByGuid(DEMOGRAPHIC_ID, "guid");
        // assert
        verify(configurationService).isLinkFeatureEnabled(MEDICATIONS_ENABLED);
        verify(gatewayDao).findAllRemoteByDemographicId(
            eq(MedicationRequest.class), any(LinkData.class), any());
      }
    }
  }

  @Nested
  class PreventionDaoUnitTest {

    @Nested
    class FetchRemotePreventions {

      @SneakyThrows
      @Test
      void givenLinkPreventionsDisabled_whenFetchRemotePreventions_thenGatewayDaoNotCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(PREVENTIONS_ENABLED)).thenReturn(false);
        // act
        preventionDao.fetchRemotePreventions(demographic);
        // assert
        verify(configurationService).isLinkFeatureEnabled(PREVENTIONS_ENABLED);
        verify(gatewayDao, times(0)).findAllRemoteByDemographicId(
            any(Class.class), any(LinkData.class), any(HashMap.class));
      }

      @SneakyThrows
      @Test
      void givenLinkPreventionsEnabled_whenFetchRemotePreventions_thenGatewayDaoCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(PREVENTIONS_ENABLED)).thenReturn(true);
        // act
        preventionDao.fetchRemotePreventions(demographic);
        // assert
        verify(configurationService).isLinkFeatureEnabled(PREVENTIONS_ENABLED);
        verify(gatewayDao).findAllRemoteByDemographicId(
            eq(Immunization.class), eq(demographic), any(HashMap.class));
      }
    }
  }

  @Nested
  class EFormDataDaoUnitTest {

    @BeforeEach
    public void setUp() {
      when(SpringUtils.getBean(GatewayDao.class)).thenReturn(gatewayDao);
      when(SpringUtils.getBean(DemographicManager.class)).thenReturn(demographicManager);
      when(SpringUtils.getBean(DocumentReferenceConverter.class)).thenReturn(converter);
    }

    @Nested
    class FetchRemoteEformByGuid {

      @SneakyThrows
      @Test
      void givenLinkEformDisabled_whenFetchRemoteEformByGuid_thenGatewayDaoNotCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(EFORM_ENABLED)).thenReturn(false);

        // act
        List<EFormData> result = eformDao.fetchRemoteEformByGuid(DEMOGRAPHIC_ID, "some-guid");

        // assert
        assertNotNull(result, "Result should be an empty list when feature flag is disabled");
        verify(configurationService).isLinkFeatureEnabled(EFORM_ENABLED);
        verifyNoInteractions(demographicManager);
        verifyNoInteractions(gatewayDao);
      }

      @SneakyThrows
      @Test
      void givenLinkEformEnabled_whenFetchRemoteEformByGuid_thenGatewayDaoCalled() {
        // setup
        when(configurationService.isLinkFeatureEnabled(EFORM_ENABLED)).thenReturn(true);
        when(demographicManager.getDemographic(DEMOGRAPHIC_ID)).thenReturn(demographic);

        Bundle mockBundle = new Bundle();
        DocumentReference mockDocument = new DocumentReference();
        mockBundle.addEntry().setResource(mockDocument);

        when(gatewayDao.findAllRemoteByDemographicId(eq(DocumentReference.class), eq(demographic),
            any(HashMap.class)))
            .thenReturn(mockBundle);

        // act
        List<EFormData> result = eformDao.fetchRemoteEformByGuid(DEMOGRAPHIC_ID, "some-guid");

        // assert
        assertNotNull(result, "Result should not be null when feature flag is enabled");
        verify(configurationService).isLinkFeatureEnabled(EFORM_ENABLED);
        verify(demographicManager).getDemographic(DEMOGRAPHIC_ID);
        verify(gatewayDao).findAllRemoteByDemographicId(
            eq(DocumentReference.class), eq(demographic), any(HashMap.class));
      }
    }

    @Nested
    class FindByDemographicIdCurrentNoData {

      @Test
      public void givenNoRemoteEforms_whenFindByDemographicIdCurrentNoData_thenNoDedupe() {
        val demographicNo = 1;

        try (val linkUtilityMock = mockStatic(LinkUtility.class)) {
          when(entityManager.createQuery(anyString())).thenReturn(query);

          val result = eformDao.findByDemographicIdCurrentExcludingFormData(demographicNo, true);

          assertNotNull(result);

          linkUtilityMock.verify(() -> LinkUtility.deduplicateByGuid(any()), times(0));
        }
      }

      @SneakyThrows
      @Test
      public void givenRemoteEforms_whenFindByDemographicIdCurrentNoData_thenDedupe() {
        val demographicNo = 1;
        val bundle = mock(Bundle.class);
        val demographic = mock(Demographic.class);
        val bundleEntryComponent = mock(Bundle.BundleEntryComponent.class);
        val documentReference = mock(DocumentReference.class);

        try (val linkUtilityMock = mockStatic(LinkUtility.class)) {
          when(entityManager.createQuery(anyString())).thenReturn(query);
          when(demographicManager.getDemographic(demographicNo)).thenReturn(demographic);
          when(configurationService.isLinkFeatureEnabled(EFORM_ENABLED)).thenReturn(true);
          when(gatewayDao.findAllRemoteByDemographicId(any(), any(), any())).thenReturn(bundle);
          when(bundle.getEntry()).thenReturn(singletonList(bundleEntryComponent));
          when(bundleEntryComponent.getResource()).thenReturn(documentReference);

          val result = eformDao.findByDemographicIdCurrentExcludingFormData(demographicNo, true);

          assertNotNull(result);
          linkUtilityMock.verify(() -> LinkUtility.deduplicateByGuid(any()));
        }
      }
    }
  }

  @Nested
  class HrmDocumentDaoUnitTest {

    @SneakyThrows
    @Test
    void givenLinkHrmDisabled_whenFindAllRemoteHrmDocumentsByDemographicId_thenReturnEmptyList() {
      // setup
      when(configurationService.isLinkFeatureEnabled(FeatureFlagEnum.HRM_ENABLED))
          .thenReturn(false);
      // act
      val result = hrmDocumentDao.findAllRemoteHrmDocumentsByDemographicId(DEMOGRAPHIC_ID);
      // assert
      assertTrue(result.isEmpty(), "Expected an empty list when feature flag is disabled.");
      verify(configurationService).isLinkFeatureEnabled(FeatureFlagEnum.HRM_ENABLED);
      verifyNoInteractions(demographicManager);
      verifyNoInteractions(gatewayDao);
    }

    @SneakyThrows
    @Test
    void givenLinkHrmEnabled_whenFindAllRemoteHrmDocumentsByDemographicId_thenGatewayDaoCalled() {
      // setup
      when(configurationService.isLinkFeatureEnabled(FeatureFlagEnum.HRM_ENABLED)).thenReturn(true);
      when(demographicManager.getDemographic(DEMOGRAPHIC_ID)).thenReturn(demographic);
      when(gatewayDao.findAllRemoteByDemographicId(
          eq(DocumentReference.class), eq(demographic), any(HashMap.class))).thenReturn(bundle);
      // act
      hrmDocumentDao.findAllRemoteHrmDocumentsByDemographicId(DEMOGRAPHIC_ID);
      // assert
      verify(configurationService).isLinkFeatureEnabled(FeatureFlagEnum.HRM_ENABLED);
      verify(demographicManager).getDemographic(DEMOGRAPHIC_ID);
      verify(gatewayDao).findAllRemoteByDemographicId(
          eq(DocumentReference.class), eq(demographic), any(HashMap.class));
    }
  }

}
