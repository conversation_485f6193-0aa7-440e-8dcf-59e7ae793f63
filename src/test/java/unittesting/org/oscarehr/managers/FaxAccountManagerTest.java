/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package unittesting.org.oscarehr.managers;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.oscarehr.common.dao.FaxAccountDao;
import org.oscarehr.common.model.FaxAccount;
import org.oscarehr.common.model.SystemPreferences;
import org.oscarehr.managers.FaxAccountManager;
import org.springframework.test.util.ReflectionTestUtils;
import oscar.util.SystemPreferencesUtils;

class FaxAccountManagerTest {

  private FaxAccountDao faxAccountDao;

  private MockedStatic<SystemPreferencesUtils> systemPreferencesUtilsMockedStatic;

  private final FaxAccountManager faxAccountManager = new FaxAccountManager();

  private static final String FAX_DEFAULT_PREFERENCE_NOT_SET = "-1";

  @BeforeEach
  public void init() {
    faxAccountDao = mock(FaxAccountDao.class);
    systemPreferencesUtilsMockedStatic = mockStatic(SystemPreferencesUtils.class);
    ReflectionTestUtils.setField(faxAccountManager, "faxAccountDao", faxAccountDao);
  }

  @Test
  public void givenSystemPreferenceNotFound_whenGetActiveFaxAccount_thenReturnOptionalEmpty() {
    systemPreferencesUtilsMockedStatic.when(() ->
        SystemPreferencesUtils.getPreferenceValueByName(
            SystemPreferences.FAX_DEFAULT_ACCOUNT_ID, FAX_DEFAULT_PREFERENCE_NOT_SET))
        .thenReturn(FAX_DEFAULT_PREFERENCE_NOT_SET);
    val activeFaxAccount = faxAccountManager.getActiveFaxAccount();
    assertFalse(activeFaxAccount.isPresent());
  }

  @Test
  public void givenNullFaxAccount_whenGetActiveFaxAccount_thenReturnOptionalEmpty() {
    systemPreferencesUtilsMockedStatic.when(() ->
        SystemPreferencesUtils.getPreferenceValueByName(
            SystemPreferences.FAX_DEFAULT_ACCOUNT_ID,
            FAX_DEFAULT_PREFERENCE_NOT_SET
        )).thenReturn("1");
    when(faxAccountDao.getEnabledFaxAccount(1)).thenReturn(null);
    val activeFaxAccount = faxAccountManager.getActiveFaxAccount();
    assertFalse(activeFaxAccount.isPresent());
  }

  @Test
  public void givenInvalidFaxAccountId_whenGetActiveFaxAccount_thenReturnOptionalEmpty() {
    systemPreferencesUtilsMockedStatic.when(() ->
        SystemPreferencesUtils.getPreferenceValueByName(
            SystemPreferences.FAX_DEFAULT_ACCOUNT_ID,
            FAX_DEFAULT_PREFERENCE_NOT_SET
        )).thenReturn("Bad data");
    val activeFaxAccount = faxAccountManager.getActiveFaxAccount();
    assertFalse(activeFaxAccount.isPresent());
  }

  @Test
  public void givenFaxAccount_whenGetActiveFaxAccount_thenReturnOptionalWithFaxAccount() {
    systemPreferencesUtilsMockedStatic.when(() ->
        SystemPreferencesUtils.getPreferenceValueByName(
            SystemPreferences.FAX_DEFAULT_ACCOUNT_ID,
            FAX_DEFAULT_PREFERENCE_NOT_SET
        )).thenReturn("1");
    val faxAccount = new FaxAccount();
    when(faxAccountDao.getEnabledFaxAccount(1)).thenReturn(faxAccount);
    val activeFaxAccount = faxAccountManager.getActiveFaxAccount();
    assertTrue(activeFaxAccount.isPresent());
  }

  @Test
  public void givenNoActiveFaxAccount_whenHasActiveFaxAccount_thenReturnFalse() {
    systemPreferencesUtilsMockedStatic.when(() ->
        SystemPreferencesUtils.getPreferenceValueByName(
            SystemPreferences.FAX_DEFAULT_ACCOUNT_ID,
            FAX_DEFAULT_PREFERENCE_NOT_SET
        )).thenReturn(FAX_DEFAULT_PREFERENCE_NOT_SET);
    assertFalse(faxAccountManager.hasActiveFaxAccount());
  }

  @Test
  public void givenActiveFaxAccount_whenHasActiveFaxAccount_thenReturnTrue() {
    systemPreferencesUtilsMockedStatic.when(() ->
        SystemPreferencesUtils.getPreferenceValueByName(
            SystemPreferences.FAX_DEFAULT_ACCOUNT_ID,
            FAX_DEFAULT_PREFERENCE_NOT_SET
        )).thenReturn("1");
    val faxAccount = new FaxAccount();
    when(faxAccountDao.getEnabledFaxAccount(1)).thenReturn(faxAccount);
    assertTrue(faxAccountManager.hasActiveFaxAccount());
  }

  @AfterEach
  public void after() {
    systemPreferencesUtilsMockedStatic.close();
  }
}