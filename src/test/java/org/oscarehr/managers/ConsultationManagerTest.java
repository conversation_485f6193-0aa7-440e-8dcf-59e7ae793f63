package org.oscarehr.managers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lowagie.text.DocumentException;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Files;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.function.Executable;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.common.dao.ConsultDocsDao;
import org.oscarehr.common.dao.ConsultRequestDao;
import org.oscarehr.common.dao.EFormDataDao;
import org.oscarehr.common.dao.EReferAttachmentDao;
import org.oscarehr.common.dao.SystemPreferencesDao;
import org.oscarehr.common.model.ConsultDocs;
import org.oscarehr.common.model.ConsultationRequest;
import org.oscarehr.common.model.Document;
import org.oscarehr.common.model.EFormData;
import org.oscarehr.common.model.EReferAttachment;
import org.oscarehr.common.model.EReferAttachmentData;
import org.oscarehr.common.model.EReferAttachmentManagerData;
import org.oscarehr.common.model.OceanWorkflowTypeEnum;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.WKHtmlToPdfUtils;
import org.oscarehr.ws.rest.to.model.OceanApiAttachment;
import org.springframework.test.util.ReflectionTestUtils;
import oscar.OscarProperties;
import oscar.dms.EDoc;
import oscar.form.dao.FormSmartEncounterDao;
import oscar.form.dao.ONPerinatal2017Dao;
import oscar.form.model.FormONPerinatal2017;
import oscar.form.model.FormSmartEncounter;
import oscar.form.pageUtil.FormSmartEncounterAction;
import oscar.log.LogAction;
import oscar.log.LogService;
import oscar.oscarLab.ca.on.LabResultData;

@ExtendWith(MockitoExtension.class)
public class ConsultationManagerTest {

  private static final Integer DEMOGRAPHIC_NO = 1;
  private static final String FILE_NAME = "filename.pdf";
  private static final String FILE_PATH = "path/to/" + FILE_NAME;
  private static final byte[] PDF_BYTES = new byte[]{1, 2, 3, 4, 5};
  private static final Integer PRINTABLE_JSON_ID = 52;
  private static final String ATTACHMENT_FILE_FORMAT_SINGLE = "%s%s";
  private static final String ATTACHMENT_FILE_FORMAT_DOUBLE = "%s %s%s";
  private static final String EFORM_ATTACHMENT_FILE_FORMAT = "%s %s %s%s";

  private static final String PRINTABLE_JSON = "{\n"
      + "\"id\":\"" + PRINTABLE_JSON_ID + "\",\n"
      + "\"name\":\"Test Name\",\n"
      + "\"type\":\"Document\",\n"
      + "\"params\":{},\n"
      + "\"previewUrl\":\"/kaiemr/api/document/getPagePreview/" + PRINTABLE_JSON_ID + "?page=1\",\n"
      + "\"date\":null,\n"
      + "\"supported\":true,\n"
      + "\"printableAttachments\":[]\n"
      + "}";

  private static final Integer LEGACY_ID = 1;
  private static final String LEGACY_FILE_NAME = "legacy.file";
  private static final byte[] LEGACY_PDF_BYTES = new byte[]{1, 101, 102, 103};
  private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

  @InjectMocks
  @Spy
  private ConsultationManager consultationManager;

  @Mock
  private EReferAttachmentDao eReferAttachmentDao;
  @Mock
  private LogService logService;
  @Mock
  private SystemPreferencesDao systemPreferencesDao;
  @Mock ConsultRequestDao consultationRequestDao;
  @Mock ConsultDocsDao requestDocDao;
  @Mock
  ONPerinatal2017Dao onPerinatal2017Dao;
  @Mock private SecurityInfoManager securityInfoManager;
  private static MockedStatic<LogAction> logActionMock;

  @Mock
  private HttpServletRequest request;
  @Mock
  private LoggedInInfo loggedInInfo;
  @Mock
  private FormSmartEncounterDao formSmartEncounterDao;
  @Mock
  private DocumentManager documentManager;
  @Mock
  private EFormDataDao eFormDataDao;

  private static MockedStatic<OscarProperties> oscarPropertiesMock;

  private static MockedStatic<WKHtmlToPdfUtils> wkhtmlToPdfUtilsMock;


  @BeforeAll
  public static void beforeAll() {
    logActionMock = mockStatic(LogAction.class);
    oscarPropertiesMock = mockStatic(OscarProperties.class);
    val oscarProperties = mock(OscarProperties.class);
    when(OscarProperties.getInstance()).thenReturn(oscarProperties);
    lenient().when(oscarProperties.getProperty("WKHTMLTOPDF_COMMAND"))
        .thenReturn("/usr/local/bin/wkhtmltopdf");
    lenient().when(oscarProperties.getProperty("WKHTMLTOPDF_ARGS"))
        .thenReturn("--quiet");
  }

  @AfterAll
  public static void afterAll() {
    logActionMock.close();
    oscarPropertiesMock.close();
  }
  
  @Test
  void givenValidPrefixAndSuffix_whenCreateTempFile_thenFileIsCreated() throws IOException {
    String prefix = "testPrefix";
    String suffix = ".testSuffix";
    
    ConsultationManager consultationManager = new ConsultationManager();
    File result = consultationManager.createTempFile(prefix, suffix);
    
    assertNotNull(result);
  }

  @ParameterizedTest
  @CsvSource({
      "Document Name, Subject, 2023-05-15, document.pdf, Document Name Subject 2023-05-15.pdf",
      "Document Name, Subject, 2023-05-15, document.png, Document Name Subject 2023-05-15.png",
      "Document Name, Subject, 2023-05-15, document.jpg, Document Name Subject 2023-05-15.jpg",
      "Document Name, , 2023-05-15, document.pdf, Document Name 2023-05-15.pdf",
      "Document Name, Subject, , document.pdf, Document Name Subject.pdf",
      "Document Name, , , document.pdf, Document Name.pdf",
      ", Subject, 2023-05-15, document.pdf, document.pdf"
  })
  void givenNameSubjectDateAndDefaultFile_whenFormattingFileName_thenReturnFormattedFileName(
      final String name,
      final String subject,
      final String dateStr,
      final String defaultName,
      final String expected
  ) throws
      NoSuchMethodException,
      InvocationTargetException,
      IllegalAccessException,
      ParseException {
    val formatFileNameMethod = ConsultationManager.class.getDeclaredMethod(
        "formatFileName", String.class, String.class, Date.class, String.class);
    formatFileNameMethod.setAccessible(true);

    Date date = null;
    if (dateStr != null && !dateStr.isEmpty()) {
      date = DATE_FORMAT.parse(dateStr);
    }

    val result = (String) formatFileNameMethod.invoke(consultationManager, name, subject, date,
        defaultName);

    assertEquals(expected, result);
  }

  @Test
  public void givenNoAttachments_whenGetLatestDemographicEReferOceanApiAttachments_thenReturnNoResults()
      throws Exception {
    when(eReferAttachmentDao.getLatestCreatedByDemographic(
        DEMOGRAPHIC_NO, OceanWorkflowTypeEnum.EREFERRAL, null))
        .thenReturn(null);

    val resultList = consultationManager.getLatestDemographicEReferOceanApiAttachments(
        loggedInInfo, request, DEMOGRAPHIC_NO, OceanWorkflowTypeEnum.EREFERRAL);

    verify(consultationManager, never())
        .printAttachmentManagerDataAndAddToOceanApiAttachmentList(
            any(), any(), any(), any());
    verify(consultationManager, never())
        .generateLegacyEReferAttachmentDataFilesAndAddToOceanApiAttachmentList(
            any(), any(), any(), any(), any());

    assertTrue(resultList.isEmpty(), "The result should be empty");
  }

  @Test
  public void givenEreferAttachmentManagerData_whenGetLatestDemographicEReferOceanApiAttachments_thenReturnResults()
      throws Exception {
    doReturn(FILE_PATH).when(consultationManager).printAndGetFilePath(any(), any());
    doReturn(PDF_BYTES).when(consultationManager).getBytesFromFile(FILE_PATH);
    when(eReferAttachmentDao.getLatestCreatedByDemographic(
        DEMOGRAPHIC_NO, OceanWorkflowTypeEnum.EREFERRAL, null))
        .thenReturn(getTestEreferAttachmentWithAttachmentManagerData());

    val resultList = consultationManager.getLatestDemographicEReferOceanApiAttachments(
        loggedInInfo, request, DEMOGRAPHIC_NO, OceanWorkflowTypeEnum.EREFERRAL);

    verify(consultationManager, times(1))
        .printAttachmentManagerDataAndAddToOceanApiAttachmentList(
            any(), any(), any(), any());
    verify(consultationManager, never())
        .generateLegacyEReferAttachmentDataFilesAndAddToOceanApiAttachmentList(
            any(), any(), any(), any(), any());

    assertEquals(1, resultList.size());
    val oceanApiAttachment = resultList.get(0);
    assertEquals(PRINTABLE_JSON_ID, oceanApiAttachment.getId());
    assertEquals(OceanWorkflowTypeEnum.AM.name(), oceanApiAttachment.getAttachmentType());
    assertEquals(FILE_NAME, oceanApiAttachment.getFileName());
    assertEquals(PDF_BYTES, oceanApiAttachment.getData());
  }

  @ParameterizedTest
  @ValueSource(
      strings = {
        ConsultDocs.DOCTYPE_DOC,
        ConsultDocs.DOCTYPE_LAB,
        ConsultDocs.DOCTYPE_HRM,
        ConsultDocs.DOCTYPE_EFORM,
        ConsultDocs.DOCTYPE_FORM_PERINATAL,
        ConsultDocs.DOCTYPE_FORM_SMART_ENCOUNTER
      })
  public void givenEreferAttachments_whenGetLatestDemographicEReferOceanApiAttachments_thenReturnResults(
          final String consultDocsType
  ) throws Exception {
    mockLegacyAttachmentMethod(consultDocsType);
    when(eReferAttachmentDao.getLatestCreatedByDemographic(
        DEMOGRAPHIC_NO, OceanWorkflowTypeEnum.EREFERRAL, null))
        .thenReturn(getTestEreferAttachmentWithAttachments(consultDocsType));

    val resultList = consultationManager.getLatestDemographicEReferOceanApiAttachments(
        loggedInInfo, request, 1, OceanWorkflowTypeEnum.EREFERRAL);

    verify(consultationManager, never())
        .printAttachmentManagerDataAndAddToOceanApiAttachmentList(
            any(), any(), any(), any());
    verify(consultationManager, times(1))
        .generateLegacyEReferAttachmentDataFilesAndAddToOceanApiAttachmentList(
            any(), any(), any(), any(), any());

    assertEquals(1, resultList.size());
    val oceanApiAttachment = resultList.get(0);
    assertEquals(LEGACY_ID, oceanApiAttachment.getId());
    assertEquals(consultDocsType, oceanApiAttachment.getAttachmentType());
    assertEquals(LEGACY_FILE_NAME, oceanApiAttachment.getFileName());
    assertEquals(LEGACY_PDF_BYTES, oceanApiAttachment.getData());
  }

  @ParameterizedTest
  @CsvSource(value = {
      "null, 2025-05-09",
      "'', 2025-05-09",
      "Document, 2025-05-09",
      "Document, null"
  },
      nullValues = "null"
  )
  public void givenValidId_whenGetDocumentAttachment_thenReturnValidAttachment(
      final String name,
      final String date
  ) throws IOException, ParseException {
    // Mock dependencies
    ReflectionTestUtils.setField(consultationManager, "documentManager", documentManager);
    when(documentManager.getDocument(any(LoggedInInfo.class), anyInt()))
        .thenReturn(createTestDocument(name, date));
    // Mock Files.readAllBytes to return test data
    try (MockedStatic<Files> filesMock = mockStatic(Files.class)) {
      filesMock.when(new Verification() {
        @Override
        public void apply() throws Throwable {
          Files.readAllBytes(any());
        }
      }).thenReturn(PDF_BYTES);
      // Act
      val result = consultationManager.getDocumentAttachment(new LoggedInInfo(), PRINTABLE_JSON_ID);
      // Assert
      assertNotNull(result);
      assertEquals(PRINTABLE_JSON_ID, result.getId());
      assertEquals(ConsultDocs.DOCTYPE_DOC, result.getAttachmentType());
      assertEquals(PDF_BYTES, result.getData());
      assertFileName(name, date, FILE_NAME, result);
    }
  }

  @Test
  public void givenInvalidId_whenGetDocumentAttachment_thenReturnNull() {
    // Mock dependencies
    ReflectionTestUtils.setField(consultationManager, "documentManager", documentManager);
    when(documentManager.getDocument(any(LoggedInInfo.class), anyInt())).thenReturn(null);
    assertThrows(IllegalArgumentException.class, new Executable() {
      @Override
      public void execute() throws Throwable {
        consultationManager.getDocumentAttachment(new LoggedInInfo(), PRINTABLE_JSON_ID);
      }
    });
  }

  @ParameterizedTest
  @CsvSource(
      value = {
          "null, Test Subject, 2025-05-09",
          "Test eForm, null, 2025-05-09",
          "Test eForm, Test Subject, null",
          "Test eForm, Test Subject, 2025-05-09"
      },
      nullValues = "null"
  )
  public void givenValidId_whenGetEformAttachment_thenReturnValidAttachment(
      final String name,
      final String subject,
      final String date
  ) throws IOException, ParseException {
    // Mock dependencies
    wkhtmlToPdfUtilsMock = mockStatic(WKHtmlToPdfUtils.class);
    ReflectionTestUtils.setField(consultationManager, "eformDataDao", eFormDataDao);
    when(eFormDataDao.find(PRINTABLE_JSON_ID)).thenReturn(createTestEform(name, subject, date));
    wkhtmlToPdfUtilsMock.when(new Verification() {
      @Override
      public void apply() throws Throwable {
        WKHtmlToPdfUtils.convertToPdf(anyString());
      }
    }).thenReturn(PDF_BYTES);

    // Act
    val result = consultationManager.getEformAttachment(new LoggedInInfo(), request,
        PRINTABLE_JSON_ID);
    // Assert
    assertNotNull(result);
    assertEquals(PRINTABLE_JSON_ID, result.getId());
    assertEquals(ConsultDocs.DOCTYPE_EFORM, result.getAttachmentType());
    assertEquals(PDF_BYTES, result.getData());
    assertEformFileName(name, subject, date, result);
    wkhtmlToPdfUtilsMock.verify(new Verification() {
      @Override
      public void apply() throws IOException {
        WKHtmlToPdfUtils.convertToPdf(anyString());
      }
    }, times(1));
    wkhtmlToPdfUtilsMock.close();

}

  @Test
  public void givenInvalidId_whenGetEformAttachment_thenReturnNull() {
    // Mock dependencies
    ReflectionTestUtils.setField(consultationManager, "eformDataDao", eFormDataDao);
    when(eFormDataDao.find(PRINTABLE_JSON_ID)).thenReturn(null);
    assertThrows(IllegalArgumentException.class, new Executable() {
      @Override
      public void execute() throws Throwable {
        consultationManager.getEformAttachment(new LoggedInInfo(), request, PRINTABLE_JSON_ID);
      }
    });
  }

  @ParameterizedTest
  @CsvSource(value = {
      "null, 2025-05-09",
      "'', 2025-05-09",
      "Smart Encounter, 2025-05-09",
      "Smart Encounter, null"
  },
      nullValues = "null"
  )
  public void givenValidId_whenGetSmartEncounterAttachment_thenReturnValidAttachment(
      final String name, final String date
  ) throws DocumentException, IOException, ParseException {
    // Mock the dependencies
    ReflectionTestUtils.setField(consultationManager, "formSmartEncounterDao",
        formSmartEncounterDao);
    when(formSmartEncounterDao.find(PRINTABLE_JSON_ID))
        .thenReturn(createTestSmartEncounter(name, date));

    try (val actionMock = mockStatic(FormSmartEncounterAction.class)) {
      val result = consultationManager.getSmartEncounterAttachment(PRINTABLE_JSON_ID);
      actionMock.verify(new Verification() {
        @Override
        public void apply() throws Throwable {
          FormSmartEncounterAction.generatePdf(
              any(FormSmartEncounter.class), any(ByteArrayOutputStream.class));
        }
      }, times(1));
      assertNotNull(result);
      assertEquals(PRINTABLE_JSON_ID, result.getId());
      assertEquals(ConsultDocs.DOCTYPE_FORM_SMART_ENCOUNTER, result.getAttachmentType());
      assertEquals(0, result.getData().length);
      assertFileName(name, date, String.format("SmartEncounter_%03d.pdf", PRINTABLE_JSON_ID),
          result);
    }
  }

  @Test()
  public void givenInvalidId_whenGetSmartEncounterAttachment_thenReturnNull() {
    // Mock the dependencies
    ReflectionTestUtils.setField(consultationManager, "formSmartEncounterDao",
        formSmartEncounterDao);
    when(formSmartEncounterDao.find(PRINTABLE_JSON_ID)).thenReturn(null);

    assertThrows(IllegalArgumentException.class, new Executable() {
      @Override
      public void execute() throws Throwable {
        consultationManager.getSmartEncounterAttachment(PRINTABLE_JSON_ID);
      }
    });
  }

  private EReferAttachment getTestEreferAttachmentWithAttachmentManagerData() {
    val ereferAttachment = getTestEReferAttachment();
    // populate attachment manager data list
    val eReferAttachmentData = new EReferAttachmentManagerData();
    eReferAttachmentData.setEReferAttachment(ereferAttachment);
    eReferAttachmentData.setPrintable(PRINTABLE_JSON);
    ereferAttachment.setAttachmentManagerData(new ArrayList<>(
        Collections.singletonList(eReferAttachmentData)));
    return ereferAttachment;
  }

  private EReferAttachment getTestEreferAttachmentWithAttachments(final String consultDocsType) {
    val eReferAttachment = getTestEReferAttachment();
    // populate attachments list
    val attachment = new EReferAttachmentData(
        eReferAttachment, 1, consultDocsType);
    eReferAttachment.setAttachments(new ArrayList<>(Collections.singletonList(attachment)));
    return eReferAttachment;
  }

  private EReferAttachment getTestEReferAttachment() {
    val eReferAttachment = new EReferAttachment();
    eReferAttachment.setId(1);
    eReferAttachment.setDemographicNo(1);
    eReferAttachment.setCreated(new Date());
    eReferAttachment.setArchived(false);
    eReferAttachment.setType(OceanWorkflowTypeEnum.EREFERRAL.name());

    return eReferAttachment;
  }

  private void mockLegacyAttachmentMethod(final String consultDocsType)
      throws IOException, DocumentException {
    switch (consultDocsType) {
      case ConsultDocs.DOCTYPE_DOC:
        doReturn(new OceanApiAttachment(
            LEGACY_ID, ConsultDocs.DOCTYPE_DOC, LEGACY_FILE_NAME, LEGACY_PDF_BYTES))
            .when(consultationManager).getDocumentAttachment(any(), any());
        break;
      case ConsultDocs.DOCTYPE_LAB:
        doReturn(new OceanApiAttachment(
            LEGACY_ID, ConsultDocs.DOCTYPE_LAB, LEGACY_FILE_NAME, LEGACY_PDF_BYTES))
            .when(consultationManager).getLabAttachment(any(), any());
        break;
      case ConsultDocs.DOCTYPE_HRM:
        doReturn(new OceanApiAttachment(
            LEGACY_ID, ConsultDocs.DOCTYPE_HRM, LEGACY_FILE_NAME, LEGACY_PDF_BYTES))
            .when(consultationManager).getHrmAttachment(any(), any());
        break;
      case ConsultDocs.DOCTYPE_EFORM:
        doReturn(new OceanApiAttachment(
            LEGACY_ID, ConsultDocs.DOCTYPE_EFORM, LEGACY_FILE_NAME, LEGACY_PDF_BYTES))
            .when(consultationManager).getEformAttachment(any(), any(), any());
        break;
      case ConsultDocs.DOCTYPE_FORM_PERINATAL:
        doReturn(
            new OceanApiAttachment(
                LEGACY_ID,
                ConsultDocs.DOCTYPE_FORM_PERINATAL,
                LEGACY_FILE_NAME,
                LEGACY_PDF_BYTES))
            .when(consultationManager)
            .getPerinatalAttachment(any(), any(), any());
        break;
      case ConsultDocs.DOCTYPE_FORM_SMART_ENCOUNTER:
        doReturn(new OceanApiAttachment(
            LEGACY_ID,
            ConsultDocs.DOCTYPE_FORM_SMART_ENCOUNTER,
            LEGACY_FILE_NAME,
            LEGACY_PDF_BYTES))
            .when(consultationManager)
            .getSmartEncounterAttachment(any());
        break;
      default:
        throw new IllegalArgumentException("Invalid consultDocsType: " + consultDocsType);
    }
  }

  @Test
  public void givenLegacyAttachments_whenGetRequestAttachments_thenReturnLegacyResults() {

    initConsultationLegacyAttachmentsMocks();

    val result = consultationManager.getRequestAttachments(loggedInInfo, 1, true, "1");

    assertEquals(8, result.size());
  }

  @Test
  public void givenAttachmentManagerAttachments_whenGetRequestAttachments_thenReturnResults() {

    when(securityInfoManager.hasPrivilege(any(LoggedInInfo.class), eq("_con"), eq("r"), eq(null)))
        .thenReturn(true);

    val consultationRequestMock = new ConsultationRequest();
    consultationRequestMock.setId(1);
    consultationRequestMock.setLegacyAttachment(false);
    consultationRequestMock.setAttachments(
        "[{\"id\":\"Perinatal-8\",\"name\":\"Perinatal 2017\",\"type\":\"Antenatal\",\"params\":{},\"previewUrl\":\"/oscar/printable/antenatal-form?preview=true&id=Perinatal-8&providerNumber=1\",\"date\":null,\"supported\":true},{\"id\":\"83\",\"name\":\"Test PDF\",\"type\":\"Document\",\"params\":{},\"previewUrl\":\"/kaiemr/api/document/getPagePreview/83?page=1\",\"date\":1716955200000,\"supported\":true},{\"id\":\"80\",\"name\":\"annie small\",\"type\":\"Document\",\"params\":{},\"previewUrl\":\"/kaiemr/api/document/getPagePreview/80?page=1\",\"date\":1716523200000,\"supported\":true},{\"id\":\"19\",\"name\":\"Rich Text Letter Best Version\",\"type\":\"Eforms\",\"params\":{},\"previewUrl\":\"/oscar/printable/eform?preview=true&id=19&providerNumber=1\",\"date\":1717012358000,\"supported\":true},{\"id\":\"18\",\"name\":\"Rich Text Letter Best Version\",\"type\":\"Eforms\",\"params\":{},\"previewUrl\":\"/oscar/printable/eform?preview=true&id=18&providerNumber=1\",\"date\":1717012340000,\"supported\":true}]");

    when(consultationRequestDao.find(1)).thenReturn(consultationRequestMock);

    val result = consultationManager.getRequestAttachments(loggedInInfo, 1, true, "1");

    assertEquals(5, result.size());
  }

  @Test
  public void
      givenAttachmentManagerNoAttachments_whenGetRequestAttachments_thenReturnEmptyResults() {
    when(securityInfoManager.hasPrivilege(any(LoggedInInfo.class), eq("_con"), eq("r"), eq(null)))
        .thenReturn(true);

    val consultationRequestMock = new ConsultationRequest();
    consultationRequestMock.setId(1);
    consultationRequestMock.setLegacyAttachment(false);
    consultationRequestMock.setAttachments("");
    when(consultationRequestDao.find(1)).thenReturn(consultationRequestMock);

    val result = consultationManager.getRequestAttachments(loggedInInfo, 1, true, "1");

    assertEquals(0, result.size());
  }

  private void initConsultationLegacyAttachmentsMocks() {
    when(securityInfoManager.hasPrivilege(any(LoggedInInfo.class), eq("_con"), eq("r"), eq(null)))
        .thenReturn(true);
    val consultationRequestMock = new ConsultationRequest();
    consultationRequestMock.setId(1);
    consultationRequestMock.setLegacyAttachment(true);
    when(consultationRequestDao.find(1)).thenReturn(consultationRequestMock);

    val mockEDocs = new ArrayList<EDoc>();
    mockEDocs.add(new EDoc());
    mockEDocs.add(new EDoc());
    doReturn(mockEDocs)
        .when(consultationManager)
        .listLegacyEDocAttachments(eq(loggedInInfo), eq("1"), eq("1"), eq(false), eq(true));

    val mockConsultDocs = new ArrayList<ConsultDocs>();
    var consultDoc = new ConsultDocs();
    consultDoc.setDocType(ConsultDocs.DOCTYPE_EFORM);
    consultDoc.setDocumentNo(1);
    mockConsultDocs.add(consultDoc);
    consultDoc = new ConsultDocs();
    consultDoc.setDocType(ConsultDocs.DOCTYPE_EFORM);
    consultDoc.setDocumentNo(2);
    mockConsultDocs.add(consultDoc);
    when(requestDocDao.findByRequestId(1)).thenReturn(mockConsultDocs);

    val mockEForms = new ArrayList<EFormData>();
    var eform = new EFormData();
    eform.setId(1);
    mockEForms.add(eform);
    eform = new EFormData();
    eform.setId(2);
    mockEForms.add(eform);
    doReturn(mockEForms).when(consultationManager).listLegacyEFormAttachments(eq("1"));

    val mockLabResults = new ArrayList<LabResultData>();
    mockLabResults.add(new LabResultData("HL7"));
    mockLabResults.add(new LabResultData("HL7"));
    doReturn(mockLabResults)
        .when(consultationManager)
        .listLegacyLabResultsAttachments(eq(loggedInInfo), eq("1"), eq("1"), eq(true));

    val perinatalResults = new ArrayList<FormONPerinatal2017>();
    perinatalResults.add(new FormONPerinatal2017());
    perinatalResults.add(new FormONPerinatal2017());
    doReturn(perinatalResults).when(onPerinatal2017Dao).findAllDistinctForms(eq(1));
  }

  private FormSmartEncounter createTestSmartEncounter(final String name, final String date)
      throws ParseException {
    val formSmartEncounter = new FormSmartEncounter();
    formSmartEncounter.setId(1);
    formSmartEncounter.setDocumentName(name);
    if (StringUtils.isNotEmpty(date)) {
      formSmartEncounter.setFormEdited(new Timestamp(DATE_FORMAT.parse(date).getTime()));
    }
    return formSmartEncounter;
  }

  private Document createTestDocument(final String name, final String date) throws ParseException {
    val document = new Document();
    document.setDocdesc(name);
    if (StringUtils.isNotEmpty(date)) {
      document.setObservationdate(DATE_FORMAT.parse(date));
    }
    document.setDocfilename(FILE_NAME);
    return document;
  }

  private EFormData createTestEform(final String name, final String subject, final String date)
      throws ParseException {
    val eformData = new EFormData();
    eformData.setId(PRINTABLE_JSON_ID);
    eformData.setFormName(name);
    eformData.setSubject(subject);
    eformData.setFormDate(null);
    if (StringUtils.isNotEmpty(date)) {
      eformData.setFormDate(DATE_FORMAT.parse(date));
    }
    return eformData;
  }

  private void assertFileName(
      final String name,
      final String date,
      final String defaultName,
      final OceanApiAttachment result
  ) {
    var extension = ".pdf";
    val lastDotIndex = defaultName.lastIndexOf('.');
    if (lastDotIndex > 0) {
        extension = defaultName.substring(lastDotIndex);
    }
    if (StringUtils.isEmpty(name)) {
      assertEquals(defaultName, result.getFileName());
    } else if (StringUtils.isEmpty(date)) {
      assertEquals(String.format(ATTACHMENT_FILE_FORMAT_SINGLE, name, extension), result.getFileName());
    } else {
      assertEquals(String.format(ATTACHMENT_FILE_FORMAT_DOUBLE, name, date, extension), result.getFileName());
    }
  }

  private void assertEformFileName(
      final String name,
      final String subject,
      final String date,
      final OceanApiAttachment result
  ) {
    var extension = ".pdf";
    val fileName = result.getFileName();
    val lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex > 0) {
        extension = fileName.substring(lastDotIndex);
    }
    if (StringUtils.isBlank(name)) {
      assertEquals(String.format("Eform_%03d.pdf", PRINTABLE_JSON_ID), result.getFileName());
    } else if (StringUtils.isBlank(subject)) {
      assertEquals(String.format(ATTACHMENT_FILE_FORMAT_DOUBLE, name, date, extension), result.getFileName());
    } else if (StringUtils.isBlank(date)) {
      assertEquals(String.format(ATTACHMENT_FILE_FORMAT_DOUBLE, name, subject, extension),
          result.getFileName());
    } else {
      assertEquals(String.format(EFORM_ATTACHMENT_FILE_FORMAT, name, subject, date, extension),
          result.getFileName());
    }
  }
}
