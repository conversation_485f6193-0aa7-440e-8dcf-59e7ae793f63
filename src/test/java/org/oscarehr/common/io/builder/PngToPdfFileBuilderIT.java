/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */
package org.oscarehr.common.io.builder;

import integration.tests.config.TestConfig;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.oscarehr.JunoApplication;
import org.oscarehr.common.io.PDFFile;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(
		classes = {JunoApplication.class, TestConfig.class},
		webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PngToPdfFileBuilderIT extends PdfFileBuilderBaseIT
{

	@Test
	public void testPngToPdf() throws Exception
	{
		File file = new File("src/test/resources/test_pdf/test_box.png");
		byte[] bytes = Files.readAllBytes(file.toPath());
		InputStream is = new ByteArrayInputStream(bytes);

		PngToPdfFileBuilder pngToPdfFileBuilder = new PngToPdfFileBuilder(is);
		PDFFile pdfFile = pngToPdfFileBuilder.buildPDF();

		/*
			We are not testing the MD5 hash
			because the file factory is causing problems
	 	*/

		Assert.assertEquals("application/pdf", pdfFile.getContentType());
		Assert.assertEquals(32540, pdfFile.getFileSize());

		pdfFile.deleteFile();
	}
}