package org.oscarehr.common.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.common.model.EFormData;

@ExtendWith(MockitoExtension.class)
class EFormDataDaoUnitTest {

  // setup Timestamp for 2024-01-02 03:04:05
  public static final Date TIMESTAMP = new Date(1704182645000L);
  public static final int ID = 10;
  public static final int FORM_ID = 11;
  public static final int DEMOGRAPHIC_ID = 12;
  public static final int REMOTE_SYSTEM_ID = 13;
  @InjectMocks private EFormDataDao subject;

  @Test
  void givenLocal_whenMapEformData_thenHasFid() {
    // given
    val eFormData = getEformData(TIMESTAMP, null, null);
    val timeString = TIMESTAMP.toString();
    // when
    val result = subject.mapEFormData(eFormData);

    // then
    assertEquals(ID, result.get("id"));
    assertEquals(FORM_ID, result.get("formId"));
    assertEquals("formName", result.get("formName"));
    assertEquals("subject", result.get("subject"));
    assertEquals(DEMOGRAPHIC_ID, result.get("demographicId"));
    assertEquals(true, result.get("current"));
    assertEquals("providerNo", result.get("providerNo"));
    assertEquals(true, result.get("patientIndependent"));
    assertEquals("roleType", result.get("roleType"));
    assertNull(result.get("guid"));
    assertNull(result.get("remoteSystemId"));
    assertEquals(timeString, result.get("formDate"));
    assertEquals(timeString, result.get("formTime"));
  }

  @Test
  void givenRemote_whenMapEformData_thenHasNoFid() {
    // given
    val dateExpected = new SimpleDateFormat("yyyy-MM-dd").format(TIMESTAMP);
    val timeExpected = new SimpleDateFormat("HH:mm:ss").format(TIMESTAMP);
    val eFormData = getEformData(TIMESTAMP, "guid", REMOTE_SYSTEM_ID);

    // when
    val result = subject.mapEFormData(eFormData);

    // then
    assertEquals(ID, result.get("id"));
    assertEquals(FORM_ID, result.get("formId"));
    assertEquals("formName", result.get("formName"));
    assertEquals("subject", result.get("subject"));
    assertEquals(DEMOGRAPHIC_ID, result.get("demographicId"));
    assertEquals(true, result.get("current"));
    assertEquals("providerNo", result.get("providerNo"));
    assertEquals(true, result.get("patientIndependent"));
    assertEquals("roleType", result.get("roleType"));
    assertEquals("guid", result.get("guid"));
    assertEquals(REMOTE_SYSTEM_ID, result.get("remoteSystemId"));
    assertEquals(dateExpected, result.get("formDate"));
    assertEquals(timeExpected, result.get("formTime"));
  }

  private static EFormData getEformData(Date dateTime, String guid, Integer remoteSystemId) {
    val eFormData = new EFormData();
    eFormData.setId(ID);
    eFormData.setFormId(FORM_ID);
    eFormData.setFormName("formName");
    eFormData.setSubject("subject");
    eFormData.setDemographicId(DEMOGRAPHIC_ID);
    eFormData.setCurrent(true);
    eFormData.setProviderNo("providerNo");
    eFormData.setPatientIndependent(true);
    eFormData.setRoleType("roleType");
    eFormData.setGuid(guid);
    eFormData.setRemoteSystemId(remoteSystemId);
    eFormData.setFormDate(dateTime);
    eFormData.setFormTime(dateTime);
    eFormData.setFormData("formData");
    return eFormData;
  }
}