/**
 * Copyright (c) 2025 WELL EMR Group Inc. This software is made available under the terms of the GNU
 * General Public License, Version 2, 1991 (GPLv2). License details are available via
 * "gnu.org/licenses/gpl-2.0.html".
 */

package org.oscarehr.common.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.oscarehr.PMmodule.dao.ProgramDao;
import org.oscarehr.PMmodule.model.Program;
import org.oscarehr.casemgmt.dao.CaseManagementNoteDAO;
import org.oscarehr.casemgmt.model.CaseManagementNote;
import org.oscarehr.common.dao.utils.SchemaUtils;
import org.oscarehr.common.model.SecRole;
import org.oscarehr.common.service.CaseManagementNoteService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.oscarehr.common.model.CaseManagementNoteJpa;
import org.oscarehr.common.model.Facility;
import org.oscarehr.util.SpringUtils;


public class CaseManagementNoteJpaDaoTest extends DaoTestFixtures {

  private static final String UUID = "12345678-1234-1234-1234-1234567890ab";

  private final CaseManagementNoteJpaDao dao = SpringUtils.getBean(CaseManagementNoteJpaDao.class);
  private final ProgramDao programDao = SpringUtils.getBean(ProgramDao.class);
  private final FacilityDao facilityDao = SpringUtils.getBean(FacilityDao.class);
  private final CaseManagementNoteDAO caseManagementNoteDAO = SpringUtils.getBean(
      CaseManagementNoteDAO.class);
  private final CaseManagementNoteService caseManagementNoteService = SpringUtils.getBean(
      CaseManagementNoteService.class);
  private final SecRoleDao secRoleDao = SpringUtils.getBean(SecRoleDao.class);

  private static final String ROLE_NAME = "Test Role Name";
  private static final String PROGRAM_NAME = "Test Program";
  private static final Date AUTO_SYNC_DATE = new Date();
  private static final Date LAST_SYNCED_DATE = new Date();

  @BeforeEach
  public void before() throws Exception {
    SchemaUtils.restoreTable(
        "casemgmt_note",
        "casemgmt_issue",
        "casemgmt_issue_notes",
        "program",
        "secRole",
        "provider",
        "demographic",
        "PeriodDef",
        "Facility"
    );
  }


  @Test
  public void givenUuids_whenGetCreateDatesAsMap_thenCorrectDates() {
    // Create notes with different UUIDs
    CaseManagementNoteJpa note1 = createCaseManagementNote();
    CaseManagementNoteJpa note2 = createCaseManagementNote("*************-9876-9876-************");

    // Create a revision for note1 to ensure the create date is the earliest date
    Date originalDate = note1.getUpdateDate();
    CaseManagementNoteJpa revision = createRevisionNote(note1);
    revision.setUpdateDate(new Date(originalDate.getTime() + 86400000)); // Add one day
    dao.merge(revision);

    // Create a set of UUIDs to query
    Set<String> uuids = new HashSet<>();
    uuids.add(note1.getUuid());
    uuids.add(note2.getUuid());

    // Run the test method
    Map<String, Date> createDatesMap = dao.getCreateDatesAsMap(uuids);

    // Assert that the map contains the correct dates
    assertNotNull(createDatesMap, "Create dates map should not be null");
    assertEquals(2, createDatesMap.size(), "Map should contain 2 entries");
    assertTrue(createDatesMap.containsKey(note1.getUuid()), "Map should contain note1's UUID");
    assertTrue(createDatesMap.containsKey(note2.getUuid()), "Map should contain note2's UUID");

    // Compare the date longs, and assure they are less or equal to one second apart
    assertTrue(
        Math.abs(createDatesMap.get(note1.getUuid()).getTime() - originalDate.getTime()) <= 1000,
        "Create date for note1 should not be more than 1 second apart from original date");
  }

  @Test
  public void givenMultipleNotes_whenGetRevisionCountsAsMap_thenCorrectCounts() {
    // Create two notes with different UUIDs
    CaseManagementNoteJpa note1 = createCaseManagementNote();
    CaseManagementNoteJpa note2 = createCaseManagementNote("*************-9876-9876-************");

    // Create revisions for note1
    CaseManagementNoteJpa revision1 = createRevisionNote(note1);
    createRevisionNote(revision1);

    // Create one revision for note2
    createRevisionNote(note2);

    // Create a set of UUIDs to query
    Set<String> uuids = new HashSet<>();
    uuids.add(note1.getUuid());
    uuids.add(note2.getUuid());

    // Run the test method
    Map<String, Integer> revisionCountsMap = dao.getRevisionCountsAsMap(uuids);

    // Assert that the map contains the correct counts
    assertNotNull(revisionCountsMap, "Revision counts map should not be null");
    assertEquals(2, revisionCountsMap.size(), "Map should contain 2 entries");
    assertEquals(3, revisionCountsMap.get(note1.getUuid()),
        "Note1 should have 3 revisions (original + 2 revisions)");
    assertEquals(2, revisionCountsMap.get(note2.getUuid()),
        "Note2 should have 2 revisions (original + 1 revision)");
  }

  @Test
  public void givenNoteId_whenFind_thenCorrectNoteReturned() {
    // Create a note
    CaseManagementNoteJpa originalNote = createCaseManagementNote();

    // Find the note by ID
    CaseManagementNoteJpa foundNote = dao.find(originalNote.getId());

    // Assert that the found note is not null and has the correct properties
    assertNotNull(foundNote, "Found note should not be null");
    assertEquals(originalNote.getId(), foundNote.getId(), "Note ID should match");
    assertEquals(originalNote.getUuid(), foundNote.getUuid(), "Note UUID should match");
    assertEquals(originalNote.getDemographicNumber(), foundNote.getDemographicNumber(),
        "Demographic number should match");
    assertEquals(originalNote.getNote(), foundNote.getNote(), "Note content should match");
    assertEquals(originalNote.getHistory(), foundNote.getHistory(), "Note history should match");
    assertEquals(originalNote.getProgramNumber(), foundNote.getProgramNumber(),
        "Program number should match");
    assertEquals(originalNote.getProviderNumber(), foundNote.getProviderNumber(),
        "Provider number should match");
    assertEquals(originalNote.getSigningProviderNo(), foundNote.getSigningProviderNo(),
        "Signing provider number should match");
    assertEquals(originalNote.isSigned(), foundNote.isSigned(), "Signed status should match");
  }

  @Test
  public void givenMultipleNotes_whenFindAll_thenAllNotesReturned() {
    // Create multiple notes
    CaseManagementNoteJpa note1 = createCaseManagementNote();
    CaseManagementNoteJpa note2 = createCaseManagementNote("*************-9876-9876-************");
    CaseManagementNoteJpa note3 = createCaseManagementNote("abcdef12-abcd-abcd-abcd-abcdef123456");

    // Find all notes
    List<CaseManagementNoteJpa> allNotes = dao.findAll(0, 100);

    // Assert that the list is not null and contains at least the notes we created
    assertNotNull(allNotes, "List of all notes should not be null");
    assertFalse(allNotes.isEmpty(), "List of all notes should not be empty");

    // Create a set of IDs from the found notes
    Set<Long> foundIds = new HashSet<>();
    for (CaseManagementNoteJpa note : allNotes) {
      foundIds.add(note.getId());
    }

    // Assert that our created notes are in the result
    assertTrue(foundIds.contains(note1.getId()), "List should contain note1");
    assertTrue(foundIds.contains(note2.getId()), "List should contain note2");
    assertTrue(foundIds.contains(note3.getId()), "List should contain note3");
  }

  private CaseManagementNoteJpa createCaseManagementNote() {
    return createCaseManagementNote("12345678-1234-1234-1234-1234567890ab");
  }

  private CaseManagementNoteJpa createCaseManagementNote(final String uuid) {
    CaseManagementNoteJpa note = new CaseManagementNoteJpa();
    note.setUuid(uuid == null ? UUID : uuid);
    note.setDemographicNumber("1");
    note.setNote("Test note");
    note.setHistory("Test history");
    note.setUpdateDate(new Date());
    note.setObservationDate(new Date());
    note.setProgramNumber("1");
    note.setProviderNumber("999998");
    note.setSigningProviderNo("999998");
    note.setReporterCaisiRole("Test Role");
    note.setReporterProgramTeam("Test Team");
    note.setBillingCode("Test Billing Code");
    note.setEncounterType("Test Encounter Type");
    note.setSigned(true);
    note.setIncludeIssue(true);
    dao.persist(note);
    return note;
  }

  private CaseManagementNoteJpa createRevisionNote(final CaseManagementNoteJpa originalNote) {
    CaseManagementNoteJpa revision = new CaseManagementNoteJpa();
    revision.setUuid(originalNote.getUuid());
    revision.setDemographicNumber(originalNote.getDemographicNumber());
    revision.setNote("Updated " + originalNote.getNote());
    revision.setHistory("Updated " + originalNote.getHistory());
    revision.setUpdateDate(originalNote.getUpdateDate());
    revision.setObservationDate(originalNote.getObservationDate());
    revision.setProgramNumber(originalNote.getProgramNumber());
    revision.setProviderNumber(originalNote.getProviderNumber());
    revision.setSigningProviderNo(originalNote.getSigningProviderNo());
    revision.setReporterCaisiRole(originalNote.getReporterCaisiRole());
    revision.setReporterProgramTeam(originalNote.getReporterProgramTeam());
    revision.setBillingCode(originalNote.getBillingCode());
    revision.setEncounterType(originalNote.getEncounterType());
    revision.setSigned(originalNote.isSigned());
    revision.setIncludeIssue(originalNote.isIncludeIssue());
    dao.persist(revision);
    return revision;
  }

  @Test
  public void givenSameNote_whenFetchingNoteWithNewAndOldDao_thenEnsureConvertedJpaNoteIsConsistentWithOldNote() {
    // Create required facility, program, and sec role to test the maps
    Facility facility = createFacility();
    Program program = createProgram(PROGRAM_NAME, facility);
    SecRole role = createSecRole();

    // Create a test note with revision
    CaseManagementNoteJpa jpaNote = createCompleteCaseManagementNoteWithRevision(program, role);

    // Retrieve the note using CaseManagementNoteJpaDao
    CaseManagementNoteJpa retrievedJpaNote = dao.find(jpaNote.getId());
    assertNotNull(retrievedJpaNote, "Retrieved JPA note should not be null");

    // Retrieve the note using CaseManagementNoteDAO
    CaseManagementNote retrievedNote = caseManagementNoteDAO.getNote(jpaNote.getId());
    assertNotNull(retrievedNote, "Retrieved note should not be null");

    // Convert the JPA note to a CaseManagementNote
    List<CaseManagementNoteJpa> jpaNotes = new ArrayList<>();
    jpaNotes.add(retrievedJpaNote);
    List<CaseManagementNote> convertedNotes = caseManagementNoteService
        .convertJpaNotesToCaseManagementNotes(jpaNotes);

    assertNotNull(convertedNotes, "Converted notes list should not be null");
    assertEquals(1, convertedNotes.size(), "Converted notes list should have 1 element");

    CaseManagementNote convertedNote = convertedNotes.get(0);

    // Verify that the Maps are populated correctly

    // 1. Verify revisionCountMap - should be 2 because we created a revision
    assertEquals("2", convertedNote.getRevision(), "Revision count should be 2");

    // 2. Verify roleNameMap - should match the role name we created
    assertEquals(ROLE_NAME, convertedNote.getRoleName(), "Role name should match");

    // 3. Verify programTeamNameMap - should match the program name we created
    assertEquals(PROGRAM_NAME, convertedNote.getProgramName(), "Program name should match");

    // 4. Verify createDateMap - should not be null
    assertNotNull(convertedNote.getCreate_date(), "Create date should not be null");

    // Compare the converted note with the one retrieved directly
    compareNotes(retrievedNote, convertedNote);
  }

  private CaseManagementNoteJpa createCompleteCaseManagementNoteWithRevision(final Program program,
      final SecRole role) {
    CaseManagementNoteJpa note = new CaseManagementNoteJpa();

    // Set all fields to ensure complete population
    note.setUuid(UUID);
    note.setDemographicNumber("1");
    note.setNote("Test note");
    note.setHistory("Test history");

    Date now = new Date();
    note.setUpdateDate(now);
    note.setObservationDate(now);
    note.setAutoSyncDate(AUTO_SYNC_DATE);
    note.setLastSyncedDate(LAST_SYNCED_DATE);

    // Use the IDs from our test entities
    note.setProgramNumber(program.getId().toString());
    note.setReporterCaisiRole(role.getId().toString());

    note.setProviderNumber("999998");
    note.setSigningProviderNo("999998");
    note.setReporterProgramTeam("Test Team");
    note.setBillingCode("Test Billing Code");
    note.setEncounterType("Test Encounter Type");

    // Set boolean fields
    note.setSigned(true);
    note.setIncludeIssue(true);
    note.setArchived(false);
    note.setAvailable(true);
    note.setLocked(false);

    // Set numeric fields
    note.setAppointmentNumber(123);
    note.setPosition(1);
    note.setHourOfEncounterTime(10);
    note.setMinuteOfEncounterTime(30);
    note.setHourOfEncTransportationTime(1);
    note.setMinuteOfEncTransportationTime(15);

    // Set other fields
    note.setPassword("password123");
    note.setRemoteSystemId(456);

    // Persist the note
    dao.persist(note);

    // Create a revision to test the revisionCountMap
    createRevisionNote(note);

    return note;
  }

  private void compareNotes(CaseManagementNote expected, CaseManagementNote actual) {
    assertEquals(expected.getId(), actual.getId(), "IDs should match");
    assertEquals(expected.getAppointmentNo(), actual.getAppointmentNo(),
        "Appointment numbers should match");
    assertEquals(expected.isArchived(), actual.isArchived(), "Archived flags should match");

    // "available" is not mapped in the old Hibernate mappings, so it defaults to false.
    // The new JPA entity has it as a boolean, so it will always read it correctly.
    assertFalse(expected.isAvailable(), "Available flag is not mapped in the old DAO, "
        + "which always makes it false.");
    assertTrue(actual.isAvailable(), "Available flag should be true in the JPA entity");

    assertEquals(expected.getBilling_code(), actual.getBilling_code(),
        "Billing codes should match");
    assertEquals(expected.getDemographic_no(), actual.getDemographic_no(),
        "Demographic numbers should match");
    assertEquals(expected.getEncounter_type(), actual.getEncounter_type(),
        "Encounter types should match");
    assertEquals(expected.getHistory(), actual.getHistory(), "Histories should match");
    assertEquals(expected.getHourOfEncounterTime(), actual.getHourOfEncounterTime(),
        "Hour of encounter times should match");
    assertEquals(expected.getHourOfEncTransportationTime(), actual.getHourOfEncTransportationTime(),
        "Hour of encounter transportation times should match");
    assertEquals(expected.isIncludeissue(), actual.isIncludeissue(),
        "Include issue flags should match");

    // Auto sync date and last sync date are no in the Hibernate mappings, so they default to null.
    // The new JPA entity has them as Date, so they will always read them correctly.
    assertNull(expected.getAutoSyncDate(), "Auto sync dates are null in the old DAO as "
        + "they have no mapping.");
    assertNull(expected.getLastSyncedDate(), "Last synced dates are null in the old DAO as "
        + "they have no mapping.");
    assertTrue(Math.abs(AUTO_SYNC_DATE.getTime() - actual.getAutoSyncDate().getTime()) <= 1000,
        "Auto sync date should be correct in JPA note and is <= 1 second off");
    assertTrue(Math.abs(LAST_SYNCED_DATE.getTime() - actual.getLastSyncedDate().getTime()) <= 1000,
        "Last synced dates should be correct in JPA note and is <= 1 second off");

    assertEquals(expected.isLocked(), actual.isLocked(), "Locked flags should match");
    assertEquals(expected.getMinuteOfEncounterTime(), actual.getMinuteOfEncounterTime(),
        "Minute of encounter times should match");
    assertEquals(expected.getMinuteOfEncTransportationTime(),
        actual.getMinuteOfEncTransportationTime(),
        "Minute of encounter transportation times should match");
    assertEquals(expected.getNote(), actual.getNote(), "Notes should match");
    assertEquals(expected.getObservation_date(), actual.getObservation_date(),
        "Observation dates should match");
    assertEquals(expected.getPassword(), actual.getPassword(), "Passwords should match");
    assertEquals(expected.getPosition(), actual.getPosition(), "Positions should match");
    assertEquals(expected.getProgram_no(), actual.getProgram_no(), "Program numbers should match");
    assertEquals(expected.getProviderNo(), actual.getProviderNo(), "Provider numbers should match");
    assertEquals(expected.isSigned(), actual.isSigned(), "Signed flags should match");
    assertEquals(expected.getSigning_provider_no(), actual.getSigning_provider_no(),
        "Signing provider numbers should match");
    assertEquals(expected.getUpdate_date(), actual.getUpdate_date(), "Update dates should match");
    assertEquals(expected.getUuid(), actual.getUuid(), "UUIDs should match");
    assertEquals(expected.getRemoteSystemId(), actual.getRemoteSystemId(),
        "Remote system IDs should match");
    assertEquals(expected.getReporter_caisi_role(), actual.getReporter_caisi_role(),
        "Reporter CAISI roles should match");
    assertEquals(expected.getReporter_program_team(), actual.getReporter_program_team(),
        "Reporter program teams should match");

    // Formula fields from original CaseManagementNote Hibernate mappings
    assertEquals(expected.getRevision(), actual.getRevision(), "Revisions should match");
    assertEquals(expected.getRoleName(), actual.getRoleName(), "Role names should match");
    assertEquals(expected.getProgramName(), actual.getProgramName(), "Program names should match");
    assertEquals(expected.getCreate_date(), actual.getCreate_date(), "Create dates should match");
  }

  private Facility createFacility() {
    Facility facility = new Facility();
    facility.setName("Test Facility");
    facility.setHic(true);
    facility.setDisabled(true);
    facility.setOrgId(1);
    facility.setSectorId(1);
    facility.setIntegratorEnabled(false);
    facility.setEnableIntegratedReferrals(false);
    facility.setEnableHealthNumberRegistry(false);
    facility.setAllowSims(false);
    facility.setEnableDigitalSignatures(true);
    facility.setOcanServiceOrgNumber("123456");
    facility.setEnableOcanForms(true);
    facility.setEnableCbiForm(false);
    facility.setEnableAnonymous(false);
    facility.setEnablePhoneEncounter(true);
    facility.setEnableGroupNotes(false);
    facility.setEnableEncounterTime(false);
    facility.setEnableEncounterTransportationTime(false);
    facility.setRxInteractionWarningLevel(0);
    facility.setDisplayAllVacancies(0);
    facilityDao.persist(facility);

    return facility;
  }

  private Program createProgram(final String name, final Facility facility) {
    Program program = new Program();
    program.setName(name);
    program.setMaxAllowed(10);
    program.setProgramStatus(Program.PROGRAM_STATUS_ACTIVE);
    program.setFacilityId(facility.getId());
    program.setTransgender(true);
    program.setFirstNation(true);
    program.setBedProgramAffiliated(true);
    program.setAlcohol(true);
    program.setPhysicalHealth(true);
    program.setMentalHealth(true);
    program.setHousing(true);
    program.setExclusiveView("exclusiveView");
    program.setAgeMin(1);
    program.setAgeMax(10);
    program.setLastUpdateDate(new Date());
    program.setEnableOCAN(true);
    programDao.saveProgram(program);

    return program;
  }

  private SecRole createSecRole() {
    SecRole role = new SecRole();
    role.setName(ROLE_NAME);
    role.setDescription("Test role description");
    secRoleDao.persist(role);

    return role;
  }
}
