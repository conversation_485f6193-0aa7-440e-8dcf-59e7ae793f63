/**
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved. 
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for the
 * Department of Family Medicine
 * McMaster University
 * Hamilton
 * Ontario, Canada
 */
package org.oscarehr.common.dao;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import health.apps.gateway.converters.DocumentReferenceConverter;
import health.apps.gateway.service.GWConfigurationService;
import health.apps.gateway.service.GatewayDao;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import lombok.val;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.hl7.fhir.instance.model.api.IBaseResource;
import org.hl7.fhir.r4.model.Bundle;
import org.hl7.fhir.r4.model.DocumentReference;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.oscarehr.common.dao.utils.EntityDataGenerator;
import org.oscarehr.common.dao.utils.SchemaUtils;
import org.oscarehr.common.enums.FeatureFlagEnum;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.common.model.EFormData;
import org.oscarehr.managers.DemographicManager;
import org.oscarehr.managers.TicklerManager;
import org.oscarehr.util.SpringUtils;
import org.springframework.test.util.ReflectionTestUtils;

public class EFormDataDaoTest extends DaoTestFixtures {

	protected EFormDataDao eFormDataDao = (EFormDataDao) SpringUtils.getBean("EFormDataDao");
  private GWConfigurationService gwConfigurationService;
  private GatewayDao gatewayDao;
  private TicklerManager ticklerManager;
  private Demographic demographic;
  private DocumentReferenceConverter converter;
  private DemographicManager demographicManager;

	public EFormDataDaoTest() {
	}

	@Before
	public void before() throws Exception {
    gwConfigurationService = mock(GWConfigurationService.class);
    gatewayDao = mock(GatewayDao.class);
    demographic = mock(Demographic.class);
    ticklerManager = mock(TicklerManager.class);
    converter = mock(DocumentReferenceConverter.class);
    demographicManager = mock(DemographicManager.class);
    ReflectionTestUtils.setField(eFormDataDao, "ticklerManager", ticklerManager);
    ReflectionTestUtils.setField(eFormDataDao, "configurationService", gwConfigurationService);
    ReflectionTestUtils.setField(eFormDataDao, "gatewayDao", gatewayDao);
    ReflectionTestUtils.setField(eFormDataDao, "documentReferenceConverter", converter);
    ReflectionTestUtils.setField(eFormDataDao, "demographicManager", demographicManager);
    when(gwConfigurationService.isLinkFeatureEnabled(FeatureFlagEnum.EFORM_ENABLED))
        .thenReturn(true);
    when(demographic.isRemote()).thenReturn(true);

		SchemaUtils.restoreTable(new String[]{"eform_data"});
	}

	@Test
	public void testGetByDemographic() throws Exception {
		EFormData model = new EFormData();
		EntityDataGenerator.generateTestDataForModelClass(model);
		model.setDemographicId(1);
		eFormDataDao.persist(model);
		assertNotNull(model.getId());

		assertEquals(1,eFormDataDao.findByDemographicId(1).size());
		assertEquals(0,eFormDataDao.findByDemographicId(2).size());
	}

	@Test
	public void testGetByDemographicAndLastDate() throws Exception {
		EFormData model = new EFormData();
		EntityDataGenerator.generateTestDataForModelClass(model);
		model.setDemographicId(1);
		Calendar cal = Calendar.getInstance();
		//set to 5 mins ago
		cal.set(Calendar.MINUTE, cal.get(Calendar.MINUTE)-5);
		model.setFormTime(cal.getTime());
		model.setFormDate(cal.getTime());

		eFormDataDao.persist(model);
		assertNotNull(model.getId());

		//set to 10 mins ago .. so we only want forms in the last 10 minutes
		Calendar cal2 = Calendar.getInstance();
		cal2.set(Calendar.MINUTE, cal2.get(Calendar.MINUTE)-10);

		List<EFormData> results = eFormDataDao.findByDemographicIdSinceLastDate(1, cal2.getTime());
		assertEquals(1,results.size());

		cal = Calendar.getInstance();
		//set yesterday
		cal.set(Calendar.DAY_OF_MONTH, cal.get(Calendar.DAY_OF_MONTH)-1);
		model.setFormTime(cal.getTime());
		model.setFormDate(cal.getTime());
		eFormDataDao.merge(model);

		results = eFormDataDao.findByDemographicIdSinceLastDate(1, cal2.getTime());
		assertEquals(0,results.size());

		cal = Calendar.getInstance();
		//set today, but too early
		cal.set(Calendar.MINUTE, cal.get(Calendar.MINUTE)-20);
		model.setFormTime(cal.getTime());
		model.setFormDate(cal.getTime());
		eFormDataDao.merge(model);

		results = eFormDataDao.findByDemographicIdSinceLastDate(1, cal2.getTime());
		assertEquals(0,results.size());
	}

	@Test
	public void testFindByDemographicAndFormName() throws Exception {
		EFormData model = new EFormData();
		EntityDataGenerator.generateTestDataForModelClass(model);
		model.setDemographicId(8888);
		model.setFormName("CZEZANAH");
		eFormDataDao.persist(model);

		List<EFormData> data = eFormDataDao.findByDemographicIdAndFormName(8888, "CZEZANAH");
		assertFalse(data.isEmpty());
	}

	@Test
	public void testGetFormsSameFidSamePatient() throws Exception {
		EFormData[] models = {new EFormData(), new EFormData(), new EFormData(), new EFormData()};

		for (EFormData model : models) {
			EntityDataGenerator.generateTestDataForModelClass(model);
			model.setFormId(3842);
			model.setDemographicId(1974);
			model.setPatientIndependent(false);
			eFormDataDao.persist(model);
		}

		List<EFormData> eformDatas = eFormDataDao.getFormsSameFidSamePatient(models[0].getId());

		for (int i=0; i<eformDatas.size(); i++) {
			for (EFormData model : models) {
				if (model.getId().equals(eformDatas.get(i).getId())) {
					assertEquals(model, eformDatas.get(i));
					eformDatas.set(i, null);
					break;
				}
			}
		}
		for (EFormData eformData : eformDatas) {
			assertNull(eformData);
		}
	}

	@Test
	public void testIsLatestPatientForm() throws Exception {
		EFormData[] models = {new EFormData(), new EFormData(), new EFormData(), new EFormData()};

		Calendar cal = new GregorianCalendar(2011, 6, 13, 14, 15, 16);
		Date setupDate = cal.getTime();

		for (int i=0; i<models.length; i++) {
			EFormData model = models[i];
			EntityDataGenerator.generateTestDataForModelClass(model);
			model.setPatientIndependent(false);
			model.setShowLatestFormOnly(true);
			model.setFormId(1023);
			model.setDemographicId(4036);
			model.setFormDate(setupDate);
			model.setFormTime(setupDate);
			eFormDataDao.persist(model);
		}
		assertTrue(eFormDataDao.isLatestShowLatestFormOnlyPatientForm(models[3].getId()));

		cal.add(Calendar.HOUR_OF_DAY, 1);
		models[0].setFormTime(cal.getTime());
		eFormDataDao.merge(models[0]);
		assertTrue(eFormDataDao.isLatestShowLatestFormOnlyPatientForm(models[0].getId()));

		models[1].setFormDate(new Date());
		eFormDataDao.merge(models[1]);
		assertTrue(eFormDataDao.isLatestShowLatestFormOnlyPatientForm(models[1].getId()));
	}

	@Test
	public void givenFormDataId_whenGetFormIdByFormDataId_thenReturnCorrectEFormId()
			throws Exception {
		val eFormData = new EFormData();
		EntityDataGenerator.generateTestDataForModelClass(eFormData);
		eFormData.setFormId(12345);
		eFormDataDao.persist(eFormData);

		val eFormId = eFormDataDao.getFormIdByFormDataId(eFormData.getId());

		assertEquals(eFormData.getFormId(), eFormId);
	}

  @Test
  public void givenFeatureFlagDisabled_whenSaveRemoteEform_thenDoNotSaveEformDataRemotely() {
    when(gwConfigurationService.isLinkFeatureEnabled(FeatureFlagEnum.EFORM_ENABLED))
        .thenReturn(false);

    eFormDataDao.saveRemoteEform(new EFormData(), demographic, mock(HttpResponse.class));

    verify(gatewayDao, never()).saveRemote(any(Demographic.class), any(IBaseResource.class));
    verify(ticklerManager, never()).createSendToPrimaryFailedTicklerForEform(any(EFormData.class));
  }

  @Test
  public void givenDemographicIsNotRemote_whenSaveRemoteEform_thenDoNotSaveEformDataRemotely() {
    when(demographic.isRemote()).thenReturn(false);

    eFormDataDao.saveRemoteEform(new EFormData(), demographic, mock(HttpResponse.class));

    verify(gatewayDao, never()).saveRemote(any(Demographic.class), any(IBaseResource.class));
    verify(ticklerManager, never()).createSendToPrimaryFailedTicklerForEform(any(EFormData.class));
  }

  @Test
  public void givenIncorrectEformFile_whenSaveRemoteEform_thenCreateSendToPrimaryFailedTickler() {
    val eformData = new EFormData();
    val eformPdfPrint = mock(HttpResponse.class);
    val mockHttpEntity = mock(HttpEntity.class);
    try (val ignored =
        mockStatic(
            EntityUtils.class,
            new Answer<Object>() {
              @Override
              public Object answer(InvocationOnMock invocation) throws Throwable {
                if (invocation.getMethod().getName().equals("toByteArray")) {
                  HttpEntity entity = invocation.getArgument(0);
                  if (entity == mockHttpEntity) {
                    throw new IOException("Simulated I/O error");
                  }
                }
                return invocation.callRealMethod();
              }
            })) {
      eFormDataDao.saveRemoteEform(eformData, demographic, eformPdfPrint);
    }

    verify(gatewayDao, never()).saveRemote(any(Demographic.class), any(IBaseResource.class));
    verify(ticklerManager, times(1)).createSendToPrimaryFailedTicklerForEform(eformData);
  }

  @Test
  public void
      givenInvalidEformFileContent_whenSaveRemoteEform_thenCreateSendToPrimaryFailedTickler() {
    val eformData = new EFormData();
    val eformPdfPrint = mock(HttpResponse.class);
    val mockHttpEntity = mock(HttpEntity.class);
    val testFilePath = "src/test/resources/test.pdf";

    when(eformPdfPrint.getEntity()).thenReturn(mockHttpEntity);
    try (val ignored =
            mockStatic(
                EntityUtils.class,
                new Answer<Object>() {
                  @Override
                  public Object answer(InvocationOnMock invocation) throws Throwable {
                    if (invocation.getMethod().getName().equals("toByteArray")) {
                      HttpEntity entity = invocation.getArgument(0);
                      if (entity == mockHttpEntity) {
                        return testFilePath.getBytes();
                      }
                    }
                    return invocation.callRealMethod();
                  }
                });
        MockedStatic<FileUtils> ignored1 =
            mockStatic(
                FileUtils.class,
                new Answer<Object>() {
                  @Override
                  public Object answer(InvocationOnMock invocation) throws Throwable {
                    if (invocation.getMethod().getName().equals("readFileToByteArray")) {
                      throw new IOException("Simulated I/O error");
                    }
                    return invocation.callRealMethod();
                  }
                })) {

      eFormDataDao.saveRemoteEform(eformData, demographic, eformPdfPrint);
    }

    verify(gatewayDao, never()).saveRemote(any(Demographic.class), any(IBaseResource.class));
    verify(ticklerManager, times(1)).createSendToPrimaryFailedTicklerForEform(eformData);
  }

  @Test
  public void givenGatewayException_whenSaveRemoteEform_thenCreateSendToPrimaryFailedTickler() {
    val eformData = new EFormData();
    val eformPdfPrint = mock(HttpResponse.class);
    val mockHttpEntity = mock(HttpEntity.class);
    val testFilePath = "src/test/resources/test.pdf";
    val testFileContent = "test content";

    when(eformPdfPrint.getEntity()).thenReturn(mockHttpEntity);
    when(converter.toFhirObject(eformData)).thenReturn(new DocumentReference());
    doThrow(new RuntimeException("Simulated gateway exception"))
        .when(gatewayDao)
        .saveRemote(any(Demographic.class), any(IBaseResource.class));
    try (val ignored =
            mockStatic(
                EntityUtils.class,
                new Answer<Object>() {
                  @Override
                  public Object answer(InvocationOnMock invocation) throws Throwable {
                    if (invocation.getMethod().getName().equals("toByteArray")) {
                      HttpEntity entity = invocation.getArgument(0);
                      if (entity == mockHttpEntity) {
                        return testFilePath.getBytes();
                      }
                    }
                    return invocation.callRealMethod();
                  }
                });
        MockedStatic<FileUtils> ignored1 =
            mockStatic(
                FileUtils.class,
                new Answer<Object>() {
                  @Override
                  public Object answer(InvocationOnMock invocation) throws Throwable {
                    if (invocation.getMethod().getName().equals("readFileToByteArray")) {
                      return testFileContent.getBytes();
                    }
                    return invocation.callRealMethod();
                  }
                })) {

      eFormDataDao.saveRemoteEform(eformData, demographic, eformPdfPrint);
    }

    verify(gatewayDao, times(1)).saveRemote(any(Demographic.class), any(IBaseResource.class));
    verify(ticklerManager, times(1)).createSendToPrimaryFailedTicklerForEform(eformData);
  }

  @Test
  public void givenValidEformData_whenSaveRemoteEform_thenSaveEformData() {
    val eformPdfPrint = mock(HttpResponse.class);
    val mockHttpEntity = mock(HttpEntity.class);
    val eformData = new EFormData();
    val testFilePath = "src/test/resources/test.pdf";
    val testFileContent = "test content";

    when(eformPdfPrint.getEntity()).thenReturn(mockHttpEntity);
    when(converter.toFhirObject(eformData)).thenReturn(new DocumentReference());
    try (val ignored =
            mockStatic(
                EntityUtils.class,
                new Answer<Object>() {
                  @Override
                  public Object answer(InvocationOnMock invocation) throws Throwable {
                    if (invocation.getMethod().getName().equals("toByteArray")) {
                      HttpEntity entity = invocation.getArgument(0);
                      if (entity == mockHttpEntity) {
                        return testFilePath.getBytes();
                      }
                    }
                    return invocation.callRealMethod();
                  }
                });
        MockedStatic<FileUtils> ignored1 =
            mockStatic(
                FileUtils.class,
                new Answer<Object>() {
                  @Override
                  public Object answer(InvocationOnMock invocation) throws Throwable {
                    if (invocation.getMethod().getName().equals("readFileToByteArray")) {
                      return testFileContent.getBytes();
                    }
                    return invocation.callRealMethod();
                  }
                })) {

      eFormDataDao.saveRemoteEform(eformData, demographic, eformPdfPrint);

      verify(gatewayDao, times(1)).saveRemote(any(Demographic.class), any(IBaseResource.class));
      verify(ticklerManager, never()).createSendToPrimaryFailedTicklerForEform(eformData);
    }
  }

  @Test
  public void givenNullResourceToSave_whenSaveRemoteEform_thenNoExceptionIsThrown() {
    val eformPdfPrint = mock(HttpResponse.class);
    val mockHttpEntity = mock(HttpEntity.class);
    val eformData = new EFormData();
    val testFilePath = "src/test/resources/test.pdf";
    val testFileContent = "test content";

    when(eformPdfPrint.getEntity()).thenReturn(mockHttpEntity);
    when(converter.toFhirObject(eformData)).thenReturn(new DocumentReference());
    when(gatewayDao.saveRemote(any(Demographic.class), any(IBaseResource.class)))
        .thenReturn(null);
    try (val ignored =
        mockStatic(
            EntityUtils.class,
            new Answer<Object>() {
              @Override
              public Object answer(InvocationOnMock invocation) throws Throwable {
                if (invocation.getMethod().getName().equals("toByteArray")) {
                  HttpEntity entity = invocation.getArgument(0);
                  if (entity == mockHttpEntity) {
                    return testFilePath.getBytes();
                  }
                }
                return invocation.callRealMethod();
              }
            });
        MockedStatic<FileUtils> ignored1 =
            mockStatic(
                FileUtils.class,
                new Answer<Object>() {
                  @Override
                  public Object answer(InvocationOnMock invocation) throws Throwable {
                    if (invocation.getMethod().getName().equals("readFileToByteArray")) {
                      return testFileContent.getBytes();
                    }
                    return invocation.callRealMethod();
                  }
                })) {

      eFormDataDao.saveRemoteEform(eformData, demographic, eformPdfPrint);

      verify(gatewayDao, times(1)).saveRemote(any(Demographic.class), any(IBaseResource.class));
      verify(ticklerManager, never()).createSendToPrimaryFailedTicklerForEform(eformData);
    }
  }

  /**
   * Show that the only difference between results fetched from findMetadataByDemographicIdCurrent
   * and findByDemographicIdCurrent is that the return type of the former is EFormDataMetadata,
   * which does not have the formData field, and the return type of the latter is EFormData.
   * @throws Exception
   */
  @Test
  public void givenEform_whenFindMetadataByDemographicIdCurrent_thenResultMatchesNonMetadataMethod()
      throws Exception {
    int demographicNo = 1;
    EFormData model = new EFormData();
    EntityDataGenerator.generateTestDataForModelClass(model);
    model.setDemographicId(demographicNo);
    model.setCurrent(true);
    model.setPatientIndependent(false);
    eFormDataDao.persist(model);
    assertNotNull(model.getId());

    val eformFromMetadata = eFormDataDao.findMetadataByDemographicIdCurrent(demographicNo, true, 0, 10, null).get(0);
    val eformFromNonMetadata = eFormDataDao.findByDemographicIdCurrent(demographicNo, true, 0, 10, null).get(0);

    assertNotNull(eformFromNonMetadata.getFormData());
    assertEquals(model.getFormData(), eformFromNonMetadata.getFormData());

    val expectedEqualFields = new String[] {
        "id",
        "formId",
        "formName",
        "subject",
        "demographicId",
        "current",
        "formDate",
        "formTime",
        "providerNo",
        "showLatestFormOnly",
        "patientIndependent",
        "appointmentNo",
        "roleType",
        "guid",
        "remoteSystemId"
    };

    for (String field : expectedEqualFields) {
      val expectedValue = ReflectionTestUtils.getField(eformFromMetadata, field);
      val actualValue = ReflectionTestUtils.getField(eformFromNonMetadata, field);
      assertEquals(expectedValue, actualValue);
    }
  }

  @Test
  public void givenFullEFormData_whenFindByDemographicIdCurrentExcludingFormData_thenReturnAllEFormData() {
    // Create test EFormData record for a specific demographic
    Integer demographicNumber = 2;
    EFormData eFormData = createFullTestEFormData(demographicNumber);
    eFormDataDao.persist(eFormData);

    // Do not get remote EForms
    when(gwConfigurationService.isLinkFeatureEnabled(FeatureFlagEnum.EFORM_ENABLED))
        .thenReturn(false);

    List<Map<String, Object>> results = eFormDataDao.findByDemographicIdCurrentExcludingFormData(
        demographicNumber, null);

    // Ensure the results are not null and contain the expected number of records
    assertNotNull(results);
    assertEquals(1, results.size());
    assertFullEFormDataInResult(results.get(0), eFormData);
  }

  private EFormData createFullTestEFormData(final Integer demographicNumber) {
    EFormData eFormData = new EFormData();
    eFormData.setFormId(12345);
    eFormData.setFormName("Test EForm");
    eFormData.setSubject("Test Subject");
    eFormData.setDemographicId(demographicNumber);
    eFormData.setCurrent(true);
    eFormData.setFormDate(new Date());
    eFormData.setFormTime(new Date());
    eFormData.setProviderNo("Provider123");
    eFormData.setShowLatestFormOnly(true);
    eFormData.setPatientIndependent(false);
    eFormData.setAppointmentNo(456);
    eFormData.setRoleType("Test Role");
    eFormData.setGuid("abcdef-ghij-klmn-opqrst");
    eFormData.setFormData("Sample form data content");
    return eFormData;
  }

  private void assertFullEFormDataInResult(Map<String, Object> result, EFormData eFormData) {
    assertEquals(eFormData.getId(), result.get("id"));
    assertEquals(eFormData.getFormId(), result.get("formId"));
    assertEquals(eFormData.getFormName(), result.get("formName"));
    assertEquals(eFormData.getSubject(), result.get("subject"));
    assertEquals(eFormData.getSubject(), result.get("formSubject"));
    assertEquals(eFormData.getDemographicId(), result.get("demographicId"));
    assertEquals(eFormData.isCurrent(), result.get("current"));

    // Use formDate to check only the date representation
    String dateString = new SimpleDateFormat("yyyy-MM-dd").format(eFormData.getFormDate());
    assertEquals(dateString, result.get("formDate"));

    // Use formTime to check only the time representation with 1 second leeway
    String expectedTimeString = new SimpleDateFormat("HH:mm:ss").format(eFormData.getFormTime());
    String actualTimeString = (String) result.get("formTime");

    // Parse the time strings to compare with 1 second leeway
    SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
    try {
      Date expectedTime = timeFormat.parse(expectedTimeString);
      Date actualTime = timeFormat.parse(actualTimeString);
      long timeDifferenceMs = Math.abs(expectedTime.getTime() - actualTime.getTime());
      // Allow 1 second (1000 ms) leeway
      assertTrue(
          "Time difference exceeds 1 second leeway. Expected: " + expectedTimeString + ", Actual: "
              + actualTimeString,
          timeDifferenceMs <= 1000);
    } catch (Exception e) {
      fail("Failed to parse time strings. Expected: " + expectedTimeString + ", Actual: "
          + actualTimeString + " " + e.getMessage());
    }

    assertEquals(eFormData.getProviderNo(), result.get("providerNo"));
    assertEquals(eFormData.isPatientIndependent(), result.get("patientIndependent"));
    assertEquals(eFormData.getRoleType(), result.get("roleType"));
    assertEquals(eFormData.getGuid(), result.get("guid"));
    assertEquals(eFormData.getRemoteSystemId(), result.get("remoteSystemId"));
    assertFalse((Boolean) result.get("isRemote"));
  }

  @Test
  public void givenLocalEFormData_whenFindByDemographicIdCurrentExcludingFormData_thenReturnLocalEFormData() {
    // Create two test EFormData records for a specific demographic
    Integer demographicNumber = 2;
    EFormData eFormData1 = createTestEFormData(demographicNumber, "Local EForm1", 111);
    EFormData eFormData2 = createTestEFormData(demographicNumber, "Local EForm2", 222);
    eFormDataDao.persist(eFormData1);
    eFormDataDao.persist(eFormData2);

    // Do not get remote EForms
    when(gwConfigurationService.isLinkFeatureEnabled(FeatureFlagEnum.EFORM_ENABLED))
        .thenReturn(false);

    List<Map<String, Object>> results = eFormDataDao.findByDemographicIdCurrentExcludingFormData(
        demographicNumber, null);

    // Ensure the results are not null and contain the expected number of records
    assertNotNull(results);
    assertEquals(2, results.size());
    assertEFormDataInResults(results, eFormData1, eFormData2);
  }

  @Test
  public void givenCurrentSetToTrue_whenFindByDemographicIdCurrentExcludingFormData_thenReturnOnlyCurrentEFormData() {
    // Create two test EFormData records for a specific demographic
    Integer demographicNumber = 2;
    EFormData eFormData1 = createTestEFormData(demographicNumber, "Local EForm1", 111);
    eFormData1.setCurrent(true);
    eFormDataDao.persist(eFormData1);

    EFormData eFormData2 = createTestEFormData(demographicNumber, "Local EForm2", 222);
    eFormData2.setCurrent(false);
    eFormDataDao.persist(eFormData2);

    List<Map<String, Object>> results = eFormDataDao.findByDemographicIdCurrentExcludingFormData(
        demographicNumber, true);

    // Ensure the results are not null and contain only the current record
    assertNotNull(results);
    assertEquals(1, results.size());
    assertEFormDataInResults(results, eFormData1);
  }

  @Test
  public void givenCurrentSetToFalse_whenFindByDemographicIdCurrentExcludingFormData_thenReturnOnlyNonCurrentEFormData() {
    // Create two test EFormData records for a specific demographic
    Integer demographicNumber = 2;
    EFormData eFormData1 = createTestEFormData(demographicNumber, "Local EForm1", 111);
    eFormData1.setCurrent(true);
    eFormDataDao.persist(eFormData1);

    EFormData eFormData2 = createTestEFormData(demographicNumber, "Local EForm2", 222);
    eFormData2.setCurrent(false);
    eFormDataDao.persist(eFormData2);

    List<Map<String, Object>> results = eFormDataDao.findByDemographicIdCurrentExcludingFormData(
        demographicNumber, false);

    // Ensure the results are not null and contain only the current record
    assertNotNull(results);
    assertEquals(1, results.size());
    assertEFormDataInResults(results, eFormData2);
  }

  @Test
  public void givenRemoteEFormData_whenFindByDemographicIdCurrentExcludingFormData_thenReturnRemoteEFormData() throws Exception {
    // Create two test EFormData records for a specific demographic
    Integer demographicNumber = 2;
    EFormData remoteEFormData1 = createTestRemoteEFormData(
        demographicNumber, "Remote EForm1", 111, 11, "abcdef-ghij-klmn-opqrst");
    EFormData remoteEFormData2 = createTestRemoteEFormData(
        demographicNumber, "Remote EForm2", 222, 22, "uvwxyz-1234-5678-90abcd");

    mockRemoteEFormData(remoteEFormData1, remoteEFormData2);

    // Mock the demographic to return a remote system ID
    Integer remoteSystemId = 7;
    mockDemographicRemoteSystemId(demographicNumber, remoteSystemId);

    List<Map<String, Object>> results = eFormDataDao.findByDemographicIdCurrentExcludingFormData(
        demographicNumber, null);

    // Ensure the results are not null and contain the expected number of records
    assertNotNull(results);
    assertEquals(2, results.size());
    assertEFormDataInResults(results, remoteEFormData1, remoteEFormData2);
  }

  @Test
  public void givenLocalAndRemoteEFormDataWithSameGuid_whenFindByDemographicIdCurrentExcludingFormData_thenFilterDuplicateGuid() throws Exception {
    // Add guid on local and remote to ensure only one of them is returned
    String commonGuid = "common-guid-1234-common";
    // Create local EFormData records
    Integer demographicNumber = 2;
    EFormData localEFormData1 = createTestEFormData(demographicNumber, "Local EForm1", 111);
    // Set the same guid as remoteEFormData1
    localEFormData1.setGuid(commonGuid);
    eFormDataDao.persist(localEFormData1);
    EFormData localEFormData2 = createTestEFormData(demographicNumber, "Local EForm2", 222);
    eFormDataDao.persist(localEFormData2);

    // Create two remote EFormData records
    EFormData remoteEFormData1 = createTestRemoteEFormData(
        demographicNumber, "Remote EForm1", 333, 22, commonGuid);
    EFormData remoteEFormData2 = createTestRemoteEFormData(
        demographicNumber, "Remote EForm2", 444, 33, "uvwxyz-1234-5678-90abcd");

    mockRemoteEFormData(remoteEFormData1, remoteEFormData2);

    // Mock the demographic to return a remote system ID
    Integer remoteSystemId = 7;
    mockDemographicRemoteSystemId(demographicNumber, remoteSystemId);

    List<Map<String, Object>> results = eFormDataDao.findByDemographicIdCurrentExcludingFormData(
        demographicNumber, null);

    // Ensure the results are not null and contain the expected number of records
    assertNotNull(results);
    assertEquals(3, results.size());

    // Since localEFormData1 has the same guid as remoteEFormData1, remoteEFormData1 is filtered
    // out, so we expect localEFormData1, localEFormData2, and remoteEFormData2 in the results.
    assertEFormDataInResults(results, localEFormData1, localEFormData2, remoteEFormData2);
  }

  private void mockRemoteEFormData(final EFormData... eFormDatas) throws Exception {
    DocumentReferenceConverter converter = new DocumentReferenceConverter();
    Bundle bundle = new Bundle();
    for (EFormData eFormData : eFormDatas) {
      DocumentReference documentReference = converter.toFhirObject(eFormData);
      bundle.addEntry().setResource(documentReference);
    }
    when(gatewayDao.findAllRemoteByDemographicId(eq(DocumentReference.class), any(), any()))
        .thenReturn(bundle);
  }

  private void mockDemographicRemoteSystemId(final Integer demographicId, final Integer remoteSystemId) {
    when(demographicManager.getDemographic(demographicId)).thenReturn(demographic);
    when(demographic.getRemoteSystemId()).thenReturn(remoteSystemId);
  }

  private void assertEFormDataInResults(List<Map<String, Object>> results, EFormData... eFormDatas) {
    for (int i = 0; i < eFormDatas.length; i++) {
      Map<String, Object> resultMap = results.get(i);
      assertNotNull(resultMap);
      EFormData eFormData = eFormDatas[i];
      assertEquals(eFormData.getId(), resultMap.get("id"));
      assertEquals(eFormData.getFormName(), resultMap.get("formName"));
    }
  }

  private EFormData createTestRemoteEFormData(final Integer demographicId, final String formName,
      final Integer fid, final Integer id, final String guid) {
    EFormData eFormData = createTestEFormData(demographicId, formName, fid);
    eFormData.setId(id);
    eFormData.setGuid(guid);
    return eFormData;
  }

  private EFormData createTestEFormData(final Integer demographicId, final String formName,
      final Integer fid) {
    EFormData eFormData = new EFormData();
    eFormData.setDemographicId(demographicId);
    eFormData.setFormName(formName);
    eFormData.setFormDate(new Date());
    eFormData.setFormTime(new Date());
    eFormData.setFormId(fid);
    return eFormData;
  }
}
