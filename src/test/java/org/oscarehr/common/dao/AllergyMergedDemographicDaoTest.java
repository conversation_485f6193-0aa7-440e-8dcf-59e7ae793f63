/**
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for the
 * Department of Family Medicine
 * McMaster University
 * Hamilton
 * Ontario, Canada
 */
package org.oscarehr.common.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.oscarehr.common.dao.utils.SchemaUtils;
import org.oscarehr.common.model.Allergy;
import org.oscarehr.common.model.DemographicMerged;
import org.oscarehr.util.SpringUtils;

public class AllergyMergedDemographicDaoTest extends DaoTestFixtures {

  protected AllergyMergedDemographicDao allergyMergedDemographicDao = SpringUtils.getBean(AllergyMergedDemographicDao.class);
  protected DemographicMergedDao demogrphicMergedDao = ((DemographicMergedDao) SpringUtils.getBean(DemographicMergedDao.class));

  private final int demographicNo = 12345;
  private final int limit = 3;

  public AllergyMergedDemographicDaoTest() {}

  @BeforeEach
  public void before() throws Exception {
    SchemaUtils.restoreTable("allergies", "demographic_merged");
  }

  /**
   * Show that when the number of allergies fetched is less than the given limit that all allergies
   * are returned in sorted order by entry date.
   */
  @Test
  public void givenAllergiesLessThanLimit_whenFindActiveAllergiesLimitBy_thenAllAlergiesReturned() {

    val allergyEarly = createActiveAllergy(1, "Peanuts", "High", LocalDate.now().minusDays(5));
    val allergyLate = createActiveAllergy(2, "Shellfish", "Medium", LocalDate.now().minusDays(3));

    List<Allergy> result = allergyMergedDemographicDao.findActiveAllergiesLimitBy(demographicNo, limit);

    assertEquals(2, result.size());
    assertTrue(result.get(0).getEntryDate().after(result.get(1).getEntryDate()));
    assertEquals(allergyLate.getDescription(), result.get(0).getDescription());
    assertEquals(allergyEarly.getDescription(), result.get(1).getDescription());

  }

  /**
   * Show that when the number of allergies fetched is greater than the given limit, then only the
   * top allergies by sort order are returned.
   */
  @Test
  public void givenManyAllergies_whenFindActiveAllergiesLimitBy_thenLimitedAllergiesReturned() {
    createActiveAllergy(1, "Peanuts", "High", LocalDate.now().minusDays(10));
    createActiveAllergy(2, "Shellfish", "Medium", LocalDate.now().minusDays(8));
    createActiveAllergy(3, "Eggs", "Low", LocalDate.now().minusDays(6));
    createActiveAllergy(4, "Milk", "High", LocalDate.now().minusDays(4));
    createActiveAllergy(5, "Soy", "Medium", LocalDate.now().minusDays(2));

    List<Allergy> result = allergyMergedDemographicDao.findActiveAllergiesLimitBy(demographicNo, limit);

    assertEquals(limit, result.size());
    assertEquals("Soy", result.get(0).getDescription());
    assertEquals("Milk", result.get(1).getDescription());
    assertEquals("Eggs", result.get(2).getDescription());
  }

  /**
   * Show that when there are archived allergies, they are not included in the results.
   */
  @Test
  public void givenArchivedAllergies_whenFindActiveAllergiesLimitBy_OnlyActiveAllergies() {

    createArchivedAllergy(1, "Old Peanuts", "High", LocalDate.now().minusDays(5));
    val activeAllergy = createActiveAllergy(2, "Old Shellfish", "Medium", LocalDate.now().minusDays(3));

    List<Allergy> result = allergyMergedDemographicDao.findActiveAllergiesLimitBy(demographicNo, limit);

    assertEquals(1, result.size());
    assertEquals(activeAllergy.getDescription(), result.get(0).getDescription());
  }

  /**
   * Show that when an allergy exists for a different demographic that isn't merged to the
   * queried demographic, it is not included in the results.
   */
  @Test
  public void givenAllergyFromDifferentDemographic_whenFindActiveAllergiesLimitBy_thenOnlySpecificDemographicAllergiesFetched() {

    int otherDemographicNo = 99999;
    createActiveAllergyForDemographic(otherDemographicNo, 1, "Other Person's Allergy", "High", LocalDate.now());
    val correctAllergy = createActiveAllergy(1, "My Allergy", "Medium", LocalDate.now().minusDays(1));

    List<Allergy> result = allergyMergedDemographicDao.findActiveAllergiesLimitBy(demographicNo, limit);

    assertEquals(1, result.size());
    assertEquals(correctAllergy.getDescription(), result.get(0).getDescription());
    assertEquals(demographicNo, result.get(0).getDemographicNo());
  }

  /**
   * Show that when an allergy exists for a different demographic record that is merged to the
   * queried demographic, it is included in the results.
   */
  @Test
  public void givenAllergyFromMergedDemographic_whenFindActiveAllergiesLimitBy_thenBothAllergiesFetched() {

    int otherDemographicNo = 99999;
    val otherAllergy = createActiveAllergyForDemographic(otherDemographicNo, 1, "Other Person's Allergy", "High", LocalDate.now());
    val myAllergy = createActiveAllergy(2, "My Allergy", "Medium", LocalDate.now().minusDays(1));
    mergeDemographicTo(otherDemographicNo);

    List<Allergy> result = allergyMergedDemographicDao.findActiveAllergiesLimitBy(demographicNo, limit);

    assertEquals(2, result.size());
    assertEquals(otherAllergy.getDescription(), result.get(0).getDescription());
    assertEquals(otherDemographicNo, result.get(0).getDemographicNo());
    assertEquals(myAllergy.getDescription(), result.get(1).getDescription());
    assertEquals(demographicNo, result.get(1).getDemographicNo());
  }

  /**
   * Show that when multiple demographics are merged to the same demographic and the count exceeds
   * the limit, the most recent allergies from all merged demographics are returned.
   */
  @Test
  public void givenMultipleMergedDemographics_whenFindActiveAllergiesLimitBy_thenMostRecentAllergiesReturned() {

    int otherDemographicNo1 = 99999;
    int otherDemographicNo2 = 88888;
    int otherDemographicNo3 = 77777;
    mergeDemographicTo(otherDemographicNo1);
    mergeDemographicTo(otherDemographicNo2);
    mergeDemographicTo(otherDemographicNo3);

    val allergy1 = createActiveAllergyForDemographic(otherDemographicNo1, 1, "Other Allergy 1", "High", LocalDate.now().minusDays(2));
    val allergy2 = createActiveAllergyForDemographic(otherDemographicNo2, 2, "Other Allergy 2", "Medium", LocalDate.now().minusDays(1));
    val allergy3 = createActiveAllergyForDemographic(otherDemographicNo3, 3, "Other Allergy 3", "Low", LocalDate.now());
    createActiveAllergy(5, "My Allergy", "Medium", LocalDate.now().minusDays(3));

    List<Allergy> result = allergyMergedDemographicDao.findActiveAllergiesLimitBy(demographicNo, limit);

    assertEquals(limit, result.size());
    assertEquals(allergy3.getDescription(), result.get(0).getDescription());
    assertEquals(otherDemographicNo3, result.get(0).getDemographicNo());
    assertEquals(allergy2.getDescription(), result.get(1).getDescription());
    assertEquals(otherDemographicNo2, result.get(1).getDemographicNo());
    assertEquals(allergy1.getDescription(), result.get(2).getDescription());
    assertEquals(otherDemographicNo1, result.get(2).getDemographicNo());
  }

  private void mergeDemographicTo(int otherDemographicNo) {
    val demographicMerged = new DemographicMerged();
    demographicMerged.setDemographicNo(otherDemographicNo);
    demographicMerged.setMergedTo(demographicNo);
    demogrphicMergedDao.persist(demographicMerged);

  }

  private Allergy createActiveAllergy(int id, String description, String severity, LocalDate entryDate) {
    return createActiveAllergyForDemographic(demographicNo, id, description, severity, entryDate);
  }

  private Allergy createBaseAllergy(int id, int demographicNo, String description, String severity, LocalDate entryDate) {
    Allergy allergy = new Allergy();
    allergy.setDemographicNo(demographicNo);
    allergy.setDescription(description);
    allergy.setSeverityOfReaction(severity);
    allergy.setEntryDate(Date.from(entryDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
    allergy.setArchived(false);
    allergy.setPosition(id);
    allergy.setTypeCode(0);
    return allergy;
  }

  private Allergy createActiveAllergyForDemographic(int demographicNo, int id, String description, String severity, LocalDate entryDate) {
    Allergy allergy = createBaseAllergy(id, demographicNo, description, severity, entryDate);
    allergyMergedDemographicDao.persist(allergy);
    return allergy;
  }

  private Allergy createArchivedAllergy(int id, String description, String severity, LocalDate entryDate) {
    Allergy allergy = createBaseAllergy(id, demographicNo, description, severity, entryDate);
    allergy.setArchived(true);
    allergyMergedDemographicDao.persist(allergy);
    return allergy;
  }
}
