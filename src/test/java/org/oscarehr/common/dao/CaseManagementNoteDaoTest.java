/**
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved.
 * This software is published under the GPL GNU General Public License. This program is free
 * software; you can redistribute it and/or modify it under the terms of the GNU General Public
 * License as published by the Free Software Foundation; either version 2 of the License, or (at
 * your option) any later version.
 * <p>
 * This program is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without
 * even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * <p>
 * You should have received a copy of the GNU General Public License along with this program; if
 * not, write to the Free Software Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA
 * 02111-1307, USA.
 * <p>
 * This software was written for the Department of Family Medicine McMaster University Hamilton
 * Ontario, Canada
 */
package org.oscarehr.common.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import health.apps.gateway.service.GWConfigurationService;
import health.apps.gateway.service.GatewayDao;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.casemgmt.dao.CaseManagementNoteDAO;
import org.oscarehr.casemgmt.model.CaseManagementNote;
import org.oscarehr.common.dao.utils.SchemaUtils;
import org.oscarehr.common.enums.FeatureFlagEnum;
import org.oscarehr.common.model.CaseManagementNoteJpa;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.common.model.Facility;
import org.oscarehr.common.model.SecRole;
import org.oscarehr.common.service.CaseManagementNoteService;
import org.oscarehr.PMmodule.dao.ProgramDao;
import org.oscarehr.PMmodule.model.Program;
import org.oscarehr.util.SpringUtils;

public class CaseManagementNoteDaoTest extends DaoTestFixtures {

	protected CaseManagementNoteDAO dao = SpringUtils.getBean(CaseManagementNoteDAO.class);
	private CaseManagementNoteJpaDao jpaDao = SpringUtils.getBean(CaseManagementNoteJpaDao.class);
	private CaseManagementNoteService noteService = SpringUtils.getBean(
			CaseManagementNoteService.class);
	private SecRoleDao secRoleDao = SpringUtils.getBean(SecRoleDao.class);
	private ProgramDao programDao = SpringUtils.getBean(ProgramDao.class);
	private FacilityDao facilityDao = SpringUtils.getBean(FacilityDao.class);

	@BeforeEach
	public void before() throws Exception {
		SchemaUtils.restoreTable("casemgmt_note", "casemgmt_note_ext", "secRole", "program",
				"Facility");
	}

	@Test
	public void testFindAll() {
		Assertions.assertNotNull(dao.findAll());
	}

	@Test
	void givenMultipleJpaNotes_whenConvertedAndSaved_thenAllSavedCorrectly() {
		// Create required facility, program, and sec role
		Facility facility = createFacility();
		Program program = createProgram("Test Program", facility);
		SecRole role = createSecRole("Test Role");

		// Create multiple JPA notes
		CaseManagementNoteJpa jpaNote1 = createCaseManagementNoteJpa(program, role);
		jpaNote1.setNote("Note 1");
		CaseManagementNoteJpa jpaNote2 = createCaseManagementNoteJpa(program, role);
		jpaNote2.setNote("Note 2");
		jpaNote2.setUuid("different-uuid");

		// Convert the JPA notes to CaseManagementNotes
		List<CaseManagementNoteJpa> jpaNotes = new ArrayList<>();
		jpaNotes.add(jpaNote1);
		jpaNotes.add(jpaNote2);
		List<CaseManagementNote> convertedNotes = noteService.convertJpaNotesToCaseManagementNotes(
				jpaNotes);

		assertNotNull(convertedNotes, "Converted notes list should not be null");
		assertEquals(2, convertedNotes.size(), "Converted notes list should have 2 elements");

		// Record the size of the table prior to saving
		int initialSize = dao.findAll().size();

		// Save all converted notes
		for (CaseManagementNote note : convertedNotes) {
			dao.saveNote(note);
		}

		// Verify the size of the table has increased by the number of notes saved
		int newSize = dao.findAll().size();
		assertEquals(initialSize + convertedNotes.size(), newSize,
				"New size should be initial size + number of notes saved");

		// Verify all notes were saved correctly
		for (CaseManagementNote convertedNote : convertedNotes) {
			CaseManagementNote savedNote = dao.getNote(convertedNote.getId());
			assertNotNull(savedNote, "Saved note should not be null");
			assertEquals(convertedNote.getId(), savedNote.getId(), "IDs should match");
			assertEquals(convertedNote.getUuid(), savedNote.getUuid(), "UUIDs should match");
			assertEquals(convertedNote.getNote(), savedNote.getNote(), "Notes should match");
		}
	}

	@Test
	void givenMultipleJpaNotes_whenConvertedAndUpdated_thenAllUpdatedCorrectly() {
		// Create required facility, program, and sec role
		Facility facility = createFacility();
		Program program = createProgram("Test Program", facility);
		SecRole role = createSecRole("Test Role");

		// Create multiple JPA notes
		CaseManagementNoteJpa jpaNote1 = createCaseManagementNoteJpa(program, role);
		jpaNote1.setNote("Note 1");
		CaseManagementNoteJpa jpaNote2 = createCaseManagementNoteJpa(program, role);
		jpaNote2.setNote("Note 2");
		jpaNote2.setUuid("different-uuid");

		// Convert the JPA notes to CaseManagementNotes
		List<CaseManagementNoteJpa> jpaNotes = new ArrayList<>();
		jpaNotes.add(jpaNote1);
		jpaNotes.add(jpaNote2);
		List<CaseManagementNote> convertedNotes = noteService.convertJpaNotesToCaseManagementNotes(
				jpaNotes);

		assertNotNull(convertedNotes, "Converted notes list should not be null");
		assertEquals(2, convertedNotes.size(), "Converted notes list should have 2 elements");

		// Save all converted notes
		for (CaseManagementNote note : convertedNotes) {
			dao.saveNote(note);
		}

		// Verify table size before updating
		int initialSize = dao.findAll().size();

		// Modify and update all notes
		List<CaseManagementNote> savedNotes = new ArrayList<>();
		for (CaseManagementNote convertedNote : convertedNotes) {
			CaseManagementNote savedNote = dao.getNote(convertedNote.getId());
			assertNotNull(savedNote, "Saved note should not be null");
			savedNote.setNote("Updated " + savedNote.getNote());
			savedNotes.add(savedNote);
			dao.updateNote(savedNote);
		}

		// Verify the size of the table remains the same after update
		int newSize = dao.findAll().size();
		assertEquals(initialSize, newSize, "New size should be equal to initial size after update");

		// Verify all notes were updated correctly
		for (CaseManagementNote savedNote : savedNotes) {
			CaseManagementNote updatedNote = dao.getNote(savedNote.getId());
			assertNotNull(updatedNote, "Updated note should not be null");
			assertEquals(savedNote.getId(), updatedNote.getId(), "IDs should match");
			assertEquals(savedNote.getUuid(), updatedNote.getUuid(), "UUIDs should match");
			assertTrue(updatedNote.getNote().startsWith("Updated "), "Note text should be updated");
		}
	}

	// Helper methods
	private Facility createFacility() {
		Facility facility = new Facility();
		facility.setName("Test Facility");
		facility.setHic(true);
		facility.setDisabled(false);
		facility.setOrgId(1);
		facility.setSectorId(1);
		facility.setIntegratorEnabled(false);
		facility.setEnableIntegratedReferrals(false);
		facility.setEnableHealthNumberRegistry(false);
		facility.setAllowSims(false);
		facility.setEnableDigitalSignatures(true);
		facility.setOcanServiceOrgNumber("123456");
		facility.setEnableOcanForms(true);
		facility.setEnableCbiForm(false);
		facility.setEnableAnonymous(false);
		facility.setEnablePhoneEncounter(true);
		facility.setEnableGroupNotes(false);
		facility.setEnableEncounterTime(false);
		facility.setEnableEncounterTransportationTime(false);
		facility.setRxInteractionWarningLevel(0);
		facility.setDisplayAllVacancies(0);
		facilityDao.persist(facility);
		return facility;
	}

	private Program createProgram(String name, Facility facility) {
		Program program = new Program();
		program.setName(name);
		program.setMaxAllowed(10);
		program.setProgramStatus(Program.PROGRAM_STATUS_ACTIVE);
		program.setFacilityId(facility.getId());
		program.setTransgender(true);
		program.setFirstNation(true);
		program.setBedProgramAffiliated(true);
		program.setAlcohol(true);
		program.setPhysicalHealth(true);
		program.setMentalHealth(true);
		program.setHousing(true);
		program.setExclusiveView("exclusiveView");
		program.setAgeMin(1);
		program.setAgeMax(10);
		program.setLastUpdateDate(new Date());
		program.setEnableOCAN(true);
		programDao.saveProgram(program);
		return program;
	}

	private SecRole createSecRole(String name) {
		SecRole role = new SecRole();
		role.setName(name);
		role.setDescription("Test role description");
		secRoleDao.persist(role);
		return role;
	}

	private CaseManagementNoteJpa createCaseManagementNoteJpa(Program program, SecRole role) {
		CaseManagementNoteJpa note = new CaseManagementNoteJpa();
		note.setUuid("test-uuid-" + System.currentTimeMillis());
		note.setDemographicNumber("1");
		note.setNote("Test note");
		note.setHistory("Test history");
		note.setUpdateDate(new Date());
		note.setObservationDate(new Date());
		note.setProgramNumber(program.getId().toString());
		note.setProviderNumber("999998");
		note.setSigningProviderNo("999998");
		note.setReporterCaisiRole(role.getId().toString());
		note.setReporterProgramTeam("Test Team");
		note.setBillingCode("Test Billing Code");
		note.setEncounterType("Test Encounter Type");
		note.setSigned(true);
		note.setIncludeIssue(true);
		note.setArchived(false);
		note.setAvailable(true);
		note.setLocked(false);
		note.setAppointmentNumber(123);
		note.setPosition(1);
		note.setHourOfEncounterTime(10);
		note.setMinuteOfEncounterTime(30);
		note.setHourOfEncTransportationTime(1);
		note.setMinuteOfEncTransportationTime(15);
		note.setPassword("password123");
		jpaDao.persist(note);
		return note;
	}
}

@ExtendWith(MockitoExtension.class)
class CaseManagementNoteDaoSaveRemoteTest {

	@InjectMocks
	private CaseManagementNoteDAO caseManagementNoteDAO;

	@Mock
	private GWConfigurationService configurationService;

	private MockedStatic<SpringUtils> springUtilsMockedStatic;
	private GatewayDao gatewayDao;

	@BeforeEach
	void beforeEach() {
		springUtilsMockedStatic = mockStatic(SpringUtils.class);
		gatewayDao = Mockito.mock(GatewayDao.class);
		when(SpringUtils.getBean(GatewayDao.class)).thenReturn(gatewayDao);
	}

	@AfterEach
	void afterEach() {
		springUtilsMockedStatic.close();
	}

	private CaseManagementNote createTestNote() {
		val note = new CaseManagementNote();
		note.setRemoteSystemId(1);
		return note;
	}

	private void mockFeatureFlag(boolean enabled) {
		when(configurationService.isLinkFeatureEnabled(FeatureFlagEnum.CPP_ENABLED)).thenReturn(
				enabled);
	}

	@Test
	void givenRemoteCaseManagementNote_whenSaveRemote_thenCallSaveRemoteAndReturnTrue() {
		mockFeatureFlag(true);

		val note = createTestNote();
		val parent = new Demographic();

		assertTrue(caseManagementNoteDAO.saveRemote(note, parent));
		verify(gatewayDao).saveRemote(any(), any());
	}

	@Test
	void givenFeatureFlagDisabled_whenSaveRemote_thenReturnFalse() {
		mockFeatureFlag(false);

		val note = createTestNote();
		val parent = new Demographic();

		assertFalse(caseManagementNoteDAO.saveRemote(note, parent));
		verifyNoInteractions(gatewayDao);
	}

	@Test
	void givenBadConnectionRemoteCaseManagementNote_whenSaveRemote_thenLogExceptionAndReturnFalse() {
		mockFeatureFlag(true);

		Mockito.doThrow(new RuntimeException("bad connection"))
				.when(gatewayDao).saveRemote(any(), any());

		val note = createTestNote();
		val parent = new Demographic();

		assertFalse(caseManagementNoteDAO.saveRemote(note, parent));
		verify(gatewayDao).saveRemote(any(), any());
	}
}
