/**
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for the
 * Department of Family Medicine
 * McMaster University
 * Hamilton
 * Ontario, Canada
 */
/**
 * <AUTHOR>
 */
package org.oscarehr.common.dao;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.oscarehr.common.dao.utils.SchemaUtils;
import org.oscarehr.common.model.Hl7TextInfo;
import org.oscarehr.common.model.PatientLabRouting;
import org.oscarehr.util.SpringUtils;
import oscar.OscarProperties;

public class Hl7TextInfoDaoTest extends DaoTestFixtures {

	// Constants for test data
	private static final Integer DEMOGRAPHIC_ID = 100;
	private static final String LAB_TYPE = "HL7";

	// Test data for labs
	private static final int LAB_NUMBER_1 = 1001;
	private static final int LAB_NUMBER_2 = 1002;
	private static final int LAB_NUMBER_3 = 1003;

	protected Hl7TextInfoDao dao = SpringUtils.getBean(Hl7TextInfoDao.class);
	private final PatientLabRoutingDao patientLabRoutingDao =
			SpringUtils.getBean(PatientLabRoutingDao.class);

	@Before
	public void before() throws Exception {
		SchemaUtils.restoreTable("hl7TextInfo", "patientLabRouting", "hl7TextInfo",
				"providerLabRouting", "ctl_document", "demographic", "hl7TextMessage", "SystemPreferences");
	}

	@Test
	public void testCreateUpdateLabelByLabNumber() {
		dao.createUpdateLabelByLabNumber("10", 10);
	}

	@Test
	public void testFindByDemographicId() {
		dao.findByDemographicId(10);
	}

	@Test
	public void testFindByHealthCardNo() {
		dao.findByHealthCardNo("HIN");
	}

	@Test
	public void testFindByLabId() {
		dao.findByLabId(10);
	}

	@Test
	public void testfindByLabIdViaMagic() {
		dao.findByLabIdViaMagic(10);
	}

	@Test
	public void testfindLabAndDocsViaMagic() {
		Integer page = 0;
		Integer pageSize = 10;

		boolean isPaged, mixLabsAndDocs, isAbnormal, searchProvider, patientSearch;
		boolean[] truthTable = new boolean[] {
				true,   true,   true,   true,   true,
				true, 	true, 	true, 	true, 	false,
				true, 	true, 	true, 	false, 	true,
				true, 	true, 	true, 	false, 	false,
				true, 	true, 	false, 	true, 	true,
				true, 	true, 	false, 	true, 	false,
				true, 	true, 	false, 	false, 	true,
				true, 	true, 	false, 	false, 	false,
				true, 	false, 	true, 	true, 	true,
				true, 	false, 	true, 	true, 	false,
				true, 	false, 	true, 	false, 	true,
				true, 	false, 	true, 	false, 	false,
				true, 	false, 	false, 	true, 	true,
				true, 	false, 	false, 	true, 	false,
				true, 	false, 	false, 	false, 	true,
				true, 	false, 	false, 	false, 	false,
				false, 	true, 	true, 	true, 	true,
				false, 	true, 	true, 	true, 	false,
				false, 	true, 	true, 	false, 	true,
				false, 	true, 	true, 	false, 	false,
				false, 	true, 	false, 	true, 	true,
				false, 	true, 	false, 	true, 	false,
				false, 	true, 	false, 	false, 	true,
				false, 	true, 	false, 	false, 	false,
				false, 	false, 	true, 	true, 	true,
				false, 	false, 	true, 	true, 	false,
				false, 	false, 	true, 	false, 	true,
				false, 	false, 	true, 	false, 	false,
				false, 	false, 	false, 	true, 	true,
				false, 	false, 	false, 	true, 	false,
				false, 	false, 	false, 	false, 	true,
				false, 	false, 	false, 	false, 	false
			};

		for (int i = 0; i < truthTable.length; i = i + 5) {
			isPaged = truthTable[i];
			mixLabsAndDocs = truthTable[i + 1];
			isAbnormal = truthTable[i + 2];
			searchProvider = truthTable[i + 3];
			patientSearch = truthTable[i + 4];

			dao.findLabAndDocsViaMagic("PROVIDER", "DEMOGRAPHIC", "FNAME", "LNAME", "HIN", "STATUS", isPaged, page, pageSize, mixLabsAndDocs, isAbnormal, searchProvider, patientSearch);
			dao.findLabAndDocsViaMagic("0", "0", "", "", "", "", isPaged, page, pageSize, mixLabsAndDocs, isAbnormal, searchProvider, patientSearch);
		}
	}

	@Test
	public void testfindLabId() {
		dao.findLabId(0);
	}

	@Test
	public void testfindLabsViaMagic() {
		dao.findLabsViaMagic("GVNO", "GVNO", "GVNO", "GVNO", "GVNO");
	}

	@Test
	public void testgetAllLabsByLabNumberResultStatus() {
		dao.getAllLabsByLabNumberResultStatus();
	}

	@Test
	public void testgetMatchingLabs() {
		dao.getMatchingLabs("BLYA");
	}

	@Test
	public void testsearchByAccessionNumber() {
		dao.searchByAccessionNumber("PZDTS");
	}

	@Test
	public void testsearchByFillerOrderNumber() {
		dao.searchByFillerOrderNumber("PRSHA", "ZHPA");
	}

	@Test
	public void testupdateReportStatusByLabId() {
		dao.updateReportStatusByLabId("STR", 0);
	}

	@Test
	public void testupdateResultStatusByLabId() {
		dao.updateResultStatusByLabId("STS", 0);
	}

   	@Test
    public void test() {
	    assertNotNull(dao.findDisciplines(100));
    }

	@Test
	public void givenDemographicWithLabs_whenFindByDemographicIdForEChartDisplay_thenReturnsAllLabs() {
		setupFindByDemographicIdForEChartDisplayTestData();

		List<Object[]> results = dao.findByDemographicIdForEChartDisplay(DEMOGRAPHIC_ID, null);

		assertNotNull(results);
		assertEquals(3, results.size());

		// Ensure results are sorted properly
		// First result should be LAB_NUMBER_3 (newest date: 2023-01-03)
		Hl7TextInfo firstLab = (Hl7TextInfo) results.get(0)[0];
		assertEquals("First lab should be LAB_NUMBER_3 with newest date", LAB_NUMBER_3,
				firstLab.getLabNumber());

		// Second result should be LAB_NUMBER_2 (middle date: 2023-01-02)
		Hl7TextInfo secondLab = (Hl7TextInfo) results.get(1)[0];
		assertEquals("Second lab should be LAB_NUMBER_2 with middle date", LAB_NUMBER_2,
				secondLab.getLabNumber());

		// Third result should be LAB_NUMBER_1 (oldest date: 2023-01-01)
		Hl7TextInfo thirdLab = (Hl7TextInfo) results.get(2)[0];
		assertEquals("Third lab should be LAB_NUMBER_1 with oldest date", LAB_NUMBER_1,
				thirdLab.getLabNumber());
	}

	@Test
	public void givenDemographicWithLabsAndLimit_whenFindByDemographicIdForEChartDisplay_thenReturnsLimitedLabs() {
		setupFindByDemographicIdForEChartDisplayTestData();
		List<Object[]> results = dao.findByDemographicIdForEChartDisplay(DEMOGRAPHIC_ID, 2);
		assertNotNull(results);
		assertEquals(2, results.size());

		// Verify the limited results maintain the correct order
		// First result should be LAB_NUMBER_3 (newest date: 2023-01-03)
		Hl7TextInfo firstLab = (Hl7TextInfo) results.get(0)[0];
		assertEquals("First lab should be LAB_NUMBER_3 with newest date", LAB_NUMBER_3,
				firstLab.getLabNumber());

		// Second result should be LAB_NUMBER_2 (middle date: 2023-01-02)
		Hl7TextInfo secondLab = (Hl7TextInfo) results.get(1)[0];
		assertEquals("Second lab should be LAB_NUMBER_2 with middle date", LAB_NUMBER_2,
				secondLab.getLabNumber());
	}

	@Test
	public void givenDemographicWithNoLabs_whenFindByDemographicIdForEChartDisplay_thenReturnsEmptyList() {
		// No setup needed, we're testing for a demographic with no labs
		List<Object[]> results = dao.findByDemographicIdForEChartDisplay(999, null);
		assertNotNull(results);
		assertTrue(results.isEmpty());
	}

	@Test
	public void givenDemographicWithAbnormalLabs_whenFindByDemographicIdForEChartDisplay_thenReturnsAbnormalLabsFirst() {
		setupFindByDemographicIdForEChartDisplayTestData();

		// Save original property value
		String originalValue = OscarProperties.getInstance().getProperty("abnormal_labs_first");

		try {
			// Set abnormal_labs_first property to true
			OscarProperties.getInstance().setProperty("abnormal_labs_first", "true");

			// When
			List<Object[]> results = dao.findByDemographicIdForEChartDisplay(DEMOGRAPHIC_ID, null);

			// Then
			assertNotNull(results);
			assertEquals(3, results.size());

			// Verify abnormal labs come first
			// First result should be LAB_NUMBER_3
			Hl7TextInfo firstLab = (Hl7TextInfo) results.get(0)[0];
			assertEquals("A", firstLab.getResultStatus());
      assertEquals("First lab should be abnormal LAB_NUMBER_3 as it has the newest obr date",
          LAB_NUMBER_3, firstLab.getLabNumber());

			// Second result should be LAB_NUMBER_1 (also abnormal)
			Hl7TextInfo secondLab = (Hl7TextInfo) results.get(1)[0];
			assertEquals("A", secondLab.getResultStatus());
      assertEquals(
          "Second lab should be abnormal LAB_NUMBER_1 as it has the second newest obr date",
          LAB_NUMBER_1, secondLab.getLabNumber());

			// Third result should be the normal lab (LAB_NUMBER_2 with resultStatus=null)
			Hl7TextInfo thirdLab = (Hl7TextInfo) results.get(2)[0];
			assertNull("Third lab should be normal (resultStatus=null)", thirdLab.getResultStatus());
			assertEquals("Third lab should be LAB_NUMBER_2 with the oldest obr date", LAB_NUMBER_2,
					thirdLab.getLabNumber());
		} finally {
			// Restore original property value
			if (originalValue == null) {
				OscarProperties.getInstance().remove("abnormal_labs_first");
			} else {
				OscarProperties.getInstance().setProperty("abnormal_labs_first", originalValue);
			}
		}
	}

	@Test
	public void givenDemographicWithAbnormalLabsAndLimit_whenFindByDemographicIdForEChartDisplay_thenReturnsLimitedAbnormalLabsFirst() {
		// Given
		setupFindByDemographicIdForEChartDisplayTestData();

		// Save original property value
		String originalValue = OscarProperties.getInstance().getProperty("abnormal_labs_first");

		try {
			// Set abnormal_labs_first property to true
			OscarProperties.getInstance().setProperty("abnormal_labs_first", "true");

			// When - use a limit of 2 to get only the first two labs
			List<Object[]> results = dao.findByDemographicIdForEChartDisplay(DEMOGRAPHIC_ID, 2);

			// Then
			assertNotNull(results);
			assertEquals(2, results.size());

			// Verify abnormal labs come first and are limited to 2
			// First result should be LAB_NUMBER_3 (newest date: 2023-01-03)
			Hl7TextInfo firstLab = (Hl7TextInfo) results.get(0)[0];
			assertEquals("A", firstLab.getResultStatus());
			assertEquals("First lab should be abnormal LAB_NUMBER_3 as it has the newest obr date",
					LAB_NUMBER_3, firstLab.getLabNumber());

			// Second result should be LAB_NUMBER_1 (also abnormal)
			Hl7TextInfo secondLab = (Hl7TextInfo) results.get(1)[0];
			assertEquals("A", secondLab.getResultStatus());
			assertEquals("Second lab should be abnormal LAB_NUMBER_1 as it has the second newest obr date",
					LAB_NUMBER_1, secondLab.getLabNumber());
		} finally {
			// Restore original property value
			if (originalValue == null) {
				OscarProperties.getInstance().remove("abnormal_labs_first");
			} else {
				OscarProperties.getInstance().setProperty("abnormal_labs_first", originalValue);
			}
		}
	}

	private void setupFindByDemographicIdForEChartDisplayTestData() {
		// Create and persist Hl7TextInfo objects
		persistHl7TextInfo(LAB_NUMBER_1, "2023-01-01", "A", 5);
		persistHl7TextInfo(LAB_NUMBER_2, "2023-01-02", null, 3);
		persistHl7TextInfo(LAB_NUMBER_3, "2023-01-03", "A", 1);

		// Create and persist PatientLabRouting objects
		persistPatientLabRouting(LAB_NUMBER_1, LAB_TYPE, DEMOGRAPHIC_ID);
		persistPatientLabRouting(LAB_NUMBER_2, LAB_TYPE, DEMOGRAPHIC_ID);
		persistPatientLabRouting(LAB_NUMBER_3, LAB_TYPE, DEMOGRAPHIC_ID);
	}

	private void persistHl7TextInfo(int labNumber, String obrDate, String resultStatus,
			int finalResultCount) {
		Hl7TextInfo hl7TextInfo = createHl7TextInfo(labNumber, obrDate, resultStatus, finalResultCount);
		dao.persist(hl7TextInfo);
	}

	private void persistPatientLabRouting(int labNo, String labType, Integer demographicNo) {
		PatientLabRouting patientLabRouting = createPatientLabRouting(labNo, labType, demographicNo);
		patientLabRoutingDao.persist(patientLabRouting);
	}

	/**
	 * Helper method to create an Hl7TextInfo object
	 */
	private Hl7TextInfo createHl7TextInfo(int labNumber, String obrDate, String resultStatus,
			int finalResultCount) {
		Hl7TextInfo hl7TextInfo = new Hl7TextInfo();
		hl7TextInfo.setLabNumber(labNumber);
		hl7TextInfo.setObrDate(obrDate);
		hl7TextInfo.setResultStatus(resultStatus);
		hl7TextInfo.setFinalResultCount(finalResultCount);
		hl7TextInfo.setReportStatus("F"); // Set a default report status
		return hl7TextInfo;
	}

	/**
	 * Helper method to create a PatientLabRouting object
	 */
	private PatientLabRouting createPatientLabRouting(int labNo, String labType,
			Integer demographicNo) {
		PatientLabRouting patientLabRouting = new PatientLabRouting(labNo, labType, demographicNo);
		return patientLabRouting;
	}
}
