/**
 * Copyright (c) 2025 WELL EMR Group Inc. This software is made available under the terms of the GNU
 * General Public License, Version 2, 1991 (GPLv2). License details are available via
 * "gnu.org/licenses/gpl-2.0.html".
 */

package org.oscarehr.common.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.PMmodule.dao.ProgramDao;
import org.oscarehr.casemgmt.dao.CaseManagementNoteLinkDAO;
import org.oscarehr.casemgmt.model.CaseManagementNote;
import org.oscarehr.common.dao.CaseManagementNoteJpaDao;
import org.oscarehr.common.dao.SecRoleDao;
import org.oscarehr.common.model.CaseManagementNoteJpa;
import org.oscarehr.util.SpringUtils;

@ExtendWith(MockitoExtension.class)
public class CaseManagementNoteServiceTest {

  @Mock
  private CaseManagementNoteJpaDao caseManagementNoteJpaDao;

  @Mock
  private SecRoleDao secRoleDao;

  @Mock
  private ProgramDao programDao;

  @Mock
  private CaseManagementNoteLinkDAO caseManagementNoteLinkDao;

  @InjectMocks
  private CaseManagementNoteService caseManagementNoteService;

  private static final String UUID = "12345678-1234-1234-1234-1234567890ab";
  private static final String ROLE_ID = "doctor";
  private static final String ROLE_NAME = "Doctor";
  private static final String PROGRAM_NUMBER = "1";
  private static final String PROGRAM_NAME = "Test Program";

  private MockedStatic<SpringUtils> springUtilsMockedStatic;

  @BeforeEach
  public void setup() {
    springUtilsMockedStatic = mockStatic(SpringUtils.class);
    mockCaseManagementNoteLinkDao();
  }

  private void mockCaseManagementNoteLinkDao() {
    springUtilsMockedStatic.when(new Verification() {
      @Override
      public void apply() throws Throwable {
        SpringUtils.getBean("CaseManagementNoteLinkDAO");
      }
    }).thenReturn(caseManagementNoteLinkDao);
  }

  @AfterEach
  public void tearDown() {
    if (springUtilsMockedStatic != null) {
      springUtilsMockedStatic.close();
    }
  }

  @Test
  public void givenNullNotes_whenConvertJpaNotesToCaseManagementNotes_thenReturnEmptyList() {
    List<CaseManagementNote> result = caseManagementNoteService
        .convertJpaNotesToCaseManagementNotes(null);
    assertNotNull(result, "Result should not be null");
    assertTrue(result.isEmpty(), "Result should be empty");
  }

  @Test
  public void givenEmptyNotes_whenConvertJpaNotesToCaseManagementNotes_thenReturnEmptyList() {
    List<CaseManagementNote> result = caseManagementNoteService
        .convertJpaNotesToCaseManagementNotes(Collections.emptyList());
    assertNotNull(result, "Result should not be null");
    assertTrue(result.isEmpty(), "Result should be empty");
  }

  @Test
  public void givenSingleJpaNote_whenConvertJpaNotesToCaseManagementNotes_thenReturnListWithOneNote() {
    // Setup mocks needed for this test
    setupMocksForSingleItem();

    // Create a test CaseManagementNoteJpa
    CaseManagementNoteJpa jpaNote = createCaseManagementNoteJpa();

    // Convert the note
    List<CaseManagementNote> result = caseManagementNoteService
        .convertJpaNotesToCaseManagementNotes(Collections.singletonList(jpaNote));

    // Verify the result
    assertNotNull(result, "Result should not be null");
    assertEquals(1, result.size(), "Result should contain 1 item");

    CaseManagementNote note = result.get(0);
    assertNotNull(note, "Converted note should not be null");
    assertEquals(jpaNote.getId(), note.getId(), "ID should match");
    assertEquals(jpaNote.getUuid(), note.getUuid(), "UUID should match");
    assertEquals(jpaNote.getDemographicNumber(), note.getDemographic_no(),
        "Demographic number should match");
    assertEquals(jpaNote.getNote(), note.getNote(), "Note content should match");
    assertEquals(jpaNote.getProviderNumber(), note.getProviderNo(), "Provider number should match");
    assertEquals(jpaNote.getProgramNumber(), note.getProgram_no(), "Program number should match");
    assertEquals(PROGRAM_NAME, note.getProgramName(), "Program name should be set");
    assertEquals("1", note.getRevision(), "Revision should be set");
    assertEquals(ROLE_NAME, note.getRoleName(), "Role name should be set");
  }

  private void setupMocksForSingleItem() {
    Map<String, Integer> revisionCountMap = new HashMap<>();
    revisionCountMap.put(UUID, 1);
    when(caseManagementNoteJpaDao.getRevisionCountsAsMap(any())).thenReturn(revisionCountMap);

    Map<String, String> roleNameMap = new HashMap<>();
    roleNameMap.put(ROLE_ID, ROLE_NAME);
    when(secRoleDao.getRoleNamesAsMap(any())).thenReturn(roleNameMap);

    Map<String, String> programTeamNameMap = new HashMap<>();
    programTeamNameMap.put(PROGRAM_NUMBER, PROGRAM_NAME);
    when(programDao.getProgramTeamNamesAsMap(any())).thenReturn(programTeamNameMap);

    Map<String, Date> createDateMap = new HashMap<>();
    createDateMap.put(UUID, new Date());
    when(caseManagementNoteJpaDao.getCreateDatesAsMap(any())).thenReturn(createDateMap);
  }

  @Test
  public void givenMultipleNotes_whenConvertJpaNotesToCaseManagementNotes_thenReturnListWithMultipleNotes() {
    // Create test CaseManagementNoteJpa objects
    CaseManagementNoteJpa jpaNote1 = createCaseManagementNoteJpa();

    CaseManagementNoteJpa jpaNote2 = createCaseManagementNoteJpa();
    jpaNote2.setId(2L);
    jpaNote2.setUuid("*************-9876-9876-************");
    jpaNote2.setDemographicNumber("2");
    jpaNote2.setNote("Test note 2");

    List<CaseManagementNoteJpa> jpaNotes = new ArrayList<>();
    jpaNotes.add(jpaNote1);
    jpaNotes.add(jpaNote2);

    // Setup mocks needed for this test
    setupMocksForMultipleItems(jpaNote2.getUuid());

    // Convert the notes
    List<CaseManagementNote> result = caseManagementNoteService.convertJpaNotesToCaseManagementNotes(
        jpaNotes);

    // Verify the result
    assertNotNull(result, "Result should not be null");
    assertEquals(2, result.size(), "Result should contain 2 items");

    // Verify first note
    CaseManagementNote note1 = result.get(0);
    assertNotNull(note1, "First converted note should not be null");
    assertEquals(jpaNote1.getId(), note1.getId(), "ID should match for first note");
    assertEquals(jpaNote1.getUuid(), note1.getUuid(), "UUID should match for first note");

    // Verify second note
    CaseManagementNote note2 = result.get(1);
    assertNotNull(note2, "Second converted note should not be null");
    assertEquals(jpaNote2.getId(), note2.getId(), "ID should match for second note");
    assertEquals(jpaNote2.getUuid(), note2.getUuid(), "UUID should match for second note");
    assertEquals("2", note2.getRevision(), "Revision should be set correctly for second note");
  }

  private void setupMocksForMultipleItems(final String secondUuid) {
    Map<String, Integer> revisionCountMap = new HashMap<>();
    revisionCountMap.put(UUID, 1);
    revisionCountMap.put(secondUuid, 2);
    when(caseManagementNoteJpaDao.getRevisionCountsAsMap(any())).thenReturn(revisionCountMap);

    Map<String, String> roleNameMap = new HashMap<>();
    roleNameMap.put(ROLE_ID, ROLE_NAME);
    when(secRoleDao.getRoleNamesAsMap(any())).thenReturn(roleNameMap);

    Map<String, String> programTeamNameMap = new HashMap<>();
    programTeamNameMap.put(PROGRAM_NUMBER, PROGRAM_NAME);
    when(programDao.getProgramTeamNamesAsMap(any())).thenReturn(programTeamNameMap);

    Map<String, Date> createDateMap = new HashMap<>();
    createDateMap.put(UUID, new Date());
    createDateMap.put(secondUuid, new Date());
    when(caseManagementNoteJpaDao.getCreateDatesAsMap(any())).thenReturn(createDateMap);
  }

  private CaseManagementNoteJpa createCaseManagementNoteJpa() {
    CaseManagementNoteJpa jpaNote = new CaseManagementNoteJpa();
    jpaNote.setId(1L);
    jpaNote.setUuid(UUID);
    jpaNote.setDemographicNumber("1");
    jpaNote.setNote("Test note");
    jpaNote.setHistory("Test history");
    jpaNote.setUpdateDate(new Date());
    jpaNote.setObservationDate(new Date());
    jpaNote.setProgramNumber(PROGRAM_NUMBER);
    jpaNote.setProviderNumber("999998");
    jpaNote.setSigningProviderNo("999998");
    jpaNote.setReporterCaisiRole(ROLE_ID);
    jpaNote.setReporterProgramTeam("Test Team");
    jpaNote.setBillingCode("Test Billing Code");
    jpaNote.setEncounterType("Test Encounter Type");
    jpaNote.setSigned(true);
    jpaNote.setIncludeIssue(true);
    return jpaNote;
  }

  @Test
  public void givenNotesWithNullOrEmptyUuid_whenConvertJpaNotesToCaseManagementNotes_thenFilterOutInvalidNotes() {
    // Setup mocks needed for this test
    setupMocksForSingleItem();

    // Create a valid note
    CaseManagementNoteJpa validNote = createCaseManagementNoteJpa();

    // Create a note with null UUID
    CaseManagementNoteJpa nullUuidNote = createCaseManagementNoteJpa();
    nullUuidNote.setId(2L);
    nullUuidNote.setUuid(null);

    // Create a note with empty UUID
    CaseManagementNoteJpa emptyUuidNote = createCaseManagementNoteJpa();
    emptyUuidNote.setId(3L);
    emptyUuidNote.setUuid("");

    // Create a list with all notes
    List<CaseManagementNoteJpa> jpaNotes = new ArrayList<>();
    jpaNotes.add(validNote);
    jpaNotes.add(nullUuidNote);
    jpaNotes.add(emptyUuidNote);

    // Convert the notes
    List<CaseManagementNote> result = caseManagementNoteService
        .convertJpaNotesToCaseManagementNotes(jpaNotes);

    // Verify the result
    assertNotNull(result, "Result should not be null");
    assertEquals(1, result.size(), "Result should contain only the valid note");

    // Verify the valid note was converted correctly
    CaseManagementNote note = result.get(0);
    assertEquals(validNote.getId(), note.getId(), "ID should match");
    assertEquals(validNote.getUuid(), note.getUuid(), "UUID should match");
  }

  @Test
  public void givenNotesWithInvalidProgramNumbers_whenConvertJpaNotesToCaseManagementNotes_thenFilterInvalidProgramNumbers() {
    // Setup mocks for a valid note
    setupMocksForSingleItem();

    // Create a note with a valid program number
    CaseManagementNoteJpa validProgramNote = createCaseManagementNoteJpa();

    // Create a note with a null program number
    CaseManagementNoteJpa nullProgramNote = createCaseManagementNoteJpa();
    nullProgramNote.setId(2L);
    nullProgramNote.setUuid("*************-2222-2222-************");
    nullProgramNote.setProgramNumber(null);

    // Create a note with an empty program number
    CaseManagementNoteJpa emptyProgramNote = createCaseManagementNoteJpa();
    emptyProgramNote.setId(3L);
    emptyProgramNote.setUuid("*************-3333-3333-************");
    emptyProgramNote.setProgramNumber("");

    // Create a note with a non-numeric program number
    CaseManagementNoteJpa nonNumericProgramNote = createCaseManagementNoteJpa();
    nonNumericProgramNote.setId(4L);
    nonNumericProgramNote.setUuid("*************-4444-4444-************");
    nonNumericProgramNote.setProgramNumber("abc");

    // Create a list with all notes
    List<CaseManagementNoteJpa> jpaNotes = new ArrayList<>();
    jpaNotes.add(validProgramNote);
    jpaNotes.add(nullProgramNote);
    jpaNotes.add(emptyProgramNote);
    jpaNotes.add(nonNumericProgramNote);

    // Setup additional mocks for the extra notes
    Map<String, Integer> revisionCountMap = new HashMap<>();
    revisionCountMap.put(UUID, 1);
    revisionCountMap.put("*************-2222-2222-************", 1);
    revisionCountMap.put("*************-3333-3333-************", 1);
    revisionCountMap.put("*************-4444-4444-************", 1);
    when(caseManagementNoteJpaDao.getRevisionCountsAsMap(any())).thenReturn(revisionCountMap);

    Map<String, Date> createDateMap = new HashMap<>();
    createDateMap.put(UUID, new Date());
    createDateMap.put("*************-2222-2222-************", new Date());
    createDateMap.put("*************-3333-3333-************", new Date());
    createDateMap.put("*************-4444-4444-************", new Date());
    when(caseManagementNoteJpaDao.getCreateDatesAsMap(any())).thenReturn(createDateMap);

    // Convert the notes
    caseManagementNoteService.convertJpaNotesToCaseManagementNotes(jpaNotes);

    // Verify that only the valid program number was passed to getProgramTeamNamesAsMap
    // We can't directly test the private getProgramNumberSet method, but we can verify
    // that only valid program numbers were passed to the DAO method
    verify(programDao).getProgramTeamNamesAsMap(
        Collections.singleton(Integer.parseInt(PROGRAM_NUMBER)));
  }
}
