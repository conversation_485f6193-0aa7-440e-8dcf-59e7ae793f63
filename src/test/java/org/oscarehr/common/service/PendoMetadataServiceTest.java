/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.common.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import javax.servlet.http.HttpSession;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.common.model.PendoAccountData;
import org.oscarehr.common.model.PendoVisitorData;
import org.oscarehr.common.model.Provider;
import org.oscarehr.config.JunoProperties;
import org.oscarehr.config.JunoProperties.PendoConfig;
import org.oscarehr.preferences.service.SystemPreferenceService;
import oscar.OscarProperties;

@ExtendWith(MockitoExtension.class)
public class PendoMetadataServiceTest {

	private static final String EMPTY = "";
	private static final String PRACTICE_ID = "test-practice";
	private static final String EMR_VERSION = "1.0.0";
	private static final String PROVINCE = "ON";
	private static final String PENDO_API_KEY = "test-api-key";
	private static final String USER_ATTRIBUTE = "testuser";
	private static final String USER_ROLE = "doctor";
	private static final String USER_FIRST_NAME = "Test";
	private static final String USER_LAST_NAME = "User";
	private static final String PROVIDER_TYPE = "doctor";
	private static final String ONTARIO_CNO_NUMBER = "CNO123456";
	private static final String ALBERTA_TAK_NUMBER = "TAK123456";
	private static final String PRACTITIONER_NUMBER = "CPSO123456";
	private static final String PENDO_ENABLED_KEY = "pendo.enabled";
	private static final String USER_ROLE_ATTRIBUTE_KEY = "userrole";
	private static final String USER_FIRST_NAME_ATTRIBUTE_KEY = "userfirstname";
	private static final String USER_LAST_NAME_ATTRIBUTE_KEY = "userlastname";
	private static final String BUILD_TAG_KEY = "buildtag";
	private static final String DB_USERNAME_KEY = "db_username";
	private static final String VISITOR_ID_DELIMITER = "#$#$SUFFIX#$#$";

	@Mock
	private HttpSession mockSession;
	@Mock
	private Provider mockProvider;
	@Mock
	private OscarProperties mockOscarProperties;
	@Mock
	private SystemPreferenceService mockSystemPreferenceService;
	@Mock
	private JunoProperties mockJunoProperties;
	@Mock
	private PendoConfig mockPendoConfig;

	private PendoMetadataService pendoMetadataService;

	/**
	 * A test subclass of PendoMetadataService that overrides the getEnvironmentVariable method
	 * to return the test practice ID.
	 */
	private static class TestPendoMetadataService extends PendoMetadataService {
		public TestPendoMetadataService(final JunoProperties junoProperties,
									   final SystemPreferenceService systemPreferenceService,
									   final OscarProperties oscarProperties) {
			super(junoProperties, systemPreferenceService, oscarProperties);
		}

		@Override
		protected String getEnvironmentVariable(String name) {
			if ("PRACTICE_ID".equals(name)) {
				return PRACTICE_ID;
			}
			return super.getEnvironmentVariable(name);
		}
	}

	@BeforeEach
	public void setUp() {
		lenient().when(mockJunoProperties.getPendoConfig()).thenReturn(mockPendoConfig);
		lenient().when(mockPendoConfig.getApiKey()).thenReturn(PENDO_API_KEY);

		lenient().when(mockOscarProperties.getProperty(eq(BUILD_TAG_KEY), anyString()))
			.thenReturn(EMR_VERSION);
		lenient().when(mockOscarProperties.getInstanceType()).thenReturn(PROVINCE);

		lenient().when(
				mockSystemPreferenceService.isPreferenceEnabled(eq(PENDO_ENABLED_KEY), eq(false)))
			.thenReturn(true);

		lenient().when(mockSession.getAttribute("user")).thenReturn(USER_ATTRIBUTE);
		lenient().when(mockSession.getAttribute(USER_ROLE_ATTRIBUTE_KEY)).thenReturn(USER_ROLE);
		lenient().when(mockSession.getAttribute(USER_FIRST_NAME_ATTRIBUTE_KEY))
			.thenReturn(USER_FIRST_NAME);
		lenient().when(mockSession.getAttribute(USER_LAST_NAME_ATTRIBUTE_KEY))
			.thenReturn(USER_LAST_NAME);

		lenient().when(mockProvider.getProviderType()).thenReturn(PROVIDER_TYPE);
		lenient().when(mockProvider.getSuperAdmin()).thenReturn(false);
		lenient().when(mockProvider.getOntarioCnoNumber()).thenReturn(ONTARIO_CNO_NUMBER);
		lenient().when(mockProvider.getAlbertaTakNo()).thenReturn(ALBERTA_TAK_NUMBER);
		lenient().when(mockProvider.getPractitionerNo()).thenReturn(PRACTITIONER_NUMBER);

		pendoMetadataService = new TestPendoMetadataService(mockJunoProperties,
															mockSystemPreferenceService,
															mockOscarProperties);
	}


	@Test
	public void givenPendoService_whenCheckingIfEnabled_thenReturnsCorrectValue() {
		assertTrue(pendoMetadataService.isPendoEnabled());

		when(mockSystemPreferenceService.isPreferenceEnabled(eq(PENDO_ENABLED_KEY), eq(false)))
			.thenReturn(false);
		assertFalse(pendoMetadataService.isPendoEnabled());
	}

	@Test
	public void givenPendoService_whenGettingApiKey_thenReturnsCorrectKey() {
		assertEquals(PENDO_API_KEY, pendoMetadataService.getPendoApiKey());
	}

	@Test
	public void givenValidProvider_whenGettingVisitorData_thenReturnsCorrectData() {
		PendoVisitorData visitorData = pendoMetadataService.getVisitorData(mockProvider,
																		   mockSession);

		assertEquals(PRACTICE_ID + VISITOR_ID_DELIMITER + USER_ATTRIBUTE, visitorData.getVisitorId());
		assertEquals(USER_FIRST_NAME + " " + USER_LAST_NAME, visitorData.getFullName());
		assertEquals(USER_ROLE, visitorData.getRole());
		assertFalse(visitorData.isSuperUser());
		assertEquals(PROVIDER_TYPE, visitorData.getProviderType());
		assertTrue(visitorData.isHasCollegeNumber());
		assertEquals(ONTARIO_CNO_NUMBER, visitorData.getCollegeType());
	}

	@Test
	public void givenNullProvider_whenGettingVisitorData_thenReturnsDataWithDefaults() {
		PendoVisitorData visitorData = pendoMetadataService.getVisitorData(null, mockSession);

		assertEquals(PRACTICE_ID + VISITOR_ID_DELIMITER + USER_ATTRIBUTE, visitorData.getVisitorId());
		assertEquals(USER_FIRST_NAME + " " + USER_LAST_NAME, visitorData.getFullName());
		assertEquals(USER_ROLE, visitorData.getRole());
		assertFalse(visitorData.isSuperUser());
		assertEquals(EMPTY, visitorData.getProviderType());
		assertFalse(visitorData.isHasCollegeNumber());
		assertEquals(EMPTY, visitorData.getCollegeType());
	}

	@Test
	public void givenSuperAdminProvider_whenGettingVisitorData_thenReturnsSuperUserFlag() {
		when(mockProvider.getSuperAdmin()).thenReturn(true);

		PendoVisitorData visitorData = pendoMetadataService.getVisitorData(mockProvider,
																		   mockSession);

		assertTrue(visitorData.isSuperUser());
	}

	@Test
	public void givenProviderWithNullSuperAdmin_whenGettingVisitorData_thenReturnsFalse() {
		when(mockProvider.getSuperAdmin()).thenReturn(null);

		PendoVisitorData visitorData = pendoMetadataService.getVisitorData(mockProvider,
																		   mockSession);

		assertFalse(visitorData.isSuperUser());
	}

	@Test
	public void givenProviderWithNoCollegeNumbers_whenGettingVisitorData_thenReturnsEmptyData() {
		when(mockProvider.getOntarioCnoNumber()).thenReturn(null);
		when(mockProvider.getAlbertaTakNo()).thenReturn(null);
		when(mockProvider.getPractitionerNo()).thenReturn(null);

		PendoVisitorData visitorData = pendoMetadataService.getVisitorData(mockProvider,
																		   mockSession);

		assertFalse(visitorData.isHasCollegeNumber());
		assertEquals(EMPTY, visitorData.getCollegeType());
	}

	@Test
	public void givenProviderWithOnlyAlbertaTak_whenGettingVisitorData_thenReturnsCorrectData() {
		when(mockProvider.getOntarioCnoNumber()).thenReturn(null);

		PendoVisitorData visitorData = pendoMetadataService.getVisitorData(mockProvider,
																		   mockSession);

		assertTrue(visitorData.isHasCollegeNumber());
		assertEquals(ALBERTA_TAK_NUMBER, visitorData.getCollegeType());
	}

	@Test
	public void givenProviderWithOnlyPractitioner_whenGettingVisitorData_thenReturnsCorrectData() {
		when(mockProvider.getOntarioCnoNumber()).thenReturn(null);
		when(mockProvider.getAlbertaTakNo()).thenReturn(null);

		PendoVisitorData visitorData = pendoMetadataService.getVisitorData(mockProvider,
																		   mockSession);

		assertTrue(visitorData.isHasCollegeNumber());
		assertEquals(PRACTITIONER_NUMBER, visitorData.getCollegeType());
	}

	@Test
	public void givenEmptySessionAttributes_whenGettingVisitorData_thenReturnsEmptyUserData() {
		when(mockSession.getAttribute(USER_FIRST_NAME_ATTRIBUTE_KEY)).thenReturn(null);
		when(mockSession.getAttribute(USER_LAST_NAME_ATTRIBUTE_KEY)).thenReturn(null);
		when(mockSession.getAttribute(USER_ROLE_ATTRIBUTE_KEY)).thenReturn(null);

		PendoVisitorData visitorData = pendoMetadataService.getVisitorData(mockProvider,
																		   mockSession);
		assertEquals(EMPTY, visitorData.getFullName());
		assertEquals(EMPTY, visitorData.getRole());
	}

	@Test
	public void givenPendoService_whenGettingAccountData_thenReturnsCorrectData() {
		PendoAccountData accountData = pendoMetadataService.getAccountData();

		assertEquals(PRACTICE_ID, accountData.getPracticeId());
		assertEquals(EMR_VERSION, accountData.getEmrVersion());
		assertEquals(PROVINCE, accountData.getProvince());
	}
}
