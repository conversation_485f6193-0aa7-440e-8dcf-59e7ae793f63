/**
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for the
 * Department of Family Medicine
 * McMaster University
 * Hamilton
 * Ontario, Canada
 */
package org.oscarehr.PMmodule.dao;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.junit.Before;
import org.junit.Test;
import org.oscarehr.PMmodule.model.Program;
import org.oscarehr.common.dao.DaoTestFixtures;
import org.oscarehr.common.dao.FacilityDao;
import org.oscarehr.common.dao.utils.SchemaUtils;
import org.oscarehr.common.model.Facility;
import org.oscarehr.util.SpringUtils;

public class ProgramDaoTest extends DaoTestFixtures {

	public ProgramDao dao = SpringUtils.getBean(ProgramDao.class);
	private final FacilityDao facilityDao = SpringUtils.getBean(FacilityDao.class);

	@Before
	public void before() throws Exception {
		SchemaUtils.restoreTable("program","admission","Facility","program_queue","lst_orgcd","secUserRole","provider");
	}

	@Test
	public void testGetActivePrograms() {
		assertNotNull(dao.getActivePrograms());
	}

	@Test
	public void givenProgramNumbers_whenGetProgramTeamNamesAsMapFromProgramDao_thenCorrectMapping() {
		// Create required facility for the programs
		Facility facility = createFacility();

		// Create a few programs with team names
		Program program1 = createProgram("Program 1", facility);
		Program program2 = createProgram("Program 2", facility);

		// Create a set of program numbers to query
		Set<Integer> programNumbers = new HashSet<>();
		programNumbers.add(program1.getId());
		programNumbers.add(program2.getId());

		// Run the test method
		Map<String, String> programTeamNamesMap = dao.getProgramTeamNamesAsMap(programNumbers);

		// Assert that the map contains entries for the program numbers
		assertNotNull("Program team names map should not be null", programTeamNamesMap);
		assertTrue("Map should contain program1's ID", 
				programTeamNamesMap.containsKey(program1.getId().toString()));
		assertTrue("Map should contain program2's ID", 
				programTeamNamesMap.containsKey(program2.getId().toString()));
		assertEquals("Program name should match", 
				"Program 1", programTeamNamesMap.get(program1.getId().toString()));
		assertEquals("Program name should match", 
				"Program 2", programTeamNamesMap.get(program2.getId().toString()));
	}

	private Facility createFacility() {
		Facility facility = new Facility();
		facility.setName("Test Facility");
		facility.setHic(true);
		facility.setDisabled(true);
		facility.setOrgId(1);
		facility.setSectorId(1);
		facility.setIntegratorEnabled(false);
		facility.setEnableIntegratedReferrals(false);
		facility.setEnableHealthNumberRegistry(false);
		facility.setAllowSims(false);
		facility.setEnableDigitalSignatures(true);
		facility.setOcanServiceOrgNumber("123456");
		facility.setEnableOcanForms(true);
		facility.setEnableCbiForm(false);
		facility.setEnableAnonymous(false);
		facility.setEnablePhoneEncounter(true);
		facility.setEnableGroupNotes(false);
		facility.setEnableEncounterTime(false);
		facility.setEnableEncounterTransportationTime(false);
		facility.setRxInteractionWarningLevel(0);
		facility.setDisplayAllVacancies(0);
		facilityDao.persist(facility);

		return facility;
	}

	private Program createProgram(final String name, final Facility facility) {
		Program program = new Program();
		program.setName(name);
		program.setMaxAllowed(10);
		program.setProgramStatus(Program.PROGRAM_STATUS_ACTIVE);
		program.setFacilityId(facility.getId());
		dao.saveProgram(program);

		return program;
	}
}
