package org.oscarehr.casemgmt.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.var;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import org.oscarehr.casemgmt.dao.CaseManagementNoteDAO;
import org.oscarehr.casemgmt.model.CaseManagementNote;
import org.oscarehr.casemgmt.service.impl.DefaultNoteService;
import org.oscarehr.casemgmt.web.NoteDisplayLocal;
import org.oscarehr.common.dao.BillingONCHeader1Dao;
import org.oscarehr.common.dao.BillingONOUReportDao;
import org.oscarehr.common.dao.CaseManagementIssueNotesDao;
import org.oscarehr.common.dao.UserPropertyDAO;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.managers.DemographicManager;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;

@ExtendWith(MockitoExtension.class)
public class DefaultNoteServiceTest {

  @Mock
  private CaseManagementNoteDAO caseManagementNoteDao;
  @Mock
  private DemographicManager demographicManager;
  @Mock
  private CaseManagementManager caseManagementManager;
  @Mock
  private UserPropertyDAO userPropertyDao;
  @Mock
  private BillingONCHeader1Dao billingONCHeader1Dao;
  @Mock
  private BillingONOUReportDao billingONOUReportDao;

  @InjectMocks
  private DefaultNoteService defaultNoteService = new DefaultNoteService();

  @Mock
  private CaseManagementIssueNotesDao caseManagementIssueNotesDao;
  MockedStatic<SpringUtils> springUtilsMockedStatic = mockStatic(SpringUtils.class);

  private static String LOCAL_NOTE_UUID = "uuid";
  private static Long LOCAL_OBSERVATION_TIME = 1724178721000L;

  private void mockCaseManagementNotesFetching() {

    CaseManagementNote note = new CaseManagementNote();
    note.setUuid(LOCAL_NOTE_UUID);
    note.setId(1L);
    note.setObservation_date(new Timestamp(LOCAL_OBSERVATION_TIME));

    List<CaseManagementNote> notes = new ArrayList<>();
    notes.add(note);
    mockCaseManagementNotesFetching(notes);

  }

  private void mockCaseManagementNotesFetching(List<CaseManagementNote> notes) {

    when(caseManagementNoteDao.getNotes(any())).thenReturn(notes);

    ArrayList<Map<String, Object>> list = new ArrayList<>();

    for (var note : notes) {
      list.add(new HashMap<String, Object>() {{
        put("providerNo", note.getProviderNo());
        put("observation_date", note.getObservation_date());
        put("id", note.getId());
        put("program_no", note.getProgram_no());
        put("uuid", note.getUuid());
        put("update_date", note.getUpdate_date());
        put("encounter_type", "face to face encounter with client");
      }});
    }
    when(caseManagementNoteDao.getRawNoteInfoMapByDemographic(any())).thenReturn(list);

    /*
     * There is an odd error that occurs when we use the simpler form:
     * thenAnswer(invocation -> invocation.getArgument(1));
     *
     * Other test suites will fail with the message:
     * Caused by: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to read
     * candidate component class: file [C:\repos\well-project\oscar\target\test-classes\org\oscarehr
     * \casemgmt\service\DefaultNoteServiceTest.class]; nested exception is
     * java.lang.ArrayIndexOutOfBoundsException: 22301
     */
    when(caseManagementManager.filterNotes1(any(), any(), any(), eq(true))).thenAnswer(
        new Answer<Object>() {
          public Object answer(InvocationOnMock invocation) {
            return invocation.getArgument(1);
          }
        });

    Demographic demographic = new Demographic();
    demographic.setPrimarySystemId(1);
    when(demographicManager.getDemographic(any())).thenReturn(demographic);

  }

  @BeforeEach
  public void beforeEach() {

    /*
     * Similar to above, there is an odd issue where lambdas cannot be used in this test class
     * without breaking other tests
     */
    springUtilsMockedStatic.when(new Verification() {
      public void apply() {
        SpringUtils.getBean("caseManagementIssueNotesDao");
      }
    }).thenReturn(caseManagementIssueNotesDao);
  }

  @AfterEach
  public void afterEach() {
    springUtilsMockedStatic.close();
  }

  /*
   * Given that a local note is mocked to appear in the database, show that when findNotes()
   * is called, the local note will appear in the results.
   */
  @Test
  public void givenLocalNote_whenFindNotes_thenNoteAppears() {
    mockCaseManagementNotesFetching();

    LoggedInInfo loggedInInfo = new LoggedInInfo();
    NoteSelectionCriteria criteria = new NoteSelectionCriteria();
    criteria.setDemographicId(1);
    criteria.setEncounterType("face to face encounter with client");

    NoteSelectionResult result = defaultNoteService.findNotes(loggedInInfo, criteria, true);

    assertEquals(1, result.getNotes().size());
    assertEquals(LOCAL_NOTE_UUID,
        ((NoteDisplayLocal) result.getNotes().get(0)).getCaseManagementNote().getUuid());

  }

  private static Object[] differentRemoteNoteStates() {
    return new Object[]{
        new Object[]{"positive control", false, false, "validRemoteUuid", true},
        new Object[]{"archived remote note", true, false, "validRemoteUuid", false},
        new Object[]{"locked remote note", false, true, "validRemoteUuid", false},
        new Object[]{"archived and locked remote note", true, true, "validRemoteUuid", false},
        new Object[]{"duplicate remote note", false, false, LOCAL_NOTE_UUID, false},
    };
  }

  @ParameterizedTest
  @MethodSource("differentRemoteNoteStates")
  public void givenDifferentRemoteNoteStates_whenFindNotes_thenRemoteNoteAppears(String description,
      boolean isArchived, boolean isLocked, String remoteUuid, boolean expectedRemoteNoteAppears) {

    CaseManagementNote remoteNote = new CaseManagementNote();
    remoteNote.setId(1L);
    remoteNote.setUuid(remoteUuid);
    remoteNote.setArchived(isArchived);
    remoteNote.setLocked(isLocked);
    remoteNote.setObservation_date(new Timestamp(LOCAL_OBSERVATION_TIME));

    ArrayList<CaseManagementNote> remoteNoteList = new ArrayList<>();
    remoteNoteList.add(remoteNote);
    when(caseManagementNoteDao.findAllRemoteByDemographicId(any(), any()))
        .thenReturn(remoteNoteList);
    mockCaseManagementNotesFetching();

    LoggedInInfo loggedInInfo = new LoggedInInfo();
    NoteSelectionCriteria criteria = new NoteSelectionCriteria();
    criteria.setDemographicId(1);
    criteria.setEncounterType("face to face encounter with client");

    NoteSelectionResult result = defaultNoteService.findNotes(loggedInInfo, criteria, true);

    if (expectedRemoteNoteAppears) {
      assertEquals(2, result.getNotes().size());
      assertEquals(LOCAL_NOTE_UUID,
          ((NoteDisplayLocal) result.getNotes().get(0)).getCaseManagementNote().getUuid());
      assertEquals(remoteUuid,
          ((NoteDisplayLocal) result.getNotes().get(1)).getCaseManagementNote().getUuid());

    } else {
      assertEquals(1, result.getNotes().size());
      assertEquals(LOCAL_NOTE_UUID,
          ((NoteDisplayLocal) result.getNotes().get(0)).getCaseManagementNote().getUuid());

    }

  }

  private static Object[] differentRemoteObservationTimes() {
    return new Object[]{
        new Object[]{"remote note is older", LOCAL_OBSERVATION_TIME - 1, 0},
        new Object[]{"remote note is same", LOCAL_OBSERVATION_TIME, 1},
        new Object[]{"remote note is newer", LOCAL_OBSERVATION_TIME + 1, 1},
    };
  }

  @ParameterizedTest
  @MethodSource("differentRemoteObservationTimes")
  public void givenRemoteNote_whenFindNotes_thenNotesAreSortedByObservationDate(String description,
      long remoteObservationTime, int expectedRemoteNoteIndex) {

    CaseManagementNote remoteNote = new CaseManagementNote();
    remoteNote.setId(1L);
    remoteNote.setObservation_date(new Timestamp(remoteObservationTime));
    remoteNote.setUuid("validRemoteUuid");

    ArrayList<CaseManagementNote> remoteNoteList = new ArrayList<>();
    remoteNoteList.add(remoteNote);
    when(caseManagementNoteDao.findAllRemoteByDemographicId(any(), any())).thenReturn(
        remoteNoteList);
    mockCaseManagementNotesFetching();

    LoggedInInfo loggedInInfo = new LoggedInInfo();
    NoteSelectionCriteria criteria = new NoteSelectionCriteria();
    criteria.setDemographicId(1);
    criteria.setEncounterType("face to face encounter with client");

    NoteSelectionResult result = defaultNoteService.findNotes(loggedInInfo, criteria, true);
    assertEquals(2, result.getNotes().size());
    assertEquals(remoteNote.getUuid(),
        ((NoteDisplayLocal) result.getNotes().get(expectedRemoteNoteIndex)).getCaseManagementNote()
            .getUuid());

  }

  /**
   * Show that when there are many notes to display, only a designated amount of notes are
   * displayed (as defined by the given noteSelectionCriteria).
   *
   * This test makes four notes (two local, two remote) ordered local-remote-local-remote.
   * We make two fetches for notes:
   *  - In the first fetch, we rig the criteria to only fetch three notes
   *  - In the second fetch, we rig the criteria to continue fetching after the three notes,
   *    expecting only the remaining note to be fetched.
   */
  @Test
  public void givenManyNotes_whenFindNotes_thenOnlyDefinedNumberOfNotesReturned() {
    ArrayList<CaseManagementNote> localNotes = new ArrayList<>();
    ArrayList<CaseManagementNote> remoteNotes = new ArrayList<>();
    for (int i = 0; i < 4; i++) {
      CaseManagementNote note = new CaseManagementNote();
      note.setId((long) i);
      note.setUuid("uuid" + i);
      note.setObservation_date(new Timestamp(1704067200000L + (i * 86400000)));
      if (i % 2 == 0) {
        localNotes.add(note);
      } else {
        note.setRemoteSystemId(1);
        remoteNotes.add(note);
      }
    }

    when(caseManagementNoteDao.findAllRemoteByDemographicId(any(), any())).thenReturn(remoteNotes);
    mockCaseManagementNotesFetching(localNotes);

    LoggedInInfo loggedInInfo = new LoggedInInfo();
    NoteSelectionCriteria criteria = new NoteSelectionCriteria();
    criteria.setDemographicId(1);
    criteria.setEncounterType("face to face encounter with client");
    criteria.setMaxResults(3);

    NoteSelectionResult result = defaultNoteService.findNotes(loggedInInfo, criteria, true);
    assertEquals(3, result.getNotes().size());
    assertEquals(1, result.getNotes().get(0).getNoteId());
    assertEquals(2, result.getNotes().get(1).getNoteId());
    assertEquals(3, result.getNotes().get(2).getNoteId());

    criteria.setFirstResult(1);
    result = defaultNoteService.findNotes(loggedInInfo, criteria, true);
    assertEquals(1, result.getNotes().size());
    assertEquals(0, result.getNotes().get(0).getNoteId());

  }

}
