package org.oscarehr.hospitalReportManager;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.hospitalReportManager.dao.HRMDocumentDao;
import org.oscarehr.hospitalReportManager.dao.HRMDocumentSubClassDao;
import org.oscarehr.hospitalReportManager.dao.HRMSubClassDao;
import org.oscarehr.hospitalReportManager.model.HRMDocument;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class HRMUtilTest {

  public static final String REPORT_TYPE = "Medical Records Report";
  public static final String REPORT_STATUS = "S";
  public static final String DESCRIPTION = "description";
  public static final int REMOTE_SYSTEM_ID = 1005;
  public static final Date TIME_RECEIVED = new Date();
  public static final Date REPORT_DATE = new Date(TIME_RECEIVED.getTime() + 86400000); // 1 day later
  public static final String FORMATTED_DATE =
      new SimpleDateFormat("yyyy-MM-dd HH:mm").format(TIME_RECEIVED);
  public static final String FORMATTED_REPORT_DATE =
      new SimpleDateFormat("yyyy-MM-dd HH:mm").format(REPORT_DATE);
  public static final String DEMOGRAPHIC_NUMBER = "123";
  public static final String GUID = "guid";

  @Mock
  private HRMDocumentDao hrmDocumentDao;
  @Mock
  private HRMSubClassDao hrmSubClassDao;
  @Mock
  private HRMDocumentSubClassDao hrmDocumentSubClassDao;
  @Mock
  private HRMReport hrmReport;

  private LoggedInInfo loggedInInfo;
  private MockedStatic<SpringUtils> springUtils;
  private MockedStatic<HRMReportParser> hrmReportParserMock;

  @BeforeEach
  public void setUp() {
    openMocks(this);

    springUtils = mockStatic(SpringUtils.class);
    hrmReportParserMock = mockStatic(HRMReportParser.class);
    loggedInInfo = mock(LoggedInInfo.class);

    ReflectionTestUtils.setField(HRMUtil.class, "hrmDocumentDao", hrmDocumentDao);
    ReflectionTestUtils.setField(HRMUtil.class, "hrmSubClassDao", hrmSubClassDao);
    ReflectionTestUtils.setField(HRMUtil.class, "hrmDocumentSubClassDao", hrmDocumentSubClassDao);

    hrmReportParserMock
        .when(() -> HRMReportParser.parseReport(loggedInInfo, StringUtils.EMPTY))
        .thenReturn(hrmReport);
  }

  @AfterEach
  public void close() {
    springUtils.close();
    hrmReportParserMock.close();
  }

  @Test
  public void givenDemographicId_whenFindAllHrmDocuments_thenReturnHrmForDemographicInMap() {
    List<HRMDocument> hrmDocuments = new ArrayList<>();
    hrmDocuments.add(createHrmDocument(1));

    when(hrmDocumentDao.getAllHrmByDemographicId(Integer.parseInt(DEMOGRAPHIC_NUMBER)))
        .thenReturn(hrmDocuments);
    when(hrmDocumentDao.findAllRemoteHrmDocumentsByDemographicId(
        Integer.parseInt(DEMOGRAPHIC_NUMBER)))
        .thenReturn(new ArrayList<>());

    val result = HRMUtil.listAllHRMDocuments(loggedInInfo, StringUtils.EMPTY, DEMOGRAPHIC_NUMBER);

    assertEquals(1, result.size());
    val resultHrmDocument = result.get(0);
    assertConvertedHrmDocument(resultHrmDocument, 1);
  }

  @Test
  public void givenDemographicIdWithMultipleHrm_whenFinalAllHrmDocuments_thenReturnMappedHrms() {
    List<HRMDocument> hrmDocuments = new ArrayList<>(
        Arrays.asList(createHrmDocument(1), createHrmDocument(2)));

    when(hrmDocumentDao.getAllHrmByDemographicId(Integer.parseInt(DEMOGRAPHIC_NUMBER)))
        .thenReturn(hrmDocuments);
    when(hrmDocumentDao.findAllRemoteHrmDocumentsByDemographicId(
        Integer.parseInt(DEMOGRAPHIC_NUMBER)))
        .thenReturn(new ArrayList<>());

    val result = HRMUtil.listAllHRMDocuments(loggedInInfo, StringUtils.EMPTY, DEMOGRAPHIC_NUMBER);

    assertEquals(2, result.size());
    assertConvertedHrmDocument(result.get(0), 1);
    assertConvertedHrmDocument(result.get(1), 2);
  }

  @Test
  public void givenDemographicWithRemoteHrms_whenFindAllHrmDocuments_thenReturnRemoteHrms() {
    List<HRMDocument> hrmDocuments = new ArrayList<>(
        Arrays.asList(createHrmDocument(1), createHrmDocument(2)));

    when(hrmDocumentDao.getAllHrmByDemographicId(Integer.parseInt(DEMOGRAPHIC_NUMBER)))
        .thenReturn(new ArrayList<>());
    when(hrmDocumentDao.findAllRemoteHrmDocumentsByDemographicId(
        Integer.parseInt(DEMOGRAPHIC_NUMBER)))
        .thenReturn(hrmDocuments);

    val result = HRMUtil.listAllHRMDocuments(loggedInInfo, StringUtils.EMPTY, DEMOGRAPHIC_NUMBER);

    assertEquals(2, result.size());
    val resultHrmDocument1 = result.get(0);
    assertConvertedHrmDocument(resultHrmDocument1, 1);

    val resultHrmDocument2 = result.get(1);
    assertConvertedHrmDocument(resultHrmDocument2, 2);
  }

  private static void assertConvertedHrmDocument(
      final HashMap<String, ?> hrmMap,
      final Integer id
  ) {
    assertEquals(FORMATTED_DATE, hrmMap.get("time_received"));
    assertEquals(FORMATTED_REPORT_DATE, hrmMap.get("report_date"));
    assertEquals(REPORT_TYPE, hrmMap.get("report_type"));
    assertEquals(REPORT_STATUS, hrmMap.get("report_status"));
    assertEquals(DESCRIPTION, hrmMap.get("description"));
    assertEquals(REMOTE_SYSTEM_ID, hrmMap.get("remoteSystemId"));
    assertEquals(StringUtils.EMPTY, hrmMap.get("category"));
    assertEquals(GUID, hrmMap.get("guid"));
    assertEquals(id, hrmMap.get("id"));
  }

  @Test
  public void givenDemographicWithNoHrms_whenFindAllHrmDocuments_thenReturnEmptyList() {
    when(hrmDocumentDao.getAllHrmByDemographicId(Integer.parseInt(DEMOGRAPHIC_NUMBER)))
        .thenReturn(new ArrayList<>());
    when(hrmDocumentDao.findAllRemoteHrmDocumentsByDemographicId(
        Integer.parseInt(DEMOGRAPHIC_NUMBER)))
        .thenReturn(new ArrayList<>());


    val result = HRMUtil.listAllHRMDocuments(loggedInInfo, StringUtils.EMPTY, DEMOGRAPHIC_NUMBER);

    assertEquals(0, result.size());
  }

  @Test
  public void givenDoc_whenCreateDocumentListFromHrm_thenReturnList() {
    val hrmDocument = createHrmDocument(1);
    val hrmResults = new ArrayList<HashMap<String, ?>>();

    HRMUtil.createDocumentListFromHrm(hrmDocument, hrmResults);

    assertEquals(1, hrmResults.size());
    val hrmMap = hrmResults.get(0);
    assertEquals(FORMATTED_DATE, hrmMap.get("time_received"));
    assertEquals(FORMATTED_REPORT_DATE, hrmMap.get("report_date"));
    assertEquals(REPORT_TYPE, hrmMap.get("report_type"));
    assertEquals(REPORT_STATUS, hrmMap.get("report_status"));
    assertEquals(DESCRIPTION, hrmMap.get("description"));
    assertEquals(REMOTE_SYSTEM_ID, hrmMap.get("remoteSystemId"));
    assertEquals(StringUtils.EMPTY, hrmMap.get("category"));
    assertEquals(1, hrmMap.get("id"));
  }

  private HRMDocument createHrmDocument(final Integer id) {
    val hrmDocument = new HRMDocument();

    hrmDocument.setId(id);
    hrmDocument.setGuid(GUID);
    hrmDocument.setTimeReceived(TIME_RECEIVED);
    hrmDocument.setReportDate(REPORT_DATE);
    hrmDocument.setReportType(REPORT_TYPE);
    hrmDocument.setReportStatus(REPORT_STATUS);
    hrmDocument.setDescription(DESCRIPTION);
    hrmDocument.setRemoteSystemId(REMOTE_SYSTEM_ID);

    return hrmDocument;
  }
}