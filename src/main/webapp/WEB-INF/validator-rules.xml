<!DOCTYPE form-validation PUBLIC
          "-//Apache Software Foundation//DTD Commons Validator Rules Configuration 1.1.3//EN"
          "http://jakarta.apache.org/commons/dtds/validator_1_1_3.dtd">
	<!--
		$Id: validator-rules.xml,v 1.6 2008-12-12 22:56:13 singhj Exp $ This
		file contains the default Struts Validator pluggable validator
		definitions. It should be placed somewhere under /WEB-INF and
		referenced in the struts-config.xml under the plug-in element for the
		ValidatorPlugIn. <plug-in
		className="org.apache.struts.validator.ValidatorPlugIn"> <set-property
		property="pathnames" value="/WEB-INF/validator-rules.xml,
		/WEB-INF/validation.xml"/> </plug-in> These are the default error
		messages associated with each validator defined in this file. They
		should be added to your projects ApplicationResources.properties file
		or you can associate new ones by modifying the pluggable validators
		msg attributes in this file. # Struts Validator Error Messages
		errors.required={0} is required. errors.minlength={0} can not be less
		than {1} characters. errors.maxlength={0} can not be greater than {1}
		characters. errors.invalid={0} is invalid. errors.byte={0} must be a
		byte. errors.short={0} must be a short. errors.integer={0} must be an
		integer. errors.long={0} must be a long. errors.float={0} must be a
		float. errors.double={0} must be a double. errors.date={0} is not a
		date. errors.range={0} is not in the range {1} through {2}.
		errors.creditcard={0} is an invalid credit card number.
		errors.email={0} is an invalid e-mail address. Note: Starting in
		Struts 1.2.0 the default javascript definitions have been consolidated
		to commons-validator. The default can be overridden by supplying a
		<javascript> element with a CDATA section, just as in struts 1.1.
	-->

<form-validation>

	<global>

		<validator name="required" classname="org.apache.struts.validator.FieldChecks"
			method="validateRequired"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			msg="errors.required" />

		<validator name="requiredif" classname="org.apache.struts.validator.FieldChecks"
			method="validateRequiredIf"
			methodParams="java.lang.Object,
                               org.apache.commons.validator.ValidatorAction,
                               org.apache.commons.validator.Field,
                               org.apache.struts.action.ActionMessages,
                               org.apache.commons.validator.Validator,
                               javax.servlet.http.HttpServletRequest"
			msg="errors.required" />

		<validator name="validwhen" msg="errors.required"
			classname="org.apache.struts.validator.validwhen.ValidWhen" method="validateValidWhen"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest" />


		<validator name="minlength" classname="org.apache.struts.validator.FieldChecks"
			method="validateMinLength"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.minlength"
			jsFunction="org.apache.commons.validator.javascript.validateMinLength" />


		<validator name="maxlength" classname="org.apache.struts.validator.FieldChecks"
			method="validateMaxLength"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.maxlength"
			jsFunction="org.apache.commons.validator.javascript.validateMaxLength" />



		<validator name="mask" classname="org.apache.struts.validator.FieldChecks"
			method="validateMask"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.invalid" />


		<validator name="byte" classname="org.apache.struts.validator.FieldChecks"
			method="validateByte"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.byte" jsFunctionName="ByteValidations" />


		<validator name="short" classname="org.apache.struts.validator.FieldChecks"
			method="validateShort"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.short" jsFunctionName="ShortValidations" />


		<validator name="integer" classname="org.apache.struts.validator.FieldChecks"
			method="validateInteger"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.integer" jsFunctionName="IntegerValidations" />



		<validator name="long" classname="org.apache.struts.validator.FieldChecks"
			method="validateLong"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.long" />


		<validator name="float" classname="org.apache.struts.validator.FieldChecks"
			method="validateFloat"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.float" jsFunctionName="FloatValidations" />

		<validator name="double" classname="org.apache.struts.validator.FieldChecks"
			method="validateDouble"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.double" />


		<validator name="date" classname="org.apache.struts.validator.FieldChecks"
			method="validateDate"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.date" jsFunctionName="DateValidations" />


		<validator name="intRange" classname="org.apache.struts.validator.FieldChecks"
			method="validateIntRange"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="integer" msg="errors.range" />


		<validator name="floatRange" classname="org.apache.struts.validator.FieldChecks"
			method="validateFloatRange"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="float" msg="errors.range" />

		<validator name="doubleRange" classname="org.apache.struts.validator.FieldChecks"
			method="validateDoubleRange"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="double" msg="errors.range" />


		<validator name="creditCard" classname="org.apache.struts.validator.FieldChecks"
			method="validateCreditCard"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.creditcard" />


		<validator name="email" classname="org.apache.struts.validator.FieldChecks"
			method="validateEmail"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.email" />

		<validator name="url" classname="org.apache.struts.validator.FieldChecks"
			method="validateUrl"
			methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.apache.struts.action.ActionMessages,
                       org.apache.commons.validator.Validator,
                       javax.servlet.http.HttpServletRequest"
			depends="" msg="errors.url" />

		<!--
			This simply allows struts to include the validateUtilities into a
			page, it should not be used as a validation rule.
		-->
		<validator name="includeJavaScriptUtilities" classname=""
			method="" methodParams="" depends="" msg=""
			jsFunction="org.apache.commons.validator.javascript.validateUtilities" />

	</global>

</form-validation>
