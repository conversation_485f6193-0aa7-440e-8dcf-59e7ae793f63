<!-- Well<PERSON><PERSON><PERSON><PERSON><PERSON> -->
<well-ai-voice-script></well-ai-voice-script>

<!-- drug searchbar -->
<app-typeahead class="drug-search" toggle-classes="'noradius-right'" clear-on-selection="true"
               gap-below="'gap-below-thin'" input="{id: 'drugSearch'}" label="'Drug Name'" options="typeAheadRx.options"
               placeholder="'Search or Enter Drug Name'" size="'large'" select-event="getFromDrugDatabase"
               typeahead-event="typeAheadSearch" auto-focus="true">
    <div class="container-custom-drug-buttons">
        <button-date-picker change-date="setMainWrittenDate" default-text="'Main Written Date'" classes="'right'">
        </button-date-picker>
        <button class="well-button"
                ng-class="{'secondary' : !isCustomDrugSelected}" ng-click="makeNewDrugCustom()">Custom Drug
        </button>
    </div>
</app-typeahead>

<!-- rx items -->
<div class="panel-1 generate-rx-panel gap-below-thin collapse"
     ng-class="{'is-active': rxItemOpen.indexOf($index) >= 0, 'collapsed-drug': rxItemOpen.indexOf($index) < 0}"
     set-focus=".dosage-instructions-input" set-focus-reverse="true" set-focus-watch="'is-active'"
     id="rxDrug_{{$index}}" ng-repeat="drug in rx.items" ng-click="rxItemShow($index)">

        <div class="panel-body collapse-content-preview">
            <div class="gap-box gap-low">
                <b>{{drug.name}}</b>
                <span>{{drug.special}}</span>
                <span class="has-text-warning has-tooltip-bottom" data-tooltip="Allergy and/or Interactions"
                      ng-if="rxItemHasWarnings(drug)">
                    <i class="fa fa-exclamation-triangle"></i>
                </span>
                <div class="is-pulled-right is-flex">
                    <button class="well-button text xsmall mr-2">Edit</button>
                    <button class="well-button text xsmall" title="Remove From Rx" ng-click="removeDrug($index)">Delete</button>
                </div>
            </div>
        </div>
        <div class="panel-body collapse-content">
            <div class="gap-box gap-low">
                <div class="columns">
                    <div class="column">
                        <input class="well-input input" type="text" ng-model="drug.name" ng-show="drug.isCustom">
                        <span ng-hide="drug.isCustom">
                            <b>{{drug.name}}</b> &nbsp;
                            <button class="well-button small text"
                                    ng-click="makeExistingDrugCustom(drug)"
                                    title="Customize Drug">
                                <i class="fa fa-edit"></i>
                            </button>
                        </span>
                        <span style="color: #d9534f">
                            {{drug.inactiveDate != null ? 'Inactive Drug Since: ' + drug.inactiveDate : ''}}
                        </span>
                    </div>
                    <div class="column is-3">
                        <div class="is-pulled-right">
                            <div ng-show="$index == 0" class="is-flex">
                                <label class="switch">
                                    <input type="checkbox" ng-checked="rxOverride" id="rxOverride" ng-click="rxOverrideAll()"/>
                                    <span class="slider round"></span>
                                </label>
                                <span class="s_a_j_label">Override All</span>
                            </div>
                        </div>
                    </div>
                    <div class="column is-2">
                        <div class="is-pulled-right is-flex">
                            <button class="text well-button xsmall mr-2" ng-click="rxItemHide($index)">Minimize</button>
                            <button class="text well-button xsmall" title="Remove From Rx" ng-click="removeDrug($index)">Delete</button>
                        </div>
                    </div>
                </div>


                <div class="columns is-gap-thin" ng-hide="drug.isCustom || drug.brandName == null">
                    <div class="column is-2">
                        <div ng-if="selectedJurisdiction.enabled && eRxSettings.enabled && drug.formularyResults == null && !drug.isCompoundDrug && !drug.isCustom">
                            <progress class="progress is-small is-primary formulary-loading" max="100"
                                      ng-if="getFormularyQueryItemState($index) === formularyQueryState.loading"></progress>
                            <span class="tag is-danger"
                                  ng-if="getFormularyQueryItemState($index) === formularyQueryState.error">Error Retrieving Coverage</span>
                        </div>

                        <div ng-if="selectedJurisdiction.enabled && drug.formularyResults.length > 0">
                            <div class="tags">
                                <span class="tag is-light has-tooltip-bottom"
                                      ng-show="drug.formularyResults[0].coverage.code != '22731000087103'"
                                      ng-click="openFormularyResultModal(drug)" data-tooltip="View full formulary info"><i
                                        class="fa fa-list-alt"></i></span>
                                <span class="tag has-tooltip-bottom"
                                      ng-class="getFormularyCoverageClass(drug.formularyResults[0].coverage.code)"
                                      data-tooltip="{{drug.formularyResults[0].coverage.longName}}">
                                    {{drug.formularyResults[0].coverage.shortName}}</span>

                                <span class="tag has-text-primary has-tooltip-bottom is-white"
                                      data-tooltip="{{drug.formularyResults[0].costRange}}">{{drug.formularyResults[0].costSymbol}}</span>
                            </div>
                        </div>
                    </div>

                    <div class="column is-10">
                        <span class="is-pulled-right"
                              ng-if="drug.genericName != null">Ingredients: {{drug.genericName}}</span>
                    </div>
                </div>

                <!-- Interaction Warnings -->
                <div class="columns" ng-if="drug.interactionWarnings.length > 0" ng-hide="drug.isCustom">
                    <div class="column is-12 tags">
                        <span class="tag has-text-weight-bold has-tooltip-bottom has-tooltip-multiline"
                              ng-repeat="interaction in drug.interactionWarnings"
                              ng-class="getDrugWarningClass(interaction.severity)"
                              ng-if="showDrugInteraction(interaction.severity)"
                              data-tooltip="{{interaction.screenMessage}}">{{interaction.interactionName}}</span>
                    </div>
                </div>

                <!-- Allergy Warnings -->
                <div class="columns" ng-if="drug.allergyWarnings.length > 0" ng-hide="drug.isCustom">
                    <div class="column is-12 tags">
                        <span class="tag has-text-weight-bold" ng-repeat="allergy in drug.allergyWarnings"
                              ng-class="getDrugWarningClass(allergy.severityOfReaction)"
                              ng-if="!allergy.lookupError">{{'Allergy: ' + allergy.description + ' Reaction: ' + (allergy.reaction != null ? allergy.reaction : 'Unknown') + ' Severity: ' + getSeverityOfReactionDescription(allergy.severityOfReaction)}}</span>

                        <span class="tag has-text-weight-bold is-danger" ng-repeat="allergy in drug.allergyWarnings"
                              ng-hide="!allergy.lookupError"
                              ng-if="allergy.lookupError">{{allergy.description + ' did not match an FDB allergen; interaction not shown. Check for an updated version of the allergen.'}}</span>
                    </div>
                </div>

                <!-- Other Warnings -->
                <div class="columns" ng-if="drug.warningMessages.length > 0" ng-hide="drug.isCustom">
                    <div class="column is-12 tags">
                        <span class="tag is-danger has-text-weight-bold has-tooltip-bottom has-tooltip-multiline"
                              ng-repeat="message in drug.warningMessages"
                              data-tooltip="Something went wrong, please try again or contact support">{{message}}</span>
                    </div>
                </div>

                <!-- dosage instructions -->
                <div class="dosage-instructions">
                    <div class="instruction-example">
                        <input type="text" class="well-input dosage-instructions-input" placeholder="Dosage Instructions"
                               ng-model="drug.special" ng-blur="parseInstructions($index)" maxlength="5000">
                        <a ng-click="openInstructionExamples($index, drug)" data-tooltip="Instruction Examples" href="#">
                            <i class="s_a_j_color_1 far fa-list-alt"></i>
                        </a>
                    </div>
                    <div class="s_a_j_checkbox parse-instructions-checkbox">
                        <label>
                            <input class="well-checkbox" type="checkbox" ng-model="drug.parseInstructions">
                            <span
                                class="s_a_j_label"
                                ng-class="{ s_a_j_color_1: drug.parseInstructions, 'text-red': !drug.parseInstructions }"
                                ng-bind="parseInstructionsText(drug)">Long Term</span>
                        </label>
                    </div>

                    <div class="editable-tags">
                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Method</span>
                            <input type="text" class="well-input tag-input small-input" ng-model="drug.method" ng-change="disableParseInstructions(drug)">
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Route</span>
                            <input type="text" class="well-input tag-input" ng-model="drug.route" ng-change="disableParseInstructions(drug)">
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Drug Form</span>
                            <input type="text" class="well-input tag-input" ng-model="drug.drugForm" ng-change="disableParseInstructions(drug)">
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Min</span>
                            <input type="text" class="well-input tag-input small-input" ng-model="drug.takeMin" ng-change="disableParseInstructions(drug)">
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Max</span>
                            <input type="text" class="well-input tag-input small-input" ng-model="drug.takeMax" ng-change="disableParseInstructions(drug)">
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Frequency</span>
                            <select class="well-input tag-select" ng-model="drug.freqCode" ng-change="disableParseInstructions(drug)">
                                <option value=""></option>
                                <option ng-repeat="frequency in ::frequencies" ng-value="frequency.unit">
                                    {{frequency.unit}}
                                </option>
                            </select>
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Site</span>
                            <input type="text" class="well-input tag-input" ng-model="drug.site" ng-change="disableParseInstructions(drug)">
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Total Quantity</span>
                            <input type="text" class="well-input tag-input small-input" ng-model="drug.quantity"
                                   ng-attr-title="{{validateQuantityFields(drug)}}" ng-change="disableParseInstructions(drug)">
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Quantity Unit</span>
                            <input type="text" class="well-input tag-input" ng-model="drug.quantityUnit" ng-change="disableParseInstructions(drug)">
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span class="label">Duration</span>
                            <input type="text" class="well-input tag-input small-input" ng-model="drug.duration" ng-change="disableParseInstructions(drug)">
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span class="label">Duration Units</span>
                            <select class="well-input tag-select" ng-model="drug.durationUnit" ng-change="disableParseInstructions(drug)">
                                <option value=""></option>
                                <option ng-repeat="duration in ::durations" value="{{duration.unit}}">
                                    {{duration.description}}
                                </option>
                            </select>
                        </label>

                        <label class="well-label-inside editable-tag" ng-click="onSelectEditableDrugField()">
                            <span  class="label">Repeats</span>
                            <input type="text" class="well-input tag-input" ng-model="drug.repeat">
                        </label>

                    </div>

                </div>
                <div class="gap-below-thin"></div>

                <!-- pharmacy instructions -->
                <label class="well-label-above harmacy-instructions-input gap-below-thin">
                    <textarea class="well-input" ng-model="drug.pharmacyInstructions" rows="3"
                              placeholder="Pharmacy Instructions" maxlength="5000"></textarea>
                </label>

                <!-- long term / no subs / outside provider / non-authoritative checkboxes -->
                <div class="is-flex flex-wrap gap-below-thinner">
                    <div class="is-small">
                        <label>
                            <input class="well-checkbox" type="checkbox" ng-model="drug.longTerm">
                            <span class="s_a_j_label s_a_j_color_1">Long Term</span>
                        </label>
                    </div>

                    <div class="is-small">
                        <label>
                            <input class="well-checkbox" type="checkbox" ng-model="drug.noSubs">
                            <span class="s_a_j_label s_a_j_color_1">No Subs</span>
                        </label>
                    </div>

                    <div class="is-small">
                        <label>
                            <input class="well-checkbox" type="checkbox" ng-model="drug.doNotAutofill">
                            <span class="s_a_j_label s_a_j_color_1">Fill When Patient Arrives</span>
                        </label>
                    </div>

                    <div class="is-small">
                        <label>
                            <input class="well-checkbox" type="checkbox" ng-model="drug.noRenewals">
                            <span class="s_a_j_label s_a_j_color_1">Consult Required For Renewal</span>
                        </label>
                    </div>

                    <div class="is-small">
                        <label>
                            <input class="well-checkbox" type="checkbox" value="documents">
                            <span class="s_a_j_label s_a_j_color_1">Non-Authoritative</span>
                        </label>
                    </div>

                    <div class="s_a_j_clearfix prescribed_by_outside_provider_break"></div>

                    <div class="prescribed_by_outside_provider_checkbox is-small">
                        <label>
                            <input class="well-checkbox" type="checkbox" value="true" ng-model="drug.outsideProviderCheckbox">
                            <span class="s_a_j_label s_a_j_color_1">Prescribed by Outside Provider</span>
                        </label>
                    </div>

                    <div class="is-small" ng-show="showDispenseInternally">
                        <label>
                            <input class="well-checkbox" type="checkbox" ng-model="drug.dispenseInternal">
                            <span class="s_a_j_label s_a_j_color_1">Dispense Internally</span>
                        </label>
                    </div>
                    <!-- Outside Provider Info -->
                    <div class="is12-12 mb-2 mt-2 prescribed_by_outside_provider_extra_info"
                         ng-show="drug.outsideProviderCheckbox">
                        <div class="columns is-gap-thin is-mobile is-multiline flex-text-center ">
                            <div class="column is12-6">
                                <label class="well-label-inside">
                                    <span>Name</span>
                                    <input type="text" class="well-input" ng-model="drug.outsideProviderName">
                                </label>
                            </div>
                            <div class="column is12-6">
                                <label class="well-label-inside">
                                    <span>OHIP</span>
                                    <input type="text" class="well-input" ng-model="drug.outsideProviderOhip">
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- dates -->
                <div class="s_a_j_clearfix part-6 gap-below">
                    <div class="columns is-gap-thin is-mobile is-multiline">
                        <div class="column is12-12 is12-sm-9 is12-md-10 is12-lg-8 is12-xl-7">
                            <div class="columns is-gap-thin is-mobile is-multiline">
                                <div class="column is10-10 is10-xs-5">
                                    <div class="s_a_j_full">
                                        <app-shared-datepicker category="'Valid From'"
                                                               default-text="drug.rxDate | date: 'yyyy-MM-dd'"
                                                               default-date="drug.rxDate" change-date="setRxDate"
                                                               size="'small'" index="$index"></app-shared-datepicker>
                                    </div>
                                </div>
                                <div class="column is10-10 is10-xs-5">
                                    <div class="s_a_j_full">
                                        <app-shared-datepicker category="'Written Date'"
                                                               default-text="drug.writtenDate | date: 'yyyy-MM-dd'"
                                                               default-date="drug.writtenDate" change-date="setWrittenDate"
                                                               size="'small'" index="$index"></app-shared-datepicker>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="column has-text-right">
                            <button class="text well-button xsmall is-pulled-right" ng-click="drugToFavourite(drug)">Add to
                                Favourites</button>
                        </div>
                    </div>
                </div>

                <!-- controlled substance -->
                <div class="controlled-substance collapse" ng-class="{'is-open': drug.controlledSubstanceInfo !== null}">
                    <h3 class="section-heading title2 collapse-trigger pb-3" ng-click="toggleControlledSubstance(drug)">
                        <strong class="s_a_j_color_1">Controlled Substance</strong>
                        <div class="icon">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </h3>

                    <div class="collapse-content">
                        <div class="columns is-gap-thin is-multiline">
                            <div class="column is-6">
                                <div class="s_a_j_clearfix">
                                    <app-singledropdown category="'Patient Identification Type'" size="'small'"
                                                        placeholder="'Please Choose'"
                                                        options="controlledSubstanceDemographicIdTypes"
                                                        select-event="setPatientIdType" index="drug"></app-singledropdown>
                                </div>
                            </div>
                            <div class="column is-6">
                                <label class="columns is-gapless is-mobile well-label-inside">
                                    <span>Patient Identification Number</span>
                                    <input type="text" class="well-input" maxlength="20"
                                           ng-model="drug.controlledSubstanceInfo.patientIdentificationNumber">
                                </label>
                            </div>
                            <div class="column is-6">
                                <label class="columns is-gapless is-mobile well-label-inside">
                                    <span>Practitioner Number</span>
                                    <input type="text" class="well-input" maxlength="20"
                                           ng-model="drug.controlledSubstanceInfo.providerPractitionerNumber">
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div ng-if="drug.limitedUseCodeList.length > 0">
                    <div class="collapse" ng-class="{'is-open': drug.limitedUseCodeList.length > 0}">
                        <h3 class="section-heading title2 collapse-trigger pb-3">
                            <strong class="s_a_j_color_1">Limited Use Codes</strong>
                            <div class="icon">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </h3>
                        <div class="collapse-content">
                            <span class="is-pulled-right" ng-show="showExpandAllLimitedUseCodes(drug)"><a href="javascript:void(0)" ng-click="toggleAllLimitedUseCodeDescriptions(drug)">{{drug.showAllLimitedUseCodeDescriptions ? 'Minimize All Descriptions' : 'Expand All Descriptions'}}</a></span>
                            <table class="well-table">
                                <tr ng-repeat="code in drug.limitedUseCodeList track by $index">
                                    <td ng-if="code.type === null">
                                        <a href="javascript:void(0)" ng-click="addLuCodeToDosageInstructions(drug, code.reasonForUseId)"><b>{{code.reasonForUseId}}</b></a>
                                    </td>
                                    <td ng-if="code.type === null">
                                        <span ng-class="code.showDescription ? 'limitedUseCodesFull' : 'limitedUseCodesPreview'" ng-click="toggleLimitedUseCodeDescription(code, drug)" id="luCode_{{rx.items.indexOf(drug)}}_{{code.reasonForUseId}}">
                                            {{code.value}}
                                        </span>
                                    </td>
                                    <td ng-if="code.type === 'R' || code.type === 'N'">&nbsp;</td>
                                    <td ng-if="code.type === 'R' || code.type === 'N'">
                                        <span ng-if="code.type === 'N'"><b>NOTE: </b></span>{{code.value}}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- buttons -->
<div class="buttons-container gap-below s_a_j_clearfix" ng-show="rx.items.length > 0">
    <div class="button-with-dropdown is-pulled-right" data-tooltip="{{getTooltip()}}">
        <button class="well-button first secondary" ng-click="saveErxPrescription(isPasteToEmrDefault())"
                ng-disabled="isSaveButtonDisabled()"
                ng-class="{'is-disabled is-loading': isLoading.btn.eSign}">{{getErxSubmitText(false)}}
        </button>
        <div class="dropdown is-right">
            <div class="dropdown-trigger">
                <button class="well-button last secondary full-content"
                        ng-disabled="isSaveButtonDisabled()">
                    <i class="fa fa-caret-down"></i>
                </button>
            </div>
            <div class="dropdown-menu">
                <div class="dropdown-content">
                    <div class="dropdown-item" ng-click="saveErxPrescription(!isPasteToEmrDefault())">{{getErxSubmitText(true)}}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="button-with-dropdown is-pulled-right mr-3">
        <button class="well-button first"
                ng-click="homeDelivery(true)"
                ng-class="{'is-loading': isLoading.btn.save}"
                ng-disabled="isLoading.btn.save"
                ng-show="isPillwayEnabled()">
            Home Delivery
        </button>
        <div class="dropdown is-right" ng-show="isPillwayEnabled()">
            <button class="well-button last full-content"
                    ng-disabled="isLoading.btn.save"
                    ng-click="homeDeliveryHelp()">
                ?
            </button>
        </div>
    </div>
    <div class="button-with-dropdown is-pulled-right mr-3">
        <button class="well-button first" ng-click="savePrescription(true)"
                ng-class="{'is-loading': isLoading.btn.save}" ng-disabled="isLoading.btn.save">
            Save & Print
        </button>
        <div class="dropdown is-right">
            <div class="dropdown-trigger">
                <button class="well-button last full-content" ng-disabled="isLoading.btn.save">
                    <i class="fa fa-caret-down"></i>
                </button>
            </div>
            <div class="dropdown-menu">
                <div class="dropdown-content">
                    <div class="dropdown-item" ng-click="savePrescription()">Save</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- drug list -->
<div class="s_a_j_clearfix">
    <div class="panel-1 current-drug-panel">
        <div class="panel-body">
            <div class="gap-box gap-low">
                <div class="current-drugs-table-actions mb-3">
                    <div class="left">
                        <div class="actions">
                            <span>Apply to selected</span>
                            <erx-drug-actions
                                rx-item="medicationListWAttrs" medication-list="medicationList"
                                is-erx-provider="ePrescribe" rx-properties="rxProperties"
                                get-demographic-items="getDemographicItems()"
                                re-prescribe="rePrescribeSelected()"
                                medication-filter="filterItems"
                                disable-warning-label="disableWarningLabel"
                                last-used-reason="lastUsedReason"
                                update-last-used-reason="updateLastUsedReason(lastUsedReason)">
                            </erx-drug-actions>
                            <button class="btn-icon fas fa-share-alt" title="Share To Portal"
                                    ng-show="rxProperties.isPortalEnabled && demographic.portalUserId"
                                    ng-click="pushToPortal()">
                            </button>
                        </div>
                        <button class="well-button secondary" ng-click="reRxAllLongTerm()">
                            RERX ALL LT MEDS
                        </button>
                    </div>

                    <div class="right">
                        <app-singledropdown category="'Filter To'"
                                            options="filters" option-name="'description'"
                                            value-name="'key'"
                                            return-obj="true" selected-val="selectedFilter.key"
                                            size="'medium'"
                                            select-event="setFilter">
                        </app-singledropdown>
                    </div>
                </div>

                <div class=" s_a_j_clearfix"> <!-- table-responsive-wrapper -->
                    <div id="currentDrugs" class="rx-current-drugs-table-container s_a_j_scroll1"
                         ng-class="{'data-table is-loading': isLoading.section.itemList}">
                        <table class="well-table">
                            <thead class="table-header">
                            <tr>
                                <th>
                                    <label>
                                        <input class="well-checkbox" type="checkbox"
                                               ng-model="medicationListAllSelect"
                                               indeterminate-checkbox
                                               property="selected"
                                               child-list="medicationListWAttrs">
                                    </label>
                                </th>
                                <th>Expiry</th>
                                <th>Start Date</th>
                                <th>Medication</th>
                                <th ng-if="isMultisite">Location</th>
                                <th>&nbsp;</th>
                                <th>&nbsp;</th>
                                <th ng-show="rxProperties.isPortalEnabled && demographic.portalUserId">Share Status</th>
                                <th>Term</th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th ng-show="showDispenseInternally">Dispense</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-if="">
                                <td colspan="10" class="is-loading has-text-centered">&nbsp;</td>
                            </tr>
                            <tr ng-repeat="item in medicationList | filter:filterItems | orderBy:-item.rxDate"
                                id="drug_{{item.id}}" ng-class="getClasses(item)" ng-click="viewMedication(item)">
                                <td ng-click="excludeFromRowClick()">
                                    <div class="is-small no-right-margin">
                                        <label>
                                            <input class="well-checkbox" type="checkbox"
                                                   ng-model="getMedWAttr(item.id).selected">
                                        </label>
                                    </div>
                                    <span class="has-text-warning has-tooltip-right"
                                          data-tooltip="Pending Renewal"
                                          ng-show="item.hasTask">
                                        <a href="javascript:void(0);" ng-click="getRenewalsByDrugId(item.id)">
                                            <span class="fa fa-info-circle has-text-warning-orange"></span>
                                        </a>
                                    </span>
                                </td>
                                <td ng-class="{'has-text-warning-orange' : item.daysToExpiry <= 30}">
                                    {{item.daysToExpiry}} days
                                </td>
                                <td style="min-width:70px;">{{item.rxDate | date: 'yyyy-MM-dd'}}</td>
                                <td class="medication-column">
                                    {{item.special.trim()}}
                                    <erx-status-badges rx-item="item"
                                                       resend-erx="resendErxPrescription(item)" ng-click="excludeFromRowClick()"></erx-status-badges>
                                </td>
                                <td ng-if="isMultisite">{{item.siteName}}</td>
                                <td>
                                    <a href="javascript:void(0);" ng-click="sendClinicianCommunication(item)">
                                        <span class="fa fa-envelope"></span>
                                    </a>
                                    <span class="far fa-clipboard" ng-if="!sentViaPrescribeIt(item)"
                                          title="Pasted in message body"></span>
                                </td>
                                <td>
                                    <span class="tag is-sm is-dark-blue"
                                          ng-if="item.outsideProviderName || item.remoteFacilityId || item.remoteFacilityName">Ext</span>
                                </td>
                                <td ng-show="rxProperties.isPortalEnabled && demographic.portalUserId"> {{ getDrugSyncStatus(item) }} </td>
                                <td ng-click="excludeFromRowClick()">
                                    <div
                                        class="chip has-tooltip-bottom"
                                        ng-class="{ active: item.longTerm, disabled: item.archived }"
                                        ng-click="saveDrug(item)"
                                        data-tooltip="{{item.longTerm ? 'Change to Short Term Med': 'Change to Long Term Med'}}">
                                        {{ item.longTerm ? 'Long': 'Short' }}
                                    </div>
                                </td>
                                <td colspan="3" ng-click="excludeFromRowClick()">
                                    <erx-drug-actions rx-item="item" medication-list="medicationList"
                                                      is-erx-provider="ePrescribe"
                                                      rx-properties="rxProperties"
                                                      get-demographic-items="getDemographicItems()"
                                                      re-prescribe="rePrescribe(item)"
                                                      disable-warning-label="disableWarningLabel"
                                                      last-used-reason="lastUsedReason"
                                                      update-last-used-reason="updateLastUsedReason(lastUsedReason)">
                                    </erx-drug-actions>
                                </td>
                                <td ng-show="showDispenseInternally && item.dispenseInternal">
                                    <a href="{{window.location.origin}}/oscar/oscarRx/Dispense.do?method=view&id={{item.id}}" onclick="window.open(this.href, 'dispenseMenu', `width=720px, height=700px`);return false;">Dispense</a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <label class="mt-3">
                    <input class="well-checkbox" type="checkbox" id="disableWarningLabel"
                           ng-model="disableWarningLabel" ng-change="warningLabelToggle()">
                    <span>Disable Warning Label When Discontinuing Drug</span>
                </label>
            </div>
        </div>
    </div>
</div>

<!-- lab results -->
<div class="s_a_j_clearfix" ng-if="lab.show">
    <div class="panel-1 panel-toggle most-recent-lab-panel">
        <div class="panel-header columns is-gapless  is-mobile">
           
            <h2 class="panel-title column is-narrow">Lab Results</h2>
            
            <div class="column has-text-right has-text-green">
                
                <button class="button is-text has-text-decoration-none fa fa-chevron-circle-left left" ng-click="getMostRecentLab('previous')" ng-disabled="lab.index == 0" title="Previous Lab"></button>

                <button class="button is-text has-text-decoration-none fa fa-chevron-circle-right right" ng-click="getMostRecentLab('next')" ng-disabled="!hasMoreLabs('next')" title="Next Lab"></button>
            </div>
            
        </div>
        <div class="panel-body">
            <iframe ng-src="{{lab.path}}" onload="resizeIFrame(1200)"></iframe>
        </div>
    </div>
</div>
