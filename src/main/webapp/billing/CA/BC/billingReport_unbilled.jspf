<%@ page import="org.owasp.encoder.Encode" %>
<%@ page import="org.oscarehr.common.dao.ProviderDataDao" %>
<%@ page import="org.oscarehr.common.model.ProviderData" %>
<!--  
/*
 * 
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved. *
 * This software is published under the GPL GNU General Public License. 
 * This program is free software; you can redistribute it and/or 
 * modify it under the terms of the GNU General Public License 
 * as published by the Free Software Foundation; either version 2 
 * of the License, or (at your option) any later version. * 
 * This program is distributed in the hope that it will be useful, 
 * but WITHOUT ANY WARRANTY; without even the implied warranty of 
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the 
 * GNU General Public License for more details. * * You should have received a copy of the GNU General Public License 
 * along with this program; if not, write to the Free Software 
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA. * 
 * 
 * <OSCAR TEAM>
 * 
 * This software was written for the 
 * Department of Family Medicine 
 * McMaster University 
 * Hamilton 
 * Ontario, Canada 
 */
-->

<table width="100%" border="2" valign="top">
	<%
	ProviderDataDao providerDao = SpringUtils.getBean(ProviderDataDao.class);
 String dateBegin = request.getParameter("xml_vdate");
   String dateEnd = request.getParameter("xml_appointment_date");
   if (dateEnd.compareTo("") == 0) dateEnd = oscar.MyDateFormat.getMysqlStandardDate(curYear, curMonth, curDay);
   if (dateBegin.compareTo("") == 0) dateBegin="1950-01-01"; // set to any early time to start search from beginning

   	List<Appointment> bs = appointmentDao.search_unbill_history_daterange(request.getParameter("providerview"),ConversionUtils.fromDateString(dateBegin),ConversionUtils.fromDateString(dateEnd));

  boolean bodd=false;
   nItems=0;
  String apptStatus="", apptNo="", demoNo = "", demoName="", userno="", apptDate="", apptTime="", reason="", provider, providerNo;
  if(bs==null) {
    out.println("failed!!!"); 
  } else {
  %>
	<tr bgcolor="#CCCCFF">
		<TH align="center" width="20%"><b><font size="2"
			face="Arial, Helvetica, sans-serif">PROVIDER</font></b></TH>
		<TH align="center" width="20%"><b><font size="2"
			face="Arial, Helvetica, sans-serif">SERVICE DATE</font></b></TH>
		<TH align="center" width="10%"><b><font size="2"
			face="Arial, Helvetica, sans-serif">TIME</font></b></TH>
		<TH align="center" width="10%"><b><font size="2"
			face="Arial, Helvetica, sans-serif">PATIENT</font></b></TH>
		<TH align="center" width="20%"><b><font size="2"
			face="Arial, Helvetica, sans-serif">DESCRIPTION</font></b></TH>
		<TH align="center" width="10%"><b><font size="2"
			face="Arial, Helvetica, sans-serif">COMMENTS</font></b></TH>
	</tr>
	<%
    for (Appointment a:bs) {
     
      bodd=bodd?false:true; //for the color of rows
      nItems++; //to calculate if it is the end of records
		apptNo = a.getId().toString();
		demoNo = String.valueOf(a.getDemographicNo());
		demoName = a.getName();
		userno=a.getProviderNo();
		apptDate = ConversionUtils.toDateString(a.getAppointmentDate());
		apptTime =  ConversionUtils.toTimeString(a.getStartTime());
		reason = a.getReason();
     	apptStatus = a.getStatus();
     	providerNo = a.getProviderNo();
     	ProviderData providerData = providerDao.findByProviderNo(providerNo);
		provider = providerData == null ? "" : providerData.getLastName() + ", " + providerData.getFirstName();
%>
	<tr bgcolor="<%=bodd?"#EEEEFF":"white"%>">
		<TD align="center" width="20%"><b><font size="2"
			face="Arial, Helvetica, sans-serif"><%=provider%></font></b></TD>
		<TD align="center" width="20%"><b><font size="2"
			face="Arial, Helvetica, sans-serif"><%=apptDate%></font></b></TD>
		<TD align="center" width="10%"><b><font size="2"
			face="Arial, Helvetica, sans-serif"><%=apptTime==null?"00:00:00":apptTime%></font></b></TD>
		<TD align="center" width="10%"><b><font size="2"
			face="Arial, Helvetica, sans-serif"><%=Encode.forHtml(demoName)%></font></b></TD>
		<TD align="center" width="20%"><b><font size="2"
			face="Arial, Helvetica, sans-serif"><%=Encode.forHtml(reason)%> </font></b></TD>
		<TD align="center" width="10%"><b> <font size="2"
			face="Arial, Helvetica, sans-serif"><a href=#
			onClick='popupPage(700,1000, "../../../billing.do?billRegion=<%=URLEncoder.encode(oscarVariables.getProperty("billregion"))%>&billForm=<%=URLEncoder.encode(oscarVariables.getProperty("default_view"))%>&hotclick=&appointment_no=<%=apptNo%>&demographic_name=<%=URLEncoder.encode(demoName)%>&status=<%=Encode.forUriComponent(apptStatus)%>&demographic_no=<%=demoNo%>&user_no=<%=Encode.forUriComponent(userno)%>&apptProvider_no=<%=providerNo%>&appointment_date=<%=apptDate%>&start_time=<%=apptTime==null?"00:00:00":apptTime%>&bNewForm=1"); return false;'
			title="<%=Encode.forHtmlAttribute(reason)%>">Bill |$|</a></font></b></TD>
	</tr>
	<%  rowCount = rowCount + 1;
    }
    if (rowCount == 0) {
    %>
	<tr bgcolor="<%=bodd?"ivory":"white"%>">
		<TD colspan="5" align="center"><b><font size="2"
			face="Arial, Helvetica, sans-serif">No unbill items</font></b></TD>
	</tr>
	<% }
}%>
</table>
