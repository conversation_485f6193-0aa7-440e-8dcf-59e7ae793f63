<%--

  Copyright (c) 2025 WELL EMR Group Inc.
  This software is made available under the terms of the
  GNU General Public License, Version 2, 1991 (GPLv2).
  License details are available via "gnu.org/licenses/gpl-2.0.html".

--%>

<%@ page import="oscar.util.SystemPreferencesUtils" %>
<%@ page import="org.apache.commons.lang.StringUtils" %>
<%@ page import="oscar.OscarProperties" %>

<%
  String defaultUrl = "https://autoscribe-public.s3.ca-central-1.amazonaws.com/wc/canary/router.js";
  String waivUrl = SystemPreferencesUtils.getPreferenceValueByName("well_ai_voice_2.url", "");
  if (StringUtils.isBlank(waivUrl)) {
    waivUrl = defaultUrl;
  }
%>

<style>
  autoscribe-app {
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 100;
    width: 360px;
    height: 90%;

    &:not(.minimized) {
      background: white;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }

    &.minimized {
      bottom: 20px;
      right: 20px;
      width: auto;
      height: 60px;
    }
  }
  autoscribe-app.maximized,
  #autoscribe-backdrop,
  div:has(#oauth-test-iframe) {
    z-index: 10006 !important;
  }
  body:has(#cortico_anchor) {
    autoscribe-app.minimized {
      right: 80px;
    }
  }
</style>
<script type="module">
  async function initAutoscribeApp() {
    try {
      await import('<%=waivUrl%>');

      const app = document.createElement('autoscribe-app');
      app.setAttribute('emr', 'oscar');
      app.setAttribute('emr-version', '<%=OscarProperties.getBuildTag()%>');
      app.classList.add('minimized');

      document.getElementById('body').appendChild(app);

      customElements.whenDefined('autoscribe-app').then(() => {
        document.addEventListener('onDisplayModeChange', ({ detail }) => {
          app.className = detail;
        });

        if (app.shadowRoot && 'adoptedStyleSheets' in Document.prototype) {
          const sheet = new CSSStyleSheet();
          sheet.replaceSync(`
            div:has(.drag-handle) {
              justify-content: space-around;
              padding-right: 0;
              >button {
                position: static;
                margin: 0!important;
              }
              .drag-handle {
                display: none;
              }
            }
          `);
          app.shadowRoot.adoptedStyleSheets = [sheet];
        }
      });
    } catch (error) {
      console.error('Failed to load or initialize autoscribe-app:', error);
    }
  }

  document.addEventListener('DOMContentLoaded', initAutoscribeApp);
</script>

