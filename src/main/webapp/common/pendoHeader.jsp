<%--
 Copyright (c) 2025 WELL EMR Group Inc.
 This software is made available under the terms of the
 GNU General Public License, Version 2, 1991 (GPLv2).
 License details are available via "gnu.org/licenses/gpl-2.0.html".
--%>

<%@ page contentType="text/html;charset=UTF-8" language="java" trimDirectiveWhitespaces="true" %>
<%@ page import="oscar.util.SystemPreferencesUtils" %>
<%@ page import="org.oscarehr.common.model.SystemPreferences" %>
<%@ page import="org.oscarehr.common.model.Provider" %>
<%@ page import="ca.oscarpro.service.PendoMetadataService" %>
<%@ page import="org.oscarehr.common.model.PendoVisitorData" %>
<%@ page import="org.oscarehr.common.model.PendoAccountData" %>
<%@ page import="org.apache.commons.lang3.StringUtils" %>
<%@ page import="org.apache.commons.text.StringEscapeUtils" %>

<%!
    private static final String EMPTY = "";
    private static final String PENDO_API_KEY_PREF = "pendo.api_key";
    private static final String OKTA_CLIENT_ID_PREF = "oscar_okta_client_id";
%>

<%
    final boolean isPendoEnabled = SystemPreferencesUtils.isReadBooleanPreferenceWithDefault(
            SystemPreferences.IS_PENDO_CLASSIC_ENABLED, false);
    final String oktaClientId = SystemPreferencesUtils.getPreferenceValueByName(
            OKTA_CLIENT_ID_PREF, EMPTY);

    if (isPendoEnabled && StringUtils.isNotEmpty(oktaClientId)) {
        final String pendoApiKey = SystemPreferencesUtils.getPreferenceValueByName(PENDO_API_KEY_PREF, EMPTY);
        final String escapedApiKey = StringEscapeUtils.escapeEcmaScript(pendoApiKey);
        final Provider provider = (Provider) session.getAttribute("provider");
        final PendoMetadataService pendoMetadataService = new PendoMetadataService();
        final PendoVisitorData visitorData = pendoMetadataService.getVisitorData(provider, session);
        final PendoAccountData accountData = pendoMetadataService.getAccountData();
%>

<script type="text/javascript">
  (function () {
    // Save a reference to the native JSON.stringify
    var nativeJSONStringify = JSON.stringify;

    // Override JSON.stringify with our patched version
    JSON.stringify = function (value, replacer, space) {
      // Remove Prototype's toJSON methods (if they exist)
      if (Array.prototype.hasOwnProperty('toJSON')) {
        delete Array.prototype.toJSON;
      }
      if (Object.prototype.hasOwnProperty('toJSON')) {
        delete Object.prototype.toJSON;
      }
      // Call the native stringify and return its result
      return nativeJSONStringify(value, replacer, space);
    };
  })();
</script>

<script>
  (function (apiKey) {
    (function (p, e, n, d, o) {
      var v, w, x, y, z;
      o = p[d] = p[d] || {};
      o._q = o._q || [];
      v = ['initialize', 'identify', 'updateOptions', 'pageLoad', 'track'];
      for (w = 0, x = v.length; w < x; ++w) (function (m) {
        o[m] = o[m] || function () {
          o._q[m === v[0] ? 'unshift' : 'push']([m].concat([].slice.call(arguments, 0)));
        };
      })(v[w]);
      y = e.createElement(n);
      y.async = !0;
      y.src = 'https://cdn.pendo.io/agent/static/' + apiKey + '/pendo.js';
      z = e.getElementsByTagName(n)[0];
      z.parentNode.insertBefore(y, z);
    })(window, document, 'script', 'pendo');

    // This function creates visitors and accounts in Pendo
    // You will need to replace <visitor-id-goes-here> and <account-id-goes-here> with values you use in your app
    // Please use Strings, Numbers, or Bools for value types.
    pendo.initialize({
      excludeAllText: true,
      visitor: {
        id: '<%= visitorData.getVisitorId() %>', // Required if user is logged in
        full_name: '<%= visitorData.getFullName() %>', // Recommended if using Pendo Feedback
        role: '<%= visitorData.getRole() %>', // Optional
        isSuperUser: <%= visitorData.isSuperUser() %>,
        providerType: '<%= visitorData.getProviderType() %>',
        hasCollegeNumber: <%= visitorData.isHasCollegeNumber() %>,
        collegeType: '<%= visitorData.getCollegeType() %>',
        isOscarProRx: '<%= visitorData.isOscarProRx() %>',
        isOscarProBilling: '<%= visitorData.isOscarProBilling() %>',
        isOscarProInbox: '<%= visitorData.isOscarProInbox() %>'

        // You can add any additional visitor level key-values here,
        // as long as it's not one of the above reserved names.
      },
      account: {
        id: '<%= oktaClientId %>',
        emrVersion: '<%= accountData.getEmrVersion() %>',
        province: '<%= accountData.getProvince() %>',
        isWaiv2Enabled: <%= accountData.isWaiv2Enabled() %>
      },
      location: {
        transforms: [
          {
            // Remove these query parameters from all URLs to protect sensitive data. Add more as needed.
            attr: 'search',
            action: 'ExcludeKeys',
            data: function (hostname, url) {
              return ['demographic_name', 'OWASP-CSRF-TOKEN', 'last_name', 'first_name', 'hin',
                'dob', 'demoName', 'lname', 'fname', 'hnum', 'name'];
            }
          },
          {
            // Remove common tracking parameters.
            attr: 'search',
            action: 'ExcludeKeys',
            data: function (hostname, url) {
              const searchParams = new URL(url).searchParams;
              return Array.from(searchParams.keys()).filter((key) => {
                return typeof key === 'string' && key.startsWith('utm');
              });
            },
          }
        ]
      }
    });
  })('<%=escapedApiKey%>');
</script>
<% } %>
