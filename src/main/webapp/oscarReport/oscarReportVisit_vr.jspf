<!--  
/*
 * 
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved. *
 * This software is published under the GPL GNU General Public License. 
 * This program is free software; you can redistribute it and/or 
 * modify it under the terms of the GNU General Public License 
 * as published by the Free Software Foundation; either version 2 
 * of the License, or (at your option) any later version. * 
 * This program is distributed in the hope that it will be useful, 
 * but WITHOUT ANY WARRANTY; without even the implied warranty of 
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the 
 * GNU General Public License for more details. * * You should have received a copy of the GNU General Public License 
 * along with this program; if not, write to the Free Software 
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA. * 
 * 
 * <OSCAR TEAM>
 * 
 * This software was written for the 
 * Department of Family Medicine 
 * McMaster University 
 * Hamilton 
 * Ontario, Canada 
 */
-->

<%@ taglib uri="/WEB-INF/security.tld" prefix="security"%>
<%
      String roleName$ = (String)session.getAttribute("userrole") + "," + (String) session.getAttribute("user");
      boolean authed=true;
%>
<security:oscarSec roleName="<%=roleName$%>" objectName="_report,_admin.reporting" rights="r" reverse="<%=true%>">
	<%authed=false; %>
	<%response.sendRedirect("../securityError.jsp?type=_report&type=_admin.reporting");%>
</security:oscarSec>
<%
if(!authed) {
	return;
}
%>

<%@page import="org.oscarehr.common.dao.ReportProviderDao" %>
<%@page import="org.oscarehr.common.model.ReportProvider" %>


<table class="table table-bordered table-striped table-hover table-condensed" style="font-size:12px;">
	<thead>
		<tr>
		<th bgcolor="#CCCCFF">Provider</th>
		<th bgcolor="#CCCCFF">&nbsp;</th>
		<th bgcolor="#CCCCFF">Clinic</th>
		<th bgcolor="#CCCCFF">Outpatient</th>
		<th bgcolor="#CCCCFF">Hospital</th>
		<th bgcolor="#CCCCFF">ER</th>
		<th bgcolor="#CCCCFF">Nursing Home</th>
		<th bgcolor="#CCCCFF">Home</th>
		<th bgcolor="#FFCC00">Clinic</th>
		<th bgcolor="#FFCC00">Outpatient</th>
		<th bgcolor="#FFCC00">Hospital</th>
		<th bgcolor="#FFCC00">ER</th>
		<th bgcolor="#FFCC00">Nursing Home</th>
		<th bgcolor="#FFCC00">Home</th>
	</tr>

	<%
String cTotal="0",hTotal="0",oTotal="0", mNum="", fNum="";
String p_last="",p_no="",p_first="", team="", oldteam="";
String dateBegin = request.getParameter("xml_vdate");
String dateEnd = request.getParameter("xml_appointment_date");
if (dateEnd.compareTo("") == 0) dateEnd = MyDateFormat.getMysqlStandardDate(curYear, curMonth, curDay);
if (dateBegin.compareTo("") == 0) dateBegin="1950-01-01"; // set to any early date to start search from beginning
String ohipNo = request.getParameter("providerview");
ResultSet rs;
ResultSet rs2;
ResultSet rs3;
ResultSet rs4;
ResultSet rs5;
ResultSet rs6;

String[] param = new String[2];
param[0] = "visitreport";
param[1] = ohipNo;

String[] param2 = new String[6];

param2[0] = "00";
param2[1] = "01";
param2[2] = "02";
param2[3] = "03";
param2[4] = "04";
param2[5] = "05";


String[] param3 = new String[4];

String[] visitcount = new String[6];
String[] apptvisitcount = new String[6];

BigDecimal ccTotal = new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);
BigDecimal hhTotal= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);
BigDecimal BigTotal= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    

BigDecimal ooTotal= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal BigTotal0= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal BigTotal1= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal BigTotal2= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal BigTotal3= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal BigTotal4= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal BigTotal5= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    

BigDecimal Total0= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal Total1= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal Total2= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal Total3= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal Total4= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);   
BigDecimal Total5= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);   

BigDecimal ABigTotal0= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal ABigTotal1= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal ABigTotal2= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal ABigTotal3= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal ABigTotal4= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal ABigTotal5= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    

BigDecimal ATotal0= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal ATotal1= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal ATotal2= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal ATotal3= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);    
BigDecimal ATotal4= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);   
BigDecimal ATotal5= new BigDecimal(0).setScale(0, BigDecimal.ROUND_HALF_UP);   

boolean bodd=true;



for(Object[] result : reportProviderDao.search_reportprovider("visitreport", ohipNo)){
	ReportProvider rp = (ReportProvider)result[0];
	Provider provider = (Provider)result[1];
	
    p_last = provider.getLastName();
    p_first = provider.getFirstName();
    p_no = provider.getProviderNo();
    team = rp.getTeam();

    oscar.oscarReport.data.VisitReportData vrd = new oscar.oscarReport.data.VisitReportData();
    vrd.setDateBegin(dateBegin);
    vrd.setDateEnd(dateEnd);
    vrd.setProviderNo(p_no);
    visitcount = vrd.getCreatorCount();
    apptvisitcount = vrd.getApptProviderCount();

    if (oldteam.compareTo(team) != 0)  bodd=bodd?false:true; //for the color of rows

    %>
	<tr>
		<td bgcolor="<%=bodd?"#EEEEFF":"white"%>"><%=oldteam.equals(team)?"":team%></td>
		<td bgcolor="<%=bodd?"#EEEEFF":"white"%>"><%=p_no%> <%=p_last%>,<%=p_first%></td>
		<td bgcolor="<%=bodd?"#EEEEFF":"white"%>"><%=visitcount[0]%></td>
		<td bgcolor="<%=bodd?"#EEEEFF":"white"%>"><%=visitcount[1]%></td>
		<td bgcolor="<%=bodd?"#EEEEFF":"white"%>"><%=visitcount[2]%></td>
		<td bgcolor="<%=bodd?"#EEEEFF":"white"%>"><%=visitcount[3]%></td>
		<td bgcolor="<%=bodd?"#EEEEFF":"white"%>"><%=visitcount[4]%></td>
		<td bgcolor="<%=bodd?"#EEEEFF":"white"%>"><%=visitcount[5]%></td>
		<td bgcolor="<%=bodd?"#FFCC00":"white"%>"><%=apptvisitcount[0]%></td>
		<td bgcolor="<%=bodd?"#FFCC00":"white"%>"><%=apptvisitcount[1]%></td>
		<td bgcolor="<%=bodd?"#FFCC00":"white"%>"><%=apptvisitcount[2]%></td>
		<td bgcolor="<%=bodd?"#FFCC00":"white"%>"><%=apptvisitcount[3]%></td>
		<td bgcolor="<%=bodd?"#FFCC00":"white"%>"><%=apptvisitcount[4]%></td>
		<td bgcolor="<%=bodd?"#FFCC00":"white"%>"><%=apptvisitcount[5]%></td>
	</tr>
	<%
    Total0= new BigDecimal(Integer.parseInt(visitcount[0].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);    
    Total1= new BigDecimal(Integer.parseInt(visitcount[1].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);    
    Total2= new BigDecimal(Integer.parseInt(visitcount[2].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);    
    Total3= new BigDecimal(Integer.parseInt(visitcount[3].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);    
    Total4= new BigDecimal(Integer.parseInt(visitcount[4].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);  
    Total5= new BigDecimal(Integer.parseInt(visitcount[5].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);  
    BigTotal0 = BigTotal0.add(Total0);
    BigTotal1 = BigTotal1.add(Total1);
    BigTotal2 = BigTotal2.add(Total2);
    BigTotal3 = BigTotal3.add(Total3);
    BigTotal4 = BigTotal4.add(Total4);
    BigTotal5 = BigTotal5.add(Total5);

    ATotal0= new BigDecimal(Integer.parseInt(apptvisitcount[0].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);    
    ATotal1= new BigDecimal(Integer.parseInt(apptvisitcount[1].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);    
    ATotal2= new BigDecimal(Integer.parseInt(apptvisitcount[2].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);    
    ATotal3= new BigDecimal(Integer.parseInt(apptvisitcount[3].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);    
    ATotal4= new BigDecimal(Integer.parseInt(apptvisitcount[4].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);  
    ATotal5= new BigDecimal(Integer.parseInt(apptvisitcount[5].toString())).setScale(0, BigDecimal.ROUND_HALF_UP);  
    ABigTotal0 = ABigTotal0.add(ATotal0);
    ABigTotal1 = ABigTotal1.add(ATotal1);
    ABigTotal2 = ABigTotal2.add(ATotal2);
    ABigTotal3 = ABigTotal3.add(ATotal3);
    ABigTotal4 = ABigTotal4.add(ATotal4);
    ABigTotal5 = ABigTotal5.add(ATotal5);
    oldteam = team;
} %>

	<tr>
		<td bgcolor="#CCCCFF">&nbsp;</td>
		<td bgcolor="#CCCCFF">TOTAL</td>
		<td bgcolor="#CCCCFF"><%=BigTotal0%></td>
		<td bgcolor="#CCCCFF"><%=BigTotal1%></td>
		<td bgcolor="#CCCCFF"><%=BigTotal2%></td>
		<td bgcolor="#CCCCFF"><%=BigTotal3%></td>
		<td bgcolor="#CCCCFF"><%=BigTotal4%></td>
		<td bgcolor="#CCCCFF"><%=BigTotal5%></td>
		<td bgcolor="#FFCC00"><%=ABigTotal0%></td>
		<td bgcolor="#FFCC00"><%=ABigTotal1%></td>
		<td bgcolor="#FFCC00"><%=ABigTotal2%></td>
		<td bgcolor="#FFCC00"><%=ABigTotal3%></td>
		<td bgcolor="#FFCC00"><%=ABigTotal4%></td>
		<td bgcolor="#FFCC00"><%=ABigTotal5%></td>
	</tr>
</table>
