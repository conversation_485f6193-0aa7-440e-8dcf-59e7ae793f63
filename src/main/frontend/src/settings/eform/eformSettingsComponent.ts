/**
* Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
* This software is published under the GPL GNU General Public License.
* This program is free software; you can redistribute it and/or
* modify it under the terms of the GNU General Public License
* as published by the Free Software Foundation; either version 2
* of the License, or (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program; if not, write to the Free Software
* Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
*
* This software was written for
* CloudPractice Inc.
* Victoria, British Columbia
* Canada
*/

angular.module('Settings').component('eformSettings',
	{
		templateUrl: 'src/settings/eform/eformSettings.jsp',
		bindings: {
			pref: "=",
			groupNames: "<",
		},
		controller: [
			'$stateParams',
			function(
				$stateParams,
			)
			{
				const ctrl = this;

				ctrl.formGroupNames = [
					{
						"value": "",
						"label": "None"
					}];

				ctrl.$onInit = (): void =>
				{
					ctrl.pref = ctrl.pref || $stateParams.pref;
					ctrl.groupNames = ctrl.groupNames || [];

					//convert to value/label object list from string array

					for (let i = 0; i < ctrl.groupNames.length; i++)
					{
						ctrl.formGroupNames.push(
							{
								"value": ctrl.groupNames[i],
								"label": ctrl.groupNames[i]
							});
					}
				}
			}]
	});