package ca.kai.document;

import ca.kai.OscarProperties;
import ca.kai.caseManagementNote.CaseManagementNote;
import ca.kai.caseManagementNote.CaseManagementNoteLink;
import ca.kai.caseManagementNote.CaseManagementNoteLinkRepository;
import ca.kai.caseManagementNote.CaseManagementNoteRepository;
import ca.kai.ctlDocument.CtlDocument;
import ca.kai.ctlDocument.CtlDocumentModule;
import ca.kai.ctlDocument.CtlDocumentPk;
import ca.kai.ctlDocument.CtlDocumentRepository;
import ca.kai.demographic.Demographic;
import ca.kai.demographic.DemographicExtKey;
import ca.kai.demographic.DemographicExtRepository;
import ca.kai.demographic.DemographicRepository;
import ca.kai.incomingLabRules.IncomingLabRule;
import ca.kai.incomingLabRules.IncomingLabRuleRepository;
import ca.kai.incomingLabRules.IncomingLabRuleService;
import ca.kai.incomingLabRules.IncomingLabRuleType;
import ca.kai.patientLabRouting.PatientDocumentRouting;
import ca.kai.patientLabRouting.PatientDocumentRoutingRepository;
import ca.kai.program.ProgramService;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderRepository;
import ca.kai.providerLabRouting.ProviderDocumentRouting;
import ca.kai.providerLabRouting.ProviderDocumentRoutingRepository;
import ca.kai.providerLabRouting.ProviderRouting;
import ca.kai.secRole.SecRole;
import ca.kai.secRole.SecRoleRepository;
import ca.kai.util.PdfUtils;
import ca.kai.util.TempFileManager;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Font;
import com.lowagie.text.PageSize;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import java.awt.Color;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.imageio.ImageIO;
import lombok.AllArgsConstructor;
import lombok.val;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.pdfbox.io.RandomAccessFile;
import org.apache.pdfbox.pdfparser.PDFParser;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@AllArgsConstructor
public class DocumentService {
    private final DocumentRepository documentRepository;
    private final DocumentCommentRepository documentCommentRepository;
    private final QueueDocumentLinkRepository queueDocumentLinkRepository;
    private final DemographicRepository demographicRepository;
    private final ProviderRepository providerRepository;
    private final ProviderDocumentRoutingRepository providerDocumentRoutingRepository;
    private final PatientDocumentRoutingRepository patientDocumentRoutingRepository;
    private final CtlDocumentRepository ctlDocumentRepository;
    private final SecRoleRepository secRoleRepository;
    private final CaseManagementNoteRepository caseManagementNoteRepository;
    private final CaseManagementNoteLinkRepository caseManagementNoteLinkRepository;
    private final IncomingLabRuleRepository incomingLabRuleRepository;
    private final DemographicExtRepository demographicExtRepository;
    private final IncomingLabRuleService incomingLabRuleService;
    private final ProgramService programService;
    private final OscarProperties oscarProperties;

    private static final Logger logger = LoggerFactory.getLogger(DocumentService.class);

    public ProviderDocumentRouting addComment(String providerNo, Integer documentNo, String comment){
        ProviderDocumentRouting providerDocumentRouting;

        // set comment to provider routing for backwards compatibility
        List<ProviderDocumentRouting> providerDocumentRoutings = providerDocumentRoutingRepository.getAllByDocumentAndProvider(documentNo, providerNo);
        if (providerDocumentRoutings.size() > 0) {
            for (ProviderDocumentRouting routing : providerDocumentRoutings) {
                if (!"0".equals(providerNo) && StringUtils.isNotBlank(comment)) {
                    routing.setComment(comment);
                }
                if (routing.getStatus().equals(ProviderRouting.ARCHIVED_STATUS)) {
                    routing.setStatus(ProviderRouting.NEW_STATUS);
                }
                providerDocumentRoutingRepository.save(routing);
            }
            providerDocumentRouting = providerDocumentRoutings.get(0);
        } else {
            providerDocumentRoutings =
                providerDocumentRoutingRepository.getAllByDocumentAndProvider(documentNo, "0");
            if (providerDocumentRoutings.size() > 0) {
                providerDocumentRouting = providerDocumentRoutings.get(0);
                if (providerRepository.getByProviderNo(providerNo) != null) {
                    providerDocumentRouting.setProvider(
                        providerRepository.getByProviderNo(providerNo)
                    );
                }
                if (StringUtils.isNotEmpty(comment)) {
                    providerDocumentRouting.setComment(comment);
                }
            } else {
                providerDocumentRouting = createProviderDocumentRouting(providerNo, documentNo);
                if (!"0".equals(providerNo) && StringUtils.isNotBlank(comment)) {
                    providerDocumentRouting.setComment(comment);
                }
            }
           providerDocumentRoutingRepository.save(providerDocumentRouting);
        }

        // save comment
        if (providerDocumentRouting != null && providerDocumentRouting.getDocument() != null && !"0".equals(providerNo) && StringUtils.isNotBlank(comment)) {
            documentCommentRepository.save(new DocumentComment(providerNo, providerDocumentRouting.getDocument().getDocumentNo(), comment));
        }

        return providerDocumentRouting;
    }
    
    public Document getDocument(Integer documentNo) {
        Document document = documentRepository.getDocumentById(documentNo);
        setDocumentRoutings(document);
        setDocumentFileData(document);
        return document;
    }

    public byte[] getDocumentBytes(Document document, Integer page) {
        return getDocumentBytes(document, page, null, false);
    }
    
    public byte[] getDocumentBytes(Document document, Integer page, Integer rotateBy, Boolean rotateAll) {
        String documentDir = oscarProperties.getProperty("DOCUMENT_DIR");
        if (documentDir == null) {
            logger.error("Error: No DOCUMENT_DIR is set in oscar.properties when fetching document");
            documentDir = "";
        }
        String fileName = documentDir + document.getDocFileName();
        byte[] documentBytes = new byte[0];
        try {
            File file = new File(fileName);
            if (file.exists()) {
                String imageExtension = new Tika().detect(file);
                imageExtension = imageExtension.substring(imageExtension.indexOf('/') + 1);
                if (document.getContentType().equals("application/pdf") || imageExtension.equals("pdf")) {
                    if (page == null) {
                        return FileUtils.readFileToByteArray(file);
                    }
                    PDDocument pdf = PDDocument.load(file);
                    if (rotateBy != null) {
                        pdf = rotatePdf(pdf, rotateBy, page, rotateAll);
                    }
                    try {
                        documentBytes = renderPDFPage(pdf, page);
                        if (rotateBy != null) {
                            // Save pdf when rotated and rendering completes without exception
                            pdf.save(file);
                        }
                    } catch (Exception e) {
                        logger.error("Failed to generate pdf image preview (page=" + page + "):", e);
                    }
                    pdf.close();
                } else if (Arrays.asList(ImageIO.getReaderFormatNames()).contains(imageExtension)) {
                    documentBytes = renderImage(rotateBy, file, imageExtension);
                } else {
                    logger.warn("Document file type ." + imageExtension + " not supported");
                    documentBytes = generatePreviewErrorImage();
                }
            }
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        return documentBytes;
    }

    public PDDocument rotatePdf(PDDocument pdf, Integer rotateBy, Integer rotatePage, Boolean rotateAll) {
        if (rotateAll) {
            for (PDPage currentPage : pdf.getPages()) {
                Integer rotation = (currentPage.getRotation());
                currentPage.setRotation((rotation + rotateBy) % 360);
            }
        } else {
            PDPage currentPage = pdf.getPage(rotatePage - 1);
            Integer rotation = (currentPage.getRotation());
            currentPage.setRotation((rotation + rotateBy) % 360);
        }
        return pdf;
    }

    public BufferedImage rotateImage(BufferedImage image, Integer rotateByDegrees) {
        double rotateBy = Math.toRadians(rotateByDegrees);
        double sin = Math.abs(Math.sin(rotateBy));
        double cos = Math.abs(Math.cos(rotateBy));
        int oldWidth = image.getWidth();
        int oldHeight = image.getHeight();
        int newWidth = (int) Math.floor(oldWidth * cos + oldHeight * sin);
        int newHeight = (int) Math.floor(oldHeight * cos + oldWidth * sin);

        // apply transformations using trigonometry to get correct image size and rotation
        AffineTransform transform = new AffineTransform();
        transform.translate((newWidth - oldWidth) / 2, (newHeight - oldHeight) / 2);
        transform.rotate(rotateBy, oldWidth / 2, oldHeight / 2);
        AffineTransformOp op = new AffineTransformOp(transform, AffineTransformOp.TYPE_BILINEAR);

        // Create new image from transformed old image, with correct width and height
        BufferedImage newImage = new BufferedImage(newWidth, newHeight, image.getType());
        op.filter(image, newImage);
        return newImage;
    }

    public byte[] renderPDFPage(PDDocument pdf, Integer page) {
        byte[] pdfBytes = new byte[0];
        try {
            PDFRenderer rend = new PDFRenderer(pdf);
            //Page index starts at 0, subtracts 1 to account for that
            BufferedImage image = rend.renderImageWithDPI(page - 1, 144f);
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                ImageIO.write(image, "png", baos);
                pdfBytes = baos.toByteArray();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        return pdfBytes;
    }

    /**
     * Routes a document to a provider as well as providers they are currently forwarding to
     * @param providerNo Provider number to forward the document to
     * @param documentNo Document number to forward
     * @return The created provider document routing for the given provider
     */
    public ProviderDocumentRouting routeProvider(String providerNo, Integer documentNo) {
        List<ProviderDocumentRouting> existingRoutings = providerDocumentRoutingRepository.getAllByDocumentAndProvider(documentNo, providerNo);
        ProviderDocumentRouting providerDocumentRouting;
        if (existingRoutings.size() == 0) {
            providerDocumentRouting = createProviderDocumentRouting(providerNo, documentNo);
            //Checks if there are any general inbox records, if so then route to it instead of making a new one
            existingRoutings = providerDocumentRoutingRepository.getAllByDocumentAndProvider(documentNo, "0");
            if (existingRoutings.size() > 0) {
                providerDocumentRouting.setId(existingRoutings.get(0).getId());
            }

            providerDocumentRoutingRepository.save(providerDocumentRouting);
            
            // Checks if the provider has any forwarding rules 
            List<IncomingLabRule> incomingLabRules = incomingLabRuleRepository.getAllByProviderNo(providerNo);
            if (incomingLabRules.size() > 0) {
                List<ProviderDocumentRouting> forwardRoutings;
                for (IncomingLabRule rule : incomingLabRules) {
                    // Filters the ruleTypes to check if the provider has documents being forwarded
                    Boolean routeDocuments = (rule.getRuleTypes().stream().filter(r -> r.getType().equals(IncomingLabRuleType.DOCUMENT)).collect(Collectors.toList())).size() > 0;
                    if (routeDocuments) {
                        forwardRoutings = providerDocumentRoutingRepository.getAllByDocumentAndProvider(documentNo, rule.getForwardProviderNo());
                        if (forwardRoutings.size() == 0) {
                            routeProvider(rule.getForwardProviderNo(), documentNo);
                        }
                    }
                }

                // Updates and saves the status of the providerDocumentRouting
                providerDocumentRouting.setStatus(incomingLabRules.get(0).getStatus());
                providerDocumentRoutingRepository.save(providerDocumentRouting);
            }
        }
        else {
            providerDocumentRouting = existingRoutings.get(0);
            if (ProviderRouting.ARCHIVED_STATUS.equals(providerDocumentRouting.getStatus())) {
                providerDocumentRouting.setStatus(ProviderRouting.NEW_STATUS);
                providerDocumentRoutingRepository.save(providerDocumentRouting);
            }
        }
        
        return providerDocumentRouting;
    }
    
    public DocumentComment updateComment(DocumentComment documentComment) {
        return documentCommentRepository.save(documentComment);
    }

    public ProviderDocumentRouting updateStatus(String providerNo, Integer documentNo, String status) {
        return updateStatus(providerNo, documentNo, status, null);
    }
    public ProviderDocumentRouting updateStatus(String providerNo, Integer documentNo, String status, @Nullable String comment) {
        ProviderDocumentRouting providerDocumentRouting;
        List<ProviderDocumentRouting> providerDocumentRoutings = providerDocumentRoutingRepository.getAllByDocumentAndProvider(documentNo, providerNo);
        if (providerDocumentRoutings.size() > 0) {
            for (ProviderDocumentRouting routing : providerDocumentRoutings) {
                if (ProviderRouting.ARCHIVED_STATUS.equals(routing.getStatus()) && !ProviderRouting.ARCHIVED_STATUS.equals(status) && "Archived".equals(routing.getComment())) {
                    // clear Archived comment if updating status (ie. Undo Remove Provider)
                    routing.setComment("");
                }
                routing.setStatus(status);
                routing.setTimestamp(new Date());
                if (StringUtils.isNotEmpty(comment)) {
                    routing.setComment(comment);
                }
                providerDocumentRoutingRepository.save(routing);
            }

            providerDocumentRouting = providerDocumentRoutings.get(0);
        } else {
            //Reuses the providerDocumentRoutings to check if there are any records in the unclaimed inbox
            providerDocumentRoutings = providerDocumentRoutingRepository.getAllByDocumentAndProvider(documentNo, "0");
            
            providerDocumentRouting = createProviderDocumentRouting(providerNo, documentNo);
            //If there is a routing to the unclaimed inbox, update it instead of creating a new one
            if (providerDocumentRoutings.size() > 0) {
                providerDocumentRouting.setId(providerDocumentRoutings.get(0).getId());
            }
            providerDocumentRouting.setStatus(status);
            if (StringUtils.isNotEmpty(comment)) {
                providerDocumentRouting.setComment(comment);
            }
            providerDocumentRoutingRepository.save(providerDocumentRouting);
        }
        
        // update forwarding provider status (if applicable)
        incomingLabRuleService.updateInboxItemStatusForForwardedProvider(documentNo, "DOC", providerNo, status);
        // save comment
        if (providerDocumentRouting != null
            && providerDocumentRouting.getDocument() != null
            && !"0".equals(providerNo)
            && StringUtils.isNotBlank(comment)) {
          documentCommentRepository.save(
              new DocumentComment(
                  providerNo, providerDocumentRouting.getDocument().getDocumentNo(), comment));
        }

        return providerDocumentRouting;
    }

    public Document routeDemographic(Provider loggedInProvider, Integer demographicNo, Integer documentNo){
        Document document = documentRepository.getDocumentById(documentNo);
        Demographic demographic = demographicRepository.findOne(demographicNo);

        val providerTypes = Arrays.asList(
            DemographicExtKey.NURSE.getKey(),
            DemographicExtKey.MIDWIFE.getKey(),
            DemographicExtKey.RESIDENT.getKey()
        );
        val demographicExts = demographicExtRepository.getAllByDemographicNumberAndKeyIn(demographicNo, providerTypes);

        PatientDocumentRouting patientDocumentRouting = new PatientDocumentRouting();
        patientDocumentRouting.setDocument(document);
        patientDocumentRouting.setDemographic(demographic);
        patientDocumentRouting.setCreated(new Date());
        patientDocumentRouting.setDateModified(new Date());

        patientDocumentRoutingRepository.save(patientDocumentRouting);
        CtlDocumentPk ctlDocumentId = new CtlDocumentPk(CtlDocumentModule.DEMOGRAPHIC.toDbString(), demographicNo, documentNo);
        if (ctlDocumentRepository.findOne(ctlDocumentId) == null) {
            ctlDocumentRepository.updateDemographicByDocument(demographicNo, documentNo);
        }

        document.setProviderDocumentRoutings(providerDocumentRoutingRepository.getAllProviderDocumentRoutingsByDocumentNo(documentNo));
        // try and find an existing routing for the demographic's provider
        String demoProviderNo = demographic.getProvider() != null ? StringUtils.trimToEmpty(demographic.getProvider().getProviderNo()) : "";
        ProviderRouting demographicProviderRouting = document.getProviderDocumentRoutings().stream()
                    .filter(routing -> routing.getProvider() != null && demoProviderNo.equals(routing.getProvider().getProviderNo()))
                    .findFirst().orElse(null);

        // if the demographic has a provider and there is no existing routing for the demographic's provider
        if (demographic.getProvider() != null && demographicProviderRouting != null) {
            // create a new routing
            ProviderDocumentRouting providerDocumentRouting = createProviderDocumentRouting(demographic.getProvider(), document);
            providerDocumentRoutingRepository.save(providerDocumentRouting);
        }

        if (oscarProperties.isEnabled("queens_resident_tagging")) {
            for (var demographicExt : demographicExts) {
                if (demographicExt.getValue() != null && !demographicExt.getValue().isEmpty()) {
                    val alternateProviderDocumentRouting = createProviderDocumentRouting(
                        providerRepository.getByProviderNo(demographicExt.getValue()),
                        document
                    );
                    providerDocumentRoutingRepository.save(alternateProviderDocumentRouting);
                }
            }
        }

        createCppNoteForDocument(loggedInProvider, demographicNo, documentNo, document.getDocDesc());
        setDocumentRoutings(document);

        return document;
    }
    
    public DocumentUploadResult uploadDocument(MultipartFile file, Provider loggedInProvider, String destination, Integer assignedQueueId, IncomingDocumentFolder destinationFolder, String assignedProviderNo) throws DocumentUploadException {
        if (destination.equals("incomingDocs")) {
            return uploadDocumentToIncomingDocs(file, assignedQueueId, destinationFolder);
        } else {
            return uploadDocumentToPendingDocs(file, loggedInProvider, assignedQueueId, assignedProviderNo);
        }
    }
    
    private DocumentUploadResult uploadDocumentToPendingDocs(MultipartFile file, Provider loggedInProvider, Integer assignedQueueId, String assignedProviderNo) throws DocumentUploadException {
        SimpleDateFormat fileDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = fileDateFormat.format(new Date()) + file.getOriginalFilename();

        // Write file to documents folder
        String documentDir = oscarProperties.getProperty("DOCUMENT_DIR");
        if (documentDir == null) {
            logger.error("Error: No DOCUMENT_DIR is set in oscar.properties when fetching document");
            documentDir = "";
        }
        File locallySavedFile = new File(documentDir + File.separator + fileName);
        try {
            writeDocumentFile(locallySavedFile, file.getBytes());
        } catch (IOException e) {
            logger.error(DocumentUploadException.UNABLE_TO_WRITE_FILE_DATA, e);
            throw new DocumentUploadException(DocumentUploadException.UNABLE_TO_WRITE_FILE_DATA, e);
        }
        
        // If the file is a pdf, attempt to parse and get amount of pages
        int numberOfPages = 0;
        try {
            String fileExtension = new Tika().detect(locallySavedFile);
            fileExtension = fileExtension.substring(fileExtension.indexOf('/') + 1);
            if (file.getContentType().equals("application/pdf") || fileExtension.equals("pdf")) {
                PDFParser parser = new PDFParser(new RandomAccessFile(locallySavedFile, "rw"));
                parser.parse();
                numberOfPages = parser.getPDDocument().getNumberOfPages();
                parser.getPDDocument().close();
            }
        } catch (IOException e) {
            logger.error(DocumentUploadException.UNABLE_TO_READ_FILE_DATA, e);
            throw new DocumentUploadException(DocumentUploadException.UNABLE_TO_READ_FILE_DATA, e);
        }

        Document newDocument = new Document();
        newDocument.setDocType("");
        newDocument.setDocClass("");
        newDocument.setDocSubClass("");
        newDocument.setDocDesc("");
        newDocument.setDocXml("");
        newDocument.setDocFileName(fileName);
        newDocument.setDocCreator(loggedInProvider);
        newDocument.setResponsible(loggedInProvider.getProviderNo());
        newDocument.setSource("");
        newDocument.setSourceFacility("");

        Integer programNo = programService.getProgramIdByProvider(loggedInProvider.getProviderNo());
        newDocument.setProgramId(programNo);

        newDocument.setUpdateDateTime(new Date());
        newDocument.setStatus("A");
        newDocument.setContentType(file.getContentType());
        newDocument.setContentDateTime(new Date());
        newDocument.setDocumentPublic("0");
        newDocument.setObservationDate(new Date());
        newDocument.setReviewer(null);
        newDocument.setNumberOfPages(numberOfPages);
        newDocument.setAppointmentNo(-1);
        newDocument.setRestrictToProgram(0);
        newDocument.setAbnormal(false);
        documentRepository.save(newDocument);

        CtlDocumentPk newCtlDocumentKey = new CtlDocumentPk();
        newCtlDocumentKey.setModule("demographic");
        newCtlDocumentKey.setModuleId(-1);
        newCtlDocumentKey.setDocumentNo(newDocument.getDocumentNo());
        CtlDocument newCtlDocument = new CtlDocument();
        newCtlDocument.setId(newCtlDocumentKey);
        newCtlDocument.setStatus(newDocument.getStatus());
        ctlDocumentRepository.save(newCtlDocument);

        // Route to assigned provider
        routeProvider(assignedProviderNo, newDocument.getDocumentNo());

        if (assignedQueueId != null && assignedQueueId != -1) {
            QueueDocumentLink queueDocumentLink = new QueueDocumentLink();
            queueDocumentLink.setDocId(newDocument.getDocumentNo());
            queueDocumentLink.setStatus("A");
            queueDocumentLink.setQueueId(assignedQueueId);
            queueDocumentLinkRepository.save(queueDocumentLink);
        }
        
        DocumentUploadResult uploadResult = new DocumentUploadResult();
        uploadResult.setFileName(file.getOriginalFilename());
        uploadResult.setUploadedDocument(newDocument);
        uploadResult.setUploadSuccess(true);
        return uploadResult;
    }
    
    private DocumentUploadResult uploadDocumentToIncomingDocs(MultipartFile file, Integer assignedQueueId, IncomingDocumentFolder destinationFolder) throws DocumentUploadException {
        
        SimpleDateFormat fileDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileName = fileDateFormat.format(new Date()) + file.getOriginalFilename();
        
        String documentFolderDir = getIncomingDocumentsFolderDir(assignedQueueId, destinationFolder);
        File locallySavedFile = new File(documentFolderDir + File.separator + fileName);
        if (locallySavedFile.exists()) {
            throw new DocumentUploadException(DocumentUploadException.FILE_WITH_SAME_NAME_EXISTS);
        } else {
            try {
                // Check for if the file is not a pdf
                String fileExtension = new Tika().detect(file.getBytes());
                fileExtension = fileExtension.substring(fileExtension.indexOf('/') + 1);
                if (!file.getContentType().equals("application/pdf") && !fileExtension.equals("pdf")) {
                    throw new DocumentUploadException(DocumentUploadException.INCOMING_DOCS_PDF_ONLY);
                }
                writeDocumentFile(locallySavedFile, file.getBytes());
            } catch (IOException e) {
                logger.error(DocumentUploadException.UNABLE_TO_READ_FILE_DATA, e);
                throw new DocumentUploadException(DocumentUploadException.UNABLE_TO_READ_FILE_DATA, e);
            }
        }
        DocumentUploadResult uploadResult = new DocumentUploadResult();
        uploadResult.setFileName(file.getOriginalFilename());
        uploadResult.setDestinationFolder(destinationFolder.getFolderName());
        uploadResult.setUploadSuccess(true);
        return uploadResult;
    }

    public File readDocumentFile(Document document) {
        String documentDir = oscarProperties.getProperty("DOCUMENT_DIR");
        if (documentDir == null) {
            logger.error("Error: No DOCUMENT_DIR is set in oscar.properties when fetching document");
            documentDir = "";
        }
        String fileName = documentDir + document.getDocFileName();

        return new File(fileName);
    }

    public String printWithTempFile(final Integer id) throws IOException {
        val document = documentRepository.getDocumentById(id);
        if (document == null) {
            throw new IllegalArgumentException("Document with id " + id + " not found");
        }
        var documentByteArray = new byte[0];

        val contentType = document.getContentType();
        if (contentType.startsWith("image/")) {
            documentByteArray = convertImageToPdf(
                FileUtils.readFileToByteArray(readDocumentFile(document)));
        } else {
            documentByteArray = FileUtils.readFileToByteArray(readDocumentFile(document));
        }
        try (val fileContents = PDDocument.load(documentByteArray);) {
            val tempFile = TempFileManager.createTempFile("Document", ".pdf");
            fileContents.save(tempFile);
            return tempFile.getPath();
        }
    }

    byte[] convertImageToPdf(final byte[] imageBytes) {
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);
            PDImageXObject pdImage = PDImageXObject.createFromByteArray(document, imageBytes,
                "image");

            val imageWidth = pdImage.getWidth();
            val imageHeight = pdImage.getHeight();
            val aspectRatio = (float) imageWidth / imageHeight;

            PDRectangle pageSize = page.getMediaBox();
            val pageWidth = pageSize.getWidth();
            val pageHeight = pageSize.getHeight();

            var width = Math.min(pageWidth, imageWidth);
            var height = width / aspectRatio;

            if (height > pageHeight) {
                height = Math.min(pageHeight, imageHeight);
                width = height * aspectRatio;
            }

            val xPosition = (pageWidth - width) / 2;
            val yPosition = (pageHeight - height) / 2;

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                contentStream.drawImage(pdImage, xPosition, yPosition, width, height);
            }

            ByteArrayOutputStream output = new ByteArrayOutputStream();
            document.save(output);
            return output.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Failed to convert image to PDF", e);
        }
    }

    private boolean writeDocumentFile(File newLocalDocumentFile, byte[] documentBytes) {
        boolean writeSuccessful = false;
        try {
            FileUtils.writeByteArrayToFile(newLocalDocumentFile, documentBytes);
            writeSuccessful = true;
        } catch (IOException e) {
            logger.error("Error writing new document with name \"" + newLocalDocumentFile.getName() + "\"", e);
        }
        
        return writeSuccessful;
    }

    private String getIncomingDocumentsFolderDir(Integer assignedQueueId, IncomingDocumentFolder destinationFolder) {

        // create folder for new file
        String incomingDocumentDir = oscarProperties.getProperty("INCOMINGDOCUMENT_DIR");
        if (!incomingDocumentDir.endsWith(File.separator)) {
            incomingDocumentDir += File.separator;
        }

        return incomingDocumentDir + assignedQueueId + File.separator + destinationFolder.getFolderName() + File.separator;
    }

    void createCppNoteForDocument(Provider loggedInProvider, Integer demographicNo, Integer documentId, String documentDescription){
        Date now = new Date();

        CaseManagementNote note = new CaseManagementNote();
        note.setDemographicNo(demographicNo);
        note.setUpdateDate(now);
        note.setObservationDate(now);
        note.setProviderNo("-1");
        String provFirstName = "";
        String provLastName = "";
        if (loggedInProvider != null) {
            provFirstName = loggedInProvider.getFirstName();
            provLastName = loggedInProvider.getLastName();
        }

        String strNote = "Document" + " " + documentDescription + " " + "created at " + now + " by " + provFirstName + " " + provLastName + ".";

        
        note.setNote(strNote);
        note.setSigned(true);
        note.setSigningProviderNo("-1");

        Integer programNo = programService.getProgramIdByProvider(loggedInProvider.getProviderNo());
        note.setProgramNo(programNo.toString());

        SecRole doctorRole = secRoleRepository.getByRoleName("doctor");
        note.setReporterCaisiRole(doctorRole.getId().toString());

        note.setReporterProgramTeam("0");
        note.setPassword(null);
        note.setLocked(false);
        note.setHistory(strNote);
        note.setPosition(0);

        note.setUuid(UUID.randomUUID().toString());

        caseManagementNoteRepository.save(note);

        // Add a noteLink to casemgmt_note_link
        CaseManagementNoteLink noteLink = new CaseManagementNoteLink();
        noteLink.setTableName(CaseManagementNoteLink.DOCUMENT);
        noteLink.setTableId(Long.valueOf(documentId));
        noteLink.setNoteId(note.getId());
        
        caseManagementNoteLinkRepository.save(noteLink);
    }

    ProviderDocumentRouting createProviderDocumentRouting(String providerNo, Integer documentNo) {
        ProviderDocumentRouting providerDocumentRouting;
        if ("0".equals(providerNo)) {
            providerDocumentRoutingRepository.insertUnclaimedEntry(documentNo);
            providerDocumentRouting = providerDocumentRoutingRepository.getRecentByDocumentAndProvider(documentNo, providerNo);
        } else {
            Document document = documentRepository.getDocumentById(documentNo);
            Provider provider = providerRepository.findOne(providerNo);
            providerDocumentRouting = createProviderDocumentRouting(provider, document);
        }
       
        return providerDocumentRouting;
    }

    ProviderDocumentRouting createProviderDocumentRouting(Provider provider, Document document) {
        ProviderDocumentRouting providerDocumentRouting = new ProviderDocumentRouting();
        providerDocumentRouting.setDocument(document);
        providerDocumentRouting.setProvider(provider);
        providerDocumentRouting.setStatus(ProviderRouting.NEW_STATUS);
        providerDocumentRouting.setTimestamp(new Date());

        return providerDocumentRouting;
    }
    
    void setDocumentRoutings(Document document) {
        Integer documentNo = document.getDocumentNo();
        
        List<ProviderDocumentRouting> providerDocumentRoutings = providerDocumentRoutingRepository.getAllProviderDocumentRoutingsByDocumentNo(documentNo);
        List<PatientDocumentRouting> patientDocumentRoutings = patientDocumentRoutingRepository.getAllPatientDocumentRoutingsByDocumentId(documentNo);
        
        CtlDocument ctlDocument = ctlDocumentRepository.getMatchedByDocumentNo(documentNo);
        PatientDocumentRouting patientRouting = patientDocumentRoutings.isEmpty() ? null : patientDocumentRoutings.get(0);
        
        if (patientRouting == null && ctlDocument != null && ctlDocument.getId() != null) {
            Demographic demographic = demographicRepository.getByDemographicNumber(ctlDocument.getId().getModuleId());
            if (demographic != null) {
                patientRouting = new PatientDocumentRouting();
                patientRouting.setDocument(document);
                patientRouting.setDemographic(demographic);
                try { 
                    patientDocumentRoutingRepository.save(patientRouting);
                } catch (Exception e) {
                    logger.error("Error creating patient lab routing from CtlDocument", e);
                    patientRouting = null;
                }
            }
        }

        document.setPatientDocumentRouting(patientRouting);
        document.setProviderDocumentRoutings(providerDocumentRoutings);
        document.setArchivedProviderDocumentRoutings(
            providerDocumentRoutingRepository.getAllArchivedProviderDocumentRoutingsByDocumentNo(
                documentNo)
        );
    }

    private void setDocumentFileData(final Document document) {
        document.setFileData(documentPagePreviewUrlGenerator(document));
    }

    public String documentPagePreviewUrlGenerator(final Document document) {
        return this.documentPagePreviewUrlGenerator(document.getDocumentNo(), 1);
    }

    public String documentPagePreviewUrlGenerator(final Integer documentNumber, final Integer page) {
        return "/" + oscarProperties.getProperty("kaiemr_deployed_context", "kaiemr")
            + "/api/document/getPagePreview/"
            + documentNumber + "?page=" + page;
    }

    private byte[] generatePreviewErrorImage() {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] pdfBytes = new byte[0];

        try {

            BaseFont baseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.EMBEDDED);
            Font font = new Font(baseFont, 9, Font.NORMAL);
            Rectangle pageSize = PageSize.getRectangle("A6");
            float margin = 10;
            float innerWidth = pageSize.getWidth() - (margin * 2);

            // Main table declaration
            PdfPTable rxTable = new PdfPTable(2);
            rxTable.setWidthPercentage(100);
            rxTable.setWidths(new int[]{1, 3});
            rxTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);

            com.lowagie.text.Document document = new com.lowagie.text.Document(pageSize, margin, margin,
                margin,
                margin + 15f);
            PdfWriter pdfWriter = PdfWriter.getInstance(document, baos);

            // Set footer into main table and set it as the table's footer
            PdfPCell cell = new PdfPCell();
            cell.setColspan(2);
            cell.setBorder(PdfPCell.LEFT + PdfPCell.RIGHT);
            cell.addElement(new Phrase("Error generating PDF preview, click here to view document in browser"));
            rxTable.addCell(cell);
            rxTable.setHeaderRows(1);
            rxTable.setFooterRows(1);


            document.add(rxTable);
            document.close();

            // Add page count and disclaimer
            pdfBytes = PdfUtils.addPageCountFooters(baos.toByteArray());
        } catch (DocumentException | IOException e) {
            logger.error("Error creating PDF document ", e);
        }

        // Get pdf as a PDD document and return:
        return pdfBytes;
    }

  /**
   * Generates a preview image for the given attachment manager document for the first pages up
   * to the specified page count.
   *
   * @param document the document to preview
   * @param pageCount the number of pages to render for PDF documents
   * @return a byte array representing the preview image, or an error image if the document type is
   *     not supported
   */
  public byte[] getDocumentForAttachmentManagerPreview(final Document document, Integer pageCount) {
    if (document == null) {
      return new byte[0];
    }

    if (pageCount == null) {
      pageCount = 5;
    }

    try {
      val documentType = StringUtils.substringAfter(document.getContentType(), "/");
      return generatePreviewByType(document, pageCount, documentType);
    } catch (Exception e) {
      logger.error(
          "Error generating attachment manager preview for document: {}",
          document.getDocumentNo(),
          e);
      return new byte[0];
    }
  }

  private byte[] generatePreviewByType(
      final Document document,
      final Integer pageCount,
      final String documentType
  ) throws IOException {
    if ("pdf".equals(documentType)) {
      try (val pdf = PDDocument.load(readDocumentFile(document))) {
        return renderPdfPages(pdf, pageCount);
      }
    } else if (Arrays.asList(ImageIO.getReaderFormatNames()).contains(documentType)) {
      return renderImage(null, readDocumentFile(document), documentType);
    } else {
      logger.warn(
          "Document file type .{} not supported for attachment manager preview", documentType);
    }

    return new byte[0];
  }

  /**
   * Renders the document as a composite image made of the first pages of the document up to the
   * specified page count.
   *
   * @param document the document to render
   * @param pageCount the number of pages to render; if null, defaults to 5
   * @return a byte array representing the rendered image, or an error image if the document cannot
   *     be rendered
   */
  protected byte[] renderPdfPages(final PDDocument document, Integer pageCount) {
    if (document == null) {
      return new byte[0];
    }

    pageCount = pageCount == null ? 5 : pageCount;

    val images = new ArrayList<BufferedImage>();
    var maxWidth = 0;
    var totalHeight = 0;

    try {
      val renderer = new PDFRenderer(document);

      for (int i = 0; i < Math.min(pageCount, document.getNumberOfPages()); i++) {
        val image = renderer.renderImageWithDPI(i, 144f);
        images.add(image);
        maxWidth = Math.max(maxWidth, image.getWidth());
        totalHeight += image.getHeight();
      }

      val combinedImage = new BufferedImage(maxWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
      val graphics = combinedImage.getGraphics();
      graphics.setColor(Color.WHITE);
      graphics.fillRect(0, 0, maxWidth, totalHeight);
      var yOffset = 0;
      for (val image : images) {
        int xOffset = (maxWidth - image.getWidth()) / 2;
        graphics.drawImage(image, xOffset, yOffset, null);
        yOffset += image.getHeight();
      }
      graphics.dispose();

      try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
        ImageIO.write(combinedImage, "png", baos);
        return baos.toByteArray();
      }
    } catch (Exception e) {
      logger.error("Error rendering PDF document for attachment manager preview", e);
      return new byte[0];
    }
  }

  protected byte[] renderImage(final Integer rotateBy, final File file, final String imageExtension)
      throws IOException {
    var image = ImageIO.read(file);
    if (rotateBy != null) {
      image = rotateImage(image, rotateBy);
      ImageIO.write(image, imageExtension, file);
    }
    try (val baos = new ByteArrayOutputStream()) {
      ImageIO.write(image, imageExtension, baos);
      return baos.toByteArray();
    }
  }
}
