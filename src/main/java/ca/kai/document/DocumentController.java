package ca.kai.document;

import ca.kai.OscarProperties;
import ca.kai.authentication.AuthenticationService;
import ca.kai.caseManagementNote.CaseManagementNote;
import ca.kai.caseManagementNote.CaseManagementNoteLink;
import ca.kai.caseManagementNote.CaseManagementNoteLinkRepository;
import ca.kai.caseManagementNote.CaseManagementNoteRepository;
import ca.kai.ctlDocType.CtlDocType;
import ca.kai.ctlDocType.CtlDocTypeRepository;
import ca.kai.ctlDocument.CtlDocumentRepository;
import ca.kai.fax.enums.FaxFileType;
import ca.kai.fax.enums.FaxStatusInternal;
import ca.kai.fax.exceptions.FaxException;
import ca.kai.fax.service.FaxMigrationService;
import ca.kai.log.Log;
import ca.kai.log.LogRepository;
import ca.kai.log.LogService;
import ca.kai.patientLabRouting.PatientDocumentRouting;
import ca.kai.patientLabRouting.PatientDocumentRoutingRepository;
import ca.kai.provider.Provider;
import ca.kai.providerLabRouting.ProviderDocumentRouting;
import ca.kai.providerLabRouting.ProviderDocumentRoutingRepository;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.http.entity.ContentType;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.owasp.html.PolicyFactory;
import org.owasp.html.Sanitizers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Log4j2
@RestController
@RequestMapping("/api/document")
public class DocumentController {

    @Autowired
    private OscarProperties oscarProperties;
    
    @Autowired
    private DocumentRepository documentRepository;
    
    @Autowired
    private DocumentService documentService;
    
    @Autowired
    private CtlDocTypeRepository ctlDocTypeRepository;
    
    @Autowired
    private CtlDocumentRepository ctlDocumentRepository;
    
    @Autowired
    private PatientDocumentRoutingRepository patientDocumentRoutingRepository;
    
    @Autowired
    private CaseManagementNoteLinkRepository caseManagementNoteLinkRepository;
    
    @Autowired
    private CaseManagementNoteRepository caseManagementNoteRepository;
    
    @Autowired
    private LogRepository logRepository;
    
    @Autowired
    private ProviderDocumentRoutingRepository providerDocumentRoutingRepository;
    
    @Autowired
    private FaxMigrationService faxService;
    
    @Autowired
    private LogService logService;

    @PreAuthorize("hasPermission(#request, '_edoc', 'r')")
    @RequestMapping(path = "/getDocument/{documentId}", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Document getDocument(HttpServletRequest request, @PathVariable("documentId") Integer documentNo) {
        logService.writeEntry(
            "viewDocument",
            "document",
            String.valueOf(documentNo),
            null,
            request.getRequestURI(),
            request
        );

        return documentService.getDocument(documentNo);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(path = "/saveDocument", method = RequestMethod.POST, consumes = {MediaType.APPLICATION_JSON_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Document saveDocument(HttpServletRequest request, @RequestBody Document document) {
        Document documentToSave = documentRepository.getDocumentById(document.getDocumentNo());

        if (!(documentToSave.getDocType().equals(document.getDocType()))){
            logService.writeEntry(
                "type",
                "document",
                String.valueOf(document.getDocumentNo()),
                null,
                "Type: " + documentToSave.getDocType() + " -> " + document.getDocType(),
                request
            );
        }

        val simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        val documentToSaveDate = Optional.ofNullable(documentToSave.getObservationDate())
                .map(simpleDateFormat::format)
                .orElse(null);
        val documentDate = Optional.ofNullable(document.getObservationDate())
                .map(simpleDateFormat::format)
                .orElse(null);
        if (!Objects.equals(documentDate, documentToSaveDate)){
            logService.writeEntry(
                "observationDate",
                "document",
                String.valueOf(document.getDocumentNo()),
                null,
                "Observation Date: " + documentToSaveDate + " -> " + documentDate,
                request
            );
        }

        if (!documentToSave.getDocDesc().equals(document.getDocDesc())){
            logService.writeEntry(
                "description",
                "document",
                String.valueOf(document.getDocumentNo()),
                null,
                "Title: " + documentToSave.getDocDesc() + " -> " + document.getDocDesc(),
                request
            );
        }

        documentToSave.setDocType(document.getDocType());
        documentToSave.setDocDesc(document.getDocDesc());
        documentToSave.setObservationDate(document.getObservationDate());
        documentToSave.setAbnormal(document.getAbnormal());
        documentToSave.setComments(document.getComments());
        documentToSave.setSource(document.getSource());
        documentToSave.setSourceFacility(document.getSourceFacility());

        return documentRepository.save(documentToSave);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'r')")
    @RequestMapping(path = "/getDocumentTypes", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public List<CtlDocType> getDocumentTypes(HttpServletRequest request) {
        return ctlDocTypeRepository.getAllByModuleAndStatus(CtlDocType.DEMOGRAPHIC, CtlDocType.ACTIVE).stream()
                .sorted(Comparator.comparing(CtlDocType::getDocType, String.CASE_INSENSITIVE_ORDER))
                .collect(Collectors.toList());
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'r')")
    @RequestMapping(path = "/getPage/{documentNo}", method = RequestMethod.GET, produces = {MediaType.TEXT_PLAIN_VALUE})
    public String getPage(HttpServletRequest request, HttpServletResponse response, @PathVariable("documentNo") Integer documentNo, @RequestParam("page") Integer page) {
        Document document = documentRepository.getDocumentById(documentNo);
        byte[] documentBytes = documentService.getDocumentBytes(document, page);
        return Base64.getEncoder().encodeToString(documentBytes);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'r')")
    @RequestMapping(path = "/getFullDocument/{documentNo}", method = RequestMethod.GET, produces = {MediaType.APPLICATION_PDF_VALUE})
    public ResponseEntity<byte[]> getFullDocument(HttpServletRequest request, @PathVariable("documentNo") Integer documentNo, HttpServletResponse response) {
        byte[] documentBytes = null;
        
        Document document = documentRepository.getDocumentById(documentNo);
        try {
            documentBytes = FileUtils.readFileToByteArray(documentService.readDocumentFile(document));
            
            if (ContentType.TEXT_HTML.getMimeType().equals(document.getContentType())) {
                PolicyFactory policy = Sanitizers.BLOCKS
                        .and(Sanitizers.STYLES)
                        .and(Sanitizers.FORMATTING)
                        .and(Sanitizers.IMAGES)
                        .and(Sanitizers.TABLES);
                String originalContents = new String(documentBytes);
                String safeHtml = policy.sanitize(originalContents);
                documentBytes = safeHtml.getBytes();
            }
        } catch(IOException e) {
            e.printStackTrace();
        }

        response.setContentType(document.getContentType());
        response.setContentLength(documentBytes.length);
        response.setHeader("Content-Disposition", "inline; filename=\"" + document.getDocFileName() + "\"");
        try (ServletOutputStream outs = response.getOutputStream()) {
            outs.write(documentBytes);
            outs.flush();
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        
        return null;
    }

  @PreAuthorize("hasPermission(#request, '_edoc', 'r')")
  @RequestMapping(path = "/getPagePreviewUrl/{documentNo}", method = RequestMethod.GET, produces = {
      MediaType.TEXT_PLAIN_VALUE})
  public String getPagePreviewUrl(
      HttpServletRequest request,
      @PathVariable("documentNo") final Integer documentNumber,
      @RequestParam("page") final Integer page) {
    return documentService.documentPagePreviewUrlGenerator(documentNumber, page);
  }

  @PreAuthorize("hasPermission(#request, '_edoc', 'r')")
  @RequestMapping(path = "/getPagePreview/{documentNo}", method = RequestMethod.GET, produces = {
      MediaType.APPLICATION_PDF_VALUE})
  public ResponseEntity<byte[]> getDocumentPreview(
      final HttpServletRequest request,
      @PathVariable("documentNo") final Integer documentNumber,
      @RequestParam("page") Integer page,
      final HttpServletResponse response
  ) {
    if (page == null) {
      page = 1;
    }
    // getting document by document number from document repository and convert the page 1 content to bytes
    Document document = documentRepository.getDocumentById(documentNumber);
    byte[] documentBytes = documentService.getDocumentBytes(document, page);
    //if content type is "text/html", policy sanitize content, and set response content type accordingly
    if (ContentType.TEXT_HTML.getMimeType().equals(document.getContentType())) {
      PolicyFactory policy = Sanitizers.BLOCKS
          .and(Sanitizers.STYLES)
          .and(Sanitizers.FORMATTING)
          .and(Sanitizers.IMAGES)
          .and(Sanitizers.TABLES);
      String originalContents = new String(documentBytes);
      String safeHtml = policy.sanitize(originalContents);
      documentBytes = safeHtml.getBytes();
      response.setContentType("text/html");
      response.setHeader("Content-Disposition",
          "inline; filename=\"" + document.getDocFileName() + "\"");
    } else {
      response.setContentType("image/png");
    }
    response.setContentLength(documentBytes.length);
    try (ServletOutputStream outs = response.getOutputStream()) {
      outs.write(documentBytes);
      outs.flush();
    } catch (IOException e) {
      e.printStackTrace();
    }
    return null;
  }

  @PreAuthorize("hasPermission(#request, '_edoc', 'r')")
  @RequestMapping(
      path = "/getAttachmentManagerPreview/{documentNo}",
      method = RequestMethod.GET,
      produces = { MediaType.IMAGE_PNG_VALUE }
  )
  public ResponseEntity<byte[]> getAttachmentManagerPreview(
      @PathVariable("documentNo") final Integer documentNumber,
      @RequestParam(required = false, name = "pageCount", defaultValue = "5") Integer pageCount,
      final HttpServletRequest request
  ) {
    val document = documentRepository.getDocumentById(documentNumber);
    if (document == null) {
      log.error("Document with ID {} not found.", documentNumber);
      return ResponseEntity.notFound().build();
    }

    try {
      val documentBytes = documentService.getDocumentForAttachmentManagerPreview(document, pageCount);

      if (documentBytes.length == 0) {
        return ResponseEntity.notFound().build();
      }

      val headers = new HttpHeaders();
      headers.setContentType(MediaType.IMAGE_PNG);
      headers.setContentLength(documentBytes.length);

      return ResponseEntity.ok()
          .headers(headers)
          .body(documentBytes);
    } catch (Exception e) {
      log.error("Error retrieving document with ID {}: {}", documentNumber, e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
  }

    @PreAuthorize("hasPermission(#request, '_edoc', 'r')")
    @RequestMapping(path = "/rotateDocument/{documentNo}", method = RequestMethod.POST, produces = {MediaType.TEXT_PLAIN_VALUE})
    public String rotateDocument(HttpServletRequest request, @PathVariable("documentNo") Integer documentNo, @RequestParam("page") Integer page, @RequestParam("rotateBy") Integer rotateBy, @RequestParam("rotateAll") Boolean rotateAll) {
        Document document = documentRepository.getDocumentById(documentNo);
        byte[] documentBytes = documentService.getDocumentBytes(document, page, rotateBy, rotateAll);
        return Base64.getEncoder().encodeToString(documentBytes);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(path = "/deleteCover/{documentNo}", method = RequestMethod.DELETE, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Document deleteCover(HttpServletRequest request, @PathVariable("documentNo") Integer documentNo) {
        Document document = documentRepository.getDocumentById(documentNo);

        logService.writeEntry(
            "deleteCover",
            "document",
            String.valueOf(documentNo),
            null,
            request.getRequestURI(),
            request
        );

        File file = documentService.readDocumentFile(document);;
        try {
            if (file.exists()) {
                PDDocument pdf = PDDocument.load(file);
                //Delete the cover page only if the pdf has more than 1 page
                if (pdf.getNumberOfPages() > 1) {
                    pdf.removePage(0);

                    pdf.save(file);
                    
                    document.setNumberOfPages(document.getNumberOfPages() - 1);
                    documentRepository.save(document);
                }
            }
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        
        return document;
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(path = "/linkDemographic", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Document linkDemographic(@RequestParam("demographicNo") Integer demographicNo, @RequestParam("documentNo") Integer documentNo, HttpServletRequest request) {
        logService.writeEntry(
            "linkDemographic",
            "document",
            String.valueOf(documentNo),
            demographicNo,
            "Demographic " + demographicNo + " linked to document " + documentNo,
            request
        );
        return documentService.routeDemographic(AuthenticationService.getAuthenticatedProviderFromRequest(request), demographicNo, documentNo);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(path = "/removeDemographic", method = RequestMethod.DELETE, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Document removeDemographic(@RequestParam("documentNo") Integer documentNo, HttpServletRequest request) {
        Document document = documentRepository.getDocumentById(documentNo);
        document.setPatientDocumentRouting(null);
        
        Integer demographicNo = null;
        List<PatientDocumentRouting> patientDocumentRoutings = patientDocumentRoutingRepository.getAllPatientDocumentRoutingsByDocumentId(documentNo);
        if (patientDocumentRoutings.size() > 0) {
            demographicNo = patientDocumentRoutings.get(0).getDemographic().getDemographicNumber();
            patientDocumentRoutingRepository.delete(patientDocumentRoutings);
        }
        
        List<CaseManagementNoteLink> noteLinks = caseManagementNoteLinkRepository.getAllByTableNameAndTableIdOrderById(CaseManagementNoteLink.DOCUMENT, Long.valueOf(document.getDocumentNo()));
        
        for (CaseManagementNoteLink noteLink : noteLinks) {
            CaseManagementNote caseManagementNote = caseManagementNoteRepository.getByIdAndArchived(noteLink.getNoteId(), false);
            
            if (caseManagementNote != null) {
                caseManagementNote.setArchived(true);
                caseManagementNoteRepository.save(caseManagementNote);
            }
        }
        
        ctlDocumentRepository.removeDemographicFromDocument(document.getDocumentNo());
        
        Provider loggedInProvider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
        Log securityLog = new Log();
        securityLog.setProviderNo(loggedInProvider.getProviderNo());
        securityLog.setDateTime(new Date());
        securityLog.setAction("unlinkDemographic");
        securityLog.setContent("document");
        securityLog.setData("Demographic " + demographicNo + " unlinked from document " + documentNo);
        securityLog.setContentId(document.getDocumentNo().toString());
        securityLog.setIp(AuthenticationService.obtainClientIpAddress(request));
        securityLog.setDemographicNo(demographicNo);
        
        logRepository.save(securityLog);

        return document;
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(path = "/linkProvider", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ProviderDocumentRouting linkProvider(HttpServletRequest request, @RequestParam("providerNo") String providerNo, @RequestParam("documentNo") Integer documentNo) {
        logService.writeEntry(
            "linkProvider",
            "document",
            String.valueOf(documentNo),
            null,
            "Provider " + providerNo + " linked to document " + documentNo,
            request
        );
        ProviderDocumentRouting providerDocumentRouting = documentService.routeProvider(providerNo, documentNo);
        
        return providerDocumentRouting;
    }

  @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
  @PostMapping(path = "/sendFax", produces = MediaType.APPLICATION_JSON_VALUE)
  public Map<String, FaxStatusInternal> sendFax(HttpServletRequest request,
      @RequestParam("demographicNo") Integer demographicId,
      @RequestParam("documentNo") Integer documentId,
      @RequestBody List<String> destinations) {
    if ((destinations == null) || destinations.isEmpty()) {
      return new HashMap<>();
    }

    val provider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
    val document = documentRepository.getDocumentById(documentId);
    val faxStatuses = new HashMap<String, FaxStatusInternal>();

    // add to faxStatuses list, the status and the associated fax number that sent or caused an error
    // and return back to front end for error handling if needed.
    destinations.stream()
        .map(destination -> destination.replaceAll("[^0-9]", ""))
        .distinct()
        .forEach(destination -> {
      try {
        faxStatuses.put(destination,
            faxService.faxDocument(document, provider.getProviderNo(), demographicId, destination,
                FaxFileType.DOCUMENT));
      } catch (IOException | FaxException ex) {
        log.error("Failed to send fax to destination " + destination, ex);
        faxStatuses.put(destination, FaxStatusInternal.ERROR);
      }
    });
    return faxStatuses;
  }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(path = "/removeProvider", method = RequestMethod.DELETE, produces = {MediaType.APPLICATION_JSON_VALUE})
    public void removeProvider(HttpServletRequest request, @RequestParam("providerNo") String providerNo, @RequestParam("documentNo") Integer documentNo) {
        logService.writeEntry(
            "unlinkProvider",
            "document",
            String.valueOf(documentNo),
            null,
            "Provider " + providerNo + " unlinked from document " + documentNo,
            request
        );
        providerDocumentRoutingRepository.archiveByProviderNoAndDocumentNo(providerNo, documentNo);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(path = "updateStatus", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ProviderDocumentRouting updateStatus(HttpServletRequest request, @RequestParam("providerNo") String providerNo, @RequestParam("documentNo") Integer documentNo, @RequestParam("status") String status,
        @RequestParam(name = "comment", required = false) String comment) {
        return documentService.updateStatus(providerNo, documentNo, status, comment);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(path= "/addComment", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    public ProviderDocumentRouting addComment(HttpServletRequest request, @RequestParam("providerNo") String providerNo, @RequestParam("documentNo") Integer documentNo, @RequestParam("comment") String comment) {
        return documentService.addComment(providerNo, documentNo, comment);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(path= "/updateComment", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    public DocumentComment updateComment(HttpServletRequest request, @RequestBody DocumentComment documentComment) {
        return documentService.updateComment(documentComment);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'u')")
    @RequestMapping(
        path = "/{documentId}/title",
        method = RequestMethod.PUT
    )
    @ResponseStatus(HttpStatus.OK)
    public void updateTitle(
        final HttpServletRequest request,
        final @PathVariable Integer documentId,
        final @RequestBody String title
    ) {
        var document = documentRepository.getDocumentById(documentId);
        document.setDocDesc(title);
        documentRepository.save(document);
    }

    @PreAuthorize("hasPermission(#request, '_edoc', 'r')")
    @RequestMapping(path = "/uploadDocument", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    public DocumentUploadResult uploadDocument(HttpServletRequest request, @RequestParam("file") MultipartFile file,
                                               @RequestParam("destination") String destination, @RequestParam("queue") Integer assignedQueueId,
                                               @RequestParam("destinationFolder") IncomingDocumentFolder destinationFolder, @RequestParam("assignedProviderNo") String assignedProviderNo) {
        Provider loggedInProvider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
        DocumentUploadResult uploadResult;
        try {
            uploadResult = documentService.uploadDocument(file, loggedInProvider, destination, assignedQueueId, destinationFolder, assignedProviderNo);
        } catch (DocumentUploadException e) {
            uploadResult = new DocumentUploadResult();
            uploadResult.setFileName(file.getOriginalFilename());
            uploadResult.setErrorMessage(e.getMessage());
            uploadResult.setUploadSuccess(false);
        }
        return uploadResult;
    }
}
