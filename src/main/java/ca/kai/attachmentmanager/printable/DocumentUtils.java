package ca.kai.attachmentmanager.printable;

import ca.kai.document.Document;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class DocumentUtils {

  /**
   * The supported types of documents.
   * Changes to this list should also reflect in the equivalent DocumentUtils class in Classic.
   */
  static final Set<String> DOCUMENT_SUPPORTED_TYPES =
      new HashSet<>(Arrays.asList(".pdf", ".jpg", ".jpeg", ".jpe", ".png"));

  /**
   * This method returns whether a document is of a supported type
   * (PDF, HTML, or image types JPEG/PNG).
   *
   * @param document A document
   * @return A boolean based on if the document is supported
   */
  public static boolean isSupported(final Document document) {
    String fileNameLowercase = document.getDocFileName().toLowerCase();
    return DOCUMENT_SUPPORTED_TYPES.stream()
        .anyMatch(type -> fileNameLowercase.contains(type.toLowerCase()));
  }
}
