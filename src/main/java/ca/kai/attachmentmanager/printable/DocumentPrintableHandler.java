package ca.kai.attachmentmanager.printable;

import ca.kai.OscarProperties;
import ca.kai.applicationcontext.ApplicationContextProvider;
import ca.kai.document.Document;
import ca.kai.document.DocumentRepository;
import ca.kai.document.DocumentService;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.springframework.stereotype.Component;

@Component
public class DocumentPrintableHandler implements PrintableHandler {

  protected static OscarProperties properties;
  protected static DocumentRepository documentRepository;
  protected static DocumentService documentService;

  static String PREVIEW_URL = "/%s/api/document/getAttachmentManagerPreview/%s?pageCount=5";

  @PostConstruct
  void postConstruct() {
    properties = ApplicationContextProvider.getBean(OscarProperties.class);
    documentRepository = ApplicationContextProvider.getBean(DocumentRepository.class);
    documentService = ApplicationContextProvider.getBean(DocumentService.class);
  }
  
  @Override
  public List<Printable> search(final Integer demographicNumber, final String providerNumber) {
    return convertResults(
        documentRepository.getActiveDocumentsByDemographicNo(demographicNumber));
  }

  @Override
  public Printable getById(final String id) {
    return new Printable(documentRepository.getDocumentById(Integer.parseInt(id)));
  }

  @Override
  public List<Printable> getAttachments(final Printable printable) {
    return Collections.emptyList();
  }

  @Override
  public Object printSelf(final String id)
      throws IOException, URISyntaxException, RuntimeException {
    return documentService.printWithTempFile(Integer.parseInt(id));
  }

  @Override
  public Object print(final Printable printable)
      throws IOException, URISyntaxException, RuntimeException {
    return printSelf(printable.getId());
  }

  @Override
  public String getPreviewUrl(String id) {
    return String.format(PREVIEW_URL, properties.getKaiemrDeployedContext(), id);
  }

  List<Printable> convertResults(final List<Document> results) {
    return results.stream()
        .map(Printable::new)
        .collect(Collectors.toList());
  }
}
