package ca.kai.attachmentmanager.attachmenttype;

import ca.kai.attachmentmanager.printable.ConsultationPrintableHandler;
import ca.kai.attachmentmanager.printable.CoverPagePrintableHandler;
import ca.kai.attachmentmanager.printable.DocumentPrintableHandler;
import ca.kai.attachmentmanager.printable.EdocPrintableHandler;
import ca.kai.attachmentmanager.printable.EformPrintableHandler;
import ca.kai.attachmentmanager.printable.FormAntenatalPrintableHandler;
import ca.kai.attachmentmanager.printable.FamilyHistoryPrintableHandler;
import ca.kai.attachmentmanager.printable.FormPrintableHandler;
import ca.kai.attachmentmanager.printable.Hl7PrintableHandler;
import ca.kai.attachmentmanager.printable.HrmPrintableHandler;
import ca.kai.attachmentmanager.printable.MedicalHistoryPrintableHandler;
import ca.kai.attachmentmanager.printable.MedicationPrintableHandler;
import ca.kai.attachmentmanager.printable.NotesPrintableHandler;
import ca.kai.attachmentmanager.printable.OngoingConcernsPrintableHandler;
import ca.kai.attachmentmanager.printable.OtherMedsPrintableHandler;
import ca.kai.attachmentmanager.printable.PreventionPrintableHandler;
import ca.kai.attachmentmanager.printable.Printable;
import ca.kai.attachmentmanager.printable.PrintableHandler;
import ca.kai.attachmentmanager.printable.RemindersPrintableHandler;
import ca.kai.attachmentmanager.printable.RiskFactorsPrintableHandler;
import ca.kai.attachmentmanager.printable.SmartEncounterPrintableHandler;
import ca.kai.attachmentmanager.printable.SocialHistoryPrintableHandler;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.Getter;

/**
 * The AttachmentType of a printable object. Contains functionality to get printables for each type 
 * as well as a printables attachments by using the associated handler. 
 */
public enum AttachmentType {
  // Report
  Antenatal (AttachmentCategory.Report, "Antenatal", FormAntenatalPrintableHandler.class),
  Consultations (AttachmentCategory.Report, "Consultations", ConsultationPrintableHandler.class),
  CoverPage (AttachmentCategory.Report, "Cover Page", CoverPagePrintableHandler.class, true),
  Document (AttachmentCategory.Report, "Documents", DocumentPrintableHandler.class),
  Eforms (AttachmentCategory.Report, "eForms", EformPrintableHandler.class),
  Hrm (AttachmentCategory.Report, "HRM", HrmPrintableHandler.class),
  Lab (AttachmentCategory.Report, "Labs", Hl7PrintableHandler.class),
  SmartEncounter (
      AttachmentCategory.Report, 
      "Smart Encounter", 
      SmartEncounterPrintableHandler.class
  ),
  Medication(AttachmentCategory.Report, "Medications", MedicationPrintableHandler.class),
  Prevention(AttachmentCategory.Report, "Preventions", PreventionPrintableHandler.class),
  Form (AttachmentCategory.Report, "Forms", FormPrintableHandler.class),

  // CPP
  FamilyHistory (AttachmentCategory.CPP, "Family History", FamilyHistoryPrintableHandler.class),
  MedicalHistory (AttachmentCategory.CPP, "Medical History", MedicalHistoryPrintableHandler.class),
  Notes (AttachmentCategory.CPP, "Notes", NotesPrintableHandler.class),
  OngoingConcerns (
      AttachmentCategory.CPP, 
      "Ongoing Concerns", 
      OngoingConcernsPrintableHandler.class
  ),
  OtherMeds (AttachmentCategory.CPP, "Other Meds", OtherMedsPrintableHandler.class),
  Reminders (AttachmentCategory.CPP, "Reminders", RemindersPrintableHandler.class),
  RiskFactors (AttachmentCategory.CPP, "Risk Factors", RiskFactorsPrintableHandler.class),
  SocialHistory (AttachmentCategory.CPP, "Social History", SocialHistoryPrintableHandler.class),
  
  // Provider
  Edocs (AttachmentCategory.Provider, "eDocs", EdocPrintableHandler.class);

  // List of attachment types that are not supported as attachments (yet)
  // and should not be shown in the search
  final static List<AttachmentType> disabledTypes = Collections.emptyList();

  @Getter final private AttachmentCategory category;
  @Getter final private String displayName;
  private PrintableHandler printableHandler;
  @Getter final private boolean internal;

  AttachmentType(
      final AttachmentCategory category,
      final String displayName,
      final Class printableHandler) {
    this(category, displayName, printableHandler, false);
  }
  
  AttachmentType(
      final AttachmentCategory category,
      final String displayName,
      final Class printableHandler,
      final boolean internal) {
    this.category = category;
    this.displayName = displayName;
    try {
      this.printableHandler = (PrintableHandler) printableHandler.newInstance();
    } catch (InstantiationException | IllegalAccessException exception) {
      exception.printStackTrace();
    }
    this.internal = internal;
  }

  public String getName() {
    return name();
  }
  
  public static AttachmentType getByName(final String name) {
    return Arrays.stream(AttachmentType.values())
        .filter(c -> c.name().equalsIgnoreCase(name))
        .findFirst()
        .orElse(null);
  }
  
  /**
   * Gets a list of printables associated with this AttachmentType. The list of printables will be 
   * associated to the provided demographic
   * 
   * @param demographicNumber The demographic to get the printables associated to
   * @param providerNumber The current provider to get the printables for
   * @return A list of printables
   */
  public List<Printable> getPrintables(
      final Integer demographicNumber, 
      final String providerNumber) {
    return this.printableHandler.search(demographicNumber, providerNumber);
  }
  
  public Printable getPrintableById(final String id) {
    return this.printableHandler.getById(id);
  }
  
  /**
   * Calls the PrintableHandler to get a list of printables that are attached to the provided 
   * printable
   * 
   * @param printable The printable to get the attachments of
   * @return A list of printables attached to the given printable
   */
  public List<Printable> getPrintableAttachments(final Printable printable) {
    return this.printableHandler.getAttachments(printable);
  }
  
  /**
   * Prints the provided printable by calling the associated PrintableHandler's print function
   * 
   * @param printable The printable to be printed
   * @return An Object containing the file path where the printed Printabled was saved to
   */
  public Object print(final Printable printable)
      throws IOException, URISyntaxException, RuntimeException {
    return this.printableHandler.print(printable);
  }

  public Object printSelf(final String id)
      throws IOException, URISyntaxException, RuntimeException {
    return this.printableHandler.printSelf(id);
  }

  /**
   * Gets the preview for the provided id from the associated PrintableHandler
   * @param id The id of the printable to get the preview URL for
   * @return The preview URL for the attachment type
   */
  public String getPreviewUrl(final String id) {
    return this.printableHandler.getPreviewUrl(id);
  }

  /**
   * Returns all attachment types that are not internal (meaning that they can be searched).
   *
   * @return String array containing the names of searchable types
   */
  public static String[] getSearchableTypes() {
    return Arrays.stream(AttachmentType.values())
        .filter(type -> !type.internal)
        .filter(type -> !disabledTypes.contains(type))
        .map(AttachmentType::getName)
        .toArray(String[]::new);
  }

  @JsonValue
  public String getValue() {
    return this.getName();
  }

  @JsonCreator
  public static AttachmentType setValue(String value) {
    return AttachmentType.valueOf(value);
  }
}
