/*
 * Copyright (c) 2024 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package ca.kai.billing.rosterreport.service;

import ca.kai.OscarProperties;
import ca.kai.authentication.AuthenticationService;
import ca.kai.billing.rosterreport.model.ReconcileResponse;
import ca.kai.billing.rosterreport.model.RosterReport;
import ca.kai.billing.rosterreport.exception.RosterReportException;
import ca.kai.billing.rosterreport.model.RosterPatientData;
import ca.kai.demographic.Demographic;
import ca.kai.demographic.DemographicExt;
import ca.kai.demographic.DemographicExtKey;
import ca.kai.demographic.DemographicExtRepository;
import ca.kai.demographic.DemographicRepository;
import ca.kai.demographic.DemographicRepositoryService;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderRepository;
import ca.kai.util.DateUtil;
import ca.kai.util.UnauthorizedException;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.ParserConfigurationException;
import lombok.val;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.ResponseEntity;
import org.xml.sax.SAXException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RosterReportService {

  private static final String ENROLLED_STATUS = "RO"; // OSCARPRO-6313: use RO instead of EN
  private static final String TERMINATED_STATUS = "TE";
  private static final String ONEDT_PREFIX = "ONEDT_";
  final DemographicRepositoryService demographicRepositoryService;
  final DemographicRepository demographicRepository;
  final ProviderRepository providerRepository;
  final OscarProperties oscarProperties;
  final DemographicExtRepository demographicExtRepository;

  public RosterReportService(
      final DemographicRepository demographicRepository,
      final ProviderRepository providerRepository,
      final OscarProperties properties,
      final DemographicRepositoryService demographicRepositoryService,
      final DemographicExtRepository demographicExtRepository) {
    this.providerRepository = providerRepository;
    this.oscarProperties = properties;
    this.demographicRepositoryService = demographicRepositoryService;
    this.demographicRepository = demographicRepository;
    this.demographicExtRepository = demographicExtRepository;
  }

  /**
   * This method will create a new RosterReport object, which will segment up and process the file
   * name information, convert and store the reports roster data, and extract the XML data from the
   * retrieved RCX file to be processed.
   * <p>
   * After this, it will extract the roster patient data from the report and sort the patients into
   * the proper lists.
   * <p>
   * The sorting of patients into their respective lists must be done here instead of in the
   * RosterReport class when the patient data is extracted, as we must check for a local demographic
   * match first. This ensures patients won't be added into multiple lists and will only go into
   * newly, previously or terminated if they match a local demographic record, which we do not want
   * to call the repository in the RosterReport class.
   *
   * @param folder   - the folder where the report is located.
   * @param fileName - the name of the report to be processed.
   * @param request  - the HTTPServletRequest to retrieve the loggedInProvider.
   * @return - the response of the processed roster report.
   * @throws ParseException               - If there is an issue parsing the date fields for
   *                                      comparison.
   * @throws IOException                  - If there is an issue reading the file.
   * @throws ParserConfigurationException - If there is an issue parsing the XML file.
   * @throws SAXException                 - If there is an issue parsing the XML file.
   * @throws RosterReportException        - If no provider is found in the system with the OHIP
   *                                      number.
   */
  public ResponseEntity<RosterReport> processReport(final String folder, final String fileName,
      final HttpServletRequest request)
      throws ParserConfigurationException, SAXException, IOException, ParseException,
      RosterReportException {
    RosterReport report = getRosterReport(
        oscarProperties.getProperty(ONEDT_PREFIX + folder),
        fileName,
        request
    );

    report.getPatientData()
        .forEach(patient -> {
          var demographic = getDemographicFromRepositoryByHin(patient.getHealthNumber());
          if (demographic == null) {
            report.getMissingDemographics().add(patient);
          } else {
            // See note in Javadoc
            val demographicId = demographic.getDemographicNumber().toString();
            if(StringUtils.isNotBlank(demographicId)){
              patient.setDemographicId(demographicId);
            }
            addPatientToList(patient, report);
          }
        });

    return ResponseEntity.ok(report);
  }

  /**
   * @param folder   - The folder where the report is located.
   * @param fileName - The name of the report to be processed.
   * @param request  - The HTTPServletRequest to retrieve the loggedInProvider.
   * @return - The ReconcileResponse with the updated patient count and other necessary information
   * @throws ParseException               - If there is an issue parsing the date fields for
   *                                      comparison.
   * @throws IOException                  - If there is an issue reading the file when processing
   *                                      the report.
   * @throws ParserConfigurationException - If there is an issue parsing the XML file.
   * @throws RosterReportException        - If no provider is found in the system with the OHIP
   *                                      number.
   * @throws SAXException                 - If there is an issue parsing the XML file.
   */
  public ResponseEntity<ReconcileResponse> reconcileReport(
      final String folder,
      final String fileName,
      final HttpServletRequest request)
      throws ParseException, IOException, ParserConfigurationException,
      RosterReportException, SAXException {
    RosterReport report = getRosterReport(
        oscarProperties.getProperty(ONEDT_PREFIX + folder),
        fileName,
        request
    );

    return ResponseEntity.ok(createReconcileResponse(report));
  }

  /**
   * This method will reconcile the demographic data based on the patient data extracted from the
   * RCX file.
   *
   * @param patients - The list of RosterPatientData objects that contain the patient data extracted
   *                 from the RCX report.
   * @param enrollmentProviderNumber - The enrollment provider to be added to the demographic record.
   * @param user - The user who is updating the demographic data, typically the logged-in user.
   * @return - The number of demographics that were updated to be added to the ReconcileResponse for
   * display to the user.
   */
  int reconcileDemographics(final List<RosterPatientData> patients, final String enrollmentProviderNumber, final String user) {

    var demographicsUpdated = 0;
    val hinToPatientMap = patients.stream()
        .collect(Collectors.toMap(RosterPatientData::getHealthNumber, patient -> patient));
    val demographics = getDemographicsFromRepositoryByHinIn(new ArrayList<>(hinToPatientMap.keySet()));

    val enrollmentProviders = updateOrCreateEnrollmentProviders(demographics, enrollmentProviderNumber);
    for (Demographic demographic : demographics) {
      val patient = hinToPatientMap.get(demographic.getHin());
      updateDemographicData(demographic, patient, user);
      demographicsUpdated++;
    }

    demographicRepositoryService.saveDemographics(demographics);
    demographicExtRepository.save(enrollmentProviders);

    return demographicsUpdated;
  }

  /**
   * This method will create a ReconcileResponse object based on the RosterReport object provided,
   * while also calling the reconcileDemographics method to update the demographics in the system.
   *
   * @param report - The RosterReport object that contains all the patient information needed *
   *               to update the demographic records
   * @return - The ReconcileResponse object that contains the updated patient count and other
   * necessary information.
   */
  private ReconcileResponse createReconcileResponse(final RosterReport report) {
    val demographicsUpdated = reconcileDemographics(report.getPatientData(), report.getProvider().getProviderNo(), report.getLoggedInUser());
    return new ReconcileResponse(report.getProvider().getFormattedName(), new Date(),
        demographicsUpdated);
  }

  /**
   * This method will assign the patient objects to a list in the RosterReport class based on a
   * number of checks including if they are terminated, is the roster end date is empty, or if this
   * is a new patient roster report.
   *
   * @param patient - The patient object to be added to their respective lists
   */
  void addPatientToList(final RosterPatientData patient, RosterReport report) {
    if (isRosterStatusTerminated(patient)) {
      report.getTerminatedDemographics().add(patient);
    } else if (StringUtils.isBlank(patient.getRosterEnd())) {
      if (isNewPatientRosterReport(DateUtil.parseDateOrReturnNull(patient.getRosterStart()),
          report.getProvider().getOnMohLastRosterReportDate())) {
        report.getNewlyRosteredDemographics().add(patient);
      } else {
        report.getPreviouslyRosteredDemographics().add(patient);
      }
    }
  }

  /**
   * This method will determine if the patient in the report has a terminated status based on
   * whether it has a roster end date or termination code.
   *
   * @param patient - The patient data extracted from the RCX report.
   * @return - True if the patient has a terminated status, false otherwise.
   */
  public boolean isRosterStatusTerminated(final RosterPatientData patient) {
    return StringUtils.isNotEmpty(patient.getRosterEnd()) ||
        StringUtils.isNotEmpty(patient.getTerminationCode());
  }

  /**
   * This method will check if the report patient's roster start date is after the provider's last
   * processed report.
   *
   * @param patientRosterDate      - The patient's roster start date from the RCX report.
   * @param providerLastRosterDate - The provider's last processed roster report date.
   * @return - True if the patient's roster start date is after the provider's last processed
   * report, false otherwise.
   */
  boolean isNewPatientRosterReport(final Date patientRosterDate,
      final Date providerLastRosterDate) {
    return (patientRosterDate != null && (providerLastRosterDate == null
        || patientRosterDate.after(
        providerLastRosterDate) || patientRosterDate.equals(
        providerLastRosterDate)));
  }

  /**
   * This method will update the demographic data based on the patient data extracted from the RCX
   * report.
   *
   * @param demographic    - The Demographic object that contains the demographic data obtained via
   *                       the HIN from the patient in the RCX report.
   * @param patient        - The RosterPatientData object that contains the patient data extracted
   *                       from the RCX report.
   * @param lastUpdateUser - The user who is updating the demographic data, typically the logged-in
   *                       user.
   */
  void updateDemographicData(Demographic demographic, final RosterPatientData patient,
      final String lastUpdateUser) {
    demographic.setRosterDate(DateUtil.parseDateOrReturnNull(patient.getRosterStart()));
    demographic.setRosterTerminationDate(DateUtil.parseDateOrReturnNull(patient.getRosterEnd()));
    demographic.setRosterTerminationReason(patient.getTerminationCode());
    demographic.setLastUpdateUser(lastUpdateUser);
    demographic.setLastUpdateDate(new Date());

    if (isRosterStatusTerminated(patient)) {
      demographic.setRosterStatus(TERMINATED_STATUS);
    } else if (StringUtils.isBlank(patient.getRosterEnd())) {
      demographic.setRosterStatus(ENROLLED_STATUS);
    }

  }

  private List<DemographicExt> updateOrCreateEnrollmentProviders(final List<Demographic> demographics, final String enrollmentProvider) {
    val enrollmentProviderExts = demographicExtRepository
        .getAllByKeyAndDemographicNumberIn(DemographicExtKey.ENROLLMENT_PROVIDER.getKey(),
            demographics.stream()
                .map(Demographic::getDemographicNumber)
                .collect(Collectors.toList()));

    val demographicNoToEnrollmentProviderExt = enrollmentProviderExts.stream()
        .collect(Collectors.toMap(DemographicExt::getDemographicNumber, ext -> ext));
    demographics.forEach(demographic -> {
      if (!demographicNoToEnrollmentProviderExt.containsKey(demographic.getDemographicNumber())) {
        demographicNoToEnrollmentProviderExt.put(demographic.getDemographicNumber(),
            new DemographicExt(demographic, enrollmentProvider,
                DemographicExtKey.ENROLLMENT_PROVIDER));
      } else {
        demographicNoToEnrollmentProviderExt.get(demographic.getDemographicNumber())
            .setValue(enrollmentProvider);
      }
    });
    return new ArrayList<>(demographicNoToEnrollmentProviderExt.values());
  }

  /**
   * This method will check if the provider's last run report is before this report's date, and if
   * so, update the provider's last run report date to this report's date.
   * <p>
   * The boolean result of this method will update the isNewRosterReport method in the RosterReport
   * object, which will be used in the frontend to decide whether to display the action buttons or
   * not.
   *
   * @param provider - The provider object that the report is associated with via their OHIP
   *                 number.
   * @param report   - The RosterReport object that contains all the important report data, provider
   *                 data, patient data and summary information.
   * @return - True if the report is a new provider roster report, false otherwise.
   */
  boolean checkIfRosterReportIsUnprocessedAndUpdateProvider(Provider provider,
      RosterReport report) {
    if (provider.getOnMohLastRosterReportDate() == null
        || provider.getOnMohLastRosterReportDate()
        .equals(report.getReportDate())
        || provider.getOnMohLastRosterReportDate()
        .before(report.getReportDate())) {
      report.setMostRecentReportDate(report.getReportDate());
      provider.setOnMohLastRosterReportDate(report.getReportDate());
      providerRepository.save(provider);
      return true;
    } else {
      report.setMostRecentReportDate(provider.getOnMohLastRosterReportDate());
      return false;
    }
  }

  /**
   * This method will attempt to retrieve the first provider object from the provider repository
   * based on the OHIP number provided. If no provider is found, it will throw a RosterReportException.
   *
   * @param ohipNumber - The OHIP number of the provider to be retrieved, extracted from the
   *                   report.
   * @return - The provider object retrieved from the provider repository.
   * @throws RosterReportException - If no provider is found in the system with the OHIP number.
   */
  Provider getFirstProviderFromRepositoryByOhipNumber(final String ohipNumber)
      throws RosterReportException {
    Provider provider = providerRepository.findFirstByOhipNo(ohipNumber);
    if (provider == null) {
      throw new RosterReportException(
          "No provider found in the system with OHIP number: " + ohipNumber);
    }
    return provider;
  }

  private Demographic getDemographicFromRepositoryByHin(final String hin) {
    return demographicRepository.getByHin(hin);
  }

  private List<Demographic> getDemographicsFromRepositoryByHinIn(final List<String> hinList) {
    return demographicRepository.getByHinIn(hinList);
  }

  /**
   * This method will retrieve the currently loggedInProvider from the request, and if no provider
   * is found, it will throw an UnauthorizedException.
   *
   * @param request - The HTTPServletRequest to retrieve the loggedInProvider.
   * @return - The currently loggedInProvider from the request.
   */
  Provider getLoggedInProviderFromRequest(final HttpServletRequest request) {
    val provider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
    if (provider == null) {
      throw new UnauthorizedException();
    }
    return provider;
  }

  /**
   * This method will create a RosterReport object based on the folder path and file name provided,
   * and set the provider object based on the OHIP number extracted from the report, the new roster
   * report flag, and the loggedInUser.
   *
   * @param folderPath - The folder path where the report is located.
   * @param fileName   - The name of the report to be processed.
   * @param request    - The HTTPServletRequest to retrieve the loggedInProvider.
   * @return - The RosterReport object created based on the folder path and file name provided.
   * @throws ParseException               - If there is an issue parsing the date fields for
   *                                      comparison.
   * @throws IOException                  - If there is an issue reading the file.
   * @throws ParserConfigurationException - If there is an issue parsing the XML file.
   * @throws SAXException                 - If there is an issue parsing the XML file.
   * @throws RosterReportException        - If no provider is found in the system with the OHIP
   *                                      number.
   */
  RosterReport getRosterReport(final String folderPath, final String fileName,
      final HttpServletRequest request)
      throws ParseException, IOException,
      ParserConfigurationException, SAXException, RosterReportException {
    RosterReport report = new RosterReport(folderPath, fileName);
    report.setProvider(getFirstProviderFromRepositoryByOhipNumber(report.getProviderOhipNumber()));
    report.setNewRosterReport(
        checkIfRosterReportIsUnprocessedAndUpdateProvider(report.getProvider(), report));
    report.setLoggedInUser(getLoggedInProviderFromRequest(request).getProviderNo());
    return report;
  }
}
