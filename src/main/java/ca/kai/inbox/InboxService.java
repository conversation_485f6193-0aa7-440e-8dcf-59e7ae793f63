package ca.kai.inbox;

import ca.kai.document.DocumentService;
import ca.kai.hrm.HRMService;
import ca.kai.lab.LabService;
import ca.kai.labQueue.Lab;
import ca.kai.labQueue.LabRepository;
import ca.kai.provider.Provider;
import ca.kai.providerLabRouting.ProviderLabRouting;
import ca.kai.providerLabRouting.ProviderLabRoutingRepository;
import ca.kai.providerLabRouting.ProviderRouting;
import ca.kai.readLab.ReadLab;
import ca.kai.readLab.ReadLabRepository;
import ca.kai.systemPreference.SystemPreference;
import ca.kai.systemPreference.SystemPreferenceService;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class InboxService {
    private final DocumentService documentService;
    private final LabService labService;
    private final HRMService hrmService;
    private final ReadLabRepository readLabRepository;
    private final InboxRepository inboxRepository;
    private final ProviderLabRoutingRepository providerLabRoutingRepository;
    private final LabRepository labRepository;
    private final SystemPreferenceService systemPreferenceService;

    private final List<String> sortBySqlList = Arrays.asList("health_number", "sex", "date_time", "date_received", "requesting_client", "disciplineDisplay", "status", "last_name", "result_status", "accession_number", "priority", "report_status", "acknowledge_count");
    private final List<String> sortOrderSqlList = Arrays.asList("DESC", "ASC");

    public InboxService(DocumentService documentService,
                        LabService labService,
                        HRMService hrmService,
                        ReadLabRepository readLabRepository,
                        InboxRepository inboxRepository,
                        ProviderLabRoutingRepository providerLabRoutingRepository,
                        LabRepository labRepository,
                        SystemPreferenceService systemPreferenceService
    ) {
        this.documentService = documentService;
        this.labService = labService;
        this.hrmService = hrmService;
        this.readLabRepository = readLabRepository;
        this.inboxRepository = inboxRepository;
        this.providerLabRoutingRepository = providerLabRoutingRepository;
        this.labRepository = labRepository;
        this.systemPreferenceService = systemPreferenceService;
    }

    /**
     * Gets a list of items in the inbox based on the supplied parameters
     * 
     * If <code>getCounts</code> is {@code true} it will also return the counts of Documents, Labs, and HRM
     * 
     * @param loggedInProvider The currently logged in provider
     * @param providerNumber The provider number to search items for, empty string will return items for all providers, 0 will return all unmatched items
     * @param nameOrHin String containing a demographic name or HIN, it will be parsed to get either the name (Last Name, First Name) or the HIN 
     * @param startDate Date to select all inbox items that came in after, may be empty
     * @param endDate Date to select all inbox items that came in before, may be empty
     * @param status Status of the inbox items to search for
     * @param abnormalStatus Abnormal status of the inbox items to search for
     * @param sortBy String 
     * @param sortOrder <code>ASC</code> or <code>DESC</code>
     * @param page Starting point to select items from (Based on the resultsPerPage param)
     * @param resultsPerPage Number of items to return
     * @param showDocuments Whether documents should be included in the search
     * @param showLabs Whether Labs should be included in the search
     * @param showHrm Whether HRM should be included in the search
     * @param getCounts Whether to get the total inbox counts based on the supplied parameters
     * @return {@code InboxResponse} Response containing the items that matched the supplied parameters, may include counts 
     */
    InboxResponse getInboxItems(
        Provider loggedInProvider,
        String providerNumber,
        String nameOrHin,
        String startDate,
        String endDate,
        String status,
        String abnormalStatus,
        String sortBy,
        String sortOrder,
        Integer page,
        Integer resultsPerPage,
        Boolean showDocuments,
        Boolean showLabs,
        Boolean showHrm,
        Boolean getCounts
    ) {

        String firstName = "";
        String lastName = "";
        String hin = "";
        String accessionNo = "";
        
        if (!nameOrHin.trim().isEmpty()) {
            if (StringUtils.isNumeric(nameOrHin.trim())) {
                hin = nameOrHin;
            } else {
                String[] nameArray = nameOrHin.split(",");
                if (nameArray.length > 0) { // set first name to everything before the ','
                    lastName = nameArray[0].trim();
                }
                if (nameArray.length > 1) { // set last name to everything after the ','
                    firstName = nameArray[1].trim();
                }
            }

            boolean showAccessionNumberColumn = systemPreferenceService.readBooleanPreference("showAccessionNumberColumn", false);
            // Accession number will never contain a comma ',' so only search for it if no name search is used
            if (showAccessionNumberColumn && firstName.isEmpty()) {
                accessionNo = nameOrHin;
            }
        }

        String sortOrderSql = sortOrderSqlList.contains(sortOrder) ? sortOrder : "";

        String sortBySql = sortBySqlList.contains(sortBy) ? sortBy : "";
        SystemPreference documentDisciplineColumnDisplay = systemPreferenceService.getByName(SystemPreference.DOCUMENT_DISCIPLINE_COLUMN_DISPLAY);
        boolean documentShowDescription = documentDisciplineColumnDisplay == null || "document_description".equals(documentDisciplineColumnDisplay.getValue());
        boolean displayLabelUnderDiscipline = systemPreferenceService.readBooleanPreference("display_discipline_as_label_in_inbox");
        
        
        return inboxRepository.getInboxDetails(loggedInProvider, providerNumber, firstName, lastName, hin, accessionNo, startDate, endDate, 
                status, abnormalStatus, sortBySql, sortOrderSql, page, resultsPerPage, showDocuments, showLabs, showHrm, getCounts, documentShowDescription, displayLabelUnderDiscipline);
    }
    
    InboxResponse getDemographicInboxItems(Provider loggedInProvider, Integer demographicNo, Boolean showDocuments, Boolean showLabs, Boolean showHrm, String sortBy, String sortOrder, Integer page, Integer pageSize) {
        boolean displayLabelUnderDiscipline = systemPreferenceService.readBooleanPreference("display_discipline_as_label_in_inbox");
        return inboxRepository.findByDemographicNo(loggedInProvider, demographicNo, showDocuments, showLabs, showHrm, sortBy, sortOrder, page, pageSize, displayLabelUnderDiscipline, true);
    }

    Optional<InboxItem> getInboxItemById(Provider loggedInProvider, InboxItemPK inboxItemKey) {
        SystemPreference documentDisciplineColumnDisplay = systemPreferenceService.getByName(SystemPreference.DOCUMENT_DISCIPLINE_COLUMN_DISPLAY);
        boolean documentShowDescription = documentDisciplineColumnDisplay == null || "document_description".equals(documentDisciplineColumnDisplay.getValue());
        boolean displayLabelUnderDiscipline = systemPreferenceService.readBooleanPreference("display_discipline_as_label_in_inbox");
        
        return inboxRepository.findById(loggedInProvider, inboxItemKey, documentShowDescription, displayLabelUnderDiscipline);
    }
    
    /**
     * Files a provided list of inbox items for the given provider
     * 
     * @param providerNo Provider number who the items will be filed for
     * @param inboxItems List of InboxItems that will be filed
     */
    void fileInboxItems(String providerNo, List<InboxItem> inboxItems) {
        for(InboxItem item : inboxItems) {
            switch (item.getId().getLabType()) {
                case "DOC":
                    documentService.updateStatus(providerNo, item.getId().getSegmentId(), ProviderRouting.FILED_STATUS);
                    break;
                case "HL7":
                    labService.updateStatus(providerNo, item.getId().getSegmentId(), ProviderRouting.FILED_STATUS, item.getAccessionNumber());
                    break;
                case "HRM":
                    hrmService.updateStatus(providerNo, item.getId().getSegmentId(), 1);
                    break;
            }
        }
    }

    /**
     * Forwards the given list of InboxItems to the given list of Providers
     * If the provider already is already linked to the item, nothing will happen
     * If they do not have an existing link then a new one will be made and the status will be set to New
     * 
     * @param providers List of providers that the InboxItems will be forwarded to
     * @param inboxItems List of InboxItems to be forwarded
     */
    void routeProvidersToInboxItems(List<Provider> providers, List<InboxItem> inboxItems) {
        for(Provider provider : providers) {
            for (InboxItem item : inboxItems) {
                switch (item.getId().getLabType()) {
                    case "DOC":
                        documentService.routeProvider(provider.getProviderNo(), item.getId().getSegmentId());
                        break;
                    case "HL7":
                        labService.routeProvider(provider.getProviderNo(), item.getId().getSegmentId());
                        break;
                    case "HRM":
                        hrmService.routeProvider(provider.getProviderNo(), item.getId().getSegmentId());
                        break;
                }
            }
        }
    }
    
    public void markAsRead(String providerNo, String labType, Integer labId) {
        ReadLab readLab = readLabRepository.getByProviderNoAndLabTypeAndLabId(providerNo, labType, labId);
        if (!"undefined".equalsIgnoreCase(providerNo) && readLab == null) {
            readLab = new ReadLab(providerNo, labType, labId);
            readLabRepository.save(readLab);
        }
    }

    public Lab getLabType(Integer labId) {
        return labRepository.findBySegmentId(Long.parseLong(String.valueOf(labId)));
    }

    public void setHrmStatus(final List<InboxItem> inboxItems, final String providerNumber, final String status) {
        hrmService.updateHrmInboxItemsStatus(inboxItems, providerNumber, status);
    }

    public void setHrmDisplayDiscipline(final List<InboxItem> inboxItems) {
        hrmService.updateHrmInboxItemsDisplayDiscipline(inboxItems);
    }
}
