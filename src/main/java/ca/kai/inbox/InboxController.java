package ca.kai.inbox;

import ca.kai.authentication.AuthenticationService;
import ca.kai.labQueue.Lab;
import ca.kai.provider.Provider;
import ca.kai.providerLabRouting.ProviderLabRouting;
import ca.kai.systemPreference.SystemPreference;
import ca.kai.systemPreference.SystemPreferenceService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.val;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.RegEx;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/inbox")
public class InboxController {
    @Autowired
    private InboxService inboxService;
    @Autowired
    private SystemPreferenceService systemPreferenceService;

    private static final Logger logger = LoggerFactory.getLogger(InboxController.class);

    @PreAuthorize("hasPermission(#request, '_lab', 'r')")
    @RequestMapping(path = "/getInboxItems", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public InboxResponse getInboxItems(@RequestParam("providerNumber") String providerNumber, @RequestParam("nameOrHin") String nameOrHin, @RequestParam(name = "startDate", required = false, defaultValue = "") String startDate,
                                       @RequestParam(name = "endDate", required = false, defaultValue = "") String endDate, @RequestParam("selectedStatus") String status,
                                       @RequestParam("selectedAbnormalStatus") String abnormalStatus,
                                       @RequestParam("sortBy") String sortBy, @RequestParam("sortOrder") String sortOrder, @RequestParam("page") Integer page,
                                       @RequestParam("resultsPerPage") Integer resultsPerPage, @RequestParam("documents") Boolean showDocuments, @RequestParam("labs") Boolean showLabs,
                                       @RequestParam("hrm") Boolean showHrm, @RequestParam("getCounts") Boolean getCounts, HttpServletRequest request) {
        Provider loggedInProvider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
        InboxResponse inboxResponse = inboxService.getInboxItems(loggedInProvider, providerNumber, nameOrHin, startDate, endDate, status, abnormalStatus, sortBy, sortOrder, page, resultsPerPage, showDocuments, showLabs, showHrm, getCounts);
        if (showHrm) {
            inboxService.setHrmStatus(inboxResponse.getInboxItems(), providerNumber, status);
            if (systemPreferenceService.readBooleanPreference("display_discipline_as_hrm_subclass", false)) {
                inboxService.setHrmDisplayDiscipline(inboxResponse.getInboxItems());
            }
        }
        return inboxResponse;
    }

    @PreAuthorize("hasPermission(#request, '_lab', 'r')")
    @RequestMapping(path = "/getInboxItem/{labType}/{labId}", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public InboxItem getInboxItem(HttpServletRequest request, @PathVariable("labType") String labType, @PathVariable("labId") Integer labId) {
        Provider loggedInProvider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
        Optional<InboxItem> result = inboxService.getInboxItemById(loggedInProvider, new InboxItemPK(labId, labType));
        return result.orElse(null);
    }

    @PreAuthorize("hasPermission(#request, '_lab', 'r')")
    @RequestMapping(path = "/getDemographicInboxItems/{demographicNo}", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public InboxResponse getDemographicInboxItems(HttpServletRequest request, @PathVariable("demographicNo") Integer demographicNo,
                                                    @RequestParam(value = "page", required = false, defaultValue = "0") Integer page,
                                                    @RequestParam(value = "resultsPerPage", required = false) Integer resultsPerPage,
                                                    @RequestParam(value = "sortBy", required = false, defaultValue = "") String sortBy, 
                                                    @RequestParam(value = "sortOrder", required = false, defaultValue = "") String sortOrder,
                                                    @RequestParam(value = "documents", required = false, defaultValue = "true") Boolean showDocuments,
                                                    @RequestParam(value = "labs", required = false, defaultValue = "true") Boolean showLabs,
                                                    @RequestParam(value = "hrm", required = false, defaultValue = "true") Boolean showHrm) {
        Provider loggedInProvider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
        return inboxService.getDemographicInboxItems(loggedInProvider, demographicNo, showDocuments, showLabs, showHrm, sortBy, sortOrder, page, resultsPerPage);
    }

    @PreAuthorize("hasPermission(#request, '_lab', 'u')")
    @RequestMapping(path = "/fileSelected", method = RequestMethod.POST, consumes = {MediaType.APPLICATION_JSON_VALUE})
    public Boolean fileSelected(HttpServletRequest request, @RequestParam("providerNo") String providerNo, @RequestBody List<InboxItem> inboxItems) {
        inboxService.fileInboxItems(providerNo, inboxItems);
        return true;
    }

    /**
     * Forwards a given list of inbox items to a given list of providers
     * 
     * @param json JSON Object containing the list of InboxItems to be forwarded and the list of Providers to be forwarded to
     */
    @PreAuthorize("hasPermission(#request, '_lab', 'u')")
    @RequestMapping(path = "/forwardSelected", method = RequestMethod.POST, consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void forwardSelected(HttpServletRequest request, @RequestBody ObjectNode json) {
        try {
            //Creates an ObjectMapper to parse the ObjectNode and disables failure when there are unmatched properties in the JSON object
            ObjectMapper mapper = new ObjectMapper();
            mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

            List<Provider> providers = mapper.reader().forType(new TypeReference<List<Provider>>(){}).readValue(json.get("providers"));
            List<InboxItem> inboxItems = mapper.reader().forType(new TypeReference<List<InboxItem>>(){}).readValue(json.get("inboxItems"));
            
            inboxService.routeProvidersToInboxItems(providers, inboxItems);
        }
        catch (IOException e){
            logger.error("Error extracting items from JSON Object", e);
        }
    }

    @PreAuthorize("hasPermission(#request, '_lab', 'u')")
    @RequestMapping(path = "/markAsRead", method = RequestMethod.POST)
    public void markAsRead(@RequestParam String providerNo, @RequestParam String labType, @RequestParam Integer labId, HttpServletRequest request) {
        inboxService.markAsRead(providerNo, labType, labId);
    }

    @PreAuthorize("hasPermission(#request, '_lab', 'u')")
    @RequestMapping(path = "/getLabType", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Lab getLabType(HttpServletRequest request, @RequestParam Integer labId) {
        return inboxService.getLabType(labId);
    }

    @PreAuthorize("hasPermission(#request, '_lab', 'u')")
    @RequestMapping(path = "/getInboxPreferences", method = RequestMethod.GET)
    public List<SystemPreference> getInboxPreferences(HttpServletRequest request) {
        List<String> inboxPreferenceList = Arrays.asList("code_show_hide_column", "lab_embed_pdf", "lab_pdf_max_size", "display_discipline_as_label_in_inbox", "discipline_character_limit_in_inbox", "showAccessionNumberColumn");
        List<SystemPreference> systemPreferenceList = new ArrayList<SystemPreference>();
        for (String value : inboxPreferenceList) {
            SystemPreference systemPreference = systemPreferenceService.getByName(value);
            if (systemPreference != null) {
                systemPreferenceList.add(systemPreference);
            }
        }
        return systemPreferenceList;
    }
}
