package ca.kai.hrm;

import ca.kai.hrm.comment.HRMComment;
import ca.kai.patientLabRouting.PatientHRMRouting;
import ca.kai.providerLabRouting.ProviderHRMRouting;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import health.apps.gateway.common.LinkData;
import java.util.UUID;
import javax.annotation.Nullable;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.codec.digest.DigestUtils;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;

import javax.persistence.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Entity
public class HRMDocument implements LinkData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private Date timeReceived;

    private String reportType;

    private String reportHash;

    private String reportLessTransactionInfoHash;

    private String reportLessDemographicInfoHash;

    private String reportStatus;

    private String reportFile;

    private String sourceFacility;

    private String unmatchedProviders;

    private Integer numDuplicatesReceived = 0;

    private Date reportDate;

    private Integer parentReport;

    private Integer hrmCategoryId;

    private String description = "";

    @OneToMany(mappedBy = "hrmDocumentId")
    @PrimaryKeyJoinColumn(name = "hrmDocumentId")
    @Where(clause = "deleted = false")
    @NotFound(action = NotFoundAction.IGNORE)
    private List<HRMComment> comments = new ArrayList<HRMComment>();

    @OneToMany(mappedBy = "hrmDocumentId")
    @PrimaryKeyJoinColumn(name = "hrmDocumentId")
    @Where(clause = "isActive = true")
    @NotFound(action = NotFoundAction.IGNORE)
    private List<HRMDocumentSubClass> subClasses = new ArrayList<HRMDocumentSubClass>();

    @Transient
    private String category = "";

    @Transient
    private String displaySubClass = "";

    @Transient
    private String noMessageIdHash;

    @Transient
    private PatientHRMRouting patientRouting;

    @Transient
    private List<ProviderHRMRouting> providerRoutings = new ArrayList<ProviderHRMRouting>();

    @Transient
    @Getter
    @Setter
    private List<ProviderHRMRouting> archivedProviderHrmRoutings = new ArrayList<ProviderHRMRouting>();

    @Transient
    @JsonIgnoreProperties(ignoreUnknown = true)
    private HRMReport report;

    @Getter
    @Setter
    private Integer remoteSystemId;

    @Getter
    @Setter
    private String guid = UUID.randomUUID().toString();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getTimeReceived() {
        return timeReceived;
    }

    public void setTimeReceived(Date timeReceived) {
        this.timeReceived = timeReceived;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getReportHash() {
        return reportHash;
    }

    public void setReportHash(String reportHash) {
        this.reportHash = reportHash;
    }

    public String getReportLessTransactionInfoHash() {
        return reportLessTransactionInfoHash;
    }

    public void setReportLessTransactionInfoHash(String reportLessTransactionInfoHash) {
        this.reportLessTransactionInfoHash = reportLessTransactionInfoHash;
    }

    public String getReportLessDemographicInfoHash() {
        return reportLessDemographicInfoHash;
    }

    public void setReportLessDemographicInfoHash(String reportLessDemographicInfoHash) {
        this.reportLessDemographicInfoHash = reportLessDemographicInfoHash;
    }

    public String getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(String reportStatus) {
        this.reportStatus = reportStatus;
    }

    public String getReportFile() {
        return reportFile;
    }

    public void setReportFile(String reportFile) {
        this.reportFile = reportFile;
    }

    public String getSourceFacility() {
        return sourceFacility;
    }

    public void setSourceFacility(String sourceFacility) {
        this.sourceFacility = sourceFacility;
    }

    public String getUnmatchedProviders() {
        return unmatchedProviders;
    }

    public void setUnmatchedProviders(String unmatchedProviders) {
        this.unmatchedProviders = unmatchedProviders;
    }

    public Integer getNumDuplicatesReceived() {
        return numDuplicatesReceived == null ? 0 : numDuplicatesReceived;
    }

    public void setNumDuplicatesReceived(Integer numDuplicatesReceived) {
        this.numDuplicatesReceived = numDuplicatesReceived;
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public Integer getParentReport() {
        return parentReport;
    }

    public void setParentReport(Integer parentReport) {
        this.parentReport = parentReport;
    }

    public Integer getHrmCategoryId() {
        return hrmCategoryId;
    }

    public void setHrmCategoryId(Integer hrmCategoryId) {
        this.hrmCategoryId = hrmCategoryId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<HRMComment> getComments() {
        return comments;
    }

    public void setComments(List<HRMComment> comments) {
        this.comments = comments;
    }

    public List<HRMDocumentSubClass> getSubClasses() {
        return subClasses;
    }

    public void setSubClasses(List<HRMDocumentSubClass> subClasses) {
        this.subClasses = subClasses;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDisplaySubClass() {
        return displaySubClass;
    }

    public void setDisplaySubClass(String displaySubClass) {
        this.displaySubClass = displaySubClass;
    }

    public String getNoMessageIdHash() {
        return noMessageIdHash;
    }

    public void setNoMessageIdHash(String noMessageIdHash) {
        this.noMessageIdHash = noMessageIdHash;
    }

    public PatientHRMRouting getPatientRouting() {
        return patientRouting;
    }

    public void setPatientRouting(PatientHRMRouting patientRouting) {
        this.patientRouting = patientRouting;
    }

    public List<ProviderHRMRouting> getProviderRoutings() {
        return providerRoutings;
    }

    public void setProviderRoutings(List<ProviderHRMRouting> providerRoutings) {
        this.providerRoutings = providerRoutings;
    }

    public HRMReport getReport() {
        return report;
    }

    public void setReport(HRMReport report) {
        if (report != null) {
            String reportFileData = report.getFileData();
            
            String noMessageId = report.getFileData().replaceAll("<MessageUniqueID>.*?</MessageUniqueID>", "<MessageUniqueID></MessageUniqueID>");
            String noTransactionInfo = reportFileData.replaceAll("<TransactionInformation>.*?</TransactionInformation>", "<TransactionInformation></TransactionInformation>");
            String noDemographicInfo = reportFileData.replaceAll("<Demographics>.*?</Demographics>", "<Demographics></Demographics>").replaceAll("<MessageUniqueID>.*?</MessageUniqueID>", "<MessageUniqueID></MessageUniqueID>");
            
            setReportHash(DigestUtils.md5Hex(noMessageId));
            setNoMessageIdHash(DigestUtils.md5Hex(noMessageId));
            setReportLessTransactionInfoHash(DigestUtils.md5Hex(noTransactionInfo));
            setReportLessDemographicInfoHash(DigestUtils.md5Hex(noDemographicInfo));
        }
        this.report = report;
    }

    public static final Comparator<HRMDocument> HRM_DATE_COMPARATOR = new Comparator<HRMDocument>() {
        @Override
        public int compare(HRMDocument o1, HRMDocument o2) {
            return (o1.timeReceived.compareTo(o2.timeReceived));
        }
    };

    public static final Comparator<HRMDocument> HRM_TYPE_COMPARATOR = new Comparator<HRMDocument>() {
        @Override
        public int compare(HRMDocument o1, HRMDocument o2) {
            return (o1.reportType.compareTo(o2.reportType));
        }
    };

    @Nullable
    public HRMDocumentSubClass getFirstSubClass() {
        if (this.subClasses == null || this.subClasses.isEmpty()) {
            return null;
        }
        return this.subClasses.get(0);
    }

    @Override
    public boolean isRemote() {
        return remoteSystemId != null;
    }
}
