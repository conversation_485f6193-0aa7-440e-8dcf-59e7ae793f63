package ca.kai.hrm;

import ca.kai.OscarProperties;
import ca.kai.document.DocumentService;
import ca.kai.hrm.comment.HRMComment;
import ca.kai.hrm.comment.HRMCommentRepository;
import ca.kai.hrm.subClass.HRMSubClass;
import ca.kai.hrm.subClass.HRMSubClassRepository;
import ca.kai.hrm.util.HRMPDFCreator;
import ca.kai.hrm.util.HRMParser;
import ca.kai.inbox.InboxItem;
import ca.kai.incomingLabRules.IncomingLabRule;
import ca.kai.incomingLabRules.IncomingLabRuleRepository;
import ca.kai.incomingLabRules.IncomingLabRuleService;
import ca.kai.incomingLabRules.IncomingLabRuleType;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderRepository;
import ca.kai.providerLabRouting.ProviderHRMRouting;
import ca.kai.providerLabRouting.ProviderHRMRoutingRepository;
import com.lowagie.text.Document;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.val;
import lombok.var;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.pdfbox.io.RandomAccessFile;
import org.apache.pdfbox.multipdf.Splitter;
import org.apache.pdfbox.pdfparser.PDFParser;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.owasp.html.PolicyFactory;
import org.owasp.html.Sanitizers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class HRMService {
    private final ProviderHRMRoutingRepository providerHrmRoutingRepository;
    private final IncomingLabRuleService incomingLabRuleService;
    private final ProviderRepository providerRepository;
    private final DocumentService documentService;
    private final HRMCommentRepository hrmCommentRepository;
    private final HRMDocumentRepository hrmDocumentRepository;
    private final HRMSubClassRepository hrmSubClassRepository;
    private final IncomingLabRuleRepository incomingLabRuleRepository;
    private final HRMPDFCreator hrmPdfCreator;
    private final OscarProperties oscarProperties;

    private HRMReport hrmReport;
    private Document document;
    private HttpServletResponse response;
    private final Logger logger = LoggerFactory.getLogger(HRMService.class);

    private String UNCLAIMED_PROVIDER_NUMBER = "-1";

    public void addReportToInbox(HRMReport report) {
        if(report == null) {
            logger.info("Cannot add null report");
            return;
        }

        logger.info("Adding Report to Inbox, for file: " + report.getFileLocation());
        HRMDocument document = new HRMDocument();

        File reportFile = new File(report.getFileLocation());
        document.setReportFile(reportFile.getName());
        document.setReport(report);
        document.setReportStatus(report.getResultStatus());
        document.setReportType(report.getFirstReportClass());
        document.setTimeReceived(new Date());
        document.setReportDate(HRMParser.getReportDate(report));
        document.setDescription("");

        HRMDocument exactMatch = hrmDocumentRepository.findByReportHash(document.getNoMessageIdHash());
        
        if (exactMatch == null) {
            // if no exact report match found
            HRMDocument sameReportDocument = hrmDocumentRepository.findByReportLessTransactionInfoHash(document.getReportLessDemographicInfoHash());
            String deliverToProviderNo = report.getDeliverToUserId().startsWith("D") ? report.getDeliverToUserId().substring(1) : report.getDeliverToUserId();
            
            if (sameReportDocument != null) {
                // if HRM document exists with a matching report for another recipient
                // route document to the current provider
                routeProvider(deliverToProviderNo, sameReportDocument.getId());
            } else {
                // else, new report
                
                // save document
                document = hrmDocumentRepository.save(document);

                // add provider routing
                if (!routeProvider(deliverToProviderNo, document.getId())) {
                    // if no provider routing

                    // add the provider name to the list of unidentified providers for this report
                    String deliverToName = ((report.getDeliverToUserIdLastName() != null) ?
                            report.getDeliverToUserIdLastName() + ", " + report.getDeliverToUserIdFirstName() // if not null, use name in report 
                            : report.getDeliverToUserId()); // else, use deliver to user ID
                    String unmatchedProviders = StringUtils.trimToEmpty(document.getUnmatchedProviders());
                    
                    document.setUnmatchedProviders(unmatchedProviders + "|" + deliverToName + " (" + deliverToProviderNo + ")");
                    hrmDocumentRepository.save(document);
                }
            }
        } else {
            // else, exact report match found
            // increment the number of duplicates received by 1
            exactMatch.setNumDuplicatesReceived(exactMatch.getNumDuplicatesReceived() + 1);
            hrmDocumentRepository.save(exactMatch);
        }
    }

    public ResponseEntity<byte[]> print(Integer id, HttpServletResponse response, HttpServletRequest request) {
        HRMDocument hrm = hrmDocumentRepository.findById(id);
        hrmReport = HRMParser.parseReport(hrm.getReportFile());
        hrm.setReport(hrmReport);

        byte[] hrmContentBytes;
        String fileExtension = hrmReport.getFileExtension();

        if (".html".equals(hrmReport.getFileExtension())) {
            hrmContentBytes = hrmReport.getBinaryContent();
            PolicyFactory policy = Sanitizers.BLOCKS
                .and(Sanitizers.STYLES)
                .and(Sanitizers.FORMATTING)
                .and(Sanitizers.IMAGES)
                .and(Sanitizers.TABLES);
            String originalContents = new String(hrmContentBytes);
            String safeHtml = policy.sanitize(originalContents);
            hrmContentBytes = safeHtml.getBytes();
        } else if (".pdf".equals(fileExtension)) {
            hrmPdfCreator.setHRMPDFCreator(new ByteArrayOutputStream(), hrm);
            hrmContentBytes = hrmPdfCreator.getPdf();
        } else {
            hrmContentBytes = hrmReport.getBinaryContent();
            if(hrmContentBytes == null) {
                hrmPdfCreator.setHRMPDFCreator(new ByteArrayOutputStream(), hrm);
                hrmContentBytes = hrmPdfCreator.getPdf();
                fileExtension = ".pdf";
            }
        }

        String downloadFile = hrmReport.getLegalLastName() + "-" + hrmReport.getLegalFirstName() + "-" + hrmReport.getFirstReportClass() + fileExtension;

        response.setContentType(getContentType(fileExtension));
        response.setContentLength(hrmContentBytes.length);
        response.setHeader("Content-Disposition", (Boolean.parseBoolean(request.getParameter("inline")) ? "inline;" : "attachment;")+ "filename=\"" + downloadFile.replaceAll("\\s", "_") + "\"");
        ServletOutputStream servletOutputStream = null;
        try  {
            servletOutputStream = response.getOutputStream();
            servletOutputStream.write(hrmContentBytes);
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        finally {
            try {
                if (servletOutputStream != null) {
                    servletOutputStream.flush();
                    servletOutputStream.close();
                }
            }
            catch (IOException e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    public String preview(Integer id, HttpServletResponse response, HttpServletRequest request) {
        HRMDocument hrm = hrmDocumentRepository.findById(id);
        hrmReport = HRMParser.parseReport(hrm.getReportFile());
        hrm.setReport(hrmReport);
        String resultString = null;
        byte[] hrmContentBytes = hrmReport.getBinaryContent();


        if (".html".equals(hrmReport.getFileExtension())) {
            PolicyFactory policy = Sanitizers.BLOCKS
                .and(Sanitizers.STYLES)
                .and(Sanitizers.FORMATTING)
                .and(Sanitizers.IMAGES)
                .and(Sanitizers.TABLES);
            String originalContents = new String(hrmContentBytes);
            resultString = policy.sanitize(originalContents);
        } else if (".pdf".equals(hrmReport.getFileExtension())){
            hrmPdfCreator.setHRMPDFCreator(new ByteArrayOutputStream(), hrm);
            hrmContentBytes = hrmPdfCreator.getPdf();

            String tempDir = oscarProperties.getProperty("TMP_DIR");
            if (tempDir == null) {
                logger.error("Error: No TMP_DIR is set in oscar.properties when fetching document");
                tempDir = "";
            }

            String fileName = tempDir + hrmReport.getLegalLastName() + "-" + hrmReport.getLegalFirstName() + "-" + hrmReport.getFirstReportClass() + ".pdf";

            File fileTemp = null;
            try {
                fileName = fileName.replaceAll("\\s", "_");
                fileTemp = new File(fileName);
                fileTemp.deleteOnExit();
                FileUtils.writeByteArrayToFile(fileTemp, hrmContentBytes);

                if (fileTemp.exists()) {
                    PDFParser parser = new PDFParser(new RandomAccessFile(fileTemp, "rw"));
                    parser.parse();
                    PDDocument pdf = parser.getPDDocument();
                    pdf.setAllSecurityToBeRemoved(true);
                    pdf.save(fileTemp);
                    hrmContentBytes = documentService.renderPDFPage(pdf, 1);
                    pdf.close();
                }
                resultString = Base64.getEncoder().encodeToString(hrmContentBytes);
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            resultString = Base64.getEncoder().encodeToString(hrmContentBytes);
        }

        return resultString;
    }

    public String getContentType(String fileExtension){

        String contentType = "application/octet-stream";

        if(fileExtension.equals(".pdf")) {
            contentType = "application/pdf";
        }
        if(fileExtension.equals(".tiff")) {
            contentType = "image/tiff";
        }
        if(fileExtension.equals(".rtf")) {
            contentType = "text/enriched";
        }
        if(fileExtension.equals(".jpg")) {
            contentType = "image/jpeg";
        }
        if(fileExtension.equals(".gif")) {
            contentType = "image/gif";
        }
        if(fileExtension.equals(".png")) {
            contentType = "image/png";
        }
        if(fileExtension.equals(".html")) {
            contentType = "text/html";
        }

        return contentType;
    }
    
    public ProviderHRMRouting routeProvider(ProviderHRMRouting providerHRMRouting) {
        val unclaimedHrmRouting = providerHrmRoutingRepository
            .getByHrmDocumentIdAndProvider(providerHRMRouting.getHrmDocumentId(), "-1");
        val existingHrmRouting = providerHrmRoutingRepository
            .getByHrmDocumentIdAndProvider(providerHRMRouting.getHrmDocumentId(), providerHRMRouting.getProviderNo());
        if (existingHrmRouting != null) {
            updateRoutingAndDeleteUnclaimed(existingHrmRouting, unclaimedHrmRouting);
            existingHrmRouting.setSignedOffTimestamp(providerHRMRouting.getSignedOffTimestamp());
            providerHrmRoutingRepository.save(existingHrmRouting);
        } else {
            if (unclaimedHrmRouting != null) {
                providerHRMRouting.setId(unclaimedHrmRouting.getId());
            }
            providerHrmRoutingRepository.save(providerHRMRouting);
        }
        
        return existingHrmRouting == null ? providerHRMRouting : existingHrmRouting;
    }
    
    public boolean routeProvider(String providerNo, Integer hrmId) {
        var existingProviderRouting = providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(hrmId, providerNo);
        val unclaimedProviderRouting = providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(hrmId, "-1");
        val providerHrmRouting = createProviderHrmRouting(providerNo, hrmId);
        if (providerRepository.getByProviderNo(providerNo) != null) {
            if (existingProviderRouting != null) {
                updateRoutingAndDeleteUnclaimed(existingProviderRouting, unclaimedProviderRouting);
                providerHrmRouting.setId(existingProviderRouting.getId());
            } else if (unclaimedProviderRouting != null) {
                providerHrmRouting.setId(unclaimedProviderRouting.getId());
            }
            providerHrmRoutingRepository.save(providerHrmRouting);
            // Checks if the provider has any forwarding rules
            List<IncomingLabRule> incomingLabRules = incomingLabRuleRepository.getAllByProviderNo(providerNo);
            if (incomingLabRules.size() > 0) {
                ProviderHRMRouting forwardRoutings;
                for (IncomingLabRule rule : incomingLabRules) {
                    // Filters the ruleTypes to check if the provider is forwarding HRMs
                    Boolean routeDocuments = (rule
                        .getRuleTypes()
                        .stream()
                        .filter(r -> r.getType().equals(IncomingLabRuleType.HRM)).collect(Collectors.toList())).size() > 0;
                    if (routeDocuments) {
                        forwardRoutings = providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(hrmId, rule.getForwardProviderNo());
                        if (forwardRoutings == null) {
                            routeProvider(rule.getForwardProviderNo(), hrmId);
                        }
                    }
                }

                // Updates and saves the status of the providerHrmRouting
                if ("F".equals(incomingLabRules.get(0).getStatus())) {
                    providerHrmRouting.setFiled(true);
                    providerHrmRoutingRepository.save(providerHrmRouting);
                }
            }
        }
        
        return existingProviderRouting != null;
    }
    
    public ProviderHRMRouting updateProviderRouting(ProviderHRMRouting providerHRMRouting) {
        
        val generalProviderHrmRouting = providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(providerHRMRouting.getHrmDocumentId(), "-1");
        val providerRouting = providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(providerHRMRouting.getHrmDocumentId(), providerHRMRouting.getProviderNo());

        if (providerRouting != null) {
            providerHRMRouting.setId((providerRouting.getId()));
            if (generalProviderHrmRouting != null) {
                providerHrmRoutingRepository.delete(generalProviderHrmRouting);
            }
        } else if (generalProviderHrmRouting != null){
            providerHRMRouting.setId(generalProviderHrmRouting.getId());
        }
        
        providerHrmRoutingRepository.save(providerHRMRouting);
        
        // update forwarding provider status (if applicable)
        incomingLabRuleService.updateInboxItemStatusForForwardedProvider(providerHRMRouting.getHrmDocumentId(), "HRM", providerHRMRouting.getProviderNo(), String.valueOf(providerHRMRouting.getSignedOff()));
        
        return providerHRMRouting;
    }
    
    public ProviderHRMRouting updateStatus(String providerNo, Integer hrmId, Integer signedOff) {
        ProviderHRMRouting providerHrmRouting = null;

        if (hrmId != null) {
            providerHrmRouting = providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(
                hrmId, providerNo);

            if (providerHrmRouting != null) {
                providerHrmRouting.setSignedOff(signedOff);
                providerHrmRouting.setSignedOffTimestamp(getSignedOffTimeStamp(signedOff));
                providerHrmRoutingRepository.save(

                providerHrmRouting );
            } else {
                providerHrmRouting = createProviderHrmRouting(providerNo, hrmId);
                providerHrmRouting.setSignedOff(signedOff);
                providerHrmRouting.setSignedOffTimestamp(getSignedOffTimeStamp(signedOff));
                //Checks if there are records for the general inbox. If there are then it updates them with the provider instead of making a new record
                val generalHrmRouting = providerHrmRoutingRepository.getByHrmDocumentIdAndProvider(
                    hrmId,
                    "-1");
                if (generalHrmRouting != null) {
                    providerHrmRouting.setId(generalHrmRouting.getId());
                }
                providerHrmRoutingRepository.save(providerHrmRouting);
            }

            // update forwarding provider status (if applicable)
            incomingLabRuleService.updateInboxItemStatusForForwardedProvider(hrmId, "HRM",
                    providerNo, String.valueOf(signedOff));
        }

        return providerHrmRouting;
    }
private void handleUnclaimedDocument(final ProviderHRMRouting providerHRMRouting) {
    if (providerHrmRoutingRepository
        .getFirstByHrmDocumentIdAndDeleted(providerHRMRouting.getHrmDocumentId(), false) == null) {
      providerHrmRoutingRepository.save(new ProviderHRMRouting(
          UNCLAIMED_PROVIDER_NUMBER,
          providerHRMRouting.getHrmDocumentId()
      ));
    }
  }

  public void removeProvider(final ProviderHRMRouting providerHRMRouting) {
    val existingHrmRouting = providerHrmRoutingRepository
        .getByHrmDocumentIdAndProvider(providerHRMRouting.getHrmDocumentId(), providerHRMRouting.getProviderNo());
    providerHRMRouting.setSignedOff(0);
    providerHRMRouting.setSignedOffTimestamp(null);
    providerHRMRouting.setFiled(false);
    providerHRMRouting.setDeleted(true);
    if (existingHrmRouting != null) {
       providerHRMRouting.setId(existingHrmRouting.getId());
    }
    // Unmatched providers
    if (providerHRMRouting.getProviderNo() == null) {
        removeUnmatchedProvider(providerHRMRouting);
    } else {
        providerHrmRoutingRepository.save(providerHRMRouting);
    }

    handleUnclaimedDocument(providerHRMRouting);
  }

  public HRMComment updateComment(final HRMComment hrmComment) {
    return hrmCommentRepository.save(hrmComment);
  }

    ProviderHRMRouting createProviderHrmRouting(String providerNo, Integer hrmId) {
        Provider provider = providerRepository.findOne(providerNo);
        
        ProviderHRMRouting providerHrmRouting = new ProviderHRMRouting();
        providerHrmRouting.setProviderNo(providerNo);
        providerHrmRouting.setProvider(provider);
        providerHrmRouting.setHrmDocumentId(hrmId);
        providerHrmRouting.setSignedOff(0);
        
        return providerHrmRouting;
    }

    public void updateHrmInboxItemsStatus(final List<InboxItem> inboxItems, final String providerNumber, final String status) {
        for (var inboxItem : inboxItems) {
            if ("HRM".equals(inboxItem.getId().getLabType())) {
                val providerHrmRouting = providerHrmRoutingRepository
                    .getByHrmDocumentIdAndProvider(inboxItem.getId().getSegmentId(), providerNumber);
                if (providerHrmRouting != null) {
                    updateItemStatus(inboxItem, Arrays.asList(providerHrmRouting));
                } else {
                    val generalHrmRoutingList = getHrmRoutingByStatus(inboxItem, status);
                    if (!generalHrmRoutingList.isEmpty()) {
                        updateItemStatus(inboxItem, generalHrmRoutingList);
                    }
                }
            }
        }
    }

    private List<ProviderHRMRouting> getHrmRoutingByStatus(final InboxItem inboxItem, final String status) {
        List<ProviderHRMRouting> generalHrmRoutingList;
        switch (status) {
            case "A":
                generalHrmRoutingList = providerHrmRoutingRepository
                    .getRoutesByHrmDocumentIdAndStatus(inboxItem.getId().getSegmentId(), 1, false, false);
                break;
            case "N":
                generalHrmRoutingList = providerHrmRoutingRepository
                    .getRoutesByHrmDocumentIdAndStatus(inboxItem.getId().getSegmentId(), 0, false, false);
                break;
            case "X":
                generalHrmRoutingList = providerHrmRoutingRepository
                    .getRoutesByHrmDocumentIdAndStatus(inboxItem.getId().getSegmentId(), 0, false, true);
                break;
            case "F":
                generalHrmRoutingList = providerHrmRoutingRepository
                    .getRoutesByHrmDocumentIdAndStatus(inboxItem.getId().getSegmentId(), 0, true, false);
                break;
            default:
                generalHrmRoutingList = providerHrmRoutingRepository
                    .getRoutesByHrmDocumentId(inboxItem.getId().getSegmentId());
        }
        return generalHrmRoutingList;
    }

    private void updateItemStatus(final InboxItem inboxItem, final List<ProviderHRMRouting> providerHrmRoutingList) {
        if (providerHrmRoutingList.isEmpty()) {
            return;
        }
        val providerHrmRouting = providerHrmRoutingList
            .get(providerHrmRoutingList.size() - 1);
        if (providerHrmRouting.getDeleted()) {
            inboxItem.setStatus("X");
        } else if (providerHrmRouting.isFiled()) {
            inboxItem.setStatus("F");
        } else if (providerHrmRouting.getSignedOff() == 1) {
            inboxItem.setStatus("A");
        }
    }

    private Date getSignedOffTimeStamp(final int signedOff) {
        return signedOff == 1 ? new Date() : null;
    }

    private void updateRoutingAndDeleteUnclaimed(
        final ProviderHRMRouting existingHrmRouting,
        final ProviderHRMRouting unclaimedHrmRouting
    ) {
        existingHrmRouting.setDeleted(false);
        if (unclaimedHrmRouting != null) {
            providerHrmRoutingRepository.delete(unclaimedHrmRouting);
        }
    }

    private void removeUnmatchedProvider(ProviderHRMRouting providerHrmRouting) {
        val hrmDocument = hrmDocumentRepository.findById(providerHrmRouting.getHrmDocumentId());
        var provider = providerHrmRouting.getProvider();
        val name = "".equals(provider.getLastName())
            ? ", " + provider.getFirstName()
            : "".equals(provider.getFirstName())
                ? provider.getLastName() + ","
                : provider.getFormattedName();
        val removedUnmatched = hrmDocument
            .getUnmatchedProviders()
            .replace("|" + name, "");
        hrmDocument.setUnmatchedProviders(removedUnmatched);
        hrmDocumentRepository.save(hrmDocument);
    }

    /**
     * This method loads the byte array of an HRM into a PDDocument.
     *
     * @param id The ID of the desired HRM document
     * @return PDDocument with the byte array loaded
     * @throws IOException IOException
     */
    public PDDocument getPdfContentsById(final Integer id) throws IOException {
        val hrm = hrmDocumentRepository.findById(id);
        hrmPdfCreator.setHRMPDFCreator(new ByteArrayOutputStream(), hrm);
        return PDDocument.load(hrmPdfCreator.getPdf());
    }

    /**
     * This method returns the byte array of the preview page (first page) of an HRM document.
     *
     * @param id ID of the HRM
     * @param response HttpServletResponse
     * @return The byte array of the preview page (first page) of a given HRM
     * @throws IOException IOException
     */
    public String getPreviewPage(final Integer id, final HttpServletResponse response)
        throws IOException {
        try (val hrmPdf = getPdfContentsById(id);
                val page1 = new Splitter().split(hrmPdf).get(0);
                val byteArrayOutputStream = new ByteArrayOutputStream();) {

            page1.save(byteArrayOutputStream);
            val byteArray = byteArrayOutputStream.toByteArray();

            try (OutputStream os = response.getOutputStream()) {
                os.write(byteArray, 0, byteArray.length);
            }

            return byteArrayOutputStream.toString();
        }
    }

    public void updateHrmInboxItemsDisplayDiscipline(List<InboxItem> inboxItems) {
        // Filter HRM items and collect their segment IDs
        List<Integer> hrmSegmentIds = inboxItems.stream()
            .filter(item -> "HRM".equals(item.getId().getLabType()))
            .map(item -> item.getId().getSegmentId())
            .collect(Collectors.toList());

        // Fetch all HRM documents in a single query and map them by ID
        Map<Integer, List<HRMDocumentSubClass>> subclassMap = hrmDocumentRepository.findAllByIdIn(hrmSegmentIds)
            .stream()
            .collect(Collectors.toMap(
                HRMDocument::getId,
                HRMDocument::getSubClasses
            ));

        // Update each HRM inbox item with its discipline display
        for (InboxItem item : inboxItems) {
            if (!"HRM".equals(item.getId().getLabType())) continue;

            int segmentId = item.getId().getSegmentId();
            List<HRMDocumentSubClass> subClasses = subclassMap.get(segmentId);
            String dispSubClass = getSubClassDisplayString(subClasses);

            if (StringUtils.isNotBlank(dispSubClass)) {
                item.setDisciplineDisplay(dispSubClass);
                item.setDiscipline(dispSubClass);
            }
        }

    }

    public String getSubClassDisplayString(List<HRMDocumentSubClass> subClasses) {
        if (subClasses == null || subClasses.isEmpty()) {
            return null;
        }

        HRMDocumentSubClass firstSubClass = subClasses.get(0);
        String description = firstSubClass.getSubClassDescription();

        if (StringUtils.isNotBlank(description)) {
            return description;
        }

        String[] parts = firstSubClass.getSubClass().split("\\^");
        return (parts.length > 1) ? parts[1] : null;
    }
}
