package ca.kai.hrm;

import java.util.List;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface HRMDocumentRepository extends CrudRepository<HRMDocument, Integer>,
    JpaSpecificationExecutor<HRMDocument> {


    HRMDocument findById(Integer id);
    List<HRMDocument> findAllByIdIn(List<Integer> ids);

    @Query(value = "select h.* from HRMDocument h where h.reportHash = ?1 limit 1", nativeQuery = true)
    HRMDocument findByReportHash(String reportHash);
    
    @Query(value = "select h.* from HRMDocument h where h.reportLessTransactionInfoHash = ?1 limit 1", nativeQuery = true)
    HRMDocument findByReportLessTransactionInfoHash(String hash);

    @Query(value = "SELECT h.* FROM HRMDocument h "
        + "LEFT JOIN HRMDocumentToDemographic e "
        + "ON h.id = e.hrmDocumentId "
        + "WHERE e.demographicNo = ?1", nativeQuery = true)
    List<HRMDocument> getAllByDemographicNumber(final Integer demographicNumber);
}
