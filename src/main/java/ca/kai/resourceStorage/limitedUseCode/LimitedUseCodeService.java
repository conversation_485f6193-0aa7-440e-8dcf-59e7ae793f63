package ca.kai.resourceStorage.limitedUseCode;

import ca.kai.OscarProperties;
import ca.kai.resourceStorage.ResourceStorage;
import ca.kai.resourceStorage.ResourceStorageRepository;
import ca.kai.util.XmlParser;
import org.apache.commons.lang.StringUtils;
import org.jdom.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class LimitedUseCodeService {

    private static final Logger logger = LoggerFactory.getLogger(LimitedUseCodeService.class);
    
    private ResourceStorageRepository resourceStorageRepository;
    private OscarProperties oscarProperties;
    private XmlParser xmlParser;

	static Map<String, List<LCCNote>> lccNoteMap = new HashMap<String, List<LCCNote>>();

	public LimitedUseCodeService(ResourceStorageRepository resourceStorageRepository, OscarProperties oscarProperties, XmlParser xmlParser) {
	    this.resourceStorageRepository = resourceStorageRepository;
	    this.oscarProperties = oscarProperties;
	    this.xmlParser = xmlParser;
	}

	public List<LCCNote> getLimitedUseCodes (String din) {
	    //Loads the lccNoteMap and stores it globally to prevent it from excessive parsing/loading
	    if (lccNoteMap.isEmpty()) {
            InputStream inputStream = null;
            try {
                //The formulary can be in one of three places: The obd formmulary file, stored in resourceStorage, or if neither of those two have entries, the default is the data extract file.
                String obdForumularyFileName = oscarProperties.getProperty("odb_formulary_file");
                if (StringUtils.isNotEmpty(obdForumularyFileName)) {
                    inputStream = new BufferedInputStream(new FileInputStream(obdForumularyFileName));
                } else {
                    ResourceStorage resourceStorage = resourceStorageRepository.findFirstByResourceTypeAndActiveIsTrue(ResourceStorage.LU_CODES);
                    if(resourceStorage != null) {
                        inputStream = new ByteArrayInputStream(resourceStorage.getFileContents());
                    } else {
                        inputStream = getClass().getClassLoader().getResourceAsStream("oscarRx/data_extract_20250627.xml");
                    }
                }

                Extract extract = (Extract) xmlParser.fromInput(Extract.class, inputStream);

                if (extract != null && extract.getFormulary() != null) {
                    extract.getFormulary().getPcg2().stream()
                            .flatMap(pcg2 -> pcg2.getPcg6().stream())
                            .flatMap(pcg6 -> pcg6.getGenericName().stream())
                            .flatMap(genericName -> genericName.getPcgGroup().stream()).forEach(pcgGroup -> {
                                if (pcgGroup.getLccNote() != null && !pcgGroup.getLccNote().isEmpty()) {
                                    pcgGroup.getPcg9().stream()
                                            .flatMap(pcg9 -> pcg9.getDrug().stream())
                                            .forEach(drug -> {
                                                if (StringUtils.isNotEmpty(drug.getId())) {
                                                    lccNoteMap.put(drug.getId(), pcgGroup.getLccNote());
                                                }
                                            });
                                }
                    });
                }
                
            } catch (Exception e) {
                logger.error("Error while parsing limited use codes - ", e);
            } finally {
                if(inputStream != null) {
                    try {
                        inputStream.close();
                    }catch(IOException e) {
                        logger.error("Error closing inputstream while parsing limited use codes - ", e);
                    }
                }
            }
        }
	    
        List<LCCNote> limitedUseCodeList = new ArrayList<LCCNote>();
        if (StringUtils.isNotEmpty(din) && lccNoteMap.containsKey(din)) {
            limitedUseCodeList = lccNoteMap.get(din);
            for (LCCNote lccNote : limitedUseCodeList) {
                lccNote.setShowDescription(false);
            }
        }
        return limitedUseCodeList;
    }
}
