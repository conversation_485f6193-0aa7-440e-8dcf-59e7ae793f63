package ca.kai.rx.drug.instructions;

import ca.kai.rx.RxUtils;
import ca.kai.rx.drug.Drug;
import ca.kai.rx.drug.DrugInstruction;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Stream;
import javax.annotation.Nonnull;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

public class RxInstructionParser implements InstructionParser {
  private static final String[] METHODS = {
      "TAKE", "CONSUME", "RUB", "APPLY", "INJECT"
  };
  private static final String[] DRUG_FORMS = {
      "TABLETS", "TABLET", "TABS", "TAB", "CAPSULES",
      "CAPSULE", "CAPS", "CAP", "SUSPENSION", "VIAL"
  };
  private static final String[] FREQ_CODE = {
      "OD", "1D", "QAM", "QPM", "QHS", "HS", "QAM&PM", "QAM&HS", "NOON", "QLUNCH", "QDINNER",
      "BID-TID", "BID-QID", "TID-QID", "BID", "TID", "3D", "QID", "4D", "5D", "6D", "QD-BID",
      "6D-8D", "QH", "Q1H", "Q2H", "Q3H", "Q4H", "Q6H", "Q8H", "Q12H", "Q4-6H", "Q6-8H", "Q24H",
      "Q72H", "Q5MIN", "Q2D", "QOTHERD", "Q2NDD", "Q3D", "Q1W", "Q1TW", "QW", "Q2W", "Q1M", "QM",
      "Q2M", "Q3M", "2TW", "2TPW", "3TW", "3TPW", "QMWSS", "QTTF", "Q3W", "Q4W", "Q5W", "Q6W",
      "Q7W", "Q8W", "Q9W", "Q10W", "Q11W", "Q12W"
  };
  public static final String[] DEFINITIONS = {
      "once daily", "once a day", "every morning", "every evening", "every day at bedtime",
      "at bedtime", "every morning and every evening", "every morning and at bedtime", "at noon",
      "at lunch", "at dinner", "2 to 3 times a day", "2 to 4 times a day", "3 to 4 times a day",
      "twice daily", "3 times daily", "3 times a day", "4 times daily", "4 times a day",
      "5 times a day", "6 times a day", "1 to 2 times daily", "6 to 8 times a day", "every hour",
      "every 1 hour", "every 2 hours", "every 3 hours", "every 4 hours", "every 6 hours",
      "every 8 hours", "every 12 hours", "every 4 to 6 hours", "every 6 to 8 hours",
      "every 24 hours", "every 72 hours", "every 5 min", "every 2 days", "every other day",
      "every 2nd day", "every 3 days", "once a week", "one time per week", "once a week",
      "once every 2 weeks", "once a month", "once monthly", "every 2 months", "every 3 months",
      "twice weekly", "two times per week", "three times weekly", "three times per week",
      "on Monday, Wednesday, Saturday, and Sunday each week",
      "on Tuesday, Thursday, and Friday each week", "once every 3 weeks", "once every 4 weeks",
      "once every 5 weeks", "once every 6 weeks", "once every 7 weeks", "once every 8 weeks",
      "once every 9 weeks", "once every 10 weeks", "once every 11 weeks", "once every 12 weeks"
  };
  private static final String[] FREQUENCIES = {
      "ONCE DAILY", "1X DAILY", "1X DAY", "TWICE DAILY", "2X DAILY", "2X DAY", "3X DAILY",
      "3X DAY", "4X DAILY", "4X DAY", "DAILY", "WEEKLY", "1X WEEK", "MONTHLY", "1X MONTH"
  };
  private static final String[] NUMBER_WORDS = {
      "ONE", "TWO", "THREE", "FOUR", "FIVE", "SIX", "SEVEN", "EIGHT", "NINE", "TEN"
  };
  private static final String[] DURATION_UNITS = {
      "DAYS", "WEEKS", "MONTHS", "YEARS", "DAY", "WEEK", "MONTH", "YEAR", "D", "W", "M", "Y"
  };
  private static final String[] DURATION_UNITS_DAY = {"DAYS", "DAY", "D"};
  private static final String[] DURATION_UNITS_WEEK = {"WEEKS", "WEEK", "W"};
  private static final String[] DURATION_UNITS_MONTH = {"MONTHS", "MONTH", "M"};
  private static final String[] DURATION_UNITS_YEAR = {"YEARS", "YEAR", "Y"};
  private static final String[] ROUTES = {"PO", "SL", "IM", "SC", "PATCH", "TOP.", "INH", "SUPP"};
  public static final String WORDS_SANITIZER_REGEX = ";|\\n";
  public static String[] frequencies = { "\\s(?i)OD\\s", "\\s(?i)BID\\s", "\\s(?i)TID\\s", "\\s(?i)QID\\s", "\\s(?i)Q1H\\s", "\\s(?i)Q2H\\s", "\\s(?i)Q1-2H\\s", "\\s(?i)Q3-4H\\s", "\\s(?i)Q4H\\s", "\\s(?i)Q4-6H\\s", "\\s(?i)Q6H\\s", "\\s(?i)Q8H\\s", "\\s(?i)Q12H\\s", "\\s(?i)QAM\\s", "\\s(?i)QPM\\s", "\\s(?i)QHS\\s", "\\s(?i)Q1Week\\s", "\\s(?i)weekly\\s", "\\s(?i)Q2Week\\s", "\\s(?i)Q1Month\\s", "\\s(?i)Q3Month\\s", "\\s(?i)monthly\\s", "\\s(?i)once daily\\s", "\\s(?i)twice daily\\s", "\\s(?i)3x day\\s",
      "\\s(?i)4x day\\s", "\\s(?i)3x daily\\s", "\\s(?i)4x daily\\s", "\\s(?i)OD$", "\\s(?i)BID$", "\\s(?i)TID$", "\\s(?i)QID$", "\\s(?i)Q1H$", "\\s(?i)Q2H$", "\\s(?i)Q1-2H$", "\\s(?i)Q3-4H$", "\\s(?i)Q4H$", "\\s(?i)Q4-6H$", "\\s(?i)Q6H$", "\\s(?i)Q8H$", "\\s(?i)Q12H$", "\\s(?i)QAM$", "\\s(?i)QPM$", "\\s(?i)QHS$", "\\s(?i)Q1Week$", "\\s(?i)weekly$", "\\s(?i)Q2Week$", "\\s(?i)Q1Month$", "\\s(?i)Q3Month$", "\\s(?i)monthly$", "\\s(?i)once daily$", "\\s(?i)twice daily$", "\\s(?i)3x day$",
      "\\s(?i)4x day$", "\\s(?i)3x daily$", "\\s(?i)4x daily$", "\\s(?i)OD;", "\\s(?i)BID;", "\\s(?i)TID;", "\\s(?i)QID;", "\\s(?i)Q1H;", "\\s(?i)Q2H;", "\\s(?i)Q1-2H;", "\\s(?i)Q3-4H;", "\\s(?i)Q4H;", "\\s(?i)Q4-6H;", "\\s(?i)Q6H;", "\\s(?i)Q8H;", "\\s(?i)Q12H;", "\\s(?i)QAM;", "\\s(?i)QPM;", "\\s(?i)QHS;", "\\s(?i)Q1Week;", "\\s(?i)weekly;", "\\s(?i)Q2Week;", "\\s(?i)Q1Month;", "\\s(?i)Q3Month;", "\\s(?i)monthly;", "\\s(?i)once daily;", "\\s(?i)twice daily;", "\\s(?i)3x day;",
      "\\s(?i)4x day;", "\\s(?i)3x daily;", "\\s(?i)4x daily;", "\\s(?i)daily\\s", "\\s(?i)daily$", "\\s(?i)daily;",// put at last because if frequency is 'twice daily', it will first be detected as 'daily'
      "\\s(?i)1D\\s", "\\s(?i)HS\\s", "\\s(?i)QAM&PM\\s", "\\s(?i)QAM&HS\\s", "\\s(?i)NOON\\s", "\\s(?i)QLUNCH\\s", "\\s(?i)QDINNER\\s", "\\s(?i)2D\\s", "\\s(?i)3D\\s", "\\s(?i)4D\\s", "\\s(?i)5D\\s", "\\s(?i)6D\\s", "\\s(?i)QD-BID\\s", "\\s(?i)BID-TID\\s", "\\s(?i)BID-QID\\s", "\\s(?i)TID-QID\\s", "\\s(?i)6D-8D\\s", "\\s(?i)QH\\s", "\\s(?i)Q6-8H\\s", "\\s(?i)Q24H\\s", "\\s(?i)Q72H\\s", "\\s(?i)Q5MIN\\s", "\\s(?i)Q2D\\s", "\\s(?i)QOTHERD\\s", "\\s(?i)Q2NDD\\s", "\\s(?i)Q3D\\s", "\\s(?i)Q3RDD\\s", "\\s(?i)Q1W\\s", "\\s(?i)Q1TW \\s", "\\s(?i)QW\\s", "\\s(?i)Q2W\\s", "\\s(?i)Q1M\\s", "\\s(?i)QM\\s", "\\s(?i)Q2M\\s", "\\s(?i)Q3M\\s", "\\s(?i)2TW\\s", "\\s(?i)2TPW\\s", "\\s(?i)3TW\\s", "\\s(?i)3TPW\\s", "\\s(?i)QMWSS\\s", "\\s(?i)QTFF\\s",
  };
  private static final Map<String, String> FREQUENCY_DEFINITION_MAP = createFrequencyDefinitionMap();
  private static final Map<String, String> NUMBERWORD_TO_NUMBER_MAP = createNumberWordToNumberMap();
  private static final int EXPECTED_FRACTION_PARTS_LENGTH = 2;

  /**
   * This function takes a drug as an input. The function takes the drug's instructions (from the
   * drug "special" attribute) and sets each drug attribute based on the instructions given.
   *
   * @param drug The input drug whose instructions will be parsed
   */
  @Override
  public void setValuesFromInstructions(final Drug drug) {
    if (drug == null) {
      return;
    }
    val instructions = drug.getSpecial();
    extractValuesFromInstruction(drug, instructions);
    setDrugValues(drug, instructions);
  }

  private void extractValuesFromInstruction(final Drug drug, @Nonnull final String instructions) {
    val method = checkForMethod(instructions);
    val amount = checkForAmount(method, instructions);
    val drugForm = checkForDrugForm(drug, instructions);
    val totalDuration = checkForDurationAndDurationUnit(instructions);
    val duration = totalDuration[0];
    val durationUnit = convertToStandardDurationUnit(totalDuration[1]);
    val frequency = checkForFrequency(drug, instructions).toUpperCase();
    try {
      drug.setTakeMin(Float.parseFloat(amount[0]));
      drug.setTakeMax(Float.parseFloat(amount[1]));
    } catch (NumberFormatException e) {
      drug.setTakeMax(0);
      drug.setTakeMin(0);
    }
    drug.setMethod(method);
    drug.setDrugForm(drugForm);
    drug.setFreqCode(frequency);
    drug.setDuration(duration);
    drug.setDurationUnit(durationUnit);
    val totalQuantity = computeTotalQuantity(duration, durationUnit, frequency, amount[1], drug);
    val formattedTotalQuantity = formatTotalQuantity(totalQuantity);
    drug.setQuantity(formattedTotalQuantity);
  }

  /**
   * Computes the total quantity of the drug to be administered, based on the provided parameters.
   * If any of the parameters (except for the drug) are blank, this method will return the quantity
   * as specified in the provided drug object. Otherwise, it calculates the total quantity based
   * on the provided duration, duration unit, frequency, and maximum take amount.
   *
   * @param duration The duration for which the drug is to be administered. Should be non-blank
   *                 to perform the calculation.
   * @param durationUnit The unit of the duration parameter, such as days, weeks, etc.
   *                     Should be non-blank to perform the calculation.
   * @param frequency The frequency at which the drug is to be administered.
   *                  Should be non-blank to perform the calculation.
   * @param takeMax The maximum amount of the drug to be taken at each administration.
   *                Should be non-blank to perform the calculation.
   * @param drug The drug object containing the default quantity to be returned if any of the
   *             other parameters are blank.
   * @return The total quantity of the drug to be administered, calculated based on the provided
   *         parameters if they are non-blank, or returned from the drug object otherwise.
   */
  private double computeTotalQuantity(
      final String duration,
      final String durationUnit,
      final String frequency,
      final String takeMax,
      final Drug drug
  ) {
    if (StringUtils.isAnyBlank(duration, durationUnit, takeMax) || "0".equals(takeMax)) {
      val quantity = StringUtils.defaultIfEmpty(drug.getQuantity(), "0");
      return Double.parseDouble(quantity);
    }
    if (StringUtils.isBlank(frequency)) {
      return calculateTotalQuantity(duration, durationUnit, "1D", takeMax);
    }
    return calculateTotalQuantity(duration, durationUnit, frequency, takeMax);
  }

  private String formatTotalQuantity(final double totalQuantity) {
    return totalQuantity == (long) totalQuantity
        ? String.format("%.0f", totalQuantity)
        : String.valueOf(totalQuantity);
  }

  private static boolean isNumeric(String str) {
    return str.matches("\\d+"); // checks if the string contains only digits
  }

  // Utility method to extract numeric part from string "for10" format
  private static String extractNumericFromFor(String str) {
    if (str.toLowerCase().startsWith("for")) {
      return str.substring(3); // remove "for" and return remaining which is assumed to be number
    }
    return "";
  }

  private void setDrugValues(final Drug drug, @Nonnull final String instructions) {
    val site = checkForSite(instructions);
    val route = checkForRoute(instructions);
    drug.setSite(StringUtils.defaultIfEmpty(site, drug.getSite()));
    drug.setRoute(StringUtils.defaultIfEmpty(route, drug.getRoute()));
    drug.setParsedInstructions(new ArrayList<>());
    drug.setSpecial(instructions);
  }

  /**
   * This function takes the drug's attributes and builds an instruction string based on those
   * attributes.
   *
   * @param d Input drug
   * @return The resulting instruction string
   */
  public String buildInstructionsFromDiscreteElements(Drug d) {
    var instructions = new StringBuilder();
    var method = d.getMethod() != null ? d.getMethod() : "";
    var drugForm = d.getDrugForm() != null ? d.getDrugForm() : "";
    var freqCode = d.getFreqCode() != null ? d.getFreqCode() : "";
    var duration = d.getDuration() != null ? d.getDuration() : "";
    var durationUnit = d.getDurationUnit() != null ? d.getDurationUnit() : "";
    // Add method to instruction string
    if (!method.isEmpty()) {
      method = method.substring(0, 1).toUpperCase() + method.substring(1).toLowerCase();
      instructions.append(method).append(" ");
    }
    // Add take min and take max to instruction string
    if (d.getTakeMin() > 0 || d.getTakeMax() > 0) {
      if (d.getTakeMin() == d.getTakeMax()) {
        instructions.append(Math.round(d.getTakeMin())).append(" ");
      } else {
        instructions
            .append(Math.round(d.getTakeMin())).append("-")
            .append(Math.round(d.getTakeMax())).append(" ");
      }
    }
    // Add frequency code definition and drug form to instruction string
    val definition = convertFrequencyToDefinition(freqCode);
    if (!definition.isEmpty()) {
      if (!drugForm.isEmpty()) {
        instructions.append(convertToStandardDrugForm(drugForm)).append(" ");
      }
      instructions.append(definition).append(" ");
    }
    // Add duration to instruction string
    durationUnit = convertToStandardDurationUnit(durationUnit);
    if (!duration.isEmpty() && !durationUnit.isEmpty()) {
      val inDays = convertDurationToDays(duration, durationUnit);
      instructions.append("for ").append(Math.round(inDays)).append(" ").append("days");
    }
    return instructions.toString();

  }

  /**
   * This function converts the frequency code input into its respective long form.
   *
   * @param frequencyCode The frequency code
   * @return The long form of the frequency code
   */
  public static String convertFrequencyCodeFullText(String frequencyCode) {
    try {
      return RxUtils.Frequency.valueOf(frequencyCode).getLongForm();
    } catch (IllegalArgumentException e) {
      return frequencyCode;
    }
  }

  /**
   * This function checks the instruction string for a method (e.g. take, rub, inject), and returns
   * the method string if it finds one, otherwise, it returns a blank string.
   *
   * @param instructions The drug instructions
   * @return The method string found in the drug instructions
   */
  public static String checkForMethod(String instructions) {
    val modifiedInstructions = instructions.toUpperCase();
    val words = modifiedInstructions.split(" ");
    for (var methods : METHODS) {
      if (modifiedInstructions.contains(methods) && (words[0].equals(methods) || (words[words.length-1].contains(methods)))) {
        var index = modifiedInstructions.indexOf(methods);
        return instructions.substring(index, index + methods.length());
      }
    }
    return "";
  }

  /**
   * This function checks the instruction string for both duration and duration unit. If duration
   * and duration unit are found in the instruction string return a string array containing the
   * duration and duration unit. Otherwise, return an array with empty strings.
   *
   * @param instructions The drug instructions
   * @return A String array containing the duration and duration unit
   */
  public static String[] checkForDurationAndDurationUnit(String instructions) {
    val instructionArray = instructions.toUpperCase().split("[ \n]");
    var duration = "";
    var durationUnit = "";
    // Check for duration and duration unit in the instruction string (one word)
    val checkForOneWord = "^x?\\d+[DWMY]$|^\\d+DAYS?$|^\\d+WEEKS?$|^\\d+MONTHS?$|^\\d+YEARS?$";
    val splitDuration = "(?=\\D)(?<=\\d)";
    Pattern p = Pattern.compile(checkForOneWord, Pattern.CASE_INSENSITIVE);
    for (var word : instructionArray) {
      val m = p.matcher(word.replaceAll(WORDS_SANITIZER_REGEX, ""));
      if (m.find()) {
        val parts = word.split(splitDuration);
        duration = parts[0];
        durationUnit = parts[1];
      }
    }
    // Check for duration and duration unit in the instruction string (space separated)
    for (var i = 0; i < instructionArray.length; i++) {
      var word = instructionArray[i].replaceAll(WORDS_SANITIZER_REGEX, "");
      if (i != 0 && inArrayIgnoreCase(word, DURATION_UNITS)) {
        if (inArrayIgnoreCase(instructionArray[i-1], NUMBER_WORDS) ||
            isNumeric(instructionArray[i-1])) {
          duration = instructionArray[i-1];
        } else if (instructionArray[i-1].toLowerCase().startsWith("for") &&
            isNumeric(extractNumericFromFor(instructionArray[i-1]))) {
          duration = extractNumericFromFor(instructionArray[i-1]);
        }
        durationUnit = word;
      }
    }
    return new String[] {
        duration.replaceAll("(?i)x", ""),
        durationUnit.replaceAll(WORDS_SANITIZER_REGEX, "")
    };
  }

  /**
   * This function checks the instruction string for route.
   * If a route is found, then return the route string.
   * Otherwise, it returns an empty string.
   *
   * @param instructions The instruction string
   * @return The route contained in the instruction string
   */
  public static String checkForRoute(String instructions) {
    //Check for route
    val instructionArray = instructions.split(" ");
    for (int i = 0 ; i < instructionArray.length; i++) {
      val word = instructionArray[i];
      if (inArrayIgnoreCase(word, ROUTES)) {
        return word;
      } else if (i+1 < instructionArray.length && word.equalsIgnoreCase("Route:")) {
        return instructionArray[i+1];
      }
    }
    return "";
  }

  /**
   * This function checks the instruction string for a site string. In the
   * instructions, this usually comes after "SITE:". If the site is found,
   * return the site, otherwise return an empty string.
   *
   * @param instructions The instruction string
   * @return The site string found in the instruction string
   */
  public static String checkForSite(String instructions) {
    //Check for site
    //The site should come after writing "Site:"
    val modifiedInstructions = instructions.toUpperCase();
    if (StringUtils.containsIgnoreCase(instructions, "SITE:")) {
      val indexSite = modifiedInstructions.indexOf("SITE:");
      if ((indexSite + 6) < instructions.length()) {
        return instructions.substring(indexSite + 6);
      }
    }
    return "";
  }

  /**
   * This function checks the instruction string for frequency.
   * If frequency is found, then return the frequency string.
   * Otherwise, it returns an empty string.
   *
   * @param instructions The instruction string
   * @return The frequency code found in the instruction string
   */
  public static String checkForFrequency(final Drug drug, final String instructions) {
    // Check for frequency
    // If initial frequency check does not find frequency keywords, check for frequency code
    var frequency = convertToStandardFrequency(checkForNonStandardFrequency(instructions));
    if (frequency.isEmpty()) {
      frequency = checkFor(instructions, FREQ_CODE);
    }
    if (frequency.isEmpty() && drug != null && drug.getFreqCode() != null) {
      frequency = drug.getFreqCode();
    }
    return frequency;
  }

  /**
   * This function checks the instruction string for drug form.
   * If a drug has a drug form by default, set it to that drug form,
   * otherwise set drug form to the form in the instructions.
   *
   * @param instructions The instruction string
   * @return The drug form found in the instruction string
   */
  public static String checkForDrugForm(Drug d, String instructions) {
    var drugForm = "";
    val drugHasDrugForm = d.getDrugForm() != null && !d.getDrugForm().isEmpty();
    if (drugHasDrugForm) {
      drugForm = d.getDrugForm();
    } else {
      drugForm = checkFor(instructions, DRUG_FORMS);
    }
    return drugForm;
  }

  /**
   * This function checks the instruction string for amount (takeMin/takeMax).
   * First, the function checks if method was set, and searches for the amount after the method.
   * Then, the function checks the whole string if it contains a single amount (with digits), ranges
   * without spaces, and ranges with spaces.
   *
   * @param instructions The instruction string
   * @return A string array containing the takeMin and takeMax values in the instruction string
   */
  public static String[] checkForAmount(String method, String instructions) {
    var takeMin = "0";
    var takeMax = "0";
    var instructionArray = instructions.toUpperCase().split(" ");
    // Check for amount
    // If method was specified, then amount must follow afterwards
    // If not, then amount must be first
    if (!method.isEmpty()) {
      if (StringUtils.isNumeric(instructionArray[1])) {
        if (instructionArray[2].equalsIgnoreCase("to")) {
          takeMin = instructionArray[1];
          takeMax = instructionArray[3];
        } else {
          takeMin = instructionArray[1];
          takeMax = instructionArray[1];
        }
      } else if (inArrayIgnoreCase(instructionArray[1], NUMBER_WORDS)) {
        takeMin = convertNumberWordToNumber(instructionArray[1]);
        takeMax = convertNumberWordToNumber(instructionArray[1]);
        if (instructionArray[2].equalsIgnoreCase("to")) {
          takeMax = convertNumberWordToNumber(instructionArray[3]);
        }
      }
    } else {
      if (StringUtils.isNumeric(instructionArray[0])) {
        if (instructionArray[1].equalsIgnoreCase("to")
            || instructionArray[1].equals("-")) {
          takeMin = instructionArray[0];
          takeMax = instructionArray[2];
        } else {
          takeMin = instructionArray[0];
          takeMax = instructionArray[0];
        }
      } else if (inArrayIgnoreCase(instructionArray[0], NUMBER_WORDS)) {
        takeMin = convertNumberWordToNumber(instructionArray[0]);
        takeMax = convertNumberWordToNumber(instructionArray[0]);
        if (instructionArray[1].equalsIgnoreCase("to")
            || instructionArray[1].equals("-")) {
          takeMax = convertNumberWordToNumber(instructionArray[2]);
        }
      }
    }
    //Check for single number amounts
    for (var i = 0; i < instructionArray.length; i++) {
      val freqCodeInArray = inArrayIgnoreCase(instructionArray[i], FREQ_CODE);
      val drugFormInArray = inArrayIgnoreCase(instructionArray[i], DRUG_FORMS);
      if (i != 0 && freqCodeInArray || drugFormInArray) {
        takeMin = convertIfNumberWord(instructionArray[i-1]);
        takeMax = convertIfNumberWord(instructionArray[i-1]);
        break;
      }
    }

    // Check for ranges and fractional instructions using digits or number words (no spaces)
    for (var word : instructionArray) {
      if (word.length() > 2) {
        //Make sure that the word is greater than length 2 since it could match with "-" or "TO" itself
        if (word.contains("-") && !word.endsWith("-")) {
          val values = word.split("-");
          takeMin = convertIfNumberWord(StringUtils.trim(values[0]));
          takeMax = convertIfNumberWord(StringUtils.trim(values[1]));
          break;
        } else if (word.contains("TO") && !word.equals("TOP.") && !word.equals("(TO") && !word.endsWith("TO")) {
          val values = word.split("TO");
          takeMin = convertIfNumberWord(StringUtils.trim(values[0]));
          takeMax = convertIfNumberWord(StringUtils.trim(values[1]));
          break;
        } else if (word.contains("/") && !word.endsWith("/") && isValidFraction(word)) {
          val num = word.split("/");
          takeMin = String.valueOf(Double.parseDouble(num[0]) / Double.parseDouble(num[1]));
          takeMax = String.valueOf(Double.parseDouble(num[0]) / Double.parseDouble(num[1]));
          break;
        }
      }
    }

    //Check for ranges using digits or number words (with spaces)
    for (var i = 1; i < instructionArray.length; i++) {
      if (instructionArray[i].equals("TO") || instructionArray[i].equals("-")) {
        takeMin = convertIfNumberWord(instructionArray[i-1]);
        takeMax = convertIfNumberWord(instructionArray[i+1]);
        break;
      }
    }

    //Swap the values if takeMax is less than takeMin
    if (Double.parseDouble(takeMax) < Double.parseDouble(takeMin)) {
      val temp = takeMax;
      takeMax = takeMin;
      takeMin = temp;
    }

    return new String[]{takeMin, takeMax};
  }

  /**
   * Validates if a given string represents a valid fraction.
   */
  private static boolean isValidFraction(final String word) {
    if (StringUtils.isBlank(word)) {
      return false;
    }
    val parts = word.split("/");
    if (parts.length != EXPECTED_FRACTION_PARTS_LENGTH) {
      return false;
    }

    if (!NumberUtils.isParsable(parts[0]) || !NumberUtils.isParsable(parts[1])) {
      return false;
    }

    try {
      return Optional.of(parts)
          .map(RxInstructionParser::extractIntegers)
          .filter(RxInstructionParser::isValidParts)
          .isPresent();
    } catch (NumberFormatException e) {
      return false;
    }
  }

  /** Extracts integers from an array of string parts. */
  private static int[] extractIntegers(final String[] parts) {
    return Stream.of(parts).mapToInt(Integer::parseInt).toArray();
  }

  /** Checks if the extracted integers represent a valid fraction. */
  private static boolean isValidParts(final int[] parts) {
    return parts.length == EXPECTED_FRACTION_PARTS_LENGTH && parts[1] != 0 && parts[0] < parts[1];
  }

  /**
   * This function calculates the total quantity of drugs to fulfill the instructions in the
   * prescription. This is calculated using the formula:
   * [total quantity] = takeMax * frequency * duration (in days)
   *
   * @param duration The duration amount
   * @param durationUnit The duration unit
   * @param frequency The frequency of application
   * @param takeMax The maximum amount of drugs to take per frequency period
   * @return The total quantity of drugs to fulfill the instructions in the prescription
   */
  public static double calculateTotalQuantity(
      final String duration,
      final String durationUnit,
      final String frequency,
      final String takeMax
  ) {
    val frequencyTime = convertFrequencyToDays(frequency);
    val durationTime = convertDurationToDays(duration, durationUnit);
    if (takeMaxIsValidNumber(takeMax)) {
      return Double.parseDouble(takeMax) * frequencyTime * durationTime;
    }
    return frequencyTime * durationTime;
  }

  private static boolean takeMaxIsValidNumber(final String takeMax) {
    return !takeMax.isEmpty() && NumberUtils.isParsable(takeMax) && !"0".equals(takeMax);
  }

  /**
   * This function returns a String where if an item from the given array is found in the
   * instructions, then return that item in the context of the instructions. Otherwise, it returns
   * a blank String.
   * @param instructions The drug instructions
   * @param array The item array to search through
   * @return The substring context where the item was found
   */
  private static String checkFor(String instructions, String[] array) {
    val instructionArray = instructions.split(" ");
    for (var word : instructionArray) {
      if (inArrayIgnoreCase(word, array)) {
        return word;
      }
    }
    return "";
  }

  private static String checkForNonStandardFrequency(final String instructions) {
    // Check for non-standard frequencies (multiple words)
    val instructionArray = instructions.split(" ");
    for (var i = 0; i < instructionArray.length - 1; i++) {
      val phrase = StringUtils.trimToEmpty(instructionArray[i] + " " + instructionArray[i+1]);
      if (inArrayIgnoreCase(phrase, FREQUENCIES)) {
        return phrase;
      }
    }
    // Check for non-standard frequencies (single words)
    return checkFor(instructions, FREQUENCIES);
  }

  /**
   * This function is a helper function to check if a string present in an array.
   *
   * @param str The search string
   * @param array The array to be searched
   * @return If the search string is in the array to be searched
   */
  private static boolean inArrayIgnoreCase(String str, String[] array) {
    return Arrays.stream(array).anyMatch(str::equalsIgnoreCase);
  }
  /**
   * This function returns a String that converts the unit input into a standardized duration unit.
   *
   * @param unit the String duration unit to be converted
   * @return the String representation of the standardized duration unit
   */
  private static String convertToStandardDurationUnit(String unit) {
    var converted = "";
    if (inArrayIgnoreCase(unit, DURATION_UNITS_DAY)) {
      converted = "D";
    } else if (inArrayIgnoreCase(unit, DURATION_UNITS_WEEK)) {
      converted = "W";
    } else if (inArrayIgnoreCase(unit, DURATION_UNITS_MONTH)) {
      converted = "M";
    } else if (inArrayIgnoreCase(unit, DURATION_UNITS_YEAR)) {
      converted = "Y";
    }
    return converted;
  }

  /**
   * This function returns a double representation of amount frequency represented in days.
   *
   * @param frequency The frequency string found in the instructions
   * @return A double representation of the frequency string (in days)
   */
  private static double convertFrequencyToDays(String frequency) {
    var converted = 1.0;
    switch (frequency.toUpperCase()) {
      case "OD":
      case "1D":
      case "QAM":
      case "QPM":
      case "QHS":
      case "HS":
      case "NOON":
      case "QLUNCH":
      case "QDINNER":
      case "Q24H":
        converted = 1.0;
        break;
      case "QAM&PM":
      case "QAM&HS":
      case "BID":
      case "2D":
      case "QD-BID":
      case "Q12H":
        converted = 2.0;
        break;
      case "TID":
      case "3D":
      case "BID-TID":
      case "8H":
        converted = 3.0;
        break;
      case "QID":
      case "4D":
      case "BID-QID":
      case "TID-QID":
      case "Q6H":
      case "Q6-8H":
        converted = 4.0;
        break;
      case "5D":
        converted = 5.0;
        break;
      case "6D":
      case "Q4H":
      case "Q4-6H":
        converted = 6.0;
        break;
      case "Q3-4H":
        converted = 8.0;
        break;
      case "Q2H":
        converted = 12.0;
        break;
      case "QH":
      case "Q1H":
      case "Q1-2H":
        converted = 24.0;
        break;
      case "Q5MIN":
        converted = 288.0;
        break;
      case "Q2D":
      case "QOTHERD":
      case "Q2NDD":
        converted = 1.0/2.0;
        break;
      case "Q72H":
      case "Q3D":
      case "Q3RDD":
        converted = 1.0/3.0;
        break;
      case "Q1W":
      case "Q1TW":
      case "QW":
      case "Q1WEEK":
        converted = 1.0/7.0;
        break;
      case "Q2W":
      case "Q2WEEK":
        converted = 1.0/14.0;
        break;
      case "Q3W":
        converted = 1.0/21.0;
        break;
      case "Q4W":
        converted = 1.0/28.0;
        break;
      case "Q5W":
        converted = 1.0/35.0;
        break;
      case "Q6W":
        converted = 1.0/42.0;
        break;
      case "Q7W":
        converted = 1.0/49.0;
        break;
      case "Q8W":
        converted = 1.0/56.0;
        break;
      case "Q9W":
        converted = 1.0/63.0;
        break;
      case "Q10W":
        converted = 1.0/70.0;
        break;
      case "Q11W":
        converted = 1.0/77.0;
        break;
      case "Q12W":
        converted = 1.0/84.0;
        break;
      case "Q1M":
      case "QM":
      case "Q1MONTH":
        converted = 1.0/30.0;
        break;
      case "Q2M":
      case "Q2MONTH":
        converted = 1.0/60.0;
        break;
      case "Q3M":
      case "Q3MONTH":
        converted = 1.0/90.0;
        break;
      case "2TW":
      case "2TPW":
        converted = 2.0/7.0;
        break;
      case "3TW":
      case "3TPW":
      case "QTTF":
        converted = 3.0/7.0;
        break;
      case "QMWSS":
        converted = 4.0/7.0;
        break;
      default:
        break;
    }
    return converted;
  }

  /**
   * This function converts both the duration period and duration unit to a set amount of days.
   *
   * @param duration The duration period to calculate the number of days
   * @param unit The duration unit to be converted into days
   * @return The total duration given in days
   */
  private static double convertDurationToDays(String duration, String unit) {
    var durationDouble = 0.0;
    if (!duration.isEmpty() && StringUtils.isNumeric(duration)) {
      durationDouble = Double.parseDouble(duration);
    }
    var unitDouble = 0.0;
    switch (unit) {
      case "D":
        unitDouble = 1.0;
        break;
      case "W":
        unitDouble = 7.0;
        break;
      case "M":
        unitDouble = 30.0;
        break;
      case "Y":
        unitDouble = 365.0;
        break;
      default:
        break;
    }
    return durationDouble * unitDouble;
  }

  /**
   * This function converts non-standard frequencies (e.g. "daily", "weekly") into standard
   * frequencies (e.g. "OD", "Q1W").
   *
   * @param frequency The non-standard frequency to be converted
   * @return The converted frequency
   */
  private static String convertToStandardFrequency(String frequency) {
    var stdFreq = "";
    switch (frequency.toUpperCase()) {
      case "DAILY":
      case "ONCE DAILY":
      case "1X DAY":
      case "1X DAILY":
        stdFreq = "OD";
        break;
      case "TWICE DAILY":
      case "2X DAILY":
      case "2X DAY":
        stdFreq = "BID";
        break;
      case "3X DAILY":
      case "3X DAY":
        stdFreq = "TID";
        break;
      case "4X DAILY":
      case "4X DAY":
        stdFreq = "QID";
        break;
      case "WEEKLY":
      case "1X WEEK":
        stdFreq = "Q1W";
        break;
      case "MONTHLY":
      case "1X MONTH":
        stdFreq = "Q1M";
      default:
        break;
    }
    return stdFreq;
  }

  /**
   * This function creates a map where the frequency code and definition
   * (given by the example sheet) are paired together.
   *
   * @return A hash map containing frequency code / definition pairs
   */
  private static Map<String, String> createFrequencyDefinitionMap() {
    val fDMap = new HashMap<String, String>();
    for (var i = 0; i < FREQ_CODE.length; i++) {
      fDMap.put(FREQ_CODE[i], DEFINITIONS[i]);
    }
    return fDMap;
  }
  /**
   * This function converts frequency code into its respective definition (according to the
   * frequency code example sheet).
   *
   * @param freqCode The frequency code to be converted
   * @return The definition of the input frequency code
   */
  private static String convertFrequencyToDefinition(String freqCode) {
    if (!FREQUENCY_DEFINITION_MAP.containsKey(freqCode)) {
      return "";
    }
    return FREQUENCY_DEFINITION_MAP.get(freqCode.toUpperCase());
  }

  /**
   * This function creates a map where the number word
   * and respective number are paired together.
   *
   * @return A hash map containing number word / number pairs
   */
  private static Map<String, String> createNumberWordToNumberMap() {
    val numWordToNumMap = new HashMap<String, String>();
    for (var i = 0; i < NUMBER_WORDS.length; i++) {
      numWordToNumMap.put(NUMBER_WORDS[i], String.valueOf(i+1));
    }
    return numWordToNumMap;
  }

  /**
   * This function converts a number word to a number (e.g. "one" -> "1")
   *
   * @param numberWord The number word to be converted into a number.
   * @return The number conversion
   */
  private static String convertNumberWordToNumber(String numberWord) {
    if (!NUMBERWORD_TO_NUMBER_MAP.containsKey(numberWord)) {
      return "1";
    }
    return NUMBERWORD_TO_NUMBER_MAP.get(numberWord.toUpperCase());
  }

  /**
   * This function is a helper function for convertToStandardDrugForm.
   * If the input string is numeric, then return it.
   * Otherwise, return the result of convertNumberWordToNumber.
   *
   * @param word The string to process
   * @return The original input string or the converted number word
   */
  private static String convertIfNumberWord(String word) {
    return NumberUtils.isParsable(word) ? word : convertNumberWordToNumber(word);
  }

  /**
   * This function converts the input drug form into a standard drug form.
   *
   * @param drugForm The input drug form
   * @return The standard drug form of the input drug form
   */
  private static String convertToStandardDrugForm(String drugForm) {
    var standardForm = drugForm;
    switch (drugForm.toUpperCase()) {
      case "TAB":
      case "TABS":
      case "TABLET":
      case "TABLETS":
        standardForm = "tablet(s)";
        break;
      case "CAP":
      case "CAPS":
      case "CAPSULE":
      case "CAPSULES":
        standardForm = "capsule(s)";
        break;
      default:
        break;
    }
    return standardForm;
  }

  /**
   * This function checks if the instruction string can be parsed. At a minimum, the frequency,
   * drug form, amount, and duration fields should be present in the instructions.
   * If the text instructions have these fields, and match with the check string, return true.
   * Otherwise, return false.
   *
   * @param instruction The input instructions to check
   * @return A boolean representing if the instructions are parsable
   */
  public static boolean isParsable(DrugInstruction instruction) {
    val instructionText = instruction.getText();
    if (instructionText == null || instructionText.isEmpty()) {
      return false;
    }

    val method = checkForMethod(instructionText);
    val frequencyCheck = checkForFrequency(null, instructionText);
    val drugFormCheck = checkFor(instructionText, DRUG_FORMS);
    val amountCheck = checkForAmount(method, instructionText);
    val durationCheck = checkForDurationAndDurationUnit(instructionText);
    val routeCheck = checkForRoute(instructionText);
    val siteCheck = checkForSite(instructionText);

    var instructionCheck = new StringBuilder();

    //Frequency, drug form, amount, and duration are the minimum fields required for parsing
    //Method, site, and route are optional fields
    instructionCheck.append(method).append("\\s*");

    if (!amountCheck[0].isEmpty() && !amountCheck[1].isEmpty()) {
      if (!amountCheck[0].equals(amountCheck[1])) {
        instructionCheck
            .append(amountCheck[0]).append("\\s*(-|to)\\s*")
            .append(amountCheck[1]).append("\\s*");
      } else {
        instructionCheck.append(amountCheck[0]).append("\\s*");
      }
    } else {
      return false;
    }

    if (!drugFormCheck.isEmpty()) {
      instructionCheck.append(drugFormCheck).append("\\s*");
    } else {
      return false;
    }

    if (!frequencyCheck.isEmpty()) {
      instructionCheck.append(frequencyCheck).append("\\s*");
    } else {
      return false;
    }

    if (!durationCheck[0].isEmpty() && !durationCheck[1].isEmpty()) {
      if (StringUtils.containsIgnoreCase(instructionText, "FOR")) {
        instructionCheck
            .append("for").append("\\s*")
            .append(durationCheck[0]).append("\\s*")
            .append(durationCheck[1]).append("\\s*");
      } else {
        instructionCheck
            .append(durationCheck[0]).append("\\s*")
            .append(durationCheck[1]).append("\\s*");
      }
    } else {
      return false;
    }

    if (!siteCheck.isEmpty()) {
      instructionCheck.append("SITE:").append("\\s*").append(siteCheck);
    }

    if (!routeCheck.isEmpty()) {
      instructionCheck = new StringBuilder(
          instructionCheck
              .toString()
              .replace("\\s*", "\\s*(" + routeCheck + ")?\\s*")
      );
    }
    return instructionText.toLowerCase().matches(instructionCheck.toString().toLowerCase());
  }

  /**
   * This function changes the instruction text found in the DrugInstruction object. This function
   * changes the frequency, drug form, and number words into their standard forms or definitions.
   *
   * @param d The input drug
   * @param drugInstruction The input drug instruction
   * @return The changed string
   */
  public String modifyInstructionText(Drug d, DrugInstruction drugInstruction) {
    if (d == null || drugInstruction == null) {
      return "";
    } else if (drugInstruction.getText().isEmpty()) {
      return buildInstructionsFromDiscreteElements(d);
    }
    var newInstructionText = drugInstruction.getText();

    var frequency = "";
    var newFrequency = "";

    //Check if instruction text contains the frequency code
    for (var freqCode : FREQ_CODE) {
      if (StringUtils.containsIgnoreCase(newInstructionText, freqCode)) {
        frequency = freqCode;
        newFrequency = convertFrequencyToDefinition(freqCode);
      }
    }

    //Check if instruction text contains any frequency strings
    for (var freq : FREQUENCIES) {
      if (StringUtils.containsIgnoreCase(newInstructionText, freq)) {
        frequency = freq;
        newFrequency = convertFrequencyToDefinition(convertToStandardFrequency(frequency));
        break;
      }
    }

    val frequencyRegex = "(?i)" + frequency;
    newInstructionText = newInstructionText.replaceAll(frequencyRegex, newFrequency);

    //Replace drug form with standard form
    var drugForm = checkForDrugForm(d, newInstructionText);
    var newDrugForm = convertToStandardDrugForm(drugForm);
    val drugFormRegex = "(?i)" + drugForm;
    newInstructionText = newInstructionText.replaceAll(drugFormRegex, newDrugForm);

    var numberWord = "";
    var newNumber = "";
    //Replace any instances of number words
    for (var word : NUMBER_WORDS) {
      if (StringUtils.containsIgnoreCase(newInstructionText, word)) {
        numberWord = word;
        newNumber = convertNumberWordToNumber(word);
        var numberRegex = "(?i)" + numberWord;
        newInstructionText = newInstructionText.replaceAll(numberRegex, newNumber);
      }
    }
    return newInstructionText;
  }
}