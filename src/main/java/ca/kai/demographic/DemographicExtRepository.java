package ca.kai.demographic;

import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface DemographicExtRepository extends
    CrudRepository<DemographicExt, Long>,
    DemographicExtRepositoryCustom {

  List<DemographicExt> getAllByDemographicNumber(Integer demographicNumber);

  List<DemographicExt> getAllByDemographicNumberIn(
      List<Integer> demographicNumbers
  );

  List<DemographicExt> getAllByDemographicNumberAndKey(Integer demographicNumber, String key);

  @Query(value = "SELECT e FROM DemographicExt e WHERE e.demographicNumber = ?1 AND e.key IN (?2)")
  List<DemographicExt> getAllByDemographicNumberAndKeyIn(
      Integer demographicNumber,
      List<String> keys
  );

  List<DemographicExt> getAllByKey(String key);

  List<DemographicExt> getAllByKeyAndProviderNoAndHiddenNot(
      String key,
      String providerNo,
      char hiddenNot
  );

  DemographicExt getByDemographicNumberAndKey(Integer demographicNumber, String keyVal);

  List<DemographicExt> getAllByKeyAndDemographicNumberIn(
      String key,
      List<Integer> demographicNumbers
  );

  DemographicExt getByKeyAndDemographicNumberAndProviderNoAndHiddenNot(
      String key,
      Integer demographicNumber,
      String providerNo,
      char hiddenNot
  );

  DemographicExt getFirstByKeyAndDemographicNumberOrderByDateTimeDesc(
      String key,
      Integer demographicNumber
  );

  DemographicExt getFirstByKeyAndDemographicNumberOrderByDateTime(
      String key,
      Integer demographicNumber
  );
}
