package ca.kai.demographic;

import ca.kai.provider.Provider;
import java.io.Serializable;
import java.util.Date;
import java.util.Optional;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@NoArgsConstructor
@Table(name = "demographicArchive")
public class DemographicArchive implements Serializable, PatientDemographicData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	@Getter @Setter private Integer id;

	@Column(name = "demographic_no")
	@Getter @Setter private Integer demographicNumber;

	@Column(name = "title")
	@Getter @Setter private String title;

	@Column(name = "last_name")
	@Getter @Setter private String lastName;

	@Column(name = "first_name")
	@Getter @Setter  private String firstName;

	@Column(name = "address")
	@Getter @Setter private String address;

	@Column(name = "city")
	@Getter @Setter private String city;

	@Column(name = "province")
	@Getter @Setter private String province;

	@Column(name = "postal")
	@Getter @Setter private String postal;

	@Column(name = "phone")
	@Getter @Setter private String phone;

	@Column(name = "phone2")
	@Getter @Setter private String alternativePhone;

	@Column(name = "email")
	@Getter @Setter private String email;

	@Getter @Setter private String myOscarUserName;

	@Column(name = "year_of_birth")
	@Getter @Setter private String yearOfBirth;

	@Column(name = "month_of_birth")
	@Getter @Setter private String monthOfBirth;

	@Column(name = "date_of_birth")
	@Getter @Setter private String dateOfBirth;

	@Column(name = "hin")
	@Getter @Setter private String hin;

	@Column(name = "ver")
	@Getter @Setter private String ver;

	@Column(name = "roster_status")
	@Getter @Setter private String rosterStatus;

	@Column(name = "roster_date")
	@Temporal(TemporalType.DATE)
	@Getter @Setter private Date rosterDate;

	@Column(name = "roster_termination_date")
	@Temporal(TemporalType.DATE)
	@Getter @Setter private Date rosterTerminationDate;

	@Column(name = "roster_termination_reason")
	@Getter @Setter private String rosterTerminationReason;

	@Column(name = "patient_status")
	@Getter @Setter private String patientStatus;

	@Column(name = "patient_status_date")
	@Temporal(TemporalType.DATE)
	@Getter @Setter private Date patientStatusDate;

	@Column(name = "date_joined")
	@Temporal(TemporalType.DATE)
	@Getter @Setter private Date dateJoined;

	@Column(name = "chart_no")
	@Getter @Setter private String chartNo;

	@Column(name = "official_lang")
	@Getter @Setter private String officialLanguage;

	@Column(name = "spoken_lang")
	@Getter @Setter private String spokenLanguage;

	@Column(name = "provider_no")
	@Getter @Setter private String providerNo;

	@Column(name = "sex")
	@Getter @Setter private String sex;

	@Column(name = "end_date")
	@Temporal(TemporalType.DATE)
	@Getter @Setter private Date endDate;

	@Column(name = "eff_date")
	@Temporal(TemporalType.DATE)
	@Getter @Setter private Date effDate;

	@Column(name = "pcn_indicator")
	@Getter @Setter private String pcnIndicator;

	@Column(name = "hc_type")
	@Getter @Setter private String hcType;

	@Column(name = "hc_renew_date")
	@Temporal(TemporalType.DATE)
	@Getter @Setter Date hcRenewDate;

	@Column(name = "alias")
	@Getter @Setter private String alias;

	@Column(name = "previousAddress")
	@Getter @Setter private String previousAddress;

	@Column(name = "children")
	@Getter @Setter private String children;

	@Column(name = "sourceOfIncome")
	@Getter @Setter private String sourceOfIncome;

	@Column(name = "citizenship")
	@Getter @Setter private String citizenship;

	@Column(name = "sin")
	@Getter @Setter private String sin;

	@Column(name = "country_of_origin")
	@Getter @Setter private String countryOfOrigin;

	@Column(name = "newsletter")
	@Getter @Setter private String newsletter;

	@Column(name = "anonymous")
	@Getter @Setter private String anonymous;

	@Column(name = "lastUpdateUser")
	@Getter @Setter private String lastUpdateUser;

	@Column(name = "lastUpdateDate")
	@Temporal(TemporalType.DATE)
	@Getter @Setter private Date lastUpdateDate;
	@Getter @Setter private String gender;
	@Getter @Setter private Integer genderId;
	@Getter @Setter private String pronoun;
	@Getter @Setter private Integer pronounId;
	@Getter @Setter private String preferredName;
	@Getter @Setter private String patientType;
	@Getter @Setter private String patientId;
	@Getter @Setter private String portalUserId;
	@Getter @Setter private Boolean consentToUseEmailForCare;
	@Getter @Setter private Boolean consentToUseEmailForEOrder;
	@Getter @Setter private Integer primarySystemId;
	@Getter @Setter private String guid;

	public DemographicArchive(Demographic demographic) {
		this.address = demographic.getAddress();
		this.alias = demographic.getAlias();
		this.anonymous = demographic.getAnonymous();
		this.chartNo = demographic.getChartNo();
		this.children = demographic.getChildren();
		this.citizenship = demographic.getCitizenship();
		this.city = demographic.getCity();
		this.countryOfOrigin = demographic.getCountryOfOrigin();
		this.dateJoined = demographic.getDateJoined();
		this.dateOfBirth = demographic.getDateOfBirth();
		this.demographicNumber = demographic.getDemographicNumber();
		this.effDate = demographic.getEffDate();
		this.email = demographic.getEmail();
		this.endDate = demographic.getEndDate();
		this.firstName = demographic.getFirstName();
		this.hcRenewDate = demographic.getHcRenewDate();
		this.hcType = demographic.getHcType();
		this.hin = demographic.getHin();
		this.lastName = demographic.getLastName();
		this.lastUpdateDate = new Date();
		this.lastUpdateUser = demographic.getLastUpdateUser();
		this.monthOfBirth = demographic.getMonthOfBirth();
		this.myOscarUserName = demographic.getMyOscarUserName();
		this.newsletter = demographic.getNewsletter();
		this.officialLanguage = demographic.getOfficialLanguage();
		this.patientStatus = demographic.getPatientStatus();
		this.patientStatusDate = demographic.getPatientStatusDate();
		this.pcnIndicator = demographic.getPcnIndicator();
		this.phone = demographic.getPhone();
		this.alternativePhone = demographic.getAlternativePhone();
		this.postal = demographic.getPostal();
		this.previousAddress = demographic.getPreviousAddress();
		this.providerNo = getProviderNumberForDemographic(demographic);
		this.province = demographic.getProvince();
		this.rosterDate = demographic.getRosterDate();
		this.rosterStatus = demographic.getRosterStatus();
		this.rosterTerminationDate = demographic.getRosterTerminationDate();
		this.rosterTerminationReason = demographic.getRosterTerminationReason();
		this.sex = demographic.getSex();
		this.sin = demographic.getSin();
		this.sourceOfIncome = demographic.getSourceOfIncome();
		this.spokenLanguage = demographic.getSpokenLanguage();
		this.title = demographic.getTitle();
		this.ver = demographic.getVer();
		this.yearOfBirth = demographic.getYearOfBirth();
		this.gender = demographic.getGenderIdentityValue();
		this.pronoun = demographic.getPronounValue();
		this.preferredName = demographic.getPreferredName();
		this.patientType = demographic.getPatientType();
		this.patientId = demographic.getPatientId();
		this.portalUserId = demographic.getPortalUserId();
		this.consentToUseEmailForCare = demographic.getConsentToUseEmailForCare();
		this.consentToUseEmailForEOrder = demographic.getConsentToUseEmailForEOrder();
		this.primarySystemId = demographic.getPrimarySystemId();
		this.guid = demographic.getGuid();
		this.genderId = demographic.getGenderId();
		this.pronounId = demographic.getPronounId();
	}

	private String getProviderNumberForDemographic(Demographic demographic) {
		return Optional.ofNullable(demographic)
				.map(Demographic::getProvider)
				.map(Provider::getProviderNo)
				.orElse(null);
	}
}
