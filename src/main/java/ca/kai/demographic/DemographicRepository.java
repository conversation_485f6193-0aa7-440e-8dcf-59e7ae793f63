package ca.kai.demographic;

import java.util.List;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

public interface DemographicRepository extends
    CrudRepository<Demographic, Integer>,
    JpaSpecificationExecutor<Demographic>,
    DemographicRepositoryCustom {

  @Query("SELECT d FROM Demographic d WHERE d.patientStatus <> 'MERGED' ORDER BY d.lastName")
  List<Demographic> getAllOrderByLastName();

  Demographic getByDemographicNumber(Integer demographicNumber);

  Demographic getByGuid(final String guid);

  @Query(value = "SELECT d.* FROM demographic d " +
      "WHERE d.patient_status = 'AC' " +
      "AND d.patient_status != 'MERGED' " +
      "AND d.first_name LIKE ?1 '%' " +
      "ORDER BY d.last_name, d.first_name",
      nativeQuery = true)
  List<Demographic> getByFirstNameStartingWith(String firstName);

  List<Demographic> getByEmail(String email);

  @Query(value = "SELECT d.* FROM demographic d " +
      "WHERE d.email = :email " +
      "AND d.patient_status != 'MERGED' " +
      "AND d.year_of_birth = :birthYear " +
      "AND d.month_of_birth = :birthMonth " +
      "AND d.date_of_birth = :birthDay ",
      nativeQuery = true)
  Demographic getByEmailAndDOB(@Param("email") String email, @Param("birthYear") String year,
      @Param("birthMonth") String month, @Param("birthDay") String day);

  Demographic getByHin(String hin);

  List<Demographic> getByHinIn(List<String> hinList);

  @Query(value = "SELECT d.* FROM demographic d " +
      "WHERE d.hin = :hin " +
      "AND d.patient_status != 'MERGED' " +
      "AND d.year_of_birth = :birthYear " +
      "AND d.month_of_birth = :birthMonth " +
      "AND d.date_of_birth = :birthDay ",
      nativeQuery = true)
  Demographic getByHINAndDOB(@Param("hin") String hin, @Param("birthYear") String year,
      @Param("birthMonth") String month, @Param("birthDay") String day);

  @Query(value = "SELECT d.* FROM demographic d " +
      "WHERE d.patient_status = 'AC' " +
      "AND d.patient_status != 'MERGED' " +
      "AND d.last_name LIKE ?1 '%' " +
      "ORDER BY d.last_name, d.first_name ",
      nativeQuery = true)
  List<Demographic> getByLastNameStartingWith(String lastName);

  Demographic getByPortalUserId(String portalUserId);

  List<Demographic> getByPortalUserIdNotNull();

  @Query(value = "SELECT d.* FROM demographic d "
      + "LEFT JOIN DataSharingSettings s ON d.demographic_no = s.demographicNo "
      + "WHERE s.demographicNo IS NULL",
      nativeQuery = true)
  List<Demographic> getDemographicsUsingOrganizationDataSharingSettings();

  Demographic getFirstByHinAndDateOfBirthAndMonthOfBirthAndYearOfBirth(
      String hin,
      String day,
      String month,
      String year
  );

  List<Demographic> findByHinAndHcType(String hin, String hcType);

  Demographic getFirstByHinAndHcType(String hin, String hcType);

  @Query(value = "SELECT * FROM demographic "
      + "WHERE demographic_no IN ("
      + "SELECT DISTINCT oldDemographic FROM DemographicMergeOperation dmo "
      + "RIGHT JOIN demographic d ON dmo.oldDemographic = d.demographic_no "
      + "WHERE dmo.mainDemographic = ?1 AND d.patient_status = 'MERGED')", nativeQuery = true)
  List<Demographic> getReplacedDemographics(Integer demographicNo);

  @Query(value = "SELECT * FROM demographic "
      + "WHERE demographic_no IN ("
      + "SELECT DISTINCT mainDemographic FROM DemographicMergeOperation dmo "
      + "RIGHT JOIN demographic d ON dmo.oldDemographic = d.demographic_no "
      + "WHERE dmo.oldDemographic = ?1 AND d.patient_status = 'MERGED')", nativeQuery = true)
  List<Demographic> getReplacedByDemographics(Integer demographicNo);

  @Query(value = "SELECT DISTINCT d.* FROM demographic d "
      + "LEFT JOIN relationships r ON r.relation_demographic_no = d.demographic_no "
      + "WHERE r.demographic_no = ?1 AND r.deleted = '0'",
      nativeQuery = true)
  List<Demographic> getActiveRelationships(Integer demographicNo);

  @Query(value = "SELECT DISTINCT d.* FROM demographic d "
      + "LEFT JOIN DemographicContact c ON c.contactId = d.demographic_no "
      + "WHERE c.demographicNo = ?1 AND c.deleted = false",
      nativeQuery = true)
  List<Demographic> getActiveDemographicContacts(Integer demographicNo);
}