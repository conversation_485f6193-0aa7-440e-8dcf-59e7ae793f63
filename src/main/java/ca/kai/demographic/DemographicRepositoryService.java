package ca.kai.demographic;

import ca.kai.fhir.subscription.FhirSubscriptionManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.PersistenceException;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@AllArgsConstructor
@Service
@Slf4j
@Transactional
public class DemographicRepositoryService {

  public static final String FHIR_RESOURCE_PATIENT = "Patient";
  @Autowired private DemographicRepository demographicRepository;
  @Autowired private DemographicArchiveRepository demographicArchiveRepository;
  @Autowired private DemographicExtRepository demographicExtRepository;
  @Autowired private DemographicExtArchiveRepository demographicExtArchiveRepository;
  @Autowired private FhirSubscriptionManager fhirSubscriptionManager;
  @PersistenceContext private EntityManager entityManager;

  @PostConstruct
  private void initialize() {
    // set circular bean dependencies
    demographicRepository.setDemographicRepositoryService(this);
    demographicExtRepository.setDemographicRepositoryService(this);
    demographicArchiveRepository.setDemographicRepositoryService(this);
    demographicExtArchiveRepository.setDemographicRepositoryService(this);
  }

  public void queueDemographicSubscription(@NonNull final String id) {
    fhirSubscriptionManager.checkResourceSubscriptionSafe(FHIR_RESOURCE_PATIENT, id);
  }

  /**
   * Archive: Archives the Demographic and DemographicExts for the given demographic.
   * @param demographic the new demographic to archive in the database.
   */
  public void archiveDemographicAndExtensions(@NonNull final Demographic demographic) {
    archiveDemographicsAndExtensions(Collections.singletonList(demographic));
  }

  /**
   * Archive: Archives the Demographics and DemographicExts for the given list of demographics.
   * @param demographics the list of demographics to archive in the database.
   */
  public void archiveDemographicsAndExtensions(@NonNull final List<Demographic> demographics) {
    val demographicArchives = new HashMap<Integer, DemographicArchive>();
    val demographicExtArchives = new ArrayList<DemographicExtArchive>();

    val extensions = demographicExtRepository.getAllByDemographicNumberIn(
        demographics.stream()
            .map(Demographic::getDemographicNumber)
            .collect(Collectors.toList())
    );
    val extensionsByDemographicNumber = extensions.stream()
        .collect(Collectors.groupingBy(DemographicExt::getDemographicNumber));

    for (Demographic demographic : demographics) {
      val archive = demographic.getArchiveRecord();
      demographicArchives.put(demographic.getDemographicNumber(), archive);
    }
    demographicArchiveRepository.save(demographicArchives.values());

    for (Demographic demographic : demographics) {
      val archive = demographicArchives.get(demographic.getDemographicNumber());
      val demographicExtensions = extensionsByDemographicNumber.getOrDefault(
          demographic.getDemographicNumber(), Collections.emptyList());
      demographicExtArchives.addAll(demographicExtensions.stream()
          .map(ext -> new DemographicExtArchive(ext, archive.getId()))
          .collect(Collectors.toList()));
      deleteDuplicateExtensions(demographicExtensions);
    }

    demographicExtArchiveRepository.save(demographicExtArchives);
  }

  /**
   * Get a list of DemographicExt of a specific DemographicKey type for a given demographic number.
   * @param demographicNumber the demographic ID
   * @param key               the DemographicExtKey type
   * @return all ext values represented as a map
   */
  public List<DemographicExt> getDemographicExtensionsByDemographicNumberAndKeyValue(
      @NonNull final Integer demographicNumber,
      @NonNull final DemographicExtKey key
  ) {
    return Optional.ofNullable(demographicExtRepository.getAllByDemographicNumberAndKey(
        demographicNumber,
        key.getKey()
    )).orElse(Collections.emptyList());
  }

  public void saveDemographics(@NonNull final List<Demographic> demographics) {
    saveDemographics(demographics, false);
  }

  /**
   * Save & Archive: Gets, saves and archives the Demographic and DemographicExts for the given
   * demographic number.
   * @param demographics the list of demographics to save in the database.
   * @param skipArchiving if true, skips the archiving functionality.
   */
  public List<Demographic> saveDemographics(@NonNull final List<Demographic> demographics,
      final boolean skipArchiving) {

    demographicRepository.save(demographics);
    if (!skipArchiving) {
      archiveDemographicsAndExtensions(demographics);
    }

    for (Demographic demographic : demographics) {
      saveExtensions(demographic);
      queueDemographicSubscription(demographic.getDemographicNumber().toString());
    }
    return demographics;

  }

  /**
   * Save & Archive: Gets, saves and archives the Demographic and DemographicExts for the given
   * demographic number.
   * @param demographic the demographic to save in the database.
   */
  public Demographic saveDemographic(@NonNull final Demographic demographic) {
    return saveDemographic(demographic, false);
  }

  /**
   * Save Demographic & Extensions with Optional Archive:
   * Some use cases require a demographic to be persisted for the demographicNo ID to be generated
   * before using the newly generated ID to populate DemographicExts. In this case we are actively
   * choosing not to archive the data because the Demographic and DemographicExts will be persisted
   * at a future time in our call path.
   * <p>
   * Saves and archives the Demographic and DemographicExts for the given demographic number.
   * @param demographic the new demographic to save in the database.
   */
  public Demographic saveDemographic(
      @NonNull final Demographic demographic,
      final boolean skipArchiving
  ) {
    return saveDemographics(Collections.singletonList(demographic), skipArchiving).get(0);
  }

  /**
   * Save & Optional Archive: Some use cases require a demographic to be persisted for the
   * demographicNo ID to be generated before using the newly generated ID to populate
   * DemographicExts. In this case we are actively choosing not to archive the data because the
   * Demographic and DemographicExts will be persisted at a future time in our call path.
   * <p>
   * Saves and archives the Demographic and DemographicExts for the given demographic number.
   *
   * @param extension     the demographicExt to save
   * @param skipArchiving skip the archive functionality
   */
  public DemographicExt saveDemographicExtension(
      @NonNull final Demographic demographic,
      @NonNull final DemographicExt extension,
      final boolean skipArchiving
  ) {
    try {
      save(DemographicExt.class, extension);
    } catch (PersistenceException e) {
      log.error(
          "Persistence error occurred while saving DemographicExt for demographicNo: {}, key: {}. "
              + "Duplicate entry not allowed. Error: {}",
          extension.getDemographicNumber(), extension.getKey(), e.getMessage());
      throw e;
    }
    if (!skipArchiving) {
      saveDemographic(demographic);
    }
    queueDemographicSubscription(extension.getDemographicNumber().toString());
    return extension;
  }

  /**
   * Save a DemographicExt WITHOUT archiving a copy.
   * @param extension the DemographicExt to save.
   */
  public void saveDemographicExtensionWithoutArchiving(
      @NonNull final DemographicExt extension
  ) {
    try {
      save(DemographicExt.class, extension);
    } catch (PersistenceException e) {
      log.error("Persistence error occurred while saving DemographicExt for demographicNo: {},"
              + " key: {}. Duplicate entry not allowed. Error: {}",
          extension.getDemographicNumber(), extension.getKey(), e.getMessage());
      throw e;
    }
  }

  /**
   * Save & Archive a demographic extension.
   *
   * @param extension the DemographicExt to save.
   * @return the demographicExt that was saved.
   */
  public DemographicExt saveDemographicExtension(@NonNull final DemographicExt extension) {
    return saveDemographicExtension(
        demographicRepository.getByDemographicNumber(extension.getDemographicNumber()),
        extension,
        false
    );
  }

  /* HELPERS */

  private void saveExtensions(final Demographic demographic) {
    for (DemographicExt extension : demographic.getExtensions()) {
      extension.setDemographicNumber(demographic.getDemographicNumber());
      extension.setProviderNo(demographic.getLastUpdateUser());
      try {
        save(DemographicExt.class, extension);
      } catch (PersistenceException e) {
        log.error("Persistence error occurred while saving DemographicExt for demographicNo: {}, "
                + "key: {}. Duplicate entry not allowed. Error: {}",
            extension.getDemographicNumber(), extension.getKey(), e.getMessage());
        throw e;
      }
    }
  }

  private void archiveExtensions(final List<DemographicExt> extensions, final Integer archiveId) {
    val archivedExtensions = new ArrayList<DemographicExtArchive>();
    for (DemographicExt extension : extensions) {
      archivedExtensions.add(new DemographicExtArchive(extension, archiveId));
    }
    demographicExtArchiveRepository.save(archivedExtensions);
  }

  private List<DemographicExt> collectDuplicateExtensions(final List<DemographicExt> extensions) {
    val duplicates = new ArrayList<DemographicExt>();
    val keys = new HashSet<String>();
    extensions.forEach(
        (extension) -> {
          if (keys.contains(extension.getKey())) {
            duplicates.add(extension);
          } else {
            keys.add(extension.getKey());
          }
        }
    );
    return duplicates;
  }

  private void deleteDuplicateExtensions(final List<DemographicExt> extensions) {
    val compareByKeyAndDate =
        Comparator.comparing(
            DemographicExt::getKey,
                Comparator.nullsLast(Comparator.reverseOrder())
            ).thenComparing(
                DemographicExt::getDateTime,
                Comparator.nullsLast(Comparator.reverseOrder())
            ).reversed();

    val sortedExtensions = extensions.stream()
        .sorted(compareByKeyAndDate)
        .collect(Collectors.toList());

    val duplicatesForDeletion = collectDuplicateExtensions(sortedExtensions);
    demographicExtRepository.delete(duplicatesForDeletion);
  }

  private <T> JpaEntityInformation<T, ?> getEntityInformation(Class<T> domainClass) {
    return JpaEntityInformationSupport.getEntityInformation(
        domainClass,
        entityManager
    );
  }

  private <T> T save(Class<T> domain, T entity) {
    var info = getEntityInformation(domain);
    if (info.isNew(entity)) {
      entityManager.persist(entity);
    } else {
      entityManager.merge(entity);
    }
    return entity;
  }
}
