package ca.kai.demographic;

import ca.kai.OscarProperties;
import ca.kai.diseaseregistry.diagnosticcode.DiagnosticCode;
import ca.kai.labQueue.Lab;
import ca.kai.labQueue.LabReqData;
import ca.kai.provider.Provider;
import ca.kai.rx.prescription.Prescription;
import health.apps.gateway.common.LinkData;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.PostLoad;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.annotate.JsonBackReference;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.joda.time.LocalDate;
import org.joda.time.Period;
import org.joda.time.PeriodType;

@Entity
@Table(name = "demographic")
@NoArgsConstructor
public class Demographic implements Serializable, PatientDemographicData, LinkData {
  public static class DefaultConstants {
    public static final String CACHED_LINK_DEMOGRAPHIC_STATE = "UNLINKED";
    public static final String LINK_DEMOGRAPHIC_STATE = "AVAILABLE";
    public static final String NEWSLETTER = "Unknown";
    public static final String OFFICIAL_LANGUAGE = "English";
    public static final String COUNTRY_OF_ORIGIN = "-1";
    public static final String PATIENT_STATUS = "AC";
  }
  public static Set<String> NEWS_LETTER_VALUES = Collections.unmodifiableSet(
          Stream.of("Unknown", "No", "Paper", "Electronic").collect(Collectors.toSet()));

  @Id
  @Column(name = "demographic_no")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Getter @Setter private Integer demographicNumber;

  @Column(name = "title")
  @Getter @Setter private String title = "";

  @Transient
  @Getter @Setter private String nameSuffix = "";

  @Column(name = "first_name")
  @Getter @Setter private String firstName;

  @Column(name = "last_name")
  @Getter @Setter private String lastName;

  @Getter @Setter private String address;

  @Getter @Setter private String city;

  @Getter @Setter private String province;

  @Getter @Setter private String postal;

  @Getter @Setter private String phone;

  @Column(name = "phone2")
  @Getter @Setter private String alternativePhone;

  @Transient
  @Getter @Setter private String cellPhone;

  @Getter @Setter private String email;

  @Getter @Setter private String myOscarUserName;

  @Column(name = "year_of_birth")
  @Getter @Setter private String yearOfBirth;

  @Column(name = "month_of_birth")
  @Getter @Setter private String monthOfBirth;

  @Column(name = "date_of_birth")
  @Getter @Setter private String dateOfBirth;

  @Getter @Setter private String hin;

  @Transient
  @Getter @Setter private String hinType;

  @Getter @Setter private String ver;

  @Column(name = "roster_status")
  @Getter @Setter private String rosterStatus;

  @Column(name = "roster_date")
  @Getter @Setter private Date rosterDate;

  @Column(name = "roster_termination_date")
  @Getter @Setter private Date rosterTerminationDate;

  @Column(name = "roster_termination_reason")
  @Getter @Setter private String rosterTerminationReason;

  @Column(name = "patient_status")
  @Getter @Setter private String patientStatus;

  @Column(name = "patient_status_date")
  @Getter @Setter private Date patientStatusDate;

  @Column(name = "date_joined")
  @Getter @Setter private Date dateJoined;

  @Column(name = "chart_no")
  @Getter @Setter private String chartNo;

  @Column(name = "hc_type")
  @Getter @Setter private String hcType;

  @Column(name = "hc_renew_date")
  @Getter @Setter private Date hcRenewDate;

  @Column(name = "official_lang")
  @Getter @Setter private String officialLanguage;

  @Column(name = "spoken_lang")
  @Getter @Setter private String spokenLanguage;

  @Getter @Setter private String sex;

  @Column(name = "end_date")
  @Getter @Setter private Date endDate;

  @Column(name = "eff_date")
  @Getter @Setter private Date effDate;

  @Column(name = "pcn_indicator")
  @Getter @Setter private String pcnIndicator;

  @Column(name = "patient_type")
  @Getter @Setter private String patientType;

  @Column(name = "patient_id")
  @Getter @Setter private String patientId;

  @Getter @Setter private Date lastUpdateDate;

  @Column(name = "portal_user_id")
  @Getter @Setter private String portalUserId;

  @Getter @Setter private String alias;

  @Getter @Setter private String previousAddress;

  @Getter @Setter private String children;

  @Getter @Setter private String sourceOfIncome;

  @Getter @Setter private String citizenship;

  @Getter @Setter private String sin;

  @Column(name = "country_of_origin")
  @Getter @Setter private String countryOfOrigin;

  @Getter @Setter private String newsletter;

  @Getter @Setter private String anonymous;

  @Getter @Setter private String lastUpdateUser;

  @Column(name = "pref_name")
  @Getter @Setter private String preferredName;

  @Getter @Setter private Integer primarySystemId;

  @Column(name="provider_no")
  private String providerNo;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "genderId", insertable = false, updatable = false)
  @NotFound(action = NotFoundAction.IGNORE)
  @Getter @Setter private DemographicGender gender;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "pronounId", insertable = false, updatable = false)
  @NotFound(action = NotFoundAction.IGNORE)
  @Getter @Setter private DemographicPronoun pronoun;

  @Getter @Setter private Integer genderId;

  @Getter @Setter private Integer pronounId;

  @JoinColumn(
      name = "provider_no",
      referencedColumnName = "provider_no",
      insertable = false,
      updatable = false)
  @JsonBackReference
  @ManyToOne
  @NotFound(action = NotFoundAction.IGNORE)
  @Getter @Setter private Provider provider;

  @Transient
  @Getter @Setter private List<DiagnosticCode> diagnoses;

  @Transient
  @Getter @Setter private List<LabReqData> labReqs;

  @Transient
  @Getter @Setter private List<Lab> labs;

  @Transient
  @Getter @Setter private String bundleId;

  @Transient
  @Getter @Setter private List<DemographicExt> extensions = new ArrayList<>();

  @Column(name = "cached_link_demographic_state")
  @Getter @Setter private String cachedLinkDemographicState;

  @Transient
  @Getter @Setter private List<Prescription> prescriptions;

  @Transient
  @Getter @Setter private List<DemographicPharmacy> preferredPharmacies;

  @Getter @Setter private Boolean consentToUseEmailForCare;
  @Getter @Setter private Boolean consentToUseEmailForEOrder;
  @Getter @Setter private Integer activeCount = 0;
  @Getter @Setter private Integer headRecord;
  @Getter @Setter private Integer hsAlertCount = 0;
  @Getter @Setter private String sexDesc;
  @Getter @Setter private String guid = UUID.randomUUID().toString();
  @Transient
  @Getter
  private DemographicArchive archiveRecord;

  @Override
  public Integer getRemoteSystemId() {
    return primarySystemId;
  }

  @Override
  public boolean isRemote() {
    return primarySystemId != null && guid != null;
  }

  @PostLoad
  void postLoad() {
    archiveRecord = new DemographicArchive(this);
  }

  @PrePersist
  @PreUpdate
  void prePersist() {
    providerNo = getProviderNo();
    if (preferredName == null) {
      preferredName = "";
    }
    if (patientStatus == null) {
      patientStatus = DefaultConstants.PATIENT_STATUS;
      patientStatusDate = new Date();
    }
    if (demographicNumber == null && dateJoined == null) {
      dateJoined = new Date();
    }
    if (hcType == null) {
      hcType = OscarProperties.getBillingRegion();
    }
    if (countryOfOrigin == null) {
      countryOfOrigin = DefaultConstants.COUNTRY_OF_ORIGIN;
    }
    if (officialLanguage == null) {
      officialLanguage = DefaultConstants.OFFICIAL_LANGUAGE;
    }
    if (newsletter == null) {
      newsletter = DefaultConstants.NEWSLETTER;
    }
    if (cachedLinkDemographicState == null) {
      cachedLinkDemographicState = DefaultConstants.CACHED_LINK_DEMOGRAPHIC_STATE;
    }
    if (address == null) {
      address = "";
    }
    if (postal == null) {
      postal = "";
    }
    if (phone == null) {
      phone = "";
    }
    if (alternativePhone == null) {
      alternativePhone = "";
    }
    if (email == null) {
      email = "";
    }
    if (hin == null) {
      hin = "";
    }
    if (ver == null) {
      ver = "";
    }
    if (rosterStatus == null) {
      rosterStatus = "";
    }
    if (chartNo == null) {
      chartNo = "";
    }
    if (spokenLanguage == null) {
      spokenLanguage = "";
    }
    if (sin == null) {
      sin = "";
    }
    if (patientType == null) {
      patientType = "";
    }
    if (patientId == null) {
      patientId = "";
    }
    if (lastUpdateDate == null) {
      lastUpdateDate = new Date();
    }
    if (sexDesc == null) {
      sexDesc = "";
    }
  }

  /**
   * get the provider number for this demographic
   * @return empty string if not found for compatability with classic schema/code
   */
  public String getProviderNo() {
    return Optional.ofNullable(provider)
        .map(Provider::getProviderNo)
        .orElse(null);
  }

  public Demographic(Demographic other) {
    this.demographicNumber = other.demographicNumber;
    this.guid = other.guid;
    this.title = other.title;
    this.firstName = other.firstName;
    this.lastName = other.lastName;
    this.nameSuffix = other.nameSuffix;
    this.address = other.address;
    this.city = other.city;
    this.province = other.province;
    this.postal = other.postal;
    this.phone = other.phone;
    this.alternativePhone = other.alternativePhone;
    this.email = other.email;
    this.yearOfBirth = other.yearOfBirth;
    this.monthOfBirth = other.monthOfBirth;
    this.dateOfBirth = other.dateOfBirth;
    this.hin = other.hin;
    this.hinType = other.hinType;
    this.ver = other.ver;
    this.rosterStatus = other.rosterStatus;
    this.rosterDate = other.rosterDate;
    this.rosterTerminationDate = other.rosterTerminationDate;
    this.rosterTerminationReason = other.rosterTerminationReason;
    this.patientStatus = other.patientStatus;
    this.patientStatusDate = other.patientStatusDate;
    this.dateJoined = other.dateJoined;
    this.chartNo = other.chartNo;
    this.hcType = other.hcType;
    this.hcRenewDate = other.hcRenewDate;
    this.officialLanguage = other.officialLanguage;
    this.spokenLanguage = other.spokenLanguage;
    this.sex = other.sex;
    this.endDate = other.endDate;
    this.effDate = other.effDate;
    this.pcnIndicator = other.pcnIndicator;
    this.alias = other.alias;
    this.previousAddress = other.previousAddress;
    this.children = other.children;
    this.sourceOfIncome = other.sourceOfIncome;
    this.citizenship = other.citizenship;
    this.sin = other.sin;
    this.countryOfOrigin = other.countryOfOrigin;
    this.newsletter = other.newsletter;
    this.anonymous = other.anonymous;
    this.lastUpdateUser = other.lastUpdateUser;
    this.lastUpdateDate = other.lastUpdateDate;
    this.patientType = other.patientType;
    this.patientId = other.patientId;
    this.preferredName = other.preferredName;
    this.portalUserId = other.portalUserId;
    this.provider = other.provider;
    this.diagnoses = other.diagnoses;
    this.labReqs = other.labReqs;
    this.labs = other.labs;
    this.cachedLinkDemographicState = other.cachedLinkDemographicState;
    this.activeCount = other.activeCount;
    this.hsAlertCount = other.hsAlertCount;
    this.consentToUseEmailForCare = other.consentToUseEmailForCare;
    this.consentToUseEmailForEOrder = other.consentToUseEmailForEOrder;
    this.headRecord = other.headRecord;
    this.sexDesc = other.sexDesc;
    this.myOscarUserName = other.myOscarUserName;
    this.gender = other.gender;
    this.genderId = other.genderId;
    this.pronoun = other.pronoun;
    this.pronounId = other.pronounId;
    this.cellPhone = other.cellPhone;
    this.bundleId = other.bundleId;
    this.primarySystemId = other.primarySystemId;
  }

  public String getFormattedName() {
    return getLastName() + ", " + getFirstName();
  }

  public String getFullFormattedName() {
    String fullFormattedName = getFirstName() + " " + getLastName();
    if (StringUtils.isNotBlank(getTitle())) {
      fullFormattedName = getTitle() + " " + fullFormattedName;
    }
    if (StringUtils.isNotBlank(getNameSuffix())) {
      fullFormattedName += " " + getNameSuffix();
    }
    return fullFormattedName;
  }

  public String getFormattedHin() {
    return (getHin() == null ? "" : getHin()) + "::" + (getVer() == null ? "" : getVer());
  }

  public String getDOB() {
    if (getYearOfBirth() != null && getMonthOfBirth() != null && getDateOfBirth() != null) {
      return getYearOfBirth() + "-" + getMonthOfBirth() + "-" + getDateOfBirth();
    } else {
      return "";
    }
  }

  public Age getAge() {
    Age age = null;
    if (getYearOfBirth() != null && getMonthOfBirth() != null && getDateOfBirth() != null) {
      Calendar calendar =
          new GregorianCalendar(
              Integer.parseInt(getYearOfBirth()),
              Integer.parseInt(getMonthOfBirth()) - 1,
              Integer.parseInt(getDateOfBirth()));
      LocalDate birthday = LocalDate.fromCalendarFields(calendar);
      LocalDate now = new LocalDate();
      Period period = new Period(birthday, now, PeriodType.yearMonthDay());

      age = new Age(period.getYears(), period.getMonths(), period.getDays());
    }

    return age;
  }

  public Date getDOBAsDate() {
    Date birthday = null;
    if (getYearOfBirth() != null && getMonthOfBirth() != null && getDateOfBirth() != null) {
      try {
        Calendar calendar =
            new GregorianCalendar(
                Integer.parseInt(getYearOfBirth()),
                Integer.parseInt(getMonthOfBirth()) - 1,
                Integer.parseInt(getDateOfBirth()));
        birthday = calendar.getTime();
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
    return birthday;
  }

  public String getReferralPhysicianRowId() {
    return getExtensionValue(DemographicExtKey.DOCTOR_ROSTER);
  }

  public String getReferralPhysicianName() {
    return getExtensionValue(DemographicExtKey.DOCTOR_ROSTER_NAME);
  }

  public String getReferralPhysicianOhip() {
    return getExtensionValue(DemographicExtKey.DOCTOR_ROSTER_OHIP);
  }

  public String getFamilyPhysicianRowId() {
    return getExtensionValue(DemographicExtKey.DOCTOR_FAMILY);
  }

  public String getFamilyPhysicianName() {
    return getExtensionValue(DemographicExtKey.DOCTOR_FAMILY_NAME);
  }

  public String getFamilyPhysicianOhip() {
    return getExtensionValue(DemographicExtKey.DOCTOR_FAMILY_OHIP);
  }

  public String getLinkStatus() {
    return Optional.of(getExtensionValue(DemographicExtKey.LINK_STATUS))
        .filter(StringUtils::isNotBlank)
        .orElse(DefaultConstants.LINK_DEMOGRAPHIC_STATE);
  }

  public String getMrpName() {
    return provider == null ? "" : getProvider().getFormattedName();
  }

  public String getMrpPractitionerNumber() {
    return provider == null ? "" : getProvider().getOhipNo();
  }

  private String getExtensionValue(final DemographicExtKey key) {
    return DemographicAdapter
        .getExtensionValueByDemographicExtensionAsString(getDemographicNumber(), key);
  }

  public void setReferralPhysicianRowId(final String referralPhysicianRowId) {
    setPhysicianExtensionValue(DemographicExtKey.DOCTOR_ROSTER, referralPhysicianRowId);
  }

  public void setReferralPhysicianName(final String referralPhysicianName) {
    setPhysicianExtensionValue(DemographicExtKey.DOCTOR_ROSTER_NAME, referralPhysicianName);
  }

  public void setReferralPhysicianOhip(final String referralPhysicianOhip) {
    setPhysicianExtensionValue(DemographicExtKey.DOCTOR_ROSTER_OHIP, referralPhysicianOhip);
  }

  public void setFamilyPhysicianRowId(final String familyPhysicianRowId) {
    setPhysicianExtensionValue(DemographicExtKey.DOCTOR_FAMILY, familyPhysicianRowId);
  }

  public void setFamilyPhysicianName(final String familyPhysicianName) {
    setPhysicianExtensionValue(DemographicExtKey.DOCTOR_FAMILY_NAME, familyPhysicianName);
  }

  public void setFamilyPhysicianOhip(final String familyPhysicianOhip) {
    setPhysicianExtensionValue(DemographicExtKey.DOCTOR_FAMILY_OHIP, familyPhysicianOhip);
  }

  private void setPhysicianExtensionValue(final DemographicExtKey key, final String value) {
    DemographicAdapter.updateOrCreateDemographicExtension(this, key, value);
  }

  public boolean hasReferralDoctor() {
    val name = this.getReferralPhysicianName();
    // if name is blank, no referral doctor is set
    return StringUtils.isNotBlank(name);
  }

  public boolean hasFamilyPhysician() {
    val name = this.getFamilyPhysicianName();
    // if name is blank, no family doctor is set
    return StringUtils.isNotBlank(name);
  }

  public void addExtension(final DemographicExt demographicExt) {
    if (demographicExt != null) {
      extensions.add(demographicExt);
    }
  }

  @Nullable
  public String getGenderIdentityValue() {
    return Optional.ofNullable(this.getGender())
        .map(DemographicGender::getValue)
        .orElse(null);
  }

  @Nullable
  public String getPronounValue() {
    return Optional.ofNullable(this.getPronoun())
        .map(DemographicPronoun::getValue)
        .orElse(null);
  }
}
