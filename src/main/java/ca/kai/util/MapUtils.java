/**
 * Copyright (c) 2023 WELL EMR Group Inc. This software is made available under the terms of the GNU
 * General Public License, Version 2, 1991 (GPLv2). License details are available via
 * "gnu.org/licenses/gpl-2.0.html".
 */
package ca.kai.util;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class MapUtils {

  /**
   * Returns the value to which the specified key is mapped,
   * or value if this map contains no mapping for the key.
   * @param map the map to check
   * @param key the key for lookup in map
   * @param value the value to return if the key does not exist in the map
   * @return the mapped result, or value if the key does not exist in the map
   */
  public static <K, V> V getOrDefault(final @NonNull Map<K, V> map, final K key, final V value) {
    return map.containsKey(key) ? map.get(key) : value;
  }

  /**
   * Convert a list of Objects into a Map.
   *
   * @param list List of Objects where each Object is an array of two objects. The first object
   *                is a String key and the second is the value.
   * @param valueClass   The class type that the values should be checked against.
   * @param <T>     The type of values in the returned map.
   * @return A Map where the keys are the first elements of the results and the values are the
   * second elements, ensuring that the values are of the specified class type.
   */
  public static <T> Map<String, T> convertQueryListToMap(final List<Object[]> list,
      final Class<T> valueClass) {
    if (list == null || list.isEmpty() || valueClass == null) {
      return Collections.emptyMap();
    }

    Map<String, T> map = new HashMap<>();
    for (Object[] result : list) {
      if (result.length != 2) {
        log.warn("Result must have 2 elements to form a key-value pair: {}", result);
        continue;
      }

      Object value = result[1];

      // Special handling for Integer class to handle different numeric types
      if (valueClass == Integer.class && value instanceof Number) {
        value = ((Number) value).intValue();
      }

      if (!valueClass.isInstance(value)) {
        log.warn("Value {} for key {} is not of type {}", value, result[0], valueClass.getName());
        continue;
      }

      if (result[0] == null) {
        log.warn("Key must not be null. Skipping adding entry to map.");
        continue;
      }

      map.put(result[0].toString(), valueClass.cast(value));
    }
    return map;
  }
}
