/**
 * Copyright (c) 2023 WELL EMR Group Inc. This software is made available under the terms of the GNU
 * General Public License, Version 2, 1991 (GPLv2). License details are available via
 * "gnu.org/licenses/gpl-2.0.html".
 */
package ca.kai.util;

import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import java.util.Base64;
import java.util.UUID;
import javax.crypto.SecretKey;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.oscarehr.PMmodule.dao.ProviderDao;
import org.oscarehr.common.dao.SecurityDao;
import org.oscarehr.common.model.Security;
import org.oscarehr.common.model.SystemPreferences;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;
import oscar.util.SystemPreferencesUtils;

import java.util.Collections;
import java.util.HashSet;

public class SecurityUtils {

  private static final String EMPTY = "";

  /**
   * Create a kai-sso cookie with the given jws. The cookie expiry is set to 72 hours.
   *
   * @param jws the jws to be stored in the cookie
   * @return A cookie with the given jws that expires after 72 hours
   */
  public static Cookie createKaiSsoCookie(final String jws) {
    Cookie cookie = new Cookie("kai-sso", jws);
    cookie.setMaxAge(259200);
    cookie.setPath("/");
    cookie.setHttpOnly(true);
    cookie.setSecure(true);
    return cookie;
  }

  /**
   * Create a kai-sso cookie for the logged-in provider.
   * If the provider already has a signing key, it will reuse their last used UUID and their
   * decoded secret key.
   * If the provider doesn't have a signing key, it will generate a new UUID and a new secret key.
   * @param request the HTTP request containing the logged-in provider information
   * @return A kai-sso cookie for the logged-in provider
   */
  public static Cookie createKaiSsoCookieForLoggedInProvider(
      final @NotNull HttpServletRequest request
  ) throws IllegalStateException {
    val loggedInInfo = LoggedInInfo.getLoggedInInfoFromRequest(request);
    if (loggedInInfo == null) {
      throw new IllegalStateException("No logged in info found in request");
    }
    val provider = loggedInInfo.getLoggedInProvider();
    if (provider == null) {
      throw new IllegalStateException("No logged in provider found in request");
    }
    String uuid;
    SecretKey secretKey;
    if (provider.getSigningKey() != null) {
      uuid = provider.getLastUsedUuid();
      val encodedSecretKey = provider.getSigningKey();
      secretKey = Keys.hmacShaKeyFor(Base64.getDecoder().decode(encodedSecretKey));
    } else {
      uuid = UUID.randomUUID().toString();
      secretKey = Keys.secretKeyFor(SignatureAlgorithm.HS256);
      val encodedSecretKey = Base64.getEncoder().encodeToString(secretKey.getEncoded());

      provider.setSigningKey(encodedSecretKey);
      provider.setLastUsedUuid(uuid);
      ProviderDao providerDao = SpringUtils.getBean(ProviderDao.class);
      providerDao.updateProvider(provider);
    }
    // Build the JWS and return a created cookie
    val jws = Jwts.builder()
        .setHeaderParam(JwsHeader.KEY_ID, provider.getProviderNo())
        .setId(uuid)
        .signWith(secretKey)
        .compact();
    return createKaiSsoCookie(jws);
  }

  /**
   * Check if the user is a support user based on the session attribute. If the session username is
   * equal to the support username, return true.
   *
   * @param session - the session to check
   * @return - true if the user is a support user, false otherwise
   */
  public static boolean isSupportUser(final HttpSession session) {
    val loggedInSecurity = (Security) session.getAttribute(
        "loggedInSecurity");
    val sessionUsername = loggedInSecurity != null
        ? StringUtils.trimToEmpty(loggedInSecurity.getUserName())
        : EMPTY;
    val supportUsername = SystemPreferencesUtils.getPreferenceValueByName(
        SystemPreferences.SUPPORT_USERNAME_KEY, EMPTY);

    return sessionUsername.equalsIgnoreCase(supportUsername);
  }

  /**
   * Checks if the current user is a support user and retrieves the security record for the support user.
   * This method encapsulates the logic that was duplicated in multiple JSP files.
   *
   * @param request - the HTTP request
   * @return - a SupportUserInfo object containing the isSupportUser flag and the security record
   */
  public static SupportUserInfo getSupportUserInfo(final HttpServletRequest request) {
    val loggedInInfo = LoggedInInfo.getLoggedInInfoFromSession(request);
    val kaiUsernameSystemPreference = SystemPreferencesUtils.findPreferenceByName(SystemPreferences.SUPPORT_USERNAME_KEY);
    val supportUsername = kaiUsernameSystemPreference != null
        ? StringUtils.trimToEmpty(kaiUsernameSystemPreference.getValue())
        : "";
    if (StringUtils.isBlank(supportUsername)) {
      return new SupportUserInfo(false, null, Collections.emptySet());
    }

    val isSupportUser = loggedInInfo != null
        && loggedInInfo.getLoggedInSecurity() != null
        && supportUsername.equalsIgnoreCase(loggedInInfo.getLoggedInSecurity().getUserName());
    SecurityDao securityDao = SpringUtils.getBean(SecurityDao.class);
    val supportList = securityDao.findByUserName(supportUsername);
    Security securityRecord = null;
    val supportProviderNos = new HashSet<String>();
    if (supportList != null && !supportList.isEmpty()) {
      securityRecord = supportList.get(0);
      for (val s : supportList) {
        val p = s.getProviderNo();
        if (p != null) {
          supportProviderNos.add(p);
        }
      }
    }
    return new SupportUserInfo(
        isSupportUser,
        securityRecord,
        java.util.Collections.unmodifiableSet(supportProviderNos));
  }

}
