package ca.kai.util.fhir;

import static ca.kai.rx.drug.instructions.RxInstructionParser.isParsable;

import ca.kai.OscarProperties;
import ca.kai.clinic.Clinic;
import ca.kai.clinic.ClinicService;
import ca.kai.demographic.Allergy;
import ca.kai.demographic.Demographic;
import ca.kai.demographic.DemographicRepository;
import ca.kai.demographic.DemographicService;
import ca.kai.demographic.PatientExternalPharmacyId;
import ca.kai.demographic.PatientExternalPharmacyIdRepository;
import ca.kai.integration.eRx.AppConfig;
import ca.kai.integration.eRx.ClinicConfig;
import ca.kai.integration.eRx.ProviderConfig;
import ca.kai.integration.infoway.TerminologyGateway;
import ca.kai.integration.infoway.TerminologyGatewayValueSet;
import ca.kai.measurement.Measurement;
import ca.kai.pharmacy.PharmacyInfo;
import ca.kai.pharmacy.PharmacyInfoRepository;
import ca.kai.property.Property;
import ca.kai.pharmacy.PharmacyServiceType;
import ca.kai.property.PropertyService;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderService;
import ca.kai.rx.RxUtils;
import ca.kai.rx.drug.Drug;
import ca.kai.rx.drug.DrugDispense;
import ca.kai.rx.drug.DrugInstruction;
import ca.kai.rx.drug.DrugRepository;
import ca.kai.rx.drug.controlledSubstance.ControlledSubstanceInfo;
import ca.kai.rx.drug.instructions.InstructionParser;
import ca.kai.rx.drug.instructions.RxInstructionParser;
import ca.kai.rx.drugDatabases.fdb.FdbDrugSearchIngredient;
import ca.kai.rx.drugDatabases.fdb.FdbService;
import ca.kai.rx.drugDatabases.fdb.FdbUtils;
import ca.kai.rx.externalPrescriptions.prescribeIT.ClinicianCommunication;
import ca.kai.rx.externalPrescriptions.prescribeIT.ClinicianCommunicationAttachment;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxNotificationDisplay;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxExtensionType;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxMessageHeader;
import ca.kai.rx.externalPrescriptions.prescribeIT.RxBundleUtil;
import ca.kai.rx.externalPrescriptions.prescribeIT.RxFillRequestService;
import ca.kai.systemPreference.SystemPreferenceService;
import ca.kai.util.fhir.converter.PractitionerConverter;
import ca.kai.util.fhir.supportingInfo.Parser;
import ca.kai.rx.externalPrescriptions.prescribeIT.TaskRepository;
import ca.kai.rx.externalPrescriptions.prescribeIT.TaskService;
import ca.kai.rx.prescription.Prescription;
import ca.kai.util.LocationsService;
import com.fdb.mkfi.core.DispensableGeneric;
import com.fdb.mkfi.core.IngredientStrength;
import com.fdb.mkfi.core.Product;
import com.fdb.mkfi.core.ProductExternalIdType;
import com.fdb.mkfi.core.ProductExternalIdentifier;
import javax.annotation.PostConstruct;
import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import lombok.val;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.hl7.fhir.dstu2016may.model.Address;
import org.hl7.fhir.dstu2016may.model.Annotation;
import org.hl7.fhir.dstu2016may.model.Attachment;
import org.hl7.fhir.dstu2016may.model.BooleanType;
import org.hl7.fhir.dstu2016may.model.CodeType;
import org.hl7.fhir.dstu2016may.model.CodeableConcept;
import org.hl7.fhir.dstu2016may.model.Coding;
import org.hl7.fhir.dstu2016may.model.Communication;
import org.hl7.fhir.dstu2016may.model.ContactPoint;
import org.hl7.fhir.dstu2016may.model.DateTimeType;
import org.hl7.fhir.dstu2016may.model.Duration;
import org.hl7.fhir.dstu2016may.model.Enumerations;
import org.hl7.fhir.dstu2016may.model.Extension;
import org.hl7.fhir.dstu2016may.model.HumanName;
import org.hl7.fhir.dstu2016may.model.Identifier;
import org.hl7.fhir.dstu2016may.model.IntegerType;
import org.hl7.fhir.dstu2016may.model.ListResource;
import org.hl7.fhir.dstu2016may.model.Medication;
import org.hl7.fhir.dstu2016may.model.MedicationDispense;
import org.hl7.fhir.dstu2016may.model.MedicationOrder;
import org.hl7.fhir.dstu2016may.model.MessageHeader;
import org.hl7.fhir.dstu2016may.model.Meta;
import org.hl7.fhir.dstu2016may.model.Narrative;
import org.hl7.fhir.dstu2016may.model.Observation;
import org.hl7.fhir.dstu2016may.model.Organization;
import org.hl7.fhir.dstu2016may.model.Patient;
import org.hl7.fhir.dstu2016may.model.Period;
import org.hl7.fhir.dstu2016may.model.Practitioner;
import org.hl7.fhir.dstu2016may.model.Quantity;
import org.hl7.fhir.dstu2016may.model.Range;
import org.hl7.fhir.dstu2016may.model.Reference;
import org.hl7.fhir.dstu2016may.model.Resource;
import org.hl7.fhir.dstu2016may.model.SimpleQuantity;
import org.hl7.fhir.dstu2016may.model.StringType;
import org.hl7.fhir.dstu2016may.model.Task;
import org.hl7.fhir.dstu2016may.model.Timing;
import org.hl7.fhir.dstu2016may.model.UriType;
import org.hl7.fhir.dstu2016may.model.ValueSet;
import org.hl7.fhir.exceptions.FHIRException;
import org.hl7.fhir.utilities.xhtml.NodeType;
import org.hl7.fhir.utilities.xhtml.XhtmlNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FhirConversionService {
    private final ClinicService clinicService;
    private final DemographicService demographicService;
    private final DrugRepository drugRepository;
    private final FdbService fdbService;
    private final LocationsService locationsService;
    private final OscarProperties oscarProperties;
    private final PharmacyInfoRepository pharmacyInfoRepository;
    private final PropertyService propertyService;
    private final ProviderService providerService;
    private final TaskRepository taskRepository;
    private final PatientExternalPharmacyIdRepository patientExternalPharmacyIdRepository;
    private final DemographicRepository demographicRepository;
    private final SystemPreferenceService systemPreferenceService;

    @Autowired
    private Map<ErxExtensionType.SupportingInfo, Parser> parserMap;
    private AppConfig appConfig = AppConfig.getInstance();
    private ClinicConfig clinicConfig = ClinicConfig.getInstance();
    private ProviderConfig providerConfig = null;

    private final TerminologyGateway terminologyGateway;
    private final Logger logger = LoggerFactory.getLogger(FhirConversionService.class);

    @Autowired private PractitionerConverter practitionerConverter;

    private static final String RX_PHONE = "rxPhone";
    private static final String RX_ADDRESS = "rxAddress";
    private static final String RX_CITY = "rxCity";
    private static final String RX_PROVINCE = "rxProvince";
    private static final String RX_POSTAL = "rxPostal";
    private static final String RX_FAX = "faxnumber";
    private static final String ERX_USE_PROVIDER_PROPERTIES = "erx.include_provider_preferences";

    @Autowired
    public FhirConversionService(
        final TerminologyGateway terminologyGateway,
        final FdbService fdbService,
        final ClinicService clinicService,
        final DemographicService demographicService,
        final DrugRepository drugRepository,
        final LocationsService locationsService,
        final OscarProperties oscarProperties,
        final PharmacyInfoRepository pharmacyInfoRepository,
        final PropertyService propertyService,
        final ProviderService providerService,
        final TaskRepository taskRepository,
        final PatientExternalPharmacyIdRepository patientExternalPharmacyIdRepository,
        final DemographicRepository demographicRepository,
        final SystemPreferenceService systemPreferenceService
    ) {
        this.terminologyGateway = terminologyGateway;
        this.fdbService = fdbService;
        this.clinicService = clinicService;
        this.demographicService = demographicService;
        this.drugRepository = drugRepository;
        this.locationsService = locationsService;
        this.oscarProperties = oscarProperties;
        this.pharmacyInfoRepository = pharmacyInfoRepository;
        this.propertyService = propertyService;
        this.providerService = providerService;
        this.taskRepository = taskRepository;
        this.patientExternalPharmacyIdRepository = patientExternalPharmacyIdRepository;
        this.demographicRepository = demographicRepository;
        this.systemPreferenceService = systemPreferenceService;
        init();
    }

    @PostConstruct
    public void init() {
        if (appConfig == null) {
            appConfig = AppConfig.getInstance();
        }
        if (clinicConfig == null) {
            clinicConfig = ClinicConfig.getInstance();
        }
    }

    public List<String> parseSupportingInfo(MedicationOrder medicationOrder,
        ErxExtensionType.SupportingInfo type) {
        Parser parser = parserMap.get(type);
        List<String> info = new ArrayList<>();
        List<Extension> supportingInfo = medicationOrder.getExtensionsByUrl(
            ErxExtensionType.MedicationOrder.SUPPORTING_INFO.getUrl());
        if (supportingInfo != null) {
            for (Extension extension : supportingInfo) {
                Resource resource = (Resource) ((Reference) extension.getValue()).getResource();
                if (resource.getId().startsWith(type.getType())) {
                    String parsedInfo = parser.parse(resource);
                    if (parsedInfo != null) {
                        info.add(parsedInfo);
                    }
                }
            }
        }
        return info;
    }

    public ListResource allergiesToList(List<Allergy> allergies, Reference subject) {
        ListResource list = new ListResource();
        List<String> allergyNames = new ArrayList<String>();
        // List.id
        list.setId(UUID.randomUUID().toString());

        // List.text
        Narrative text = new Narrative();
        text.setStatus(Narrative.NarrativeStatus.ADDITIONAL);

        for (Allergy allergy : allergies) {
            allergyNames.add(allergy.getDescription());
        }

        XhtmlNode allergyNode = new XhtmlNode(NodeType.Text, "div");
        allergyNode.setContent(StringUtils.join(allergyNames, "|"));
        text.setDiv(allergyNode);
        list.setText(text);

        // List.status
        list.setStatus(ListResource.ListStatus.CURRENT);

        // List.mode
        list.setMode(ListResource.ListMode.SNAPSHOT);

        // List.code
        CodeableConcept listCode = new CodeableConcept();
        listCode.addCoding(IdentifierCoding.USE_CODES_ALLERGIES);
        list.setCode(listCode);

        // List.subject
        list.setSubject(subject);

        return list;
    }

    public Communication communicationFromClinicianCommunication(ClinicianCommunication communication, String delegatePractitionerUuid) {
        Communication eRxCommunication = new Communication();
        eRxCommunication.setId(communication.getUuid());

        // Set subject
        if (!StringUtils.isBlank(communication.getSubject())) {
            Extension subjectExtension = new Extension(ErxExtensionType.Communication.SUBJECT.getUrl(), new StringType(communication.getSubject()));
            eRxCommunication.addExtension(subjectExtension);
        }

        // Set topic
        if (communication.getTopic() != null) {

            CodeableConcept identifierTypeCoding = new CodeableConcept();
            identifierTypeCoding.addCoding(IdentifierCoding.PLAC);

            Identifier identifier = new Identifier();
            identifier.setType(identifierTypeCoding);
            identifier.setSystem(clinicConfig.getApplicationId() + ".2");
            identifier.setValue(communication.getTopic());

            Extension identifierExtension = new Extension();
            identifierExtension.setUrl(ErxExtensionType.Reference.IDENTIFIER.getUrl());
            identifierExtension.setValue(identifier);

            Reference topicReference = new Reference();
            topicReference.addExtension(identifierExtension);

            Extension topicExtension = new Extension(ErxExtensionType.Communication.TOPIC.getUrl(), topicReference);
            eRxCommunication.addExtension(topicExtension);
        }

        if (StringUtils.isNotEmpty(communication.getTopicRequisition())) {
            Identifier identifier = new Identifier();
            identifier.setSystem(clinicConfig.getApplicationId() + ".3");
            identifier.setValue(communication.getTopicRequisition());

            Extension topicRequisitionExtension = new Extension(ErxExtensionType.Communication.TOPIC_REQUISITION.getUrl(), identifier);
            eRxCommunication.addExtension(topicRequisitionExtension);
        }

        // Set priority
        if (communication.getPriority() != null) {
            Extension priorityExtension = new Extension(ErxExtensionType.Communication.PRIORITY.getUrl(), new CodeType(communication.getPriority().name().toLowerCase()));
            eRxCommunication.addExtension(priorityExtension);
        }

        // Set threadId
        Extension threadIdExtension = new Extension(ErxExtensionType.Communication.THREAD_ID.getUrl(), new StringType(communication.getThreadId()));
        eRxCommunication.addExtension(threadIdExtension);

        if (delegatePractitionerUuid != null) {
            Extension entererExtension = new Extension(ErxExtensionType.Communication.ENTERER.getUrl(), new Reference(delegatePractitionerUuid));
            eRxCommunication.addExtension(entererExtension);
        }

        // Set identifier
        Identifier identifier = new Identifier();
        identifier.setSystem("urn:ietf:rfc:3986");
        identifier.setValue("urn:uuid:" + communication.getUuid());
        eRxCommunication.addIdentifier(identifier);

        // Set category
        CodeableConcept category = new CodeableConcept();
        category.addCoding(new Coding("https://fhir.infoway-inforoute.ca/CodeSystem/communicationcategory", communication.getCategory().name().toLowerCase(), null));
        eRxCommunication.setCategory(category);

        // Set sender
        Reference sender = new Reference(communication.getSendingReferenceId());
        if (!communication.getSendingReferenceId().contains("Organization")) {
            Extension onBehalfOfExtension = new Extension(ErxExtensionType.Reference.ON_BEHALF_OF.getUrl(), new Reference(communication.getMessageHeader().getSourceReferenceId()));
            sender.addExtension(onBehalfOfExtension);
        }
        eRxCommunication.setSender(sender);

        // Set replyTo
        Extension replyToExtension;
        if (communication.getReplyToReferenceId().contains("Organization")) {
            replyToExtension = new Extension(ErxExtensionType.Communication.REPLY_TO.getUrl(), new Reference(communication.getReplyToReferenceId()));
        } else { // if (communication.getReplyToReferenceId().contains("Practitioner")) {
            Reference replyToReference = new Reference(communication.getReplyToReferenceId());
            Extension onBehalfOfExtension = new Extension(ErxExtensionType.Reference.ON_BEHALF_OF.getUrl(), new Reference(communication.getMessageHeader().getSourceReferenceId()));
            replyToReference.addExtension(onBehalfOfExtension);
            replyToExtension = new Extension(ErxExtensionType.Communication.REPLY_TO.getUrl(), replyToReference);
        }
        eRxCommunication.addExtension(replyToExtension);

        // Set recipient
        Reference recipient = new Reference();
        if (communication.getRecipientReferenceId().contains("Organization")) {
            recipient.setReference(communication.getMessageHeader().getDestinationReferenceId());
        } else {
            Extension organizationExtension = new Extension(ErxExtensionType.Reference.ON_BEHALF_OF.getUrl(), new Reference(communication.getMessageHeader().getDestinationReferenceId()));
            recipient.addExtension(organizationExtension);
            recipient.setReference(communication.getRecipientReferenceId());
        }
        eRxCommunication.addRecipient(recipient);

        // Set payload(s)
        // Set message body
        Communication.CommunicationPayloadComponent bodyPayload = new Communication.CommunicationPayloadComponent();
        bodyPayload.setContent(new StringType(communication.getMessageBody()));
        eRxCommunication.addPayload(bodyPayload);

        // Set attachment(s)
        for (ClinicianCommunicationAttachment attachment : communication.getAttachments()) {
                File attachmentFile = new File(ClinicConfig.MESSAGE_ATTACHMENT_PATH + attachment.getFileName());
                Communication.CommunicationPayloadComponent attachmentPayload = new Communication.CommunicationPayloadComponent();
                Attachment eRxAttachment = new Attachment();
                eRxAttachment.setContentType(attachment.getContentType().getMediaType());
                eRxAttachment.setUrl(attachment.getUrl());
                eRxAttachment.setSize((int) attachmentFile.length());
                eRxAttachment.setTitle(attachment.getTitle());
                attachmentPayload.setContent(eRxAttachment);
                eRxCommunication.addPayload(attachmentPayload);
        }

        // Set status
        eRxCommunication.setStatus(Communication.CommunicationStatus.INPROGRESS);

        // Set sent date
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        String sent = sdf.format(new Date());
        eRxCommunication.setSentElement(new DateTimeType(sent));

        // Set subject demographic
        if (communication.getDemographic() != null) {
            eRxCommunication.setSubject(new Reference("Patient/" + communication.getDemographic().getBundleId()));
        } else if (StringUtils.isNotBlank(communication.getSubjectReference())) {
            eRxCommunication.setSubject((new Reference(communication.getSubjectReference())));
        }

        return eRxCommunication;
    }

    public Task externalTaskFromTask(ca.kai.rx.externalPrescriptions.prescribeIT.Task task, String medicationOrderUuid, String patientBundleUuid) {
        Task eRxTask = new Task();

        // Task.id
        eRxTask.setId(task.getUuid());

        // Task.meta
        eRxTask.setMeta(new Meta().addProfile("http://prescribeit.ca/fhir/StructureDefinition/task-" + task.getType()));

        // Task.extension (groupIdentifier)
        Identifier identifier = new Identifier();
        identifier.setSystem(clinicConfig.getApplicationId() + ".3"); // ${Source_application-id}.3
        identifier.setValue(task.getGroupIdentifier());
        eRxTask.addExtension(new Extension(ErxExtensionType.Task.GROUP_IDENTIFIER.getUrl(), identifier));

        // Task.basedOn
        String basedOn = task.getBasedOn();
        if (basedOn != null && !basedOn.isEmpty()) {
            Reference reference = new Reference();
            reference.setReference(task.getBasedOn());
            eRxTask.addExtension(new Extension(ErxExtensionType.Task.BASED_ON.getUrl(), reference));
        }

        // Medication.code
        Optional<ValueSet.ValueSetExpansionContainsComponent> taskReason = getTgTaskReason(task);

        if (taskReason.isPresent()) {
            CodeableConcept code = new CodeableConcept();
            Coding coding = new Coding();
            coding.setSystem(taskReason.get().getSystem());
            coding.setCode(taskReason.get().getCode());
            coding.setDisplay(taskReason.get().getDisplay());
            code.addCoding(coding);
            if (!StringUtils.isBlank(task.getReasonText())) {
                code.setText(task.getReasonText().trim());
            }
            eRxTask.addExtension(new Extension(ErxExtensionType.Task.REASON.getUrl(), code));
        }

        // Task.type
        CodeableConcept type = new CodeableConcept();
        Coding typeCoding = new Coding();
        typeCoding.setSystem("https://fhir.infoway-inforoute.ca/CodeSystem/prescriptiontasktype");
        typeCoding.setCode(task.getType());
        typeCoding.setDisplay("");
        type.addCoding(typeCoding);
        eRxTask.setType(type);

        // Task.priority
        eRxTask.setPriority(Task.TaskPriority.valueOf(task.getPriority().toUpperCase()));

        // Task.status
        eRxTask.setStatus(Task.TaskStatus.valueOf(task.getStatus().toUpperCase()));

        // Task.subject
        eRxTask.setSubject(new Reference("MedicationOrder/" + medicationOrderUuid));

        // Task.for
        eRxTask.setFor( new Reference("Patient/" + patientBundleUuid));

        // Task.created
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        String created = sdf.format(new Date());
        eRxTask.setCreatedElement(new DateTimeType(created));

        // Task.lastModified
        String lastModified = sdf.format(new Date());
        eRxTask.setLastModifiedElement(new DateTimeType(lastModified));

        // Task.creator
        eRxTask.setCreator(new Reference(task.getCreator()));

        // Task.owner
        if (!RxFillRequestService.RXTaskRequest.DEFERRED.getCode().equals(task.getType())) {
            eRxTask.setOwner(new Reference(task.getOwner()));
        }
        return eRxTask;
    }

    public Clinic clinicFromOrganization(Organization organization) {
        init();
        Clinic clinic = clinicService.getByOrganizationId(organization.getIdElement().getIdPart());

        if (clinic == null) {
            clinic = new Clinic();
            clinic.setStatus("1");

            clinic.setName(organization.getName());

            Address address = organization.getAddress().get(0);
            if (address != null) {
                if (address.getLine() != null && !address.getLine().isEmpty() && address.getLine().get(0) != null) {
                    clinic.setAddress(address.getLine().get(0).toString());
                }
                clinic.setCity(address.getCity());
                clinic.setProvince(address.getState());
                clinic.setPostalCode(address.getPostalCode());
            }

            for (ContactPoint telecom : organization.getTelecom()) {
                String phone = telecom.getValue();
                if (phone.substring(0, 2).equals("00")) {
                    phone = phone.substring(2);
                }
                switch (telecom.getSystem()) {
                    case FAX:
                        clinic.setFax(phone);
                        break;
                    case PHONE:
                        if (StringUtils.isEmpty(clinic.getPhone())) {
                            clinic.setPhone(phone);
                        } else if (StringUtils.isEmpty(clinic.getDelimPhone())) {
                            clinic.setDelimPhone(phone);
                        }
                        break;
                }
            }

            clinic.setCprId(organization.getIdElement().getIdPart());
        }

        return clinic;
    }

    public Organization clinicToOrganization(final Clinic clinic, final Provider provider) {
        init();
        Organization organization = new Organization();

        // Organization.id
        organization.setId(clinic.getCprId());

        // Organization.identifier
        Identifier identifier = new Identifier();
        identifier.setSystem("http://sharedhealth.exchange/fhir/NamingSystem/registry-id-organization");
        identifier.setValue(clinic.getCprId());
        organization.addIdentifier(identifier);


        // Organization.type
        CodeableConcept type = new CodeableConcept();
        type.addCoding(IdentifierCoding.ROLE_CODE_PROFF);
        organization.setType(type);

        // Organization.name
        organization.setName(clinic.getName());

        // Organization.telecom
        val clinicPhone = createTelecom(ContactPoint.ContactPointSystem.PHONE,
            getClinicOrProviderProperty(clinic.getPhone(), provider, RX_PHONE), ContactPoint.ContactPointUse.WORK);
        if (clinicPhone != null) {
            organization.addTelecom(clinicPhone);
        }
        val clinicFax = createTelecom(ContactPoint.ContactPointSystem.FAX,
            getClinicOrProviderProperty(clinic.getFax(), provider, RX_FAX), ContactPoint.ContactPointUse.WORK);
        if (clinicFax != null) {
            organization.addTelecom(clinicFax);
        }

        // Organization.address
        Address address = getClinicOrProviderAddress(clinic, provider);

        if (address != null) {
            organization.addAddress(address);
        }

        return organization;
    }

    public Demographic demographicFromPatient(Patient patient, boolean matchLocalPatient) {
        init();
        HashMap<String, String> demographicExts = new HashMap<String, String>();
        String jhn, patientId, sourceApplicationId;
        Integer demographicNumber = null;
        Demographic demographic = null;

        // This will get all the items necessary to find if a demographic is already linked to this requests source application
        HashMap<String, String> identifiers = getExternalIdAndSourceApplication(patient);
        jhn = identifiers.getOrDefault("patientHin", "");
        String jhnType = identifiers.get("jhnType");
        patientId = identifiers.getOrDefault("patientId", "");
        sourceApplicationId = identifiers.getOrDefault("sourceApplication", "");
        if (!sourceApplicationId.isEmpty()) {
            sourceApplicationId = sourceApplicationId.replaceAll("urn:oid:", "");
        }

        demographic = new Demographic();

        // patient.getIdentifier().get(0).getSystem()
        Coding hinIdentifierCoding = new Coding("http://hl7.org/fhir/v2/0203", "JHN", null);
        Identifier hinIdentifier = null;
        for (Identifier identifier : patient.getIdentifier()) {
            if (identifier.getType() != null) {
                for (Coding coding : identifier.getType().getCoding()) {
                    if (coding.getSystem().equals(hinIdentifierCoding.getSystem()) && coding.getCode().equals(hinIdentifierCoding.getCode())) {
                        hinIdentifier = identifier;
                        break;
                    }
                }
            }
            if (hinIdentifier != null) { break; }
        }
        if (hinIdentifier != null && StringUtils.isNotBlank(hinIdentifier.getSystem())) {
            Optional<ValueSet.ValueSetExpansionContainsComponent> healthIdentifierType = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.PATIENT_HEALTH_IDENTIFIER_TYPE, hinIdentifier.getSystem());
            if (healthIdentifierType.isPresent()) {
                demographic.setHinType(healthIdentifierType.get().getDisplay());
            }
        }

        jhn = jhn.length() >= 10 ? jhn.substring(0, 10) : jhn;
        demographic.setHin(jhn);

        if (patient.hasName()) {
            HumanName humanName = patient.getName().get(0);

            if (humanName.hasPrefix() && !humanName.getPrefix().isEmpty()) {
                demographic.setTitle(humanName.getPrefix().get(0).getValueNotNull());
            }

            demographic.setLastName(humanName.getFamilyAsSingleString());
            demographic.setFirstName(humanName.getGivenAsSingleString());

            if (humanName.hasSuffix() && !humanName.getSuffix().isEmpty()) {
                demographic.setNameSuffix(humanName.getSuffix().get(0).getValueNotNull());
            }
        }

        if (patient.hasTelecom()) {
            for (ContactPoint telecom : patient.getTelecom()) {
                // Phone number formatting was incorrect for display
                String telecomValue = telecom.getValue();
                if (telecomValue.substring(0, 2).equals("00")) {
                    telecomValue = telecomValue.substring(2);
                }

                switch (telecom.getUse()) {
                    case HOME:
                        String contact = StringUtils.trimToNull(telecomValue);
                        switch (telecom.getSystem()) {
                            case PHONE:
                                demographic.setPhone(contact);
                                break;
                            case EMAIL:
                                demographic.setEmail(contact);
                        }
                        break;
                    case MOBILE:
                        if (telecom.getSystem().equals(ContactPoint.ContactPointSystem.PHONE)) {
                            demographicExts.put("demo_cell", StringUtils.trimToNull(telecomValue));
                            demographic.setCellPhone(StringUtils.trimToNull(telecomValue));
                        }
                        break;
                    case WORK:
                        if (telecom.getSystem().equals(ContactPoint.ContactPointSystem.PHONE)) {
                            demographic.setAlternativePhone(StringUtils.trimToNull(telecomValue));
                        }
                        break;
                    case NULL:
                        if (StringUtils.isEmpty(demographic.getPhone())) {
                            demographic.setPhone(StringUtils.trimToNull(telecomValue));
                        }
                        break;
                }
            }
        }

        if (patient.hasGender()) {
            Enumerations.AdministrativeGender administrativeGender = patient.getGender();
            switch (administrativeGender) {
                case FEMALE:
                    demographic.setSex("F");
                    break;
                case MALE:
                    demographic.setSex("M");
                    break;
                case OTHER:
                    demographic.setSex("O");
                    break;
                case UNKNOWN:
                    demographic.setSex("U");
                    break;
                case NULL:
                    break;
            }
        }

        if (patient.hasBirthDate()) {
            Calendar birthDate = Calendar.getInstance();
            birthDate.setTime(patient.getBirthDate());

            demographic.setYearOfBirth(String.valueOf(birthDate.get(Calendar.YEAR)));
            demographic.setMonthOfBirth(StringUtils.leftPad(String.valueOf(birthDate.get(Calendar.MONTH) + 1), 2, "0"));
            demographic.setDateOfBirth(StringUtils.leftPad(String.valueOf(birthDate.get(Calendar.DATE)), 2, "0"));
        }

        if (patient.hasAddress()) {
            for(Address address : patient.getAddress()) {
                if (StringUtils.isEmpty(demographic.getAddress()) && !address.hasUse() ||
                        address.hasUse() && (address.getUse() == Address.AddressUse.HOME || address.getUse() == Address.AddressUse.NULL)) {

                    if (address.hasLine()) {
                        StringBuilder addressLines = new StringBuilder();
                        for (StringType addressLine : address.getLine()) {
                            addressLines.append(StringUtils.trimToEmpty(addressLine.toString())).append("\n");
                        }
                        demographic.setAddress(addressLines.toString());
                    }

                    demographic.setCity(StringUtils.trimToEmpty(address.getCity()));
                    demographic.setProvince(StringUtils.trimToEmpty(address.getState()));
                    demographic.setPostal(StringUtils.trimToEmpty(address.getPostalCode()));
                }
            }
        }



        // Only match to local patient for demographic linking, not information display on the front end
        if (matchLocalPatient) {
            PatientExternalPharmacyId patientExternalPharmacyId = patientExternalPharmacyIdRepository.getByExternalPatientIdAndPharmacyId(patientId, sourceApplicationId);
            Demographic localDemographic = null;
            if (patientExternalPharmacyId != null) {
                demographicNumber = patientExternalPharmacyId.getDemographicNo();
            }

            if (demographicNumber != null) {
                localDemographic = demographicService.getByDemographicNumber(demographicNumber);
            }

            if (localDemographic != null) {
                if (!StringUtils.trimToEmpty(demographic.getDOB()).equals(StringUtils.trimToEmpty(localDemographic.getDOB())) ||
                        !StringUtils.trimToEmpty(demographic.getHin()).equals(StringUtils.trimToEmpty(localDemographic.getHin())) ||
                        !StringUtils.trimToEmpty(demographic.getHinType()).equals(StringUtils.trimToEmpty(localDemographic.getHinType()))) {
                    patientExternalPharmacyIdRepository.delete(patientExternalPharmacyId);
                } else {
                    demographic = localDemographic;
                }
            }
        }

        return demographic;
    }

    public Patient demographicToPatient(Demographic demographic) {
        init();
        Patient patient = new Patient();
        String bundleId = demographic.getBundleId();

        // Patient.id
        patient.setId(bundleId); // ${Patient-id}

        Optional<ValueSet.ValueSetExpansionContainsComponent> hcProvince = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.CANADIAN_JURISDICTION, demographic.getHcType());
        Optional<ValueSet.ValueSetExpansionContainsComponent> healthIdentifierType = hcProvince.isPresent() ? terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.PATIENT_HEALTH_IDENTIFIER_TYPE, hcProvince.get().getDisplay()) : Optional.empty();

        // Patient.identifier (JHN/OHIP)
        if (!StringUtils.isBlank(demographic.getHin()) && healthIdentifierType.isPresent()) {
            CodeableConcept hinCoding = new CodeableConcept();
            hinCoding.addCoding(IdentifierCoding.JHN);
            Identifier hin = new Identifier();
            hin.setType(hinCoding);
            hin.setSystem(healthIdentifierType.get().getCode());
            hin.setValue(demographic.getHin());

            if (!StringUtils.isBlank(demographic.getVer())) {
                Extension phnVersion = new Extension(ErxExtensionType.Patient.PHN_VERSION.getUrl());
                phnVersion.setValue(new StringType(demographic.getVer()));
                hin.addExtension(phnVersion);
            }

            patient.addIdentifier(hin);
        }


        // Patient.identifier (MR)
        CodeableConcept medicalRecordCoding = new CodeableConcept();
        medicalRecordCoding.addCoding(IdentifierCoding.MR);
        medicalRecordCoding.setText("Local Patient ID");

        Identifier medicalRecordNo = new Identifier();
        medicalRecordNo.setType(medicalRecordCoding);
        medicalRecordNo.setSystem(clinicConfig.getApplicationId() + ".1");
        medicalRecordNo.setValue(demographic.getDemographicNumber().toString());
        patient.addIdentifier(medicalRecordNo);

        // Patient.name
        HumanName patientName = new HumanName();
        patientName.setUse(HumanName.NameUse.OFFICIAL);
        patientName.addFamily(demographic.getLastName());
        patientName.addGiven(demographic.getFirstName());

        if (StringUtils.isNotEmpty(demographic.getTitle())) {
            patientName.addPrefix(demographic.getTitle());
        }
        patient.addName(patientName);

        // Patient.telecom
        ContactPoint patientPhone = createTelecom(ContactPoint.ContactPointSystem.PHONE, demographic.getPhone(), ContactPoint.ContactPointUse.HOME);
        if (patientPhone != null) {
            patient.addTelecom(patientPhone);
        }
        String patientCellPhoneString = demographicService.getDemographicExtStringValueForKey("demo_cell", demographic.getDemographicNumber());
        ContactPoint patientCellPhone = createTelecom(ContactPoint.ContactPointSystem.PHONE, patientCellPhoneString, ContactPoint.ContactPointUse.MOBILE);
        if (patientCellPhone != null) {
            patient.addTelecom(patientCellPhone);
        }
        ContactPoint patientWorkPhone = createTelecom(ContactPoint.ContactPointSystem.PHONE, demographic.getAlternativePhone(), ContactPoint.ContactPointUse.WORK);
        if (patientWorkPhone != null) {
            patient.addTelecom(patientWorkPhone);
        }


        // Patient.gender
        patient.setGender(getAdministrativeGender(demographic.getSex()));

        // Patient.birthDate
        patient.setBirthDate(demographic.getDOBAsDate());

        // Patient.address
        Address address = createAddress(
                demographic.getAddress(), "",
                demographic.getCity(), demographic.getProvince(),
                demographic.getPostal(),
                Address.AddressUse.HOME, null);

        if (address != null) {
            patient.addAddress(address);
        }

        return patient;
    }

    /**
     * Converts {@link MedicationDispense MedicationDispense} object to a {@link DrugDispense DrugDispense}
     *
     * @param medicationDispense
     * @return {@link DrugDispense DrugDispense} object
     * @throws FHIRException
     */
    public DrugDispense medicationDispenseToDrugDispense(MedicationDispense medicationDispense) throws FHIRException {
        DrugDispense drugDispense = new DrugDispense();
        DrugDispense localDrugDispense = null;

        if (!StringUtils.isEmpty(medicationDispense.getId())) {
            try {
                //get by identifier
                Integer drugDispenseId = Integer.parseInt(medicationDispense.getIdentifier().getValue());
                drugDispense.setId(drugDispenseId);
            } catch (NumberFormatException e) { /* Not found, do nothing */ }
        }


        List<Extension> extensions = medicationDispense.getExtension();
        for (Extension extension : extensions) {
            if (extension.hasUrl() && extension.hasValue()) {
                if (extension.getUrl().equals(ErxExtensionType.Medication.RENDERED_DOSAGE_INSTRUCTION.getUrl())) {
                    // MedicationDispense.extension (rendered dosage instruction)
                    drugDispense.setInstruction(StringUtils.trimToEmpty(extension.getValue().toString()));
                } else if (extension.getUrl().equals(ErxExtensionType.Dispense.QUANTITY_REMAINING.getUrl()) && extension.getValue() instanceof Quantity) {
                    // MedicationDispense.extension (quantity remaining)
                    Quantity valueQuantity = (Quantity) extension.getValue();
                    drugDispense.setQuantityRemaining(valueQuantity.getValue().toString());
                    drugDispense.setUnitRemaining(valueQuantity.getUnit());
                }
            }
        }

        // MedicationDispense.status
        drugDispense.setStatus(medicationDispense.getStatus().toCode());

        // MedicationDispense.medicationCodeableConcept
        CodeableConcept medicationCodeableConcept = medicationDispense.getMedicationCodeableConcept();
        if (medicationCodeableConcept.hasCoding()) {
            Coding medicationCodeableConceptCoding = medicationCodeableConcept.getCoding().get(0);
            drugDispense.setRegionalIdentifier(medicationCodeableConceptCoding.getCode());
            drugDispense.setSystem(medicationCodeableConceptCoding.getSystem());
            drugDispense.setDisplayName(medicationCodeableConceptCoding.hasDisplay() ? medicationCodeableConceptCoding.getDisplay() : medicationCodeableConcept.getText());
            drugDispense.setCode(StringUtils.trimToEmpty(medicationCodeableConceptCoding.getCode()));
        } else if (medicationCodeableConcept.hasText()) {
            drugDispense.setDisplayName(medicationCodeableConcept.getText());
        }

        // MedicationDispense.dispenser
        if (medicationDispense.hasDispenser()) {
            try {
                if (medicationDispense.getDispenser().hasExtension() &&
                        medicationDispense.getDispenser().getExtension().get(0).hasValue() &&
                        medicationDispense.getDispenser().getExtension().get(0).getValue() instanceof Reference &&
                        ((Reference) medicationDispense.getDispenser().getExtension().get(0).getValue()).getResource() instanceof Organization
                ) {
                    // dispensing pharmacy Organization is assumed to be included in Bundle per PrescribeIT specifications
                    Organization dispenser = (Organization) ((Reference) medicationDispense.getDispenser().getExtension().get(0).getValue()).getResource();
                    String organizationId = dispenser.hasId() && dispenser.getId().contains("Organization/") ? dispenser.getId().substring(dispenser.getId().lastIndexOf("/") + 1) : dispenser.getId();
                    String organizationPhone = "";
                    if (dispenser.hasTelecom() && dispenser.getTelecom().get(0) != null) {
                        organizationPhone = dispenser.getTelecom().get(0).getValue();
                    }
                    drugDispense.setDispensingOrganizationName(dispenser.getName());
                    drugDispense.setDispensingOrganizationPhone(StringUtils.trimToEmpty(organizationPhone));
                    drugDispense.setDispensingOrganizationRef(organizationId);
                } else if (medicationDispense.getDispenser().hasReference()) {
                    drugDispense.setDispensingOrganizationRef(medicationDispense.getDispenser().getReference());
                }
            } catch (Exception e) {
                logger.error("Error retrieving Organization details from MedicationDispense", e);
            }
        }


        // MedicationDispense.authorizingPrescription
        Extension authorizingPrescriptionExtension = medicationDispense.getAuthorizingPrescription().get(0).getExtension().get(0);
        if (authorizingPrescriptionExtension.hasValue() && authorizingPrescriptionExtension.getValue() instanceof Identifier) {
            drugDispense.setDrugId(Integer.parseInt(((Identifier)authorizingPrescriptionExtension.getValue()).getValue()));
        }

        // MedicationDispense.quantity
        drugDispense.setQuantity(medicationDispense.getQuantity().getValue().toString());
        drugDispense.setUnit(medicationDispense.getQuantity().getUnit());


        // MedicationDispense.daysSupply
        drugDispense.setDuration(medicationDispense.getDaysSupply().getValue().toString());
        drugDispense.setDurationUnit(medicationDispense.getDaysSupply().getCode());

        // MedicationDispense.whenPrepared
        drugDispense.setPreparedDate(medicationDispense.getWhenPrepared());

        return drugDispense;
    }

    public Drug medicationOrderToDrug(MedicationOrder medicationOrder) throws FHIRException {
        Drug drug = new Drug();
        Drug localMedication = null;
        if (!StringUtils.isEmpty(medicationOrder.getId())) {
            try { // Get local drug id by local system identifier
                // Set local system Id for this clinic for getting local system identifiers from drugs
                String localPrescribeITSystemId = ClinicConfig.getInstance().getApplicationId();
                String drugIdString = RxBundleUtil.getIdentifierValueFromResource(medicationOrder, IdentifierCoding.PLAC, localPrescribeITSystemId + ".2");
                // If the drug is from our system, gets the local medication and sets the pharmacy drug system and id to the our clinic id and the drug id string
                if (drugIdString != null) {
                    Integer drugId = Integer.parseInt(drugIdString);
                    localMedication = drugRepository.findOne(drugId);
                    drug.setPharmacyDrugSystem(localPrescribeITSystemId + ".2");
                    drug.setPharmacyDrugId(drugIdString);
                } else {
                    // If the drug isn't from our system, sets the system id and drug id from the xml
                    drug.setPharmacyDrugSystem(medicationOrder.getIdentifier().get(0).getSystem());
                    drug.setPharmacyDrugId(medicationOrder.getIdentifier().get(0).getValue());
                }
            } catch (NumberFormatException e) {
                drug.setPharmacyDrugSystem(medicationOrder.getIdentifier().get(0).getSystem());
                drug.setPharmacyDrugId(medicationOrder.getIdentifier().get(0).getValue());
            }
        }
        if (localMedication != null) {
            drug.setDemographicNo(localMedication.getDemographicNo());
            drug.setScriptNo(localMedication.getScriptNo());
        }
        List<Resource> resources = medicationOrder.getContained();
        for (Resource resource : resources) {
            if (resource instanceof Medication) {
                drug.setBrandName(((Medication) resource).getCode().getText().toUpperCase());
                if (((Medication) resource).getCode().getCoding().size() > 0) {
                    Product fdbProduct = fdbService.getProductFromDrug(((Medication) resource).getCode().getCoding().get(0).getCode());
                    if (fdbProduct != null) {
                        DispensableGeneric[] generics = fdbProduct.getDispensableGenerics();
                        StringBuilder genericName = new StringBuilder();
                        for (DispensableGeneric generic : generics) {
                            if (generic != generics[0]) {
                                genericName.append(" / ");
                            }
                            genericName.append(generic.toString().toUpperCase());
                        }

                        List<String> dosages = new ArrayList<>();
                        for (IngredientStrength is : fdbProduct.getNDDFIngredientStrength()) {
                            FdbDrugSearchIngredient ingredient = new FdbDrugSearchIngredient(is);
                            dosages.add(ingredient.getStrength() + " " + ingredient.getStrengthUnit());
                        }

                        for (ProductExternalIdentifier identifier : fdbProduct.getExternalIdentifiers()) {
                            if (identifier.getExternalIdType() == ProductExternalIdType.DIN) {
                                drug.setRegionalIdentifier(identifier.getExternalID());
                                drug.setRegionalIdType(FdbService.SystemExternalIds.DIN.getUri());
                            }
                        }
                        drug.setGenericName(genericName.toString());
                        drug.setGcnSeqNo(fdbProduct.getGenericMedID());
                        drug.setDosage(String.join(" ", dosages).trim());
                    }
                }
            }
        }

        List<Extension> renderedDosageInstructionsExt = medicationOrder.getExtensionsByUrl(ErxExtensionType.Medication.RENDERED_DOSAGE_INSTRUCTION.getUrl());
        if (renderedDosageInstructionsExt != null && !renderedDosageInstructionsExt.isEmpty() && renderedDosageInstructionsExt.get(0).getValue() != null) {
           drug.setSpecial(renderedDosageInstructionsExt.get(0).getValue().toString());
        }

        List<Extension> treatmentTypesExt = medicationOrder.getExtensionsByUrl(ErxExtensionType.MedicationOrder.TREATMENT_TYPE.getUrl());
        if (treatmentTypesExt != null && !treatmentTypesExt.isEmpty() && treatmentTypesExt.get(0).getValue() != null) {
            drug.seteTreatmentType(treatmentTypesExt.get(0).getValue().toString());
        }

        List<Extension> noRenewalsExt = medicationOrder.getExtensionsByUrl(ErxExtensionType.MedicationOrder.NO_RENEWALS.getUrl());
        if (noRenewalsExt != null && !noRenewalsExt.isEmpty() && noRenewalsExt.get(0).getValue() != null) {
            drug.setNoRenewals(Boolean.parseBoolean(noRenewalsExt.get(0).getValue().toString()));
        }

        drug.setRxStatus(medicationOrder.getStatus().toCode());

        if (medicationOrder.hasPatient()) {
            Patient forPatient = (Patient) medicationOrder.getPatient().getResource();
            Integer demographicNo = retrieveDemographicNoFromRequest(forPatient, medicationOrder.getIdentifier());
            if (demographicNo != null) {
                drug.setDemographicNo(demographicNo);
            }
        }

        drug.setWrittenDate(medicationOrder.getDateWritten());

        String externalProviderId = null;
        List<Property> externalIdProperties = new ArrayList<>();
        if (medicationOrder.hasPrescriber()) {
            externalProviderId = medicationOrder.getPrescriber().getReference().substring(medicationOrder.getPrescriber().getReference().lastIndexOf("/") + 1);
            externalIdProperties = propertyService.getProperties("erx.provider.id", externalProviderId);

            if (externalIdProperties != null && !externalIdProperties.isEmpty() && externalIdProperties.get(0).getProvider() != null) {
                drug.setProviderNo(StringUtils.trimToNull(externalIdProperties.get(0).getProvider().getProviderNo()));
            }
        }

        List<DrugInstruction> instructions = new ArrayList<>();
        List<MedicationOrder.MedicationOrderDosageInstructionComponent> dosageInstructions = medicationOrder.getDosageInstruction();
        for (MedicationOrder.MedicationOrderDosageInstructionComponent dosageInstructionComponent : dosageInstructions) {
            DrugInstruction instruction = convertMedicationOrderDosageInstructionComponentToDrugInstruction(dosageInstructionComponent);

            if (dosageInstructions.indexOf(dosageInstructionComponent) == 0) {
                if (dosageInstructionComponent.hasDoseRange()) {
                    drug.setTakeMin(dosageInstructionComponent.getDoseRange().getLow().getValue().floatValue());
                    drug.setTakeMax(dosageInstructionComponent.getDoseRange().getHigh().getValue().floatValue());
                    drug.setDrugForm(dosageInstructionComponent.getDoseRange().getLow().getUnit());
                } else if (dosageInstructionComponent.hasDoseSimpleQuantity()){
                    drug.setTakeMin(dosageInstructionComponent.getDoseSimpleQuantity().getValue().floatValue());
                    drug.setTakeMax(dosageInstructionComponent.getDoseSimpleQuantity().getValue().floatValue());
                    drug.setDrugForm(dosageInstructionComponent.getDoseSimpleQuantity().getUnit());
                }

                drug.setRepeat(0);

                if (dosageInstructionComponent.getTiming().getRepeat().getDurationUnit() != null) {
                    drug.setDurationUnit(dosageInstructionComponent.getTiming().getRepeat().getDurationUnit().getDisplay().toUpperCase());
                }
            }


            instructions.add(instruction);
        }
        drug.setParsedInstructions(instructions);

        MedicationOrder.MedicationOrderDispenseRequestComponent dispenseRequest = medicationOrder.getDispenseRequest();
        Quantity quantity = dispenseRequest.getQuantity();
        if (quantity != null && !quantity.isEmpty()) {
            drug.setQuantity(quantity.getValue() != null ? quantity.getValue().toString() : "");
            drug.setQuantityUnit(quantity.getUnit() != null ? quantity.getUnit() : "");
            drug.setDispensingUnits(quantity.getUnit() != null ? quantity.getUnit() : "");
        }
        drug.setRepeat(dispenseRequest.getNumberOfRepeatsAllowed());
        drug.setRxDate(dispenseRequest.getValidityPeriod().getStart());
        drug.setEndDate(dispenseRequest.getValidityPeriod().getEnd());

        if (dispenseRequest.hasExpectedSupplyDuration()) {
            Duration duration = dispenseRequest.getExpectedSupplyDuration();
            Integer totalDays = convertToDaysByUnit(duration.getCode(), duration.getValue().intValue());
            drug.setDuration(String.valueOf(totalDays));
            drug.setDurationUnit("D");
        }

        if (medicationOrder.hasPriorPrescription()) {
            drug.setPriorRxRefId(medicationOrder.getPriorPrescription().getReference());
        }

        //drug.setSpecial(RxService.createSpecial(drug));
        drug.setArchived(false);
        drug.setCustomNote(false);
        drug.setCustomInstructions(false);
        drug.setLongTerm(false);
        drug.setPastMed(false);
        drug.setLastUpdateDate(new Date());

        List<ca.kai.rx.externalPrescriptions.prescribeIT.Task> dispenseNotifications = taskRepository.findTasksByDrugIdAndTypeInOrderByCreateDateDesc(drug.getId(),
                Collections.singletonList(TaskService.RXTaskRequest.DISPENSE_NOTIFICATION.getCode()));
        for (ca.kai.rx.externalPrescriptions.prescribeIT.Task notificationResponse : dispenseNotifications) {
            ca.kai.rx.externalPrescriptions.prescribeIT.Task matchingCancelNotification = taskRepository.findFirstByCancelDispenseTaskByDispenseTaskId(notificationResponse.getId());
            if (matchingCancelNotification != null) {
                drug.getErxNotificationDisplay().getDispenses().add(new ErxNotificationDisplay.DispenseNotification(notificationResponse, matchingCancelNotification));
            } else {
                drug.getErxNotificationDisplay().getDispenses().add(new ErxNotificationDisplay.DispenseNotification(notificationResponse));
            }
        }
        return drug;
    }

    public Medication drugToMedication(Drug drug) {
        Medication medication = new Medication();

        // Medication.id
        medication.setId("med");

        // Medication.code
        CodeableConcept code = new CodeableConcept();
        Coding coding = new Coding();

        // Medication.code.coding
        if (StringUtils.isNotEmpty(drug.getRegionalIdentifier())) {
            coding.setSystem(StringUtils.isNotEmpty(drug.getRegionalIdType())? drug.getRegionalIdType() : FdbService.SystemExternalIds.DIN.getUri());
            coding.setCode(drug.getRegionalIdentifier());
            coding.setDisplay(drug.getBrandName());

            code.addCoding(coding);

        } else if (StringUtils.isEmpty(drug.getCustomName())) {
            Map<String, String> repDrug = FdbUtils.getRepresentativeDrug(drug);

            if (!repDrug.isEmpty()) {
                Extension extMedicationCodeRepresentative = new Extension("http://prescribeit.ca/fhir/StructureDefinition/ext-medication-code-representative");
                extMedicationCodeRepresentative.setValue(new BooleanType(true));
                coding.addExtension(extMedicationCodeRepresentative);

                coding.setSystem("http://hl7.org/fhir/NamingSystem/ca-hc-din");
                coding.setCode(repDrug.get("DIN"));
                coding.setDisplay(repDrug.get("name"));

                code.addCoding(coding);
            }

            // Medication.extension.ext-medication-strength-description
            Extension extMedicationStrengthDescription = new Extension("http://prescribeit.ca/fhir/StructureDefinition/ext-medication-strength-description");
            extMedicationStrengthDescription.setValue(new StringType(drug.getDosage()));
            medication.addExtension(extMedicationStrengthDescription);
        }

        if (StringUtils.isNotEmpty(drug.getBrandName())) {
            code.setText(drug.getBrandName());
        } else {
            code.setText(drug.getName());
        }

        medication.setCode(code);

        if (StringUtils.isNotEmpty(drug.getDrugForm())) {
            Optional<ValueSet.ValueSetExpansionContainsComponent> prescriptionDrugForm = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.DRUG_FORM, drug.getDrugForm());
            Medication.MedicationProductComponent productForm = new Medication.MedicationProductComponent();
            CodeableConcept drugForm = new CodeableConcept();

            if (prescriptionDrugForm.isPresent()) {
                Coding drugFormCoding = new Coding();
                drugFormCoding.setSystem(prescriptionDrugForm.get().getSystem());
                drugFormCoding.setCode(prescriptionDrugForm.get().getCode());
                drugFormCoding.setDisplay(prescriptionDrugForm.get().getDisplay());

                drugForm.addCoding(drugFormCoding);
            }
            drugForm.setText(drug.getDrugForm());

            productForm.setForm(drugForm);
            medication.setProduct(productForm);
        }

        return medication;
    }

    public static MessageHeader messageHeaderFromErxMessageHeader(
        final ErxMessageHeader erxMessageHeader) {
        val appConfig = AppConfig.getInstance();
        val messageHeader = new MessageHeader();

        // MessageHeader.id
        messageHeader.setId(erxMessageHeader.getUuid());

        // MessageHeader.meta
        val meta = new Meta();
        meta.addProfile(erxMessageHeader.getProfileUrl());
        messageHeader.setMeta(meta);

        // MessageHeader.timestamp
        messageHeader.setTimestamp(erxMessageHeader.getTimestamp());

        // MessageHeader.event
        messageHeader.setEvent(new Coding("https://fhir.infoway-inforoute.ca/CodeSystem/sharedmessageevents", erxMessageHeader.getEventCode(), null));

        // MessageHeader.source
        MessageHeader.MessageSourceComponent source = new MessageHeader.MessageSourceComponent();
        val applicationId = new Extension("http://sharedhealth.exchange/fhir/StructureDefinition/ext-messageheader-application-id");
        applicationId.setValue(new UriType(erxMessageHeader.getSourceApplicationId()));
        source.addExtension(applicationId);

        val conformanceVersion = new Extension("http://sharedhealth.exchange/fhir/StructureDefinition/ext-messageheader-conformance-version");
        conformanceVersion.setValue(new StringType(appConfig.getConformanceVersion())); // ${conformance_version}
        source.addExtension(conformanceVersion);

        source.setName(appConfig.getVendor());
        source.setSoftware(appConfig.getVendorSoftware());
        source.setVersion(appConfig.getVendorVersion());
        source.setEndpoint(erxMessageHeader.getSourceReferenceId());

        messageHeader.setSource(source);

        // MessageHeader.destination
        messageHeader.addDestination().setEndpoint(erxMessageHeader.getDestinationReferenceId());

        for (var dataReference : erxMessageHeader.getDataReferences()) {
            messageHeader.addData(new Reference(dataReference.getDataReference()));
        }

        return messageHeader;
    }

    public PharmacyInfo pharmacyFromOrganization(Organization organization) {
        init();
        PharmacyInfo pharmacy = pharmacyInfoRepository.getByOrganizationId(organization.getIdElement().getIdPart());

        if (pharmacy == null) {
            pharmacy = new PharmacyInfo();
        }

        pharmacy.setName(organization.getName());

        Address address = organization.getAddress().get(0);
        if (address != null) {
            if (address.getLine() != null && !address.getLine().isEmpty() && address.getLine().get(0) != null) {
                pharmacy.setAddress(address.getLine().get(0).toString());
            }
            pharmacy.setCity(address.getCity());
            pharmacy.setProvince(address.getState());
            pharmacy.setPostalCode(address.getPostalCode());
        }

        for (ContactPoint telecom : organization.getTelecom()) {
            String phone = telecom.getValue();
            if (phone.substring(0, 2).equals("00")) {
                phone = phone.substring(2);
            }
            switch (telecom.getSystem()) {
                case FAX:
                    pharmacy.setFax(phone);
                    break;
                case PHONE:
                    if (StringUtils.isEmpty(pharmacy.getPhone1())) {
                        pharmacy.setPhone1(phone);
                    } else if (StringUtils.isEmpty(pharmacy.getPhone2())) {
                        pharmacy.setPhone2(phone);
                    }
                    break;
            }
        }

        pharmacy.setEmail("");
        pharmacy.setNotes("");
        pharmacy.setStatus('1');
        pharmacy.setServices(new ArrayList<>());

        Set<Extension> services = new HashSet<>(organization.getExtensionsByUrl(ErxExtensionType.Organization.SERVICE.getUrl()));
        if (!services.isEmpty()) {
            HashSet<PharmacyServiceType.Concept> serviceTypes = new HashSet<>();
            for (Extension service : services) {
                String serviceStatus = service.getExtensionsByUrl("status").isEmpty() ? null : service.getExtensionsByUrl("status").get(0).getValue().toString();
                String serviceCode = service.getExtensionsByUrl("code").isEmpty() ? null : service.getExtensionsByUrl("code").get(0).getValue().toString();
                if ("active".equals(serviceStatus) && serviceCode != null && PharmacyServiceType.Concept.getByCode(serviceCode) != null) {
                    serviceTypes.add(PharmacyServiceType.Concept.getByCode(serviceCode));
                }
            }
            for (PharmacyServiceType.Concept serviceType : serviceTypes) {
                pharmacy.getServices().add(new PharmacyServiceType(pharmacy.getId(), serviceType));
            }
        }
        pharmacy.setOrganizationId(organization.getIdElement().getIdPart());
        return pharmacy;
    }

    public Organization pharmacyToOrganization(PharmacyInfo pharmacy) {
        init();
        Organization organization = new Organization();

        // Organization.id
        organization.setId(pharmacy.getOrganizationId());

        // Organization.identifier
        Identifier identifier = new Identifier();
        identifier.setSystem("http://sharedhealth.exchange/fhir/NamingSystem/registry-id-organization");
        identifier.setValue(pharmacy.getOrganizationId());
        organization.addIdentifier(identifier);

        // Organization.type
        CodeableConcept type = new CodeableConcept();
        type.addCoding(IdentifierCoding.ROLE_CODE_OUTPHARM);
        organization.setType(type);

        // Organization.name
        organization.setName(pharmacy.getName());

        // Organization.telecom
        if (StringUtils.isNotEmpty(pharmacy.getPhone1())) {
            ContactPoint phone = createTelecom(ContactPoint.ContactPointSystem.PHONE, pharmacy.getPhone1(), ContactPoint.ContactPointUse.WORK);
            if (phone != null) {
                organization.addTelecom(phone);
            }
        }

        if (StringUtils.isNotEmpty(pharmacy.getPhone2()) && !pharmacy.getPhone2().equals(pharmacy.getPhone1())) {
            ContactPoint phone = createTelecom(ContactPoint.ContactPointSystem.PHONE, pharmacy.getPhone2(), ContactPoint.ContactPointUse.WORK);
            if (phone != null) {
                organization.addTelecom(phone);
            }
        }

        if (StringUtils.isNotEmpty(pharmacy.getFax())) {
            ContactPoint phone = createTelecom(ContactPoint.ContactPointSystem.FAX, pharmacy.getFax(), ContactPoint.ContactPointUse.WORK);
            if (phone != null) {
                organization.addTelecom(phone);
            }
        }

        // Organization.address
        Address address = createAddress(
                pharmacy.getAddress(), "",
                pharmacy.getCity(), pharmacy.getProvince(),
                pharmacy.getPostalCode(),
                null, Address.AddressType.BOTH);

        if (address != null) {
            organization.addAddress(address);
        }

        return organization;
    }

    public List<MedicationOrder> prescriptionToMedicationOrders(Prescription prescription) {
        List<MedicationOrder> orders = new ArrayList<>();
        providerConfig = ProviderConfig.getInstance(prescription.getProviderNo());
        for (Drug drug : prescription.getItems()) {
            MedicationOrder medicationOrder = convertDrugToMedicationOrder(drug, providerConfig, prescription.getDemographic().getBundleId());
            orders.add(medicationOrder);
        }

        return orders;
    }

    public MedicationOrder convertDrugToMedicationOrder(Drug drug, ProviderConfig providerConfig, String patientBundleUuid) {
        MedicationOrder medicationOrder = new MedicationOrder();
        RxInstructionParser parser = (RxInstructionParser) InstructionParser.getDefaultInstructionParser();

        // MedicationOrder.id
        medicationOrder.setId(UUID.randomUUID().toString());

        // MedicationOrder.contained.Medication
        Medication medication = drugToMedication(drug);

        if (medication != null) {
            medicationOrder.addContained(medication);
        }

        // MedicationOrder.extension (category)
        String cat = RxUtils.getCategory(drug);
        if (StringUtils.isNotEmpty(cat)) {
            Coding coding = new Coding();
            coding.setSystem("https://fhir.infoway-inforoute.ca/CodeSystem/prescriptiontype");
            coding.setCode(StringUtils.trimToNull(cat));
            coding.setDisplay(StringUtils.trimToNull(cat));
            CodeableConcept valueCodeableConcept = new CodeableConcept();
            valueCodeableConcept.addCoding(coding);
            medicationOrder.addExtension(new Extension("http://hl7.org/fhir/StructureDefinition/extension-MedicationRequest.category", valueCodeableConcept));
        }

        // MedicationOrder.extension (renderedDosageInstruction)
        StringType renderedDosageInstruction =
            new StringType(RxUtils.getInstructionsWithRouteAndSite(drug, true));
        if (!renderedDosageInstruction.isEmpty()) {
            medicationOrder.addExtension(new Extension(ErxExtensionType.Medication.RENDERED_DOSAGE_INSTRUCTION.getUrl(), renderedDosageInstruction));
        }

        // MedicationOrder.extension (treatmentType) - eTreatment type potentially set in incoming
        if (StringUtils.isNotEmpty(drug.geteTreatmentType())) {
            medicationOrder.addExtension(new Extension(ErxExtensionType.MedicationOrder.TREATMENT_TYPE.getUrl(),
                    new CodeType(drug.geteTreatmentType())));
        } else if (drug.getLongTerm()) { // If drug is long term, adds a "CHRON" treatment type extension - outgoing
            medicationOrder.addExtension(new Extension(ErxExtensionType.MedicationOrder.TREATMENT_TYPE.getUrl(),
                    new CodeType("CHRON")));
        }

        // Gets the pharmacy instructions
        String drugPharmacyInstructions = drug.getPharmacyInstructions();
        // If there are No Substitutions allow, adds a message to the pharmacy instructions
        if (drug.getNoSubs()) {
            drugPharmacyInstructions = "No Substitutions; " + StringUtils.trimToEmpty(drugPharmacyInstructions);
        }

        // MedicationOrder.extension (pharmacyInstructions)
        StringType pharmacyInstructions = new StringType(drugPharmacyInstructions);
        if (!pharmacyInstructions.isEmpty()) {
            medicationOrder.addExtension(new Extension("http://prescribeit.ca/fhir/StructureDefinition/ext-request-dispenser-instructions", pharmacyInstructions));
        }

        // MedicationOrder.extension (noRenewals)
        if (drug.getNoRenewals()) {
            medicationOrder.addExtension(new Extension(ErxExtensionType.MedicationOrder.NO_RENEWALS.getUrl(),
                    new BooleanType(drug.getNoRenewals())));
        }

        // MedicationOrder.identifier
        CodeableConcept identifierTypeCoding = new CodeableConcept();
        identifierTypeCoding.addCoding(IdentifierCoding.PLAC);

        Identifier identifier = new Identifier();
        identifier.setType(identifierTypeCoding);
        identifier.setSystem(clinicConfig.getApplicationId() + ".2"); // ${Source_application-id}.2
        // Uses the drug id for the medication id, if the drug id is null, uses the pharmacy drug id instead
        String medicationId = drug.getId() != null ? String.valueOf(drug.getId()) : drug.getPharmacyDrugId();
        identifier.setValue(medicationId); // RX_${Rx-id}
        medicationOrder.addIdentifier(identifier);

        //  MedicationOrder.status
        MedicationOrder.MedicationOrderStatus medicationOrderStatus = MedicationOrder.MedicationOrderStatus.ACTIVE;
        if (StringUtils.isNotEmpty(drug.getRxStatus())) {
            try {
                medicationOrderStatus = MedicationOrder.MedicationOrderStatus.fromCode(drug.getRxStatus());
            } catch (FHIRException fe) {
                logger.error(fe.getMessage());
            }
        }
        medicationOrder.setStatus(medicationOrderStatus);

        // MedicationOrder.medicationReference
        medicationOrder.setMedication(new Reference("#" + medicationOrder.getContained().get(0).getId()));

        // MedicationOrder.patient
        Reference patientReference = new Reference("Patient/" + patientBundleUuid); // Patient/${Patient-id}
        medicationOrder.setPatient(patientReference);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

        if (drug.getWrittenDate() != null) {
            String writtenDate = sdf.format(drug.getWrittenDate());
            // MedicationOrder.dateWritten
            medicationOrder.setDateWrittenElement(new DateTimeType(writtenDate));
        }

        // MedicationOrder.prescriber
        medicationOrder.setPrescriber(
                new Reference(appConfig.getWebserviceUrl() + appConfig.getWebserviceTprPath() + "/Practitioner/" + providerConfig.getIdentifier())); // ${Practitioner_url}

        String orderNote = "";

        if (StringUtils.isNotEmpty(drug.getComment())) {
            orderNote += drug.getComment();
        }

        ControlledSubstanceInfo controlledSubstanceInfo = drug.getControlledSubstanceInfo();
        if (controlledSubstanceInfo != null && StringUtils.isNotEmpty(controlledSubstanceInfo.getPatientIdentificationType())){
            orderNote += (StringUtils.isNotEmpty(orderNote)? " // " : "") + ("Patient ID Type: " + controlledSubstanceInfo.getPatientIdentificationType()
                                                                        + " / Patient ID Number: " + controlledSubstanceInfo.getPatientIdentificationNumber()
                                                                        + " / Practitioner ID number " + controlledSubstanceInfo.getProviderPractitionerNumber());
        }

        if (StringUtils.isNotEmpty(orderNote)){
            medicationOrder.addNote(new Annotation(new StringType(orderNote)));
        }

        List<DrugInstruction> instructions = drug.getParsedInstructions();
        boolean allowDiscrete = true;

        for (DrugInstruction instruction : instructions) {
            if (!isParsable(instruction)) {
                allowDiscrete = false;
                break;
            }
        }

        if (allowDiscrete) {
            for (DrugInstruction instruction : instructions) {
                instruction.setText(parser.modifyInstructionText(drug, instruction));

                int sequence = instructions.indexOf(instruction) + 1;
                // MedicationOrder.dosageInstruction
                MedicationOrder.MedicationOrderDosageInstructionComponent dosageInstruction = new MedicationOrder.MedicationOrderDosageInstructionComponent();

                // MedicationOrder.dosageInstruction.extension (dosageSequence)
                dosageInstruction.addExtension(new Extension("http://hl7.org/fhir/StructureDefinition/extension-Dosage.sequence", new IntegerType(sequence)));
                dosageInstruction.setText(instruction.getText());

                // MedicationOrder.dosageInstruction.timing
                Timing dosageInstructionTiming = null;

                dosageInstructionTiming = new Timing();
                // MedicationOrder.dosageInstruction.timing.repeat
                Timing.TimingRepeatComponent timingRepeat = new Timing.TimingRepeatComponent();

                if (StringUtils.isNotEmpty(instruction.getFreqCode())) {
                    convertFreqCodeToPeriod(timingRepeat, instruction);
                }

                dosageInstructionTiming.setRepeat(timingRepeat);


                if (dosageInstructionTiming != null) {
                    dosageInstruction.setTiming(dosageInstructionTiming);
                }

                // MedicationOrder.dosageInstruction.route
                if (StringUtils.isNotEmpty(drug.getRoute())) {
                    CodeableConcept route = new CodeableConcept();

                    Optional<ValueSet.ValueSetExpansionContainsComponent> routeOfAdministration = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.ROUTE_OF_ADMINISTRATION, drug.getRoute());
                    if (routeOfAdministration.isPresent()) {
                        Coding routeCoding = new Coding();
                        routeCoding.setSystem(routeOfAdministration.get().getSystem());
                        routeCoding.setCode(routeOfAdministration.get().getCode());
                        routeCoding.setDisplay(routeOfAdministration.get().getDisplay());
                        route.addCoding(routeCoding);
                    }

                    route.setText(drug.getRoute());

                    dosageInstruction.setRoute(route);
                }

                // MedicationOrder.dosageInstruction.site
                if (StringUtils.isNotEmpty(drug.getSite())) {
                    CodeableConcept site = new CodeableConcept();

                    Optional<ValueSet.ValueSetExpansionContainsComponent> administrationSite = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.ADMINISTRATION_SITE, drug.getSite());
                    if (administrationSite.isPresent()) {
                        Coding siteCoding = new Coding();
                        siteCoding.setSystem(administrationSite.get().getSystem());
                        siteCoding.setCode(administrationSite.get().getCode());
                        siteCoding.setDisplay(administrationSite.get().getDisplay());
                        site.addCoding(siteCoding);
                    }

                    site.setText(drug.getSite());
                    dosageInstruction.setSite(site);
                }

                // MedicationOrder.dosageInstruction.doseQuantity
                if (instruction.getTakeMin() != 0 && instruction.getTakeMax() != 0) {
                    // Sets the default Code and Unit Name
                    String dosageCode = "";
                    String doseSystem = "";
                    String dosageUnit = drug.getQuantityUnit() != null ? drug.getQuantityUnit() : drug.getDispensingUnits();
                    Optional<ValueSet.ValueSetExpansionContainsComponent> doseQuantityUnit = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.DOSE_QUANTITY_UNIT, dosageUnit);

                    // If a matching value is found, sets the dosage code and unit accordingly
                    if (doseQuantityUnit.isPresent()) {
                        dosageCode = doseQuantityUnit.get().getCode();
                        dosageUnit = doseQuantityUnit.get().getDisplay();
                        doseSystem = doseQuantityUnit.get().getSystem();
                    }

                    if (instruction.getTakeMin() == instruction.getTakeMax()) {
                        SimpleQuantity doseQuantity = new SimpleQuantity();
                        doseQuantity.setValue(instruction.getTakeMin());
                        doseQuantity.setUnit(dosageUnit);
                        doseQuantity.setCode(dosageCode);
                        doseQuantity.setSystem(doseSystem);
                        dosageInstruction.setDose(doseQuantity);
                    } else {
                        // Minimum dosage for range
                        SimpleQuantity minDosage = new SimpleQuantity();
                        minDosage.setValue(instruction.getTakeMin());
                        minDosage.setUnit(dosageUnit);
                        minDosage.setCode(dosageCode);
                        minDosage.setSystem(doseSystem);

                        // Maximum dosage for range
                        SimpleQuantity maxDosage = new SimpleQuantity();
                        maxDosage.setValue(instruction.getTakeMax());
                        maxDosage.setUnit(dosageUnit);
                        maxDosage.setCode(dosageCode);
                        maxDosage.setSystem(doseSystem);

                        Range doseRange = new Range();
                        doseRange.setLow(minDosage);
                        doseRange.setHigh(maxDosage);
                        dosageInstruction.setDose(doseRange);
                    }
                }
                medicationOrder.addDosageInstruction(dosageInstruction);
            }
        }

        // MedicationOrder.dispenseRequest
        MedicationOrder.MedicationOrderDispenseRequestComponent dispenseRequest = new MedicationOrder.MedicationOrderDispenseRequestComponent();

        if (drug.getRepeat() > 0) {
            dispenseRequest.setNumberOfRepeatsAllowed(drug.getRepeat());
        }

        // If drug quantity is not null and isn't empty, sets the quantity and total quantity for the medication order
        if (drug.getQuantity() != null && !drug.getQuantity().isEmpty()) {
            SimpleQuantity quantity = new SimpleQuantity();
            Quantity totalQuantity = new Quantity();

            // Gets the drug's repeats
            int repeats = drug.getRepeat() == null ? 0 : drug.getRepeat();

            // If the drug's quantity contains a decimal, then it should be parsed as a double, if not then parses it as an int
            if (drug.getQuantity().contains(".")) {
                // Parses the quantity to a Big Decimal and sets it to the quantity element for the quantity of each fill
                BigDecimal prescribedQuantity = new BigDecimal(drug.getQuantity());
                quantity.setValue(prescribedQuantity);

                // Multiples the quantity by the number of repeats and adds the prescribed quantity to get the proper unit quantity for the initial rx as well as the repeats
                BigDecimal totalPrescribedQuantity = prescribedQuantity.multiply(new BigDecimal(repeats)).add(prescribedQuantity);
                totalQuantity.setValue(totalPrescribedQuantity);
            } else {
                // Parses the quantity to an integer and sets it to the quantity element for the quantity of each fill
                int prescribedQuantity = Integer.parseInt(drug.getQuantity());
                quantity.setValue(prescribedQuantity);

                // Multiplies the quantity by the number of repeats and adds the prescribed quantity to get the proper unit quantity for the initial rx as well as the repeats
                int totalPrescribedQuantity = prescribedQuantity * repeats + prescribedQuantity;
                totalQuantity.setValue(totalPrescribedQuantity);
            }

            // Gets the prescribed quantity unit from the terminology gateway
            Optional<ValueSet.ValueSetExpansionContainsComponent> prescribedQuantityUnit = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.PRESCRIBED_QUANTITY_UNIT, drug.getQuantityUnit() != null ? drug.getQuantityUnit() : drug.getDispensingUnits());

            // Checks if the prescribed quantity unit was matched to a terminology gateway value, if so, then it uses the code and display instead of the entered text
            if (prescribedQuantityUnit.isPresent()) {
                // Sets the unit names for quantity and the total quantity with the prescribedQuantityUnit from the temrinology gateway
                quantity.setCode(prescribedQuantityUnit.get().getCode());
                quantity.setUnit(prescribedQuantityUnit.get().getDisplay());
                quantity.setSystem(prescribedQuantityUnit.get().getSystem());

                totalQuantity.setCode(prescribedQuantityUnit.get().getCode());
                totalQuantity.setUnit(prescribedQuantityUnit.get().getDisplay());
                totalQuantity.setSystem(prescribedQuantityUnit.get().getSystem());
            } else {
                // Sets the quantity and total quantity unit names
                quantity.setUnit(drug.getQuantityUnit() != null ? drug.getQuantityUnit() : drug.getDispensingUnits());
                totalQuantity.setUnit(drug.getQuantityUnit() != null ? drug.getQuantityUnit() : drug.getDispensingUnits());
            }

            // Sets the dispense request's quantity for each fill and total quantity for all fills, initial and repeats
            dispenseRequest.setQuantity(quantity);
            dispenseRequest.addExtension(new Extension(ErxExtensionType.MedicationOrder.TOTAL_QUANTITY.getUrl(), totalQuantity));
        }
        // MedicationOrder.dispenseRequest.extension (total days supply)
        Quantity totalDaysSupply = new Quantity();
        if (StringUtils.isNotEmpty(drug.getDuration())) {
            val duration = Double.valueOf(drug.getDuration()).intValue();
            // Gets the number of repeats
            int repeats = drug.getRepeat() == null ? 0 : drug.getRepeat();
            Integer totalDays = convertToDaysByUnit(drug.getDurationUnit() != null ? drug.getDurationUnit() : "d", duration);
            // Multiples the total days by the number of repeats and adds it to the current total days to get the number of days for the initial rx as well as the repeats
            totalDays += totalDays * repeats;

            totalDaysSupply.setValue(totalDays);
            totalDaysSupply.setSystem("http://unitsofmeasure.org");
            totalDaysSupply.setCode("d");

            Extension extTotalDaysSupply = new Extension(ErxExtensionType.MedicationOrder.TOTAL_DAYS_SUPPLY.getUrl(), totalDaysSupply);
            dispenseRequest.addExtension(extTotalDaysSupply);
        }

        // MedicationOrder.dispenseRequest.validityPeriod
        Period validityPeriod = new Period();
        String rxDate = sdf.format(drug.getRxDate());
        validityPeriod.setStartElement(new DateTimeType(rxDate));
        dispenseRequest.setValidityPeriod(validityPeriod);
        if (StringUtils.isNotEmpty(drug.getDuration()) && !drug.getDuration().equals("0")) {
            val duration = Double.valueOf(drug.getDuration()).intValue();
            Integer expectedDuration = convertToDaysByUnit(drug.getDurationUnit() != null ? drug.getDurationUnit() : "d", duration);
            // MedicationOrder.dispenseRequest.expectedSupplyDuration
            Duration expectedSupplyDuration = new Duration();

            expectedSupplyDuration.setValue(expectedDuration);
            expectedSupplyDuration.setSystem(totalDaysSupply.getSystem());
            expectedSupplyDuration.setCode(totalDaysSupply.getCode());

            dispenseRequest.setExpectedSupplyDuration(expectedSupplyDuration);
        }
        medicationOrder.setDispenseRequest(dispenseRequest);

        String priorDrugId = "";
        // If the drug has a prior renewal drug id, it is from a renewal request and we should use the id that is sent by the pharmacy
        // If the drug isn't from a renewal request, check if the priorRxRefId exists and if so, uses it for the priorDrugId
        if (StringUtils.isNotEmpty(drug.getPharmacyDrugId())) {
            priorDrugId = drug.getPharmacyDrugId();
        } else if (StringUtils.isNotEmpty(drug.getPriorRxRefId())) {
            priorDrugId = drug.getPriorRxRefId();
        }

        if (!priorDrugId.isEmpty()) {
            Reference priorPrescription = new Reference();

            CodeableConcept priorPrescriptionTypeCoding = new CodeableConcept();
            priorPrescriptionTypeCoding.addCoding(IdentifierCoding.PLAC);

            Identifier referenceIdentifier = new Identifier();
            referenceIdentifier.setType(priorPrescriptionTypeCoding);
            // If the pharmacy drug system exists, uses it, otherwise uses the local clinic id
            if (StringUtils.isNotEmpty(drug.getPharmacyDrugSystem())) {
                referenceIdentifier.setSystem(drug.getPharmacyDrugSystem());
            } else {
                referenceIdentifier.setSystem(clinicConfig.getApplicationId() + ".2");
            }
            referenceIdentifier.setValue(priorDrugId);

            priorPrescription.addExtension(new Extension(ErxExtensionType.Reference.IDENTIFIER.getUrl(), referenceIdentifier));
            medicationOrder.setPriorPrescription(priorPrescription);
        }

        return medicationOrder;
    }

    private Integer convertToDaysByUnit(String unit, Integer value) {
        switch (unit) {
            case "W":
                return (value * 7);
            case "M":
                return (value * 30);
            case "Y":
                return (value * 365);
        }

        return value;
    }

    private String toExternalDurationUnit(String durationUnit) {
        switch (durationUnit) {
            case "W":
                return "wk";
            case "M":
                return "mo";
            case "Y":
                return "a";
        }
        return durationUnit;
    }

    public Provider providerFromPractitioner(Practitioner practitioner) {
        init();

        HashMap<String, String> identifiers = getIdentifiers(practitioner.getIdentifier());
        String licenseNo = identifiers.getOrDefault("LN", "");
        if (licenseNo.startsWith("ON")) {
            licenseNo = licenseNo.replace("ON", "");
        }
        String cprId = identifiers.getOrDefault("PRN", "");

        Provider provider = null;
        if (StringUtils.isNotEmpty(licenseNo)) {
            provider = providerService.getByPractitionerNo(licenseNo);
        }

        if (provider == null) {
            return convertPractitionerToProvider(practitioner);
        }else {
            return provider;
        }
    }

    public Provider convertPractitionerToProvider(Practitioner practitioner){
        Provider provider = new Provider();

        HashMap<String, String> identifiers = getIdentifiers(practitioner.getIdentifier());
        String licenseNo = identifiers.getOrDefault("LN", "");
        if (licenseNo.startsWith("ON")) {
            licenseNo = licenseNo.replace("ON", "");
        }
        provider.setPractitionerNo(licenseNo);

        if (practitioner.hasName()) {
            HumanName humanName = practitioner.getName().get(0);
            provider.setLastName(humanName.getFamilyAsSingleString());
            provider.setFirstName(humanName.getGivenAsSingleString());
        }

        if (practitioner.hasTelecom()) {
            for (ContactPoint telecom : practitioner.getTelecom()) {
                if (!telecom.getSystem().toCode().equals("email")) {
                    String phoneNumber = telecom.getValue().replaceAll("\\D", "");
                    if (phoneNumber.substring(0, 2).equals("00")) {
                        phoneNumber = phoneNumber.substring(2);
                    }

                    switch (telecom.getUse()) {
                        case HOME:
                            provider.setPhone(StringUtils.trimToNull(phoneNumber));
                            break;
                        case MOBILE:
                            provider.getProviderComments().setCellPhone(phoneNumber);
                            break;
                        case WORK:
                            provider.setWorkPhone(StringUtils.trimToNull(phoneNumber));
                            break;
                        case NULL:
                            if (StringUtils.isEmpty(provider.getPhone())) {
                                provider.setPhone(StringUtils.trimToNull(phoneNumber));
                            }
                            break;
                    }
                } else {
                    provider.setEmail(StringUtils.trimToEmpty(telecom.getValue()));
                }
            }
        }

        if (practitioner.hasAddress()) {
            for(Address address : practitioner.getAddress()) {
                if (StringUtils.isEmpty(provider.getAddress()) && !address.hasUse() ||
                        address.hasUse() && (address.getUse() == Address.AddressUse.WORK || address.getUse() == Address.AddressUse.NULL)) {
                    StringBuilder addressString = new StringBuilder();
                    if (address.hasLine()) {
                        StringBuilder addressLines = new StringBuilder();
                        for (StringType addressLine : address.getLine()) {
                            addressLines.append(StringUtils.trimToEmpty(addressLine.toString())).append("\n");
                        }
                        addressString.append(addressLines.toString());
                    }

                    addressString.append(StringUtils.trimToEmpty(address.getCity())).append(", ");
                    addressString.append(StringUtils.trimToEmpty(address.getState())).append("\n");
                    addressString.append(StringUtils.trimToEmpty(address.getPostalCode()));
                    provider.setAddress(addressString.toString());
                }
            }
        }

        if (practitioner.hasPractitionerRole() && !practitioner.getPractitionerRole().isEmpty()) {
            for (Practitioner.PractitionerPractitionerRoleComponent practitionerRoleComponent : practitioner.getPractitionerRole()) {
                CodeableConcept specialty = (practitionerRoleComponent.hasSpecialty() && !practitionerRoleComponent.getSpecialty().isEmpty() && StringUtils.isEmpty(provider.getSpecialty())) ? practitionerRoleComponent.getSpecialty().get(0) : null;
                if (specialty != null) {
                    Coding specialtyCoding = specialty.hasCoding() && !specialty.getCoding().isEmpty() ? specialty.getCoding().get(0) : null;
                    if (specialtyCoding != null && specialtyCoding.getCode() != null) {
                        provider.setSpecialty(specialtyCoding.getCode());
                    }
                }
            }
        }
        return provider;
    }

    public Practitioner providerToPractitioner(final Provider provider, final String saml) {
        init();
        providerConfig = ProviderConfig.getInstance(provider.getProviderNo());
        return practitionerConverter.toFhirObject(providerConfig, provider, saml);
    }

    /**
     *     Creates a Practitioner while ignoring PrescribeIT sending data.
     *     Functionally this means:
     *     - do not use any saml (empty saml value returns null)
     *     - prepend "ON" to the provider number when assigning a license ID
     */
    public Practitioner providerToDelegatedPractitioner(Provider provider) {
        init();
        providerConfig = ProviderConfig.getInstance(provider.getProviderNo());
        return practitionerConverter.toFhirObject(providerConfig, provider, true);
    }

    public Address createAddress(String addressLine1, String addressLine2, String city, String province, String postalCode, Address.AddressUse addressUse, Address.AddressType addressType) {
        Address address = null;

        if (StringUtils.isNotEmpty(addressLine1)) {
            address = new Address();

            if (addressType != null) {
                address.setType(addressType);
            }

            if (addressUse != null) {
                address.setUse(addressUse);
            }

            address.addLine(addressLine1);
            if (StringUtils.isNotEmpty(addressLine2)) {
                address.addLine(addressLine2);
            }


            if (StringUtils.isNotEmpty(city)){
                address.setCity(city);
            }

            String locationCode = StringUtils.trimToEmpty(province);
            String subdivisionShortName = getSubdivisionShortName(locationCode);
            String countryName = getCountryName(locationCode);

            if (StringUtils.isNotEmpty(subdivisionShortName)) {
                address.setState(subdivisionShortName);
            }

            if (StringUtils.isNotEmpty(postalCode)) {
                address.setPostalCode(postalCode);
            }

            if (StringUtils.isNotEmpty(countryName)) {
                address.setCountry(countryName);
            }
        }

        return address;
    }

    /**
     * Creates a {@link ContactPoint} object from the provided phone number. If the provided number is not a
     * valid phone number, the method returns null
     * @param contactSystem The {@link ContactPoint.ContactPointSystem} to use for the ContactPoint
     * @param phoneNumber The phone number the ContactPoint describes
     * @param contactUse The {@link ContactPoint.ContactPointUse} to use for the ContactPoint
     * @return A ContactPoint from the provided phone number, null if phone number is invalid
     */
    public ContactPoint createTelecom(ContactPoint.ContactPointSystem contactSystem, String phoneNumber, ContactPoint.ContactPointUse contactUse) {
        ContactPoint phone = null;
        if (StringUtils.isNotEmpty(phoneNumber) && phoneNumber.length() >= 10) {
            phoneNumber = phoneNumber.trim();
            phoneNumber = phoneNumber.replaceAll("[^\\d.]", "");

            phone = new ContactPoint();
            phone.setSystem(contactSystem);
            phone.setUse(contactUse);

            if (phoneNumber.length() == 11) { // has long distance number
                phoneNumber = phoneNumber.substring(1);
            }
            String numberFormat = "(%s) %s-%s";
            phoneNumber = String.format(numberFormat, phoneNumber.substring(0, 3),
                    phoneNumber.substring(3, 6), phoneNumber.substring(6, 10));
            phone.setValue(phoneNumber.trim());
        }

        return phone;
    }

    public void setPractitionerRoles(Practitioner practitioner, String organizationId, String specialityCode, String providerType) {
        Practitioner.PractitionerPractitionerRoleComponent practitionerRole;
        String includePractitionerRole = propertyService.readProperty(PropertyService.ERX.INCLUDE_PRACTITIONER_ROLE.getKey());

        if (StringUtils.isNotEmpty(organizationId)) {
            practitionerRole = new Practitioner.PractitionerPractitionerRoleComponent();
            practitionerRole.setOrganization(new Reference(appConfig.getWebserviceUrl() + appConfig.getWebserviceTprPath() + "/Organization/" + organizationId));
            practitioner.addPractitionerRole(practitionerRole);
        }

        // If there is a specialty for the provider, adds in their type (if it exists) and the specialty
        // Skip if ERX_INCLUDE_PRACTITIONER_ROLE is disabled in properties (checked as string since empty should be treated as enabled)
        if (StringUtils.isNotEmpty(specialityCode) && (StringUtils.isEmpty(includePractitionerRole) || "true".equals(includePractitionerRole))) {
            practitionerRole = new Practitioner.PractitionerPractitionerRoleComponent();

            // Gets the matching component for the practitioner role value set and the provider type
            Optional<ValueSet.ValueSetExpansionContainsComponent> role = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.PRACTITIONER_ROLE, providerType);
            // If a role was found, populates a CodeableComponent and adds it to the role component
            if (role.isPresent()) {
                CodeableConcept practitionerRoleCoding = new CodeableConcept();
                Coding roleCoding = new Coding();
                roleCoding.setSystem(role.get().getSystem());
                roleCoding.setCode(role.get().getCode());
                roleCoding.setDisplay(role.get().getDisplay());
                practitionerRoleCoding.addCoding(roleCoding);
                practitionerRole.setRole(practitionerRoleCoding);
            }

            CodeableConcept specialty = new CodeableConcept();
            specialty.setText(specialityCode);
            practitionerRole.addSpecialty(specialty);

            // Adds the role component to the practitioner element
            practitioner.addPractitionerRole(practitionerRole);
        }
    }

    /**
     * Creates the Observation element for the patient's Height and Weight
     *
     * @param measurement The recorded measurement that the observation is being created for
     * @param observationId The generated uuid for the observation
     * @param patientId The patient elements id/uuid
     * @return A populated Observation element
     */
    public Observation createObservation(Measurement measurement, String observationId, String patientId) {
        Observation observation = new Observation();

        // Sets the observation id with the provider uuid
        observation.setId(observationId);
        // Sets the observation status to final
        observation.setStatus(Observation.ObservationStatus.FINAL);
        // Sets the patient id as the subject of the observation
        observation.setSubject(new Reference("Patient/" + patientId));
        // Gets the observation date of the measurement and replaces any spaces with a T for the Time

        // Creates a SimpleDateFormat and formats the observation date
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        String observationDate = sdf.format(measurement.getDateObserved());

        // Creates the date time type for the observation date, setting the date/time and the timezone
        DateTimeType observationsDateTime = new DateTimeType(observationDate);

        // Sets the date/time that the measurement was observed on
        observation.setEffective(observationsDateTime);

        // Creates and adds the vital-signs Category to the observation
        CodeableConcept observationCategory = new CodeableConcept();
        observationCategory.addCoding(IdentifierCoding.OBSERVATION_CATEGORY_VITAL_SIGNS);
        observation.setCategory(observationCategory);

        // Creates and prepares the coding element for the Observation
        CodeableConcept code = new CodeableConcept();
        Coding codeCoding = new Coding();
        codeCoding.setSystem("https://fhir.infoway-inforoute.ca/CodeSystem/pCLOCD");

        // Creates and prepares the Observation Quantity
        Quantity quantity = new Quantity();
        quantity.setSystem("http://unitsofmeasure.org");
        quantity.setValue(Double.parseDouble(measurement.getDataField()));


        // Sets the code for the observation (8302-2 = Height, 29463-7 = Weight)
        // Also sets the quantity unit code; Height is in cm and Weight is in kg
        if (measurement.getType().equals("HT")) {
            codeCoding.setCode("8302-2");
            quantity.setCode("cm");
        } else {
            codeCoding.setCode("29463-7");
            quantity.setCode("kg");
        }
        // Adds the coding to the code
        code.addCoding(codeCoding);

        // Sets the completed code and quantity elements for the Observation
        observation.setCode(code);
        observation.setValue(quantity);

        return observation;
    }



    private Enumerations.AdministrativeGender getAdministrativeGender(String gender) {
        Enumerations.AdministrativeGender administrativeGender = Enumerations.AdministrativeGender.NULL;
        gender = StringUtils.trimToEmpty(gender).toUpperCase();

        if (!gender.isEmpty()) {
            if ("M".equals(gender.substring(0, 1))) {
                administrativeGender = Enumerations.AdministrativeGender.MALE;
            } else if ("F".equals(gender.substring(0, 1))) {
                administrativeGender = Enumerations.AdministrativeGender.FEMALE;
            } else if ("O".equals(gender.substring(0, 1)) || "T".equals(gender.substring(0, 1))) {
                administrativeGender = Enumerations.AdministrativeGender.OTHER;
            } else if ("U".equals(gender.substring(0, 1))) {
                administrativeGender = Enumerations.AdministrativeGender.UNKNOWN;
            }
        }

        return administrativeGender;
    }

    private String getCountryName(String locationCode) {
        String countryName = "";
        if (StringUtils.isNotEmpty(locationCode)) {

            if (!locationCode.contains("-")) {
                locationCode = LocationsService.DEFAULT_COUNTRY_CODE + "-" + locationCode;
            }

            countryName = locationsService.getCountryNameFromCode(locationCode);
        }
        return countryName;
    }

    public static HashMap<String, String> getIdentifiers(List<Identifier> identifiers) {
        HashMap<String, String> patientIdentifiers = new HashMap<String, String>();

        for (Identifier identifier :  identifiers) {
            CodeableConcept identifierType = identifier.getType();
            if (!identifierType.getCoding().isEmpty() && StringUtils.isNotEmpty(identifierType.getCoding().get(0).getCode())) {
                patientIdentifiers.put(identifierType.getCoding().get(0).getCode(), StringUtils.trimToEmpty(identifier.getValue()));
            }
        }

        return patientIdentifiers;
    }

    private String getSubdivisionShortName(String locationCode) {
        String subdivisionShortName = "";
        if (StringUtils.isNotEmpty(locationCode)) {

            if (!locationCode.contains("-")) {
                locationCode = LocationsService.DEFAULT_COUNTRY_CODE + "-" + locationCode;
            }

            subdivisionShortName = locationsService.getSubdivisionShortNameFromCode(locationCode);
        }
        return subdivisionShortName;
    }

    private void convertFreqCodeToPeriod(Timing.TimingRepeatComponent timingRepeat, DrugInstruction instruction) {
        Integer period = RxUtils.getPeriod(instruction.getFreqCode());
        Integer frequencyMax = RxUtils.getFrequencyMax(instruction.getFreqCode());
        String periodUnit = RxUtils.getPeriodUnit(instruction.getFreqCode());
        Integer freqValue = RxUtils.getFrequency(instruction.getFreqCode());

        if (period != null) {
            timingRepeat.setPeriod(period);
            try {
                timingRepeat.setPeriodUnit(Timing.UnitsOfTime.fromCode(periodUnit));
            } catch (FHIRException e) {
                timingRepeat.setPeriodUnit(Timing.UnitsOfTime.NULL);
                logger.error("Error setting Period Unit for prescription dosage instructions.", e);
            }

            if (frequencyMax != null) {
                timingRepeat.setFrequencyMax(frequencyMax);
            }

            timingRepeat.setFrequency(freqValue);
        }
    }

    private DrugInstruction convertMedicationOrderDosageInstructionComponentToDrugInstruction(MedicationOrder.MedicationOrderDosageInstructionComponent dosageInstructionComponent) {
        DrugInstruction drugInstruction = new DrugInstruction();

        try {
            if (dosageInstructionComponent != null) {
                drugInstruction.setText(dosageInstructionComponent.getText());
                if (dosageInstructionComponent.hasDoseRange()) {
                    drugInstruction.setTakeMin(dosageInstructionComponent.getDoseRange().getLow().getValue().floatValue());
                    drugInstruction.setTakeMax(dosageInstructionComponent.getDoseRange().getHigh().getValue().floatValue());
                } else if (dosageInstructionComponent.hasDoseSimpleQuantity()){
                    drugInstruction.setTakeMin(dosageInstructionComponent.getDoseSimpleQuantity().getValue().floatValue());
                    drugInstruction.setTakeMax(dosageInstructionComponent.getDoseSimpleQuantity().getValue().floatValue());
                }

                if (dosageInstructionComponent.getTiming().getRepeat().getDuration() != null) {
                    drugInstruction.setDuration(dosageInstructionComponent.getTiming().getRepeat().getDuration().toString());
                    drugInstruction.setDurationUnit(dosageInstructionComponent.getTiming().getRepeat().getDurationUnit().getDisplay().toUpperCase());
                }
            }
        } catch (FHIRException fe) {
            fe.printStackTrace();
            drugInstruction = new DrugInstruction();
        }

        return drugInstruction;
    }

    public String encodeSpecialCharacters(String messageBody) {
        messageBody = messageBody.replaceAll("\n", "&#xD;&#xA;");
        messageBody = messageBody.replaceAll("\t", "&#x9;");
        messageBody = messageBody.replaceAll("\r", "&#xD;");
        return messageBody;
    }

    /**
     * This method will attempt to link an incoming request to a local demographic by the following logic:
     *
     * 1. If the 'local' pharmacy patient ID and source application exist as a unique key in the 'patient_external_pharmacy_id' table, retrieve demographic from there
     * 2. Retrieve the local prescription number from the Medication Order, and if exists, retrieve the demographic number from there
     * 3. Retrieve the Patient bundles HIN information if exists, and look up the demographic in the local database by that.
     *
     * @param patient The FHIR Patient object from the request
     * @return The demographic number of the patient in the local database
     */
    public Integer retrieveDemographicNoFromRequest(Patient patient, List<Identifier> medicationIdentifiers) {
        String patientId, systemId, patientHin;
        int drugId = -1;
        HashMap<String, String> identifiers = getExternalIdAndSourceApplication(patient);
        patientId = identifiers.getOrDefault("patientId", null);
        systemId = identifiers.getOrDefault("sourceApplication", null);
        patientHin = identifiers.getOrDefault("patientHin", null);


        if (patientId != null && systemId != null) {
            systemId = systemId.replaceAll("urn:oid:", "");
            PatientExternalPharmacyId patientExternalRecord = patientExternalPharmacyIdRepository.getByExternalPatientIdAndPharmacyId(patientId, systemId);
            if (patientExternalRecord != null) {
                return patientExternalRecord.getDemographicNo();
            } else {
                String neededSystemId = ClinicConfig.getInstance().getApplicationId() + ".2";
                Optional<String> matchedIdentifierValue = RxBundleUtil.getIdentifierValueFromIdentifierList(medicationIdentifiers, IdentifierCoding.PLAC, neededSystemId);
                if (matchedIdentifierValue.isPresent() && StringUtils.isNumeric (matchedIdentifierValue.get())) {
                    try {
                        drugId = Integer.parseInt(matchedIdentifierValue.get());
                    } catch (NumberFormatException nfe) {
                        nfe.printStackTrace();
                    }
                }

                if (drugId != -1) {
                    Drug drug = drugRepository.findOne(drugId);
                    if (drug != null && drug.getDemographicNo() != null) {
                        return drug.getDemographicNo();
                    }
                }

                if (patientHin != null) {
                    Demographic demographic = demographicRepository.getByHin(patientHin);
                    if (demographic != null) {
                        return demographic.getDemographicNumber();
                    }
                }
            }
        }
        return null;
    }

    /**
     * This information will take a Patient object and extract necessary attributes from it in order to link to a demographic record
     *
     * @param patient Patient object from the request to retrieve information from
     * @return HashMap containing the patient ID, source application ID and patient HIN from the XML
     */
    public HashMap<String, String> getExternalIdAndSourceApplication(Patient patient) {
        HashMap<String, String> identifiers = new HashMap<>();
        List<Identifier> patientIdentifiers = patient.getIdentifier();
        String JHN = "";
        String jhnIdentifierText = "";
        for (Identifier identifier : patientIdentifiers) {
            if (identifier.hasType() && identifier.getType().hasCoding()) {
                for (Coding coding : identifier.getType().getCoding()) {
                    if (coding.hasCode()) {
                        switch (coding.getCode()) {
                            case "MR":
                                identifiers.put("patientId", identifier.getValue());
                                identifiers.put("sourceApplication", identifier.getSystem());
                                break;
                            case "OHIP":
                                identifiers.put("patientHin", identifier.getValue());
                                break;
                            case "JHN":
                                JHN = identifier.getValue();
                                if (identifier.getType() != null && StringUtils.isNotBlank(identifier.getType().getText())) {
                                    jhnIdentifierText = identifier.getType().getText();
                                }
                        }
                    }
                }
            }
        }
        if (!identifiers.containsKey("patientHin") && !JHN.isEmpty()) {
            identifiers.put("patientHin", JHN);
            identifiers.put("jhnType", jhnIdentifierText);
        }
        return identifiers;
    }

    /**
     * Using the provided task, gets the appropriate reason entry from the Terminology Gateway
     * Gets the related entry for Denied and Under Review renewal request responses
     * @param task Task to get the appropriate reason for based on the task's type and the selected reason code
     * @return Optional Value Set containing the matching TG entry
     */
    public Optional<ValueSet.ValueSetExpansionContainsComponent> getTgTaskReason(ca.kai.rx.externalPrescriptions.prescribeIT.Task task) {
        Optional<ValueSet.ValueSetExpansionContainsComponent> taskReason = Optional.empty();
        // If the task type is Deny, gets the coded value from the Terminology Gate for Renewal Reject Reasons
        // If the task type is Under Review,. gets the coded value from the Terminology Gateway for Renewal Review Reasons
        if (task.getType().equals(RxFillRequestService.RXTaskRequest.RESPONSE_DENIED.getCode())) {
            taskReason = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.RENEWAL_REJECT_REASON, task.getReason());
        } else if (task.getType().equals(RxFillRequestService.RXTaskRequest.RESPONSE_UNDER_REVIEW.getCode())) {
            taskReason = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.RENEWAL_REVIEW_REASON, task.getReason());
        } else if (task.getType().equals(RxFillRequestService.RXTaskRequest.CANCEL.getCode())) {
            taskReason = terminologyGateway.getMatchingTgValue(TerminologyGatewayValueSet.CANCEL_REASON, task.getReason());
        }

        return taskReason;
    }

    private String readProperty(String propertyName, Provider provider) {
        if (provider == null) {
            return "";
        }
        return propertyService.readProperty(propertyName, provider.getProviderNo());
    }

    private String getClinicOrProviderProperty(
        final String clinicProperty,
        final Provider provider,
        final String propertyName
    ) {
        val property = readProperty(propertyName, provider);
        if (isProviderPreferenceEnabled() && !StringUtils.trimToEmpty(property).isEmpty()) {
            return property;
        }
        return clinicProperty;
    }

    private Address getClinicOrProviderAddress(final Clinic clinic, final Provider provider) {
        val providerAddress = readProperty(RX_ADDRESS, provider);
        if (isProviderPreferenceEnabled() && !StringUtils.trimToEmpty(providerAddress).isEmpty()) {
            return createProviderAddress(provider);
        }
        return createClinicAddress(clinic);
    }

    private Address createProviderAddress(final Provider provider) {
        return createAddress(
            propertyService.readProperty(RX_ADDRESS, provider.getProviderNo()),
            "",
            propertyService.readProperty(RX_CITY, provider.getProviderNo()),
            propertyService.readProperty(RX_PROVINCE, provider.getProviderNo()),
            propertyService.readProperty(RX_POSTAL, provider.getProviderNo()),
            null,
            Address.AddressType.BOTH);
    }

    private Address createClinicAddress(final Clinic clinic) {
        return createAddress(
            clinic.getAddress(),
            "",
            clinic.getCity(),
            clinic.getProvince(),
            clinic.getPostalCode(),
            null,
            Address.AddressType.BOTH);
    }

    private boolean isProviderPreferenceEnabled() {
        return systemPreferenceService.readBooleanPreference(ERX_USE_PROVIDER_PROPERTIES);
    }
}
