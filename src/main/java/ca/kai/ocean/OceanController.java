package ca.kai.ocean;

import ca.kai.OscarProperties;
import ca.kai.applicationcontext.ApplicationContextProvider;
import ca.kai.systemPreference.SystemPreferenceService;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import javax.annotation.PostConstruct;
import lombok.val;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

@RequestMapping("/api/v1/ocean")
@RestController
public class OceanController {

  private final RestTemplate restTemplate;

  private static final List<String> OCEAN_PREFERENCE_NAMES =
      Arrays.asList(
          "attachment_manager.ocean.warning_dismiss", "attachment_manager.ocean.warning_message");
  private static OscarProperties properties;

  private final SystemPreferenceService systemPreferenceService;

  @Autowired
  public OceanController(RestTemplate restTemplate, SystemPreferenceService systemPreferenceService) {
    this.restTemplate = restTemplate;
    this.systemPreferenceService = systemPreferenceService;
  }

  @PostConstruct
  void postConstruct() {
    properties = ApplicationContextProvider.getBean(OscarProperties.class);
  }

  @GetMapping(path = "/enabled", produces = MediaType.APPLICATION_JSON_VALUE)
  public boolean isOceanEnabled() {
    var toolbarType = systemPreferenceService.readString("echart_toolbar_type", "");
    if (toolbarType.isEmpty()) {
      toolbarType = properties.getProperty("cme_js", "");
    }
    return "ocean".equals(toolbarType);
  }

  @GetMapping(path = "/preferences", produces = MediaType.APPLICATION_JSON_VALUE)
  public String getOceanPreferences() {
    return systemPreferenceService.getByNamesAsJsonMap(OCEAN_PREFERENCE_NAMES);
  }

  @GetMapping(path = "/host", produces = MediaType.TEXT_PLAIN_VALUE)
  public String getOceanHost() {
    return properties.getProperty("ocean_host");
  }

  /**
   * Hits the relevant Ocean endpoint to create a token to be used to authenticate with Ocean serivces
   * @param oceanBasePath - Base path to hit against
   * @param siteCredential - Part of the credentials to use for authentication
   * @param siteKey - Part of the credentials to use for authentication
   * @param siteNum - Site number used as part of the credentials for authentication
   * @param externalRef - Usually the demographic number/patient id
   * @param providerName - The provider name you are authenticating
   * @return
   */
  @PostMapping("/createEmrUserToken")
  public ResponseEntity<String> createEmrUserToken(
      @RequestParam String oceanBasePath,
      @RequestParam String siteCredential,
      @RequestParam String siteKey,
      @RequestParam String siteNum,
      @RequestParam String externalRef,
      @RequestParam(required = false) String providerName) {
    try {
      var url =
          oceanBasePath
              + "/api/createEmrUserToken?"
              + "uploadPtExternalRef="
              + URLEncoder.encode(externalRef, StandardCharsets.UTF_8.name());

      if (providerName != null && !providerName.isEmpty()) {
        url += "&userName=" + URLEncoder.encode(providerName, StandardCharsets.UTF_8.name());
      }

      if (siteNum != null && !siteNum.isEmpty()) {
        url += "&siteNum=" + URLEncoder.encode(siteNum, StandardCharsets.UTF_8.name());
      }

      url += "&v=1&client=oscar";

      val headers = new HttpHeaders();
      headers.set("sitePassword", siteCredential);
      headers.set("siteKey", siteKey);

      val entity = new HttpEntity<>(null, headers);
      val response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

      return ResponseEntity.status(response.getStatusCode()).body(response.getBody());
    } catch (Exception exception) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(exception.getMessage());
    }
  }
}
