package ca.kai.eForm;


import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Data
@Table(name = "eform")
public class EFormTemplate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "fid")
    private Integer id;
    @Column(name = "form_name")
    private String formName;
    @Column(name = "file_name")
    private String fileName;
    @Column(name = "subject")
    private String subject;
    @Column(name = "form_date")
    private Date formDate = new Date();
    @Column(name = "form_time")
    private Date formTime = formDate;
    @Column(name = "form_creator")
    private String creator;
    @Column(name = "status")
    private boolean status;
    @Column(name = "form_html")
    private String formHtml;
    @Column(name = "showLatestFormOnly")
    private boolean showLatestFormOnly;
    @Column(name = "patient_independent")
    private boolean patientIndependent;
    @Column(name = "roleType")
    private String roleType;
}
