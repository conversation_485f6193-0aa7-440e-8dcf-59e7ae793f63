package ca.kai.fhir.converter.r4;

import static ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls.HRM_SUBCLASS_URL;
import static ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls.ISSUE_ACUTE;
import static ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls.ISSUE_CERTAIN;
import static ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls.ISSUE_MAJOR;
import static ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls.ISSUE_RESOLVED;
import static ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls.ISSUE_TYPE;
import static ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls.RESPONSIBLE;
import static ca.kai.fhir.resource.r4.PatientResourceProvider.getHcTypeFromExtension;
import static ca.kai.fhir.resource.r4.PatientResourceProvider.getJhnIdentifier;
import static ca.kai.fhir.utils.GatewayFhirUtils.isGatewayResource;
import static java.util.Collections.singletonList;
import static org.apache.commons.io.FileUtils.readFileToByteArray;

import ca.kai.attachmentmanager.printable.Printable;
import ca.kai.attachmentmanager.printable.PrintableService;
import ca.kai.caseManagementNote.CaseManagementIssue;
import ca.kai.caseManagementNote.CaseManagementIssueRepository;
import ca.kai.caseManagementNote.CaseManagementNote;
import ca.kai.caseManagementNote.CaseManagementNoteExt;
import ca.kai.caseManagementNote.CaseManagementNoteExtKey;
import ca.kai.caseManagementNote.CaseManagementNoteExtRepository;
import ca.kai.caseManagementNote.CaseManagementNoteLink;
import ca.kai.caseManagementNote.CaseManagementNoteLinkRepository;
import ca.kai.caseManagementNote.IssueRepository;
import ca.kai.ctlDocument.CtlDocumentRepository;
import ca.kai.demographic.DemographicRepository;
import ca.kai.document.Document;
import ca.kai.document.DocumentRepository;
import ca.kai.document.DocumentService;
import ca.kai.eForm.EForm;
import ca.kai.eForm.EFormRepository;
import ca.kai.fhir.converter.CodeSystemUrlConstants;
import ca.kai.fhir.converter.DemographicIdentifierData;
import ca.kai.fhir.converter.LanguageConstants;
import ca.kai.fhir.converter.r4.extension.DocumentReferenceExtensionUrls;
import ca.kai.fhir.exception.UnmatchedRemoteRecordException;
import ca.kai.hrm.HRMDocument;
import ca.kai.hrm.HRMReport;
import ca.kai.hrm.util.HRMParser;
import ca.kai.patientLabRouting.PatientDocumentRoutingRepository;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderRepository;
import ca.uhn.fhir.rest.server.exceptions.InvalidRequestException;
import health.apps.gateway.common.configuration.GWConfigurationService;
import java.io.File;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import lombok.val;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.hl7.fhir.r4.model.Attachment;
import org.hl7.fhir.r4.model.BooleanType;
import org.hl7.fhir.r4.model.CodeableConcept;
import org.hl7.fhir.r4.model.DateTimeType;
import org.hl7.fhir.r4.model.DateType;
import org.hl7.fhir.r4.model.DocumentReference;
import org.hl7.fhir.r4.model.DocumentReference.DocumentReferenceContentComponent;
import org.hl7.fhir.r4.model.DocumentReference.DocumentReferenceContextComponent;
import org.hl7.fhir.r4.model.DocumentReference.ReferredDocumentStatus;
import org.hl7.fhir.r4.model.Enumerations.DocumentReferenceStatus;
import org.hl7.fhir.r4.model.Extension;
import org.hl7.fhir.r4.model.Group;
import org.hl7.fhir.r4.model.IdType;
import org.hl7.fhir.r4.model.Identifier;
import org.hl7.fhir.r4.model.IntegerType;
import org.hl7.fhir.r4.model.Meta;
import org.hl7.fhir.r4.model.Patient;
import org.hl7.fhir.r4.model.Practitioner;
import org.hl7.fhir.r4.model.Reference;
import org.hl7.fhir.r4.model.ResourceType;
import org.hl7.fhir.r4.model.StringType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component(value = "documentReferenceConverterR4")
public class DocumentReferenceConverter extends BaseConverter {

  private static final Logger LOG = LoggerFactory.getLogger(DocumentReferenceConverter.class);
  public static final String CATEGORY_SYSTEM = valueSystem("document-reference-category");
  public static final String NOTES_CODE = "note";
  public static final String DOCUMENTS_CODE = "document";
  public static final String HRM_CODE = "hrm";
  public static final String CATEGORY_ISSUE = valueSystem("document-reference-note-issue");
  public static final String HRM_SYSTEM = valueSystem("hospital-report-manager");

  private static final String DOCUMENT_STATUS_ACTIVE = "A";
  private static final String DOCUMENT_STATUS_DELETED = "D";
  private static final String LOINC_CONSULT_NOTE_CODE = "11488-4";
  private static final String LOINC_CONSULT_NOTE_CODE_DISPLAY = "Consult note";
  public static final String DOC_TYPE = "docType";
  public static final String DOC_CLASS = "docClass";
  public static final String DOC_SUB_CLASS = "docSubClass";
  public static final String SOURCE = "source";
  public static final String ABNORMAL = "abnormal";
  public static final String DISCRIMINATOR = "discriminator";
  public static final String EFORM_CODE = "EFORM";

  @Autowired private DemographicRepository demographicRepository;
  @Autowired private ProviderRepository providerRepository;
  @Autowired private CtlDocumentRepository ctlDocumentRepository;

  @Autowired private PatientDocumentRoutingRepository patientDocumentRoutingRepository;
  @Autowired private DocumentService documentService;
  @Autowired private CaseManagementNoteExtRepository caseManagementNoteExtRepository;
  @Autowired private IssueRepository issueRepository;
  @Autowired private CaseManagementIssueRepository caseManagementIssueRepository;
  @Autowired private EncounterConverter encounterConverter;
  @Autowired private GWConfigurationService gwConfigurationService;
  @Autowired
  private CaseManagementNoteLinkRepository caseManagementNoteLinkRepository;
  @Autowired
  private DocumentRepository documentRepository;
  @Autowired private PrintableService printableService;
  @Autowired private EFormRepository eformRepository;

  public DocumentReference toFhirObject(Document oscar) {
    LOG.trace("toFhirObject");
    if (oscar == null) {
      LOG.warn("document is null.");
      return null;
    }

    val fhir = new DocumentReference();
    setIntegerField(oscar::getDocumentNo, id -> fhir.setId(new IdType(id)));
    fhir.addIdentifier().setSystem(GUID_SYSTEM).setValue(oscar.getGuid());
    setCodable(CATEGORY_SYSTEM, DOCUMENTS_CODE, () -> DISCRIMINATOR, fhir::addCategory);
    setCodable(CATEGORY_SYSTEM, DOC_TYPE, oscar::getDocType, fhir::addCategory);
    setCodable(CATEGORY_SYSTEM, DOC_CLASS, oscar::getDocClass, fhir::addCategory);
    addExt(oscar.getDocClass(), DocumentReferenceExtensionUrls.DOC_CLASS, fhir::addExtension);
    setCodable(CATEGORY_SYSTEM, DOC_SUB_CLASS, oscar::getDocSubClass, fhir::addCategory);
    addExt(
        oscar.getDocSubClass(), DocumentReferenceExtensionUrls.DOC_SUB_CLASS, fhir::addExtension
    );
    setCodable(CATEGORY_SYSTEM, SOURCE, oscar::getSource, fhir::addCategory);
    setBoolCodable(CATEGORY_SYSTEM, ABNORMAL, oscar::getAbnormal, fhir::addCategory);
    addExt(oscar.getAbnormal(), DocumentReferenceExtensionUrls.ABNORMAL, fhir::addExtension);
    setStringField(oscar::getDocDesc, fhir::setDescription);

    val attachment = new Attachment();
    val data = documentService.getDocumentBytes(oscar, null);
    attachment.setData(data);
    setStringField(oscar::getDocFileName, attachment::setTitle);
    setStringField(oscar::getContentType, attachment::setContentType);
    setDateField(oscar::getContentDateTime, attachment::setCreation);
    attachment.setSize(data.length);

    val contentComponent = new DocumentReferenceContentComponent();
    contentComponent.setAttachment(attachment);
    fhir.addContent(contentComponent);

    val author = oscar.getDocCreator();
    toReference(
        ResourceType.Practitioner,
        () -> author == null ? null : author.getFormattedName(),
        () -> author == null ? null : author.getProviderNo(),
        fhir::addAuthor);

    // responsible
    toReference(ResourceType.Practitioner, oscar::getResponsible, null, fhir::getAuthenticator);
    addExt(oscar.getResponsible(), DocumentReferenceExtensionUrls.RESPONSIBLE, fhir::addExtension);
    // source
    addExt(oscar.getSource(), DocumentReferenceExtensionUrls.SOURCE, fhir::addExtension);
    // sourceFacility
    addExt(
        oscar.getSourceFacility(),
        DocumentReferenceExtensionUrls.SOURCE_FACILITY,
        fhir::addExtension
    );
    toReference(ResourceType.Organization, oscar::getSourceFacility, null, fhir::getCustodian);
    // programId
    val programId = oscar.getProgramId();
    if (programId != null) {
      addExt(
          new Reference(Group.class.getSimpleName() + "/" + programId),
          DocumentReferenceExtensionUrls.PROGRAM_ID,
          fhir::addExtension
      );
    }

    // updateDateTime
    fhir.getMeta().setLastUpdated(oscar.getUpdateDateTime());
    // status
    fhir.setStatus(toFhirDocStatus(oscar.getStatus()));
    // observationDate
    addExt(
        oscar.getObservationDate(),
        DocumentReferenceExtensionUrls.OBSERVATION_DATE,
        fhir::addExtension
    );
    setDateField(oscar::getObservationDate, fhir::setDate);
    // reportMedia
    addExt(oscar.getReportMedia(), DocumentReferenceExtensionUrls.REPORT_MEDIA, fhir::addExtension);
    // documentPublic
    addExt(oscar.getDocumentPublic(), DocumentReferenceExtensionUrls.PUBLIC1, fhir::addExtension);
    // reviewer
    addExt(oscar.getReviewer(), DocumentReferenceExtensionUrls.REVIEWER, fhir::addExtension);
    // reviewDateTime
    addExt(oscar.getReviewDateTime(), DocumentReferenceExtensionUrls.REVIEW_DATE_TIME,
        fhir::addExtension);
    // numberOfPages
    addExt(oscar.getNumberOfPages(), DocumentReferenceExtensionUrls.NUMBER_OF_PAGES,
        fhir::addExtension);
    // routed patient to subject
    val routing = patientDocumentRoutingRepository.getAllPatientDocumentRoutingsByDocumentId(
        oscar.getDocumentNo());
    if (routing != null && !routing.isEmpty()) {
      val demographic = routing.get(0).getDemographic();
      toReference(
          ResourceType.Patient,
          () -> demographic == null ? null : demographic.getFormattedName(),
          () -> demographic == null ? null : demographic.getDemographicNumber().toString(),
          fhir::getSubject);
    } else {
      val ctlDocument = ctlDocumentRepository.getById_DocumentNo(oscar.getDocumentNo());
      val ctlStatus = ctlDocument == null ? null : ctlDocument.getStatus();
      addExt(ctlStatus, DocumentReferenceExtensionUrls.CTL_STATUS, fhir::addExtension);
    }
    // appointmentNo
    val context = fhir.getContext();
    toReference(
        ResourceType.Encounter,
        () -> oscar.getAppointmentNo() == null ? null : oscar.getAppointmentNo().toString(),
        () -> oscar.getAppointmentNo() == null ? null : oscar.getAppointmentNo().toString(),
        context::addEncounter);
    return fhir;
  }

  public Document toOscarObject(DocumentReference docRef) {

    if (docRef == null) {
      LOG.warn("documentReference is null.");
      return null;
    }

    val document = new Document();
    // documentNo
    val documentId = docRef.getIdElement().getIdPart();
    if (org.apache.commons.lang3.StringUtils.isNumeric(documentId)) {
      document.setDocumentNo(Integer.parseInt(documentId));
    }
    //guid
    val docGuid = getIdentifierValue(docRef.getIdentifier(), GUID_SYSTEM);
    document.setGuid(docGuid != null ? docGuid : document.getGuid());
    // document type, class and subclass from category
    val docTypeCategory = getDocumentReferenceCategoryByCode(DOC_TYPE, docRef);
    if (docTypeCategory != null) {
      setStringField(docTypeCategory::getText, document::setDocType);
    }
    val docClassCategory = getDocumentReferenceCategoryByCode(DOC_CLASS, docRef);
    setStringField(
        () -> getStringFromCategoryOrExtension(
            docClassCategory, DocumentReferenceExtensionUrls.DOC_CLASS, docRef),
        document::setDocClass
    );
    val docSubClassCategory = getDocumentReferenceCategoryByCode(DOC_SUB_CLASS, docRef);
    setStringField(
        () -> getStringFromCategoryOrExtension(
            docSubClassCategory, DocumentReferenceExtensionUrls.DOC_SUB_CLASS, docRef),
        document::setDocSubClass
    );
    val sourceCategory = getDocumentReferenceCategoryByCode(SOURCE, docRef);
    setStringField(
        () -> getStringFromCategoryOrExtension(
            sourceCategory, DocumentReferenceExtensionUrls.SOURCE, docRef),
        document::setSource
    );
    val abnormalCategory = getDocumentReferenceCategoryByCode(ABNORMAL, docRef);
    setBooleanField(
        () -> String.valueOf(getBooleanFromCategoryOrExtension(
            abnormalCategory, DocumentReferenceExtensionUrls.ABNORMAL, docRef)),
        document::setAbnormal
    );
    // observationDate
    setDateField(() -> getObservationDateFromExtensionOrDate(docRef), document::setObservationDate);
    // contentType & content date time
    val content = docRef.getContentFirstRep();
    if (content != null && !content.isEmpty()) {
      val attachment = content.getAttachment();
      if (attachment != null && StringUtils.isNotBlank(attachment.getContentType())) {
        document.setContentType(attachment.getContentType());
        document.setContentDateTime(attachment.getCreation());
        setStringField(attachment::getTitle, document::setDocFileName);
      }
    }
    // docCreator to author
    val docRefAuthor = docRef.getAuthorFirstRep();
    val provider = new Provider();
    if (docRefAuthor != null && docRefAuthor.getReference() != null && docRefAuthor.hasDisplay()) {
      val name = docRefAuthor.getDisplay().split(",", 2);
      setStringField(() -> name.length > 1 ? name[1].trim() : null, provider::setFirstName);
      setStringField(() -> name.length > 0 ? name[0].trim() : null, provider::setLastName);
      setStringField(() -> docRefAuthor.getReferenceElement().getIdPart(), provider::setProviderNo);
      document.setDocCreator(provider);
    }
    // responsible - get from authenticator, if not available, get from extension
    setStringField(
        () -> getResponsibleByAuthenticatorOrExtension(docRef), document::setResponsible);
    // sourceFacility
    val refOrg = docRef.getCustodian();
    setStringField(
        () -> getSourceFacilityByOrgOrExtension(refOrg, docRef), document::setSourceFacility);
    // docDesc
    document.setDocDesc(docRef.getDescription());
    // documentPublic
    fromExt(docRef, DocumentReferenceExtensionUrls.PUBLIC1, document::setDocumentPublic);
    // numberOfPages
    fromExt(docRef, DocumentReferenceExtensionUrls.NUMBER_OF_PAGES, document::setNumberOfPages);
    // programId
    val extension = docRef.getExtensionByUrl(DocumentReferenceExtensionUrls.PROGRAM_ID);
    if (extension != null && extension.hasValue()) {
      setIntegerField(() -> getProgramId(docRef), document::setProgramId);
    }
    // reportMedia
    fromExt(docRef, DocumentReferenceExtensionUrls.REPORT_MEDIA, document::setReportMedia);
    // reviewDateTime
    fromExt(docRef, DocumentReferenceExtensionUrls.REVIEW_DATE_TIME, document::setReviewDateTime);
    // reviewer
    fromExt(docRef, DocumentReferenceExtensionUrls.REVIEWER, document::setReviewer);
    // status
    document.setStatus(getStatus(docRef.getStatus()));
    // updateDateTime
    if (docRef.getMeta() != null && docRef.getMeta().getLastUpdated() != null) {
      setDateField(docRef.getMeta()::getLastUpdated, document::setUpdateDateTime);
    }
    // appointmentNo
    val context = docRef.getContext();
    if (context != null && !context.isEmpty()) {
      val encounter = context.getEncounterFirstRep();
      if (encounter != null && encounter.getReference() != null) {
        val appointmentNo = encounter.getReferenceElement().getIdPart();
        if (StringUtils.isNotBlank(appointmentNo)) {
          document.setAppointmentNo(Integer.parseInt(appointmentNo));
        }
      }
    }

    return document;
  }

  public DocumentReference toFhirObject(CaseManagementNote note) {

    if (note == null) {
      LOG.warn("note is null");
      return null;
    }

    val docRef = new DocumentReference();

    // author
    if (StringUtils.isNotBlank(note.getProviderNo())) {
      List<Reference> authors = new ArrayList<>();
      authors.add(new Reference(Practitioner.class.getSimpleName() + "/" + note.getProviderNo()));
      docRef.setAuthor(authors);
    }
    // category and issues
    docRef.setCategory(getCategory(note));
    // content
    docRef.setContent(createNoteContent(note));
    // date
    docRef.setDate(note.getObservationDate());
    // doc status
    docRef.setDocStatus(getDocStatus(note.isArchived()));
    // extension
    docRef.setExtension(createExtensions(note));
    // related models
    val link = caseManagementNoteLinkRepository.getByNoteIdOrOrderByIdDesc(note.getId());
    if (link != null) {
      addExt(link.getTableName(), DocumentReferenceExtensionUrls.ATTACHED_MODEL_LINK, docRef.getExtension());

      if (link.getTableName().equals(CaseManagementNoteLink.DOCUMENT)) {
        val document = documentRepository.getDocumentById(link.getTableId().intValue());
        addExt(document.getGuid(), DocumentReferenceExtensionUrls.ATTACHED_MODEL_GUID, docRef.getExtension());
      }
    }


    // id
    if (note.getId() != null) {
      docRef.setId(Long.toString(note.getId()));
    }
    // language
    docRef.setLanguage(LanguageConstants.ENGLISH_CA);
    // meta
    if (note.getUpdateDate() != null) {
      Meta meta = new Meta();
      meta.setLastUpdated(note.getUpdateDate());
      docRef.setMeta(meta);
    }
    // status
    docRef.setStatus(getStatus(note.isArchived()));
    // subject
    if (note.getDemographicNo() != null) {
      val subject = new Reference(Patient.class.getSimpleName() + "/" + note.getDemographicNo());
      val demographic = demographicRepository.findOne(note.getDemographicNo());
      val identifier = new Identifier().setValue(demographic.getGuid()).setType(
          createCodeableConcept(
              CodeSystemUrlConstants.APPS_HEALTH_CODE_SYSTEM_URL,
              DemographicIdentifierData.getCode(DemographicIdentifierData.LINK_GUID),
              DemographicIdentifierData.getCode(DemographicIdentifierData.LINK_GUID)
          ));
      subject.setIdentifier(identifier);
      docRef.setSubject(subject);
    }
    // type
    docRef.setType(
        createCodeableConcept(
            CodeSystemUrlConstants.LOINC_CODE_SYSTEM_URL,
            LOINC_CONSULT_NOTE_CODE,
            LOINC_CONSULT_NOTE_CODE_DISPLAY));
    setContextForNoteReference(note, docRef);

    val notePrimarySystemId =
        note.isRemote() ? note.getRemoteSystemId() : gwConfigurationService.getSystemId();
    val guidIdentifier = new Identifier().setSystem(GUID_SYSTEM).setValue(note.getUuid());
    if (notePrimarySystemId != null) {
      guidIdentifier
          .getAssigner()
          .setReference(ResourceType.Organization.name() + "/" + notePrimarySystemId);
    }
    docRef.addIdentifier(guidIdentifier);

    return docRef;
  }

  public CaseManagementNote toOscarCaseManagementNote(final DocumentReference documentReference)
      throws UnmatchedRemoteRecordException {
    return toOscarCaseManagementNote(documentReference, null);
  }

  public CaseManagementNote toOscarCaseManagementNote(final DocumentReference documentReference,
                                                      final Integer chartDemographicNo)
      throws UnmatchedRemoteRecordException {
    if (documentReference == null) {
      return null;
    }

    CaseManagementNote note = new CaseManagementNote();
    // archived
    fromFhirStatus(documentReference, note);
    // issue codes
    val issueCodeableConcepts = documentReference.getCategory().stream()
            .filter(cc -> cc.getCodingFirstRep().getSystem().equals(CATEGORY_ISSUE))
            .collect(Collectors.toList());
    for (final CodeableConcept issueCodeableConcept : issueCodeableConcepts) {
      val issue = issueRepository.findByCode(issueCodeableConcept.getCodingFirstRep().getCode());
      if (issue != null) {
        CaseManagementIssue demoIssue = new CaseManagementIssue();
        demoIssue.setIssueId(issue.getId());
        Optional.ofNullable((BooleanType)
                        getExtensionValue(DocumentReferenceExtensionUrls.ISSUE_ACUTE, issueCodeableConcept))
                .ifPresent(ext -> demoIssue.setAcute(ext.getValue()));
        Optional.ofNullable(((BooleanType)
                        getExtensionValue(DocumentReferenceExtensionUrls.ISSUE_CERTAIN, issueCodeableConcept)))
                .ifPresent(ext -> demoIssue.setCertain(ext.getValue()));
        Optional.ofNullable(((BooleanType)
                        getExtensionValue(DocumentReferenceExtensionUrls.ISSUE_MAJOR, issueCodeableConcept)))
                .ifPresent(ext -> demoIssue.setMajor(ext.getValue()));
        Optional.ofNullable(((BooleanType)
                        getExtensionValue(DocumentReferenceExtensionUrls.ISSUE_RESOLVED, issueCodeableConcept)))
                .ifPresent(ext -> demoIssue.setResolved(ext.getValue()));
        Optional.ofNullable(((StringType)
                        getExtensionValue(DocumentReferenceExtensionUrls.ISSUE_TYPE, issueCodeableConcept)))
                .ifPresent(ext -> demoIssue.setType(ext.getValue()));
        demoIssue.setUpdateDate(new Date());
        note.addIssue(demoIssue);
      }
    }
    // billingCode
    val billingCode =
        (StringType)
            getExtensionValue(DocumentReferenceExtensionUrls.BILLING_CODE, documentReference);
    if (billingCode != null) {
      note.setBillingCode(billingCode.getValue());
    }
    // demographicNo
    if (chartDemographicNo != null) {
      note.setDemographicNo(chartDemographicNo);
    } else if (documentReference.getSubject().getIdentifier().getType().getText() != null
        && documentReference.getSubject()
        .getIdentifier().getType().getText()
        .equals(DemographicIdentifierData.getCode(DemographicIdentifierData.LINK_GUID))) {

      val demographicUuid = documentReference.getSubject().getIdentifier().getValue();

      if (demographicUuid != null) {

        val localDemographic = demographicRepository.getByGuid(demographicUuid);

        if (localDemographic != null) {
          note.setDemographicNo(localDemographic.getDemographicNumber());
        } else {
          throw new UnmatchedRemoteRecordException("Unknown demographic with uuid: " + demographicUuid);
        }
      }
    }
    // encounterType
    val encounterType =
        (StringType)
            getExtensionValue(DocumentReferenceExtensionUrls.ENCOUNTER_TYPE, documentReference);
    if (encounterType != null) {
      note.setEncounterType(encounterType.getValue());
    }
    // id
    if (documentReference.hasId() && chartDemographicNo == null) {
      note.setId(documentReference.getIdElement().getIdPartAsLong());
    }
    // note
    if (documentReference.getContent() != null && documentReference.getContentFirstRep() != null) {
      DocumentReferenceContentComponent content = documentReference.getContentFirstRep();
      if (content.getAttachment() != null
          && content.getAttachment().getData() != null
          && content.getAttachment().getData().length > 0) {
        note.setNote(new String(content.getAttachment().getData()));
      }
      val historyExtension = (Attachment)
              getExtensionValue(DocumentReferenceExtensionUrls.NOTE_HISTORY, content);
      if (historyExtension != null && historyExtension.getData() != null) {
        note.setHistory(new String(historyExtension.getData()));
      }
    }
    // observationDate
    val observationDate =
        (DateTimeType)
            getExtensionValue(DocumentReferenceExtensionUrls.OBSERVATION_DATE, documentReference);
    if (observationDate != null) {
      note.setObservationDate(observationDate.getValue());
    }
    // position
    val position =
        (IntegerType) getExtensionValue(DocumentReferenceExtensionUrls.POSITION, documentReference);
    if (position != null) {
      note.setPosition(position.getValue());
    }
    // programNo
    val programNo =
        (StringType)
            getExtensionValue(DocumentReferenceExtensionUrls.PROGRAM_ID, documentReference);
    if (programNo != null) {
      note.setProgramNo(programNo.getValue());
    }
    // providerNo
    if (documentReference.getAuthor() != null && documentReference.getAuthorFirstRep() != null) {
      Reference providerNo = documentReference.getAuthorFirstRep();
      note.setProviderNo(getId(providerNo));
    }
    // reporterCaisiRole
    val reporterCaisiRole =
        (StringType)
            getExtensionValue(
                DocumentReferenceExtensionUrls.REPORTER_CAISI_ROLE, documentReference);
    if (reporterCaisiRole != null) {
      note.setReporterCaisiRole(reporterCaisiRole.getValue());
    }
    // reporterProgramTeam
    val reporterProgramTeam =
        (StringType)
            getExtensionValue(
                DocumentReferenceExtensionUrls.REPORTER_PROGRAM_TEAM, documentReference);
    if (reporterProgramTeam != null) {
      note.setReporterProgramTeam(reporterProgramTeam.getValue());
    }
    // appointment no
    val appointmentNo =
            (IntegerType) getExtensionValue(DocumentReferenceExtensionUrls.APPOINTMENT_NO, documentReference);
    if (appointmentNo != null) {
      note.setAppointmentNo(appointmentNo.getValue());
    }
    // signed
    val signed =
        (BooleanType)
            getExtensionValue(DocumentReferenceExtensionUrls.IS_SIGNED, documentReference);
    if (signed != null) {
      note.setSigned(signed.getValue());
    }
    // signing provider
    val signingProviderNo =
            (StringType)
                    getExtensionValue(DocumentReferenceExtensionUrls.SIGNING_PROVIDER_NO, documentReference);
    if (signingProviderNo != null) {
      note.setSigningProviderNo(signingProviderNo.getValue());
    }
    // includeIssueInNote
    val includeIssueInNote =
            (BooleanType)
                    getExtensionValue(DocumentReferenceExtensionUrls.IS_INCLUDE_ISSUE_IN_NOTE, documentReference);
    if (includeIssueInNote != null) {
      note.setIncludeIssueInNote(includeIssueInNote.getValue());
    }
    // updateDate
    if (documentReference.getMeta() != null) {
      note.setUpdateDate(documentReference.getMeta().getLastUpdated());
    }
    // uuid
    val uuid = getIdentifierValue(documentReference.getIdentifier(), GUID_SYSTEM);
    if (uuid != null) {
      note.setUuid(uuid);
      note.setRemoteSystemId(getRemoteSystemId(documentReference.getIdentifier(), GUID_SYSTEM));
    }

    // auto sync date
    val autoSyncDate = (DateTimeType) getExtensionValue(DocumentReferenceExtensionUrls.AUTO_SYNC_DATE, documentReference);
    if (autoSyncDate != null) {
      note.setAutoSyncDate(autoSyncDate.getValue());
    }
    // last sync date
    val lastSyncDate = (DateTimeType) getExtensionValue(DocumentReferenceExtensionUrls.LAST_SYNC_DATE, documentReference);
    if (lastSyncDate != null) {
      note.setLastSyncedDate(lastSyncDate.getValue());
    }

    createExtsForNote(note, documentReference);
    if (isGatewayResource(documentReference.getIdentifier())) {
      removeOscarDomainSpecificIds(note);
    }

    return note;
  }

  /**
   * Converts an EForm object into a FHIR DocumentReference object. This method optionally includes
   * a PDF attachment in the FHIR object, either as raw bytes or Base64 encoded.
   *
   * @param oscarEform   the EForm object to be converted to FHIR DocumentReference. Must not be
   *                     null.
   * @param attachBase64 if true, attaches the PDF content to the FHIR DocumentReference as raw byte
   *                     data.
   * @return a FHIR DocumentReference object populated with data from the given EForm, or null if
   * the EForm is null.
   */
  @Nullable
  public DocumentReference toFhirObject(final EForm oscarEform, boolean attachBase64) {
    if (oscarEform == null) {
      LOG.warn("Eform object is null.");
      return null;
    }

    val fhir = new DocumentReference();
    // id
    fhir.setId(String.valueOf(oscarEform.getId()));
    // guid identifier
    fhir.addIdentifier().setSystem(GUID_SYSTEM).setValue(oscarEform.getGuid());
    // status -> status
    fhir.setStatus(
        oscarEform.isStatus()
            ? DocumentReferenceStatus.CURRENT
            : DocumentReferenceStatus.SUPERSEDED
    );
    val categories = new ArrayList<CodeableConcept>();
    categories.add(createCodeableConcept(CATEGORY_SYSTEM, EFORM_CODE, EFORM_CODE));
    fhir.setCategory(categories);
    // form name -> description
    fhir.setDescription(oscarEform.getFormName());
    // date -> date
    fhir.setDate(oscarEform.getFormDate());
    // subject -> type
    fhir.setType(new CodeableConcept().setText(oscarEform.getSubject()));
    // demographic -> subject
    fhir.setSubject(
        new Reference(Patient.class.getSimpleName() + "/" + oscarEform.getDemographicNo()));
    // provider -> author
    fhir.addAuthor(
        new Reference(Practitioner.class.getSimpleName() + "/" + oscarEform.getProviderNo()));
    if (attachBase64) {
      try {
        // when remote we base64 decode pdf in formData otherwise create pdf from form data
        val pdfData = oscarEform.isRemote()
            ? Base64.getDecoder().decode(oscarEform.getFormData())
            : readFileToByteArray(new File(printableService.expandAndPrintPrintables(
                singletonList(new Printable(oscarEform))
            )));
        addPdfAttachment(fhir, pdfData, oscarEform);
      } catch (Exception ex) {
        LOG.error("Error while processing attachment for EForm: " + oscarEform.getId(), ex);
      }
    }

    return fhir;
  }

  /**
   * Adds a PDF attachment to a FHIR DocumentReference.
   *
   * @param fhir        The DocumentReference to which the attachment will be added.
   * @param pdfData     The byte array containing the PDF data.
   * @param oscarEform  The source EForm object for metadata.
   */
  protected void addPdfAttachment(
      final DocumentReference fhir,
      final byte[] pdfData,
      final EForm oscarEform
  ) {
    val attachment = new Attachment();
    attachment.setData(pdfData);
    attachment.setTitle(oscarEform.getFormName());
    attachment.setContentType("application/pdf");
    attachment.setCreation(oscarEform.getFormDate());
    attachment.setSize(pdfData.length);

    val contentComponent = new DocumentReference.DocumentReferenceContentComponent();
    contentComponent.setAttachment(attachment);
    fhir.addContent(contentComponent);
  }

  /**
   * Converts a FHIR {@link DocumentReference} to an {@link EForm} object for integration and
   * storage in the system. This conversion method is currently used solely for eForm posting via
   * Link, ensuring that the required data fields are mapped accurately from FHIR to the internal
   * eForm model.
   *
   * <p>During conversion, default values are assigned to certain fields to maintain consistency
   * and compatibility:
   * <ul>
   *   <li>{@code formId} is initialized to {@code -1} as a placeholder, indicating that it is
   *       a new or temporary form until assigned an actual ID.</li>
   *   <li>{@code showLatestFormOnly} is set to {@code false} to ensure the form is processed
   *       correctly in the system when posted to the primary database.</li>
   *   <li>{@code status} is set based on {@link DocumentReferenceStatus#CURRENT} to mark the
   *       document as active.</li>
   * </ul>
   * </p>
   *
   * @param documentReference the FHIR DocumentReference to convert; if null, a warning is logged
   *                          and null is returned.
   * @return the converted {@link EForm} object, or null if the input documentReference is null.
   */
  @Nullable
  public EForm toOscarEform(final DocumentReference documentReference) {
    if (documentReference == null) {
      LOG.warn("DocumentReference object is null.");
      return null;
    }

    val eform = new EForm();
    eform.setFormId(-1);
    // id
    if (documentReference.hasId()) {
      val id = documentReference.getIdElement().getIdPart();
      if (StringUtils.isNumeric(id)) {
        eform.setId(Long.parseLong(id));
      }
    }

    val identifiers = documentReference.getIdentifier();
    // uuid and remote system id
    setEformUuidAndRemoteSystemId(
        eform,
        getIdentifierValue(identifiers, GUID_SYSTEM),
        getRemoteSystemId(identifiers, GUID_SYSTEM)
    );
    // status
    eform.setStatus(DocumentReferenceStatus.CURRENT.equals(documentReference.getStatus()));
    // demographic
    extractDemographic(documentReference, eform);
    // eform name
    if (documentReference.hasDescription()) {
      eform.setFormName(documentReference.getDescription());
    }
    // eform date
    if (documentReference.hasDate()) {
      eform.setFormDate(documentReference.getDate());
      eform.setFormTime(documentReference.getDate());
    }
    // eform subject
    eform.setSubject(StringUtils.EMPTY);
    if (documentReference.hasType()) {
      val subject = documentReference.getType().getText();
      if (subject != null) {
        eform.setSubject(subject);
      }
    }
    val content = documentReference.getContentFirstRep();
    val attachment = content.getAttachment();
    if (attachment.hasData()) {
      val encodedData = Base64.getEncoder().encodeToString(attachment.getData());
      eform.setFormData(encodedData);
    }

    return eform;
  }

  /**
   * Returns a FHIR {@link DocumentReference} resource mapped to an Oscar {@link HRMDocument} object
   * by mapping the corresponding fields between the two models. The DocumentReference resource will
   * contain the file data and name of the HRMDocument. If the input HRMDocument is null, a warning
   * will be logged and null is returned.
   *
   * @param hrmDocument the HRMDocument object to convert to a DocumentReference
   * @return the converted DocumentReference object, or null if the input HRMDocument is null
   */
  public DocumentReference toFhirObject(final HRMDocument hrmDocument) {
    if (hrmDocument == null) {
      LOG.warn("HRMDocument object is null.");
      return null;
    }
    val docRef = new DocumentReference();
    // id
    docRef.setId(String.valueOf(hrmDocument.getId()));
    // guid
    docRef.addIdentifier().setSystem(GUID_SYSTEM).setValue(hrmDocument.getGuid());
    // status
    docRef.setStatus(DocumentReferenceStatus.CURRENT);
    // docStatus
    docRef.setDocStatus(getDocStatusFromHrmStatus(hrmDocument.getReportStatus()));
    // date
    docRef.setDate(hrmDocument.getReportDate());
    // description
    docRef.setDescription(hrmDocument.getDescription());
    // type
    docRef.setType(createCodeableConcept(HRM_SYSTEM, HRM_CODE, hrmDocument.getReportType()));
    // content
    val attachment = new Attachment();
    val hrmReport = HRMParser.parseReport(hrmDocument.getReportFile());
    if (hrmReport != null) {
      addExt(getSubClassFromHrmReport(hrmReport), HRM_SUBCLASS_URL, docRef::addExtension);
      val data = hrmReport.getFileData().getBytes();
      attachment.setData(data);
      attachment.setSize(data.length);
    }
    attachment.setTitle(hrmDocument.getReportFile());
    attachment.setCreation(hrmDocument.getTimeReceived());
    docRef.addContent().setAttachment(attachment);

    return docRef;
  }

  /**
   * Finds the HRM's subclass using the HRM Report's First Accompanying Subclass
   * Report Class if it is not a Diagnostic Imaging Report or Cardio Respiratory Report. Otherwise
   * get the subclass from the First Report Subclass.
   * <br>
   * <br>
   * NOTE: This function logic is copied from Oscar Classic.
   *
   * @param hrmReport the HRM Report object to extract the subclass from
   * @return the extracted subclass from the HRM Report
   */
  private String getSubClassFromHrmReport(final HRMReport hrmReport) {
    if (hrmReport.getFirstReportClass().equalsIgnoreCase("Diagnostic Imaging Report")
        || hrmReport.getFirstReportClass().equalsIgnoreCase("Cardio Respiratory Report")) {
      return hrmReport.getFirstAccompanyingSubClass();
    }

    // Medical Records Report
    val reportSubClass = StringUtils.split(hrmReport.getFirstReportSubClass(), "\\^");
    return reportSubClass != null && reportSubClass.length > 1 ? reportSubClass[1] : "";
  }

  /**
   * Returns an Oscar {@link HRMDocument} object mapped to a FHIR {@link DocumentReference} resource
   * by mapping the corresponding fields between the two models.
   *
   * @param documentReference the DocumentReference object to convert to an HRMDocument
   * @return the converted HRMDocument object, or null if the input DocumentReference is null
   */
  public HRMDocument toHrmDocument(final DocumentReference documentReference) {
    if (documentReference == null) {
      LOG.warn("DocumentReference object is null.");
      return null;
    }
    val hrmDoc = new HRMDocument();
    if (documentReference.hasId()) {
      val id = documentReference.getIdElement().getIdPart();
      if (StringUtils.isNumeric(id)) {
        hrmDoc.setId(Integer.parseInt(id));
      }
    }
    // guid
    val hrmGuid = getIdentifierValue(documentReference.getIdentifier(), GUID_SYSTEM);
    hrmDoc.setGuid(hrmGuid != null ? hrmGuid : hrmDoc.getGuid());
    // report status
    hrmDoc.setReportStatus(getHrmStatusFromDocStatus(documentReference));
    // report date
    hrmDoc.setReportDate(documentReference.getDate());
    // description
    hrmDoc.setDescription(documentReference.getDescription());
    // content
    if (documentReference.hasContent() && documentReference.getContentFirstRep().hasAttachment()) {
      val attachment = documentReference.getContentFirstRep().getAttachment();
      if (attachment.hasData()) {
        val report = new HRMReport();
        report.setFileData(new String(attachment.getData()));
        hrmDoc.setReport(report);
      }
      hrmDoc.setReportFile(attachment.getTitle());
    }

    return hrmDoc;
  }

  private ReferredDocumentStatus getDocStatusFromHrmStatus(final String status) {
    if (status == null) {
      return ReferredDocumentStatus.NULL;
    }
    switch (status) {
      case "S":
        return ReferredDocumentStatus.FINAL;
      case "C":
        return ReferredDocumentStatus.ENTEREDINERROR;
      default:
        LOG.warn("Unsupported HRM document status: {}", status);
        return ReferredDocumentStatus.NULL;
    }
  }

  private String getHrmStatusFromDocStatus(final DocumentReference documentReference) {
    if (documentReference == null || !documentReference.hasDocStatus()) {
      return null;
    }
    val docStatus = documentReference.getDocStatus();
    switch (docStatus) {
      case FINAL:
        return "S";
      case ENTEREDINERROR:
        return "C";
      default:
        LOG.warn("Unsupported document status: {}", docStatus.getDisplay());
        return null;
    }
  }

  private void extractDemographic(
      final DocumentReference documentReference,
      final EForm eform
  ) {
    if (!documentReference.hasSubject()) {
      return;
    }
    val subjectResource = documentReference.getSubject().getResource();
    if (subjectResource == null){
      return;
    }
    extractDemographicFromResource(eform, (Patient) subjectResource);
  }

  private void extractDemographicFromResource(final EForm eform, final Patient subjectResource) {
    val patient = (Patient) subjectResource;
    val hinIdentifier = getJhnIdentifier(patient);
    val hinType = getHcTypeFromExtension(patient);
    if (hinIdentifier == null || hinType == null) {
      LOG.warn(
          "HIN or HC type not found while creating eform object, setting demographic number to 0");
      eform.setDemographicNo(0);
      return;
    }
    val hinParts = hinIdentifier.getValue().split("::");
    val demographic = demographicRepository.getFirstByHinAndHcType(hinParts[0], hinType);
    if (demographic != null) {
      eform.setDemographicNo(demographic.getDemographicNumber());
    } else {
      LOG.warn(
          "Demographic not found while creating eform object, setting demographic number to 0");
      eform.setDemographicNo(0);
    }
  }

  // ------------------------------------------------------------------------

  protected Integer getProgramId(DocumentReference docRef) {
    return Optional.ofNullable(
            (Reference) getExtensionValue(DocumentReferenceExtensionUrls.PROGRAM_ID, docRef)
        )
        .map(BaseConverter::getId)
        .filter(StringUtils::isNotBlank)
        .map(id -> {
          try {
            return Integer.parseInt(id);
          } catch (NumberFormatException e) {
            LOG.warn("Invalid programId: {}", id);
            return null;
          }
        })
        .orElse(null);
  }

  protected static String getResponsibleByAuthenticatorOrExtension(DocumentReference docRef) {
    return docRef.hasAuthenticator()
        ? docRef.getAuthenticator().getDisplay()
        : docRef.hasExtension(RESPONSIBLE)
            ? ((StringType) docRef.getExtensionByUrl(RESPONSIBLE).getValue()).getValue()
            : null;
  }

  // Private Methods
  private void setEformUuidAndRemoteSystemId(
      final EForm eform,
      final String uuid,
      final Integer remoteSystemId
  ) {
    if (uuid == null) {
      return;
    }
    val existingEform = eformRepository.findFirstByGuid(uuid);
    if (existingEform != null) {
      eform.setId(existingEform.getId());
    }
    eform.setGuid(uuid);
    eform.setRemoteSystemId(remoteSystemId);
  }

  private static void fromFhirStatus(DocumentReference documentReference, CaseManagementNote note) {
    if (documentReference.getStatus() != null) {
      if (DocumentReferenceStatus.SUPERSEDED.equals(documentReference.getStatus())) {
        note.setArchived(true);
      } else if (DocumentReferenceStatus.CURRENT.equals(documentReference.getStatus())) {
        note.setArchived(false);
      }
    }
  }

  private DocumentReferenceStatus getStatus(Boolean archived) {
    if (archived == null) {
      return DocumentReferenceStatus.NULL;
    }
    return archived ? DocumentReferenceStatus.SUPERSEDED : DocumentReferenceStatus.CURRENT;
  }

  private static DocumentReferenceStatus toFhirDocStatus(String status) {
    if (status == null) {
      return DocumentReferenceStatus.NULL;
    }
    switch (status) {
      case DOCUMENT_STATUS_ACTIVE:
        return DocumentReferenceStatus.CURRENT;
      case DOCUMENT_STATUS_DELETED:
        return DocumentReferenceStatus.SUPERSEDED;
      default:
        return DocumentReferenceStatus.NULL;
    }
  }

  private String getStatus(DocumentReferenceStatus status) {
    if (status == null) {
      return null;
    }
    switch (status) {
      case CURRENT:
        return DOCUMENT_STATUS_ACTIVE;
      case SUPERSEDED:
        return DOCUMENT_STATUS_DELETED;
      case ENTEREDINERROR:
      case NULL:
      default:
        val theMessage = "Unsupported document reference status " + status;
        LOG.warn(theMessage);
        throw new InvalidRequestException(theMessage);
    }
  }

  private ReferredDocumentStatus getDocStatus(Boolean archived) {
    if (archived == null) {
      return ReferredDocumentStatus.NULL;
    }
    return archived ? ReferredDocumentStatus.ENTEREDINERROR : ReferredDocumentStatus.FINAL;
  }

  private List<CodeableConcept> getCategory(final CaseManagementNote note) {
    List<CodeableConcept> categories = new ArrayList<>();
    categories.add(createCodeableConcept(CATEGORY_SYSTEM, NOTES_CODE, NOTES_CODE));
    // issues
    if (note.getId() != null) {
      val noteIssues = caseManagementIssueRepository.findByNoteId(note.getId());
      for (final CaseManagementIssue noteIssue : noteIssues) {
        categories.add(createIssueCodeableConcept(noteIssue));
      }
    }
    return categories;
  }

  private CodeableConcept createIssueCodeableConcept(final CaseManagementIssue noteIssue) {
    val issue = issueRepository.findOne(noteIssue.getIssueId());
    val codeableConcept = createCodeableConcept(CATEGORY_ISSUE, issue.getCode(), issue.getCode());
    addExt(noteIssue.isAcute(), ISSUE_ACUTE, codeableConcept::addExtension);
    addExt(noteIssue.isCertain(), ISSUE_CERTAIN, codeableConcept::addExtension);
    addExt(noteIssue.isMajor(), ISSUE_MAJOR, codeableConcept::addExtension);
    addExt(noteIssue.isResolved(), ISSUE_RESOLVED, codeableConcept::addExtension);
    addExt(noteIssue.getType(), ISSUE_TYPE, codeableConcept::addExtension);
    return codeableConcept;
  }

  private List<Extension> createExtensions(final CaseManagementNote note) {
    List<Extension> list = new ArrayList<>();
    addExt(note.getAppointmentNo(), DocumentReferenceExtensionUrls.APPOINTMENT_NO, list);
    addExt(note.isSigned(), DocumentReferenceExtensionUrls.IS_SIGNED, list);
    addExt(note.getIncludeIssueInNote(), DocumentReferenceExtensionUrls.IS_INCLUDE_ISSUE_IN_NOTE,
        list);
    addExt(note.getBillingCode(), DocumentReferenceExtensionUrls.BILLING_CODE, list);
    addExt(note.getEncounterType(), DocumentReferenceExtensionUrls.ENCOUNTER_TYPE, list);
    addExt(note.getSigningProviderNo(), DocumentReferenceExtensionUrls.SIGNING_PROVIDER_NO, list);
    addExt(note.getObservationDate(), DocumentReferenceExtensionUrls.OBSERVATION_DATE, list);
    addExt(note.getPosition(), DocumentReferenceExtensionUrls.POSITION, list);
    addExt(note.getProgramNo(), DocumentReferenceExtensionUrls.PROGRAM_ID, list);
    addExt(note.getReporterCaisiRole(), DocumentReferenceExtensionUrls.REPORTER_CAISI_ROLE, list);
    addExt(note.getReporterProgramTeam(), DocumentReferenceExtensionUrls.REPORTER_PROGRAM_TEAM,
        list);
    addExt(note.getAutoSyncDate(), DocumentReferenceExtensionUrls.AUTO_SYNC_DATE, list);
    addExt(note.getLastSyncedDate(), DocumentReferenceExtensionUrls.LAST_SYNC_DATE, list);

    if (note.getId() != null) {
      val noteExts = caseManagementNoteExtRepository.findByNoteId(note.getId());
      addExtsForDocument(list, noteExts);
    }
    return list;
  }

  private List<DocumentReferenceContentComponent> createNoteContent(final CaseManagementNote note) {
    val contents = new ArrayList<DocumentReferenceContentComponent>();
    if (StringUtils.isNotBlank(note.getNote())) {
      val attachment = new Attachment();
      attachment.setContentType("text");
      attachment.setData(note.getNote().getBytes());
      val content = new DocumentReferenceContentComponent(attachment);

      if (StringUtils.isNotEmpty(note.getHistory())) {
        val noteHistoryAttachment = new Attachment();
        noteHistoryAttachment.setContentType("text");
        noteHistoryAttachment.setData(note.getHistory().getBytes());
        content.addExtension(createExtension(DocumentReferenceExtensionUrls.NOTE_HISTORY, noteHistoryAttachment));
      }

      contents.add(content);
    }
    return contents;
  }

  /**
   * Creates note ext entries from documentReference extensions
   * @param note note to add ext entries to
   * @param documentReference document with note ext data
   */
  private void createExtsForNote(final CaseManagementNote note, final DocumentReference documentReference) {
    for (var extKey : CaseManagementNoteExtKey.values()) {
      switch (extKey) {
        case START_DATE:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.START_DATE,
                  documentReference, DocumentReferenceExtensionUrls.START_DATE);
          break;
        case RESOLUTION_DATE:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.RESOLUTION_DATE,
                  documentReference, DocumentReferenceExtensionUrls.RESOLUTION_DATE);
          break;
        case PROCEDURE_DATE:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.PROCEDURE_DATE,
                  documentReference, DocumentReferenceExtensionUrls.PROCEDURE_DATE);
          break;
        case AGE_AT_ONSET:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.AGE_AT_ONSET,
                  documentReference, DocumentReferenceExtensionUrls.AGE_AT_ONSET);
          break;
        case TREATMENT:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.TREATMENT,
                  documentReference, DocumentReferenceExtensionUrls.TREATMENT);
          break;
        case PROBLEM_STATUS:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.PROBLEM_STATUS,
                  documentReference, DocumentReferenceExtensionUrls.PROBLEM_STATUS);
          break;
        case EXPOSURE_DETAIL:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.EXPOSURE_DETAIL,
                  documentReference, DocumentReferenceExtensionUrls.EXPOSURE_DETAIL);
          break;
        case RELATIONSHIP:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.RELATIONSHIP,
                  documentReference, DocumentReferenceExtensionUrls.RELATIONSHIP);
          break;
        case LIFE_STAGE:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.LIFE_STAGE,
                  documentReference, DocumentReferenceExtensionUrls.LIFE_STAGE);
          break;
        case HIDE_CPP:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.HIDE_CPP,
                  documentReference, DocumentReferenceExtensionUrls.HIDE_CPP);
          break;
        case PROBLEM_DESC:
          createNoteExtIfValueExists(note, CaseManagementNoteExtKey.PROBLEM_DESC,
                  documentReference, DocumentReferenceExtensionUrls.PROBLEM_DESC);
          break;
        default:
      }
    }
  }

  /**
   * Adds NoteExt to provided CaseManagementNote using the provided CaseManagementNoteExtKey if it exists in
   * the document extensions with the provided URL
   */
  private void createNoteExtIfValueExists(
      final CaseManagementNote note,
      final CaseManagementNoteExtKey extKey,
      final DocumentReference documentReference,
      final String url
  ) {
    val extensionValue = getExtensionValue(url, documentReference);
    if (extensionValue != null) {
      if (extensionValue instanceof DateTimeType) {
        note.addExtension(new CaseManagementNoteExt(extKey.getKey(), ((DateTimeType) extensionValue).getValue()));
      } else {
        note.addExtension(new CaseManagementNoteExt(extKey.getKey(), ((StringType) extensionValue).getValue()));
      }
    }
  }

  /**
   * Creates note ext entries from documentReference extensions
   */
  private void addExtsForDocument(
      final List<Extension> extensions,
      final List<CaseManagementNoteExt> noteExts
  ) {
    for (var noteExt : noteExts) {
      val extKey = noteExt.getKeyVal();
      if (CaseManagementNoteExtKey.START_DATE.getKey().equals(extKey)) {
        addExt(noteExt.getDateValue(), DocumentReferenceExtensionUrls.START_DATE, extensions);
      } else if (CaseManagementNoteExtKey.RESOLUTION_DATE.getKey().equals(extKey)) {
        addExt(noteExt.getDateValue(), DocumentReferenceExtensionUrls.RESOLUTION_DATE, extensions);
      } else if (CaseManagementNoteExtKey.PROCEDURE_DATE.getKey().equals(extKey)) {
        addExt(noteExt.getDateValue(), DocumentReferenceExtensionUrls.PROCEDURE_DATE, extensions);
      } else if (CaseManagementNoteExtKey.AGE_AT_ONSET.getKey().equals(extKey)) {
        addExt(noteExt.getValue(), DocumentReferenceExtensionUrls.AGE_AT_ONSET, extensions);
      } else if (CaseManagementNoteExtKey.TREATMENT.getKey().equals(extKey)) {
        addExt(noteExt.getValue(), DocumentReferenceExtensionUrls.TREATMENT, extensions);
      } else if (CaseManagementNoteExtKey.PROBLEM_STATUS.getKey().equals(extKey)) {
        addExt(noteExt.getValue(), DocumentReferenceExtensionUrls.PROBLEM_STATUS, extensions);
      } else if (CaseManagementNoteExtKey.EXPOSURE_DETAIL.getKey().equals(extKey)) {
        addExt(noteExt.getValue(), DocumentReferenceExtensionUrls.EXPOSURE_DETAIL, extensions);
      } else if (CaseManagementNoteExtKey.RELATIONSHIP.getKey().equals(extKey)) {
        addExt(noteExt.getValue(), DocumentReferenceExtensionUrls.RELATIONSHIP, extensions);
      } else if (CaseManagementNoteExtKey.HIDE_CPP.getKey().equals(extKey)) {
        addExt(noteExt.getValue(), DocumentReferenceExtensionUrls.HIDE_CPP, extensions);
      } else if (CaseManagementNoteExtKey.PROBLEM_DESC.getKey().equals(extKey)) {
        addExt(noteExt.getValue(), DocumentReferenceExtensionUrls.PROBLEM_DESC, extensions);
      }
    }
  }
  private void setContextForNoteReference(
      final CaseManagementNote note,
      final DocumentReference documentReference
  ) {
    val context = new DocumentReferenceContextComponent();
    val encounter = encounterConverter.toFhirObject(note);
    documentReference.addContained(encounter);
    context.addEncounter(new Reference("#" + encounter.getId()));
    documentReference.setContext(context);
  }

  /**
   * Get the CodeableConcept category from a DocumentReference resource by the codeToFind value.
   * This method will only match on categories/codings with a system value of CATEGORY_SYSTEM.
   *
   * @param codeToFind code to find in the category
   * @param resource DocumentReference resource to search
   * @return CodeableConcept category with the codeToFind value, or null if not found
   */
  private CodeableConcept getDocumentReferenceCategoryByCode(
      final String codeToFind,
      final DocumentReference resource
  ) {
    for (val category : resource.getCategory()) {
      val coding = category.getCodingFirstRep();
      val code = coding.getCode();
      if (code == null || !CATEGORY_SYSTEM.equals(coding.getSystem())) {
        continue;
      }
      if (codeToFind.equals(code)) {
        return category;
      }
    }
    return null;
  }

  private String getSourceFacilityByOrgOrExtension(
      final Reference refOrg,
      final DocumentReference docRef
  ) {
    return refOrg != null && refOrg.hasDisplay()
        ? refOrg.getDisplay()
        : docRef.hasExtension(DocumentReferenceExtensionUrls.SOURCE_FACILITY)
            ? ((StringType) docRef.getExtensionByUrl(DocumentReferenceExtensionUrls.SOURCE_FACILITY)
                        .getValue()).getValue()
            : null;
  }

  private Date getObservationDateFromExtensionOrDate(final DocumentReference docRef) {
    return docRef.hasDate()
        ? docRef.getDate()
        : Optional.ofNullable(
                (DateType)
                    getExtensionValue(DocumentReferenceExtensionUrls.OBSERVATION_DATE, docRef))
            .map(DateType::getValue)
            .orElse(null);
  }

  private void removeOscarDomainSpecificIds(CaseManagementNote note) {
    note.setId(null);
    note.setProviderNo("-1");
    note.setSigningProviderNo("-1");
    note.setAppointmentNo(-1);
  }
}
