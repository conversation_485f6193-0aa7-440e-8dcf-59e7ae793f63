/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */

package org.oscarehr.config;

import java.util.List;
import lombok.Data;
import org.oscarehr.integration.ontarioehr.util.oidc.OntarioEhrUrls;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Optional;

// These properties can be used in the application.properties files and any other method of setting
// properties according to this: https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-external-config
// It should be possible to autocomplete these settings in application.properties with intellij,
// but if you add a new one you might need to "Rebuild Project" for them to show up (it has to
// generate a metadata file to let intellij know they exist)
@ConfigurationProperties(prefix = "juno")
@Data
public class JunoProperties
{
	private JunoPropertiesConfig properties;
	private AdministratorConfig administratorConfig;
	private RedisSessionStore redisSessionStore;
	private NetcareConfig netcareConfig;
	private Olis olis;
	private Test test;
	private FaxConfig faxConfig;
	private PolarisConfig polarisConfig;
	private Hrm hrm;
	private Aqs aqs;
	private Myhealthaccess myhealthaccess;
	private ExternalApi externalApi;
	private OntarioEhr ontarioEhr;

	@Data
	public static class JunoPropertiesConfig
	{
		private String filename;
	}

	@Data
	public static class AdministratorConfig
	{
		private String loginName;
		private boolean sessionCookieEnabled;
		private String sessionCookieName;
		private String sessionCookieFile;
	}

	@Data
	public static class RedisSessionStore
	{
		private boolean enabled = false;
		private String endpoint;
		private String password;
		private String storageKeyPrefix = "";
	}

	@Data
	public static class NetcareConfig
	{
		private String conformanceCode;
		private String launcherUrl;
		private String loginUrl;
		private String logoutUrl;
	}

	@Data
	public static class Test
	{
		private boolean headless = true;
	}

	@Data
	public static class Olis
	{
		private String keystore;
		private String sslKeystore;
		private String sslKeystorePassword;
		private String sslKeystoreAlias;
		private String truststore;
		private String truststorePassword;
		private String returnedCert;
		private String vendorId;
		private String requestUrl;
		private int defaultPollingIntervalMin;
		private String responseSchema;
		private boolean enableSearchResultRemoval;
		private int maxFetchMonths;

		private boolean simulate;
		private String processingId;
		private String pollingFacilityId;
	}

	@Data
	public static class FaxConfig
	{
		private String dataStoreLocation;
		private String ringcentralApiLocation;
		private String ringcentralRedirectUrl;
		private String ringcentralClientId;
		private String ringcentralClientSecret;
	}

	@Data
	public static class Hrm
	{
		private int defaultPollingIntervalSeconds;
		private int minPollingIntervalSeconds;
		private int sftpTimeoutSeconds;
		private String accessKeyLocation;
		private String baseDirectory;
		private boolean decryptRemoteFiles;
		private boolean localOverrideEnabled;
		private String localOverrideDirectory;
		private boolean decryptLocalFiles;
	}

	@Data
	public static class Aqs
	{
		private String aqsDomain;
		private String aqsProtocol;
		private String aqsApiUri;
	}

	@Data
	public static class Myhealthaccess
	{
		private boolean myhealthaccessTelehealthEnabled;
		private String myhealthaccessProtocol;
		private boolean myhealthaccessDevMode;
		private String myhealthaccessApiUri;
		private String myhealthaccessDomain;
		private String cloudmdDomain;
	}

	@Data
	public static class ExternalApi
	{
		private boolean whitelistEnabled;
		private String systemIpsWhitelist;
	}


	@Data
	public static class OntarioEhr
	{
		private OIDC oidc;
		private OAG oag;
		private CCV ccv;
		private DHDR dhdr;
		private HPG hpg;
		private EConsult eConsult;
		private EReport eReport;

		/**
		 * EHR properties for the Clinical Connect Viewer (CCV) module
		 */
		@Data
		public static class CCV
		{
			private String defaultUrl;
			private String defaultName;
			private String identityProviderUrl;
		}

		/**
		 * EHR properties for the Digital Health Drug Repository (DHDR) module
		 */
		@Data
		public static class DHDR
		{
			private String defaultUrl;
			private String defaultName;
			private String drugFormularyUrl;
			private String specialAuthorizationDigitalInformationExchangeUrl;

			public Optional<String> getDefaultUrl()
			{
				return Optional.ofNullable(defaultUrl);
			}
		}

		/**
		 * EHR properties for the eConsult module
		 */
		@Data
		public static class EConsult
		{
			private String defaultKey;
			private String defaultName;
		}

		/**
		 * EHR properties for the eReport module
		 */
		@Data
		public static class EReport
		{
			private String defaultKey;
			private String defaultName;
		}

		/**
		 * EHR properties for the Health Partner Gateway (HPG) module
		 */
		@Data
		public static class HPG
		{
			private String defaultKey;
			private String defaultName;
		}

		/**
		 * EHR properties for the OAuth/Open ID Connect (OIDC)
		 */
		@Data
		public static class OIDC
		{
			private UrlOverrides urlOverrides;

			/**
			 * This class holds URLS which override any of the URLS provided by the discovery service
			 * At present, all of them are used, as we were advised by Ontario Health not to use
			 * any of the endpoints returned by the discovery service.  This class is an all-or-nothing
			 * repository.  If useUrlOverrides is true, then all of these urls will be used.
			 */
			@Data
			public static class UrlOverrides implements OntarioEhrUrls
			{
				/**
				 * Absolute url to the authorization endpoint to obtain an access code
				 */
				private String authorizationUrl;
				/**
				 * Absolute url to the token endpoint to exchange the access code for an authorization token
				 */
				private String tokenUrl;

				/**
				 * Absolute url to the endpoint which describes the Json Web keys for the ONE ID service
				 */
				private String jwksUrl;

				/**
				 * Absolute url to the revoke token endpoint
				 */
				private String revokeTokenUrl;

				/**
				 * Absolute url to the end session endpoint.
				 */
				private String endSessionUrl;

				/**
				 * Absolute url of the OpenID service
				 */
				private String issuer;
			}

			/**
			 * Enable to use hardcoded configuration urls instead of the urls provided by the discovery service
			 */
			private boolean useUrlOverrides;

			/**
			 * ONE ID url (absolute) which defines the structure and location of the API endpoints
			 */
			private String discoveryUrl;

			/**
			 * Expected version of the discovery URL.
			 * Will warn if the version encountered is not expected one.
			 */
			private String discoveryVersion;

			/**
			 * Absolute url for redirecting to during 2nd leg of OAuth login.
			 * MUST be whitelisted in advance with Ontario Health
			 */
			private String loginRedirectUrl;

			/**
			 * Absolute url for redirect after logout
			 * MUST be whitelisted in advance with Ontario Health
			 */
			private String logoutRedirectUrl;

			/**
			 * Absolute path to the pk12 signing certificate used to generate JWTs for OAuth/OIDC request
			 */
			private String signingCertificate;

			/**
			 * Password to the certificate
			 */
			private String signingCertificatePassword;

			/**
			 * Audience claim for our JWT assertion when logging in via OAuth/OIDC.  Provided
			 * by Ontario health.
			 */
			private String jwtAssertionAud;

			/**
			 * Default interval (in minutes) used to refresh access_tokens.  The interval is relative
			 * to system time, not per token.
			 */
			private Integer defaultTokenRefreshInterval;

		}

		/**
		 * EHR properties for the One Access Gateway (OAG)
		 */
		@Data
		public static class OAG
		{
			/**
			 * Lookup key which points to the FHIR base url in the toolbar
			 */
			private String toolbarFhirKey;

			/**
			 * Lookup key which points to the Context Management System (CMS) url in the toolbar
			 */
			private String toolbarCmsKey;

			/**
			 * Default timeout period (in seconds) for CMS requests
			 */
			private Integer defaultTimeoutSeconds;
		}
	}



	/**
	 * Configuration class for Polaris API settings.
	 * <p>
	 * This class holds OAuth2 and OpenID Connect configuration values required for
	 * interacting with the Polaris authentication and authorization system.
	 * </p>
	 */
	@Data
	public static class PolarisConfig {

		/**
		 * The expected issuer identifier for the tokens issued by Polaris.
		 * This is used to validate the `iss` claim in the ID token.
		 */
		private String issuer;

		/**
		 * The OAuth2 Authorization Endpoint URL used to initiate the authentication request.
		 */
		private String authorizationEndpoint;

		/**
		 * The OAuth2 Token Endpoint URL where client credentials are exchanged for tokens.
		 */
		private String tokenEndpoint;

		/**
		 * The OpenID Connect UserInfo Endpoint URL used to retrieve information about the authenticated user.
		 */
		private String userinfoEndpoint;

		/**
		 * The URL for the JSON Web Key Set (JWKS) used to validate JWT signatures. (public key).
		 */
		private String jwksUri;

		/**
		 * The list of supported signing algorithms for the ID token.
		 * Used to verify if the token received is signed with a supported algorithm.
		 */
		private List<String> idTokenSigningAlgValuesSupported;

	}

}