/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.managers;

import java.util.Optional;
import lombok.val;
import org.oscarehr.common.dao.FaxAccountDao;
import org.oscarehr.common.model.FaxAccount;
import org.oscarehr.common.model.SystemPreferences;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import oscar.util.SystemPreferencesUtils;

@Service
public class FaxAccountManager {

  @Autowired
  private FaxAccountDao faxAccountDao;

  protected static final String FAX_DEFAULT_PREFERENCE_NOT_SET = "-1";

  public Optional<FaxAccount> getActiveFaxAccount() {
    try {
      val faxAccountId = Integer.parseInt(SystemPreferencesUtils.getPreferenceValueByName(
          SystemPreferences.FAX_DEFAULT_ACCOUNT_ID, FAX_DEFAULT_PREFERENCE_NOT_SET));
      return Optional.ofNullable(faxAccountDao.getEnabledFaxAccount(faxAccountId));
    } catch (NumberFormatException e) {
      return Optional.empty();
    }
  }

  public boolean hasActiveFaxAccount() {
    return getActiveFaxAccount().isPresent();
  }
}
