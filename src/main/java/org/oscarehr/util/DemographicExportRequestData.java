/**
 * Copyright (c) 2024 WELL EMR Group Inc. This software is made available under the terms of the GNU
 * General Public License, Version 2, 1991 (GPLv2). License details are available via
 * "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.util;

import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.apache.struts.util.MessageResources;

@Getter
@Setter
public class DemographicExportRequestData {

  private final Map<String, String[]> parameterMap;
  private final LoggedInInfo loggedInInfo;
  private final String clientIp;
  private final MessageResources messageResources;

  public DemographicExportRequestData(
      final Map<String, String[]> parameterMap,
      final LoggedInInfo loggedInInfo,
      final String clientIp, 
      final MessageResources messageResources) {
    this.parameterMap = parameterMap;
    this.loggedInInfo = loggedInInfo;
    this.clientIp = clientIp;
    this.messageResources = messageResources;
  }

  public String getParameter(final String name) {
    String[] values = parameterMap.get(name);
    return (values != null && values.length > 0) ? values[0] : null;
  }
}
