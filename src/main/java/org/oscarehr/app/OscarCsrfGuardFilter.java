package org.oscarehr.app;

import lombok.val;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.owasp.csrfguard.CsrfGuard;
import org.owasp.csrfguard.CsrfGuardException;
import org.owasp.csrfguard.action.IAction;
import org.owasp.csrfguard.http.InterceptRedirectResponse;
import org.owasp.csrfguard.log.LogLevel;
import org.owasp.csrfguard.util.RandomGenerator;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import oscar.OscarProperties;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Map;

/**
 * Oscar OscarCsrfGuardFilter
 * A CsrfGuardFilter implementation that supports detecting and paring multipart/form-data requests in addition to 
 * the existing support
 */
public class OscarCsrfGuardFilter implements Filter {

	private FilterConfig filterConfig = null;

  private final boolean doRedirect = OscarProperties.isCsrfDoRedirectEnabled();

	@Override
	public void destroy() {
		filterConfig = null;
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws IOException, ServletException {

		//maybe the short circuit to disable is set
		if (!CsrfGuard.getInstance().isEnabled()) {
			filterChain.doFilter(request, response);
			return;
		}

		/* only work with HttpServletRequest objects */
		if (request instanceof HttpServletRequest && response instanceof HttpServletResponse) {

			HttpServletRequest httpRequest = (HttpServletRequest) request;
			HttpSession session = httpRequest.getSession(false);

			//if there is no session and we aren't validating when no session exists
			if (session == null && !CsrfGuard.getInstance().isValidateWhenNoSessionExists()) {
				// If there is no session, no harm can be done
				filterChain.doFilter(httpRequest, (HttpServletResponse) response);
				return;
			}

			CsrfGuard csrfGuard = CsrfGuard.getInstance();
			csrfGuard.getLogger().log(String.format("CsrfGuard analyzing request %s", httpRequest.getRequestURI()));
			InterceptRedirectResponse httpResponse = new InterceptRedirectResponse((HttpServletResponse) response, httpRequest, csrfGuard);

			if ((session != null && session.isNew()) && csrfGuard.isUseNewTokenLandingPage()) {
                csrfGuard.writeLandingPage(httpRequest, httpResponse);
            } else if (ServletFileUpload.isMultipartContent(httpRequest)) {
                MultiReadHttpServletRequest multiReadHttpRequest = new MultiReadHttpServletRequest(httpRequest);
                if (isValidMultipartRequest(multiReadHttpRequest, httpResponse)) {
                    filterChain.doFilter(multiReadHttpRequest, httpResponse);
                } else if (!doRedirect) {
                    filterChain.doFilter(multiReadHttpRequest, httpResponse);
                }
			} else if (csrfGuard.isValidRequest(httpRequest, httpResponse)) {
				filterChain.doFilter(httpRequest, httpResponse);
			} else if (!doRedirect) {
                filterChain.doFilter(httpRequest, httpResponse);
			}

			/* update tokens */
			csrfGuard.updateTokens(httpRequest);

		} else {
			filterConfig.getServletContext().log(String.format("[WARNING] CsrfGuard does not know how to work with requests of class %s ", request.getClass().getName()));

			filterChain.doFilter(request, response);
		}
	}

	@Override
	public void init(@SuppressWarnings("hiding") FilterConfig filterConfig) throws ServletException {
		this.filterConfig = filterConfig;
    val csrfGuard = CsrfGuard.getInstance();
    if (!this.doRedirect) {
      IAction redirectActionToRemove = null;
      for (val action : csrfGuard.getActions()) {
        if ("Redirect".equals(action.getName())) {
          redirectActionToRemove = action;
          break;
        }
      }
      if (redirectActionToRemove != null) {
        csrfGuard.getActions().remove(redirectActionToRemove);
      }
    }
  }

    private boolean isValidMultipartRequest(MultiReadHttpServletRequest request, HttpServletResponse response) {
        CsrfGuard csrfGuard = CsrfGuard.getInstance();
        boolean valid = !csrfGuard.isProtectedPageAndMethod(request);
        HttpSession session = request.getSession(true);
        String tokenFromSession = (String) session.getAttribute(csrfGuard.getSessionKey());

        /** sending request to protected resource - verify token **/
        if (tokenFromSession != null && !valid) {
            try {
                if (csrfGuard.isAjaxEnabled() && isAjaxRequest(request)) {
                    String tokenFromRequest = request.getHeader(csrfGuard.getTokenName());

                    if (tokenFromRequest == null) {
                        /** FAIL: token is missing from the request **/
                        throw new CsrfGuardException("required token is missing from the request");
                    } else {
                        //if there are two headers, then the result is comma separated
                        if (!tokenFromSession.equals(tokenFromRequest)) {
                            if (tokenFromRequest.contains(",")) {
                                tokenFromRequest = tokenFromRequest.substring(0, tokenFromRequest.indexOf(',')).trim();
                            }
                            if (!tokenFromSession.equals(tokenFromRequest)) {
                                /** FAIL: the request token does not match the session token **/
                                throw new CsrfGuardException("request token does not match session token");
                            }
                        }
                    }
                } else if (csrfGuard.isTokenPerPageEnabled()) {
                    verifyPageToken(request);
                } else {
                    verifySessionToken(request);
                }
                valid = true;
            } catch (CsrfGuardException csrfe) {
                callActionsOnError(request, response, csrfe);
            }

            /** rotate session and page tokens **/
            if (!isAjaxRequest(request) && csrfGuard.isRotateEnabled()) {
                rotateTokens(request);
            }
            /** expected token in session - bad state and not valid **/
        } else if (tokenFromSession == null && !valid) {
            try {
                throw new CsrfGuardException("CsrfGuard expects the token to exist in session at this point");
            } catch (CsrfGuardException csrfe) {
                callActionsOnError(request, response, csrfe);

            }
        } else {
            /** unprotected page - nothing to do **/
        }

        return valid;
    }
    
    private boolean isAjaxRequest(MultiReadHttpServletRequest request) {
        return request.getHeader("X-Requested-With") != null;
    }

    private void verifyPageToken(MultiReadHttpServletRequest request) throws CsrfGuardException {
        CsrfGuard csrfGuard = CsrfGuard.getInstance();
        HttpSession session = request.getSession(true);
        @SuppressWarnings("unchecked")
        Map<String, String> pageTokens = (Map<String, String>) session.getAttribute(CsrfGuard.PAGE_TOKENS_KEY);

        String tokenFromPages = (pageTokens != null ? pageTokens.get(request.getRequestURI()) : null);
        String tokenFromSession = (String) session.getAttribute(csrfGuard.getSessionKey());
        MultipartHttpServletRequest multipartRequest = new CommonsMultipartResolver().resolveMultipart(request);
        String tokenFromRequest = multipartRequest.getParameter(csrfGuard.getTokenName());

        if (tokenFromRequest == null) {
            /** FAIL: token is missing from the request **/
            throw new CsrfGuardException("required token is missing from the request");
        } else if (tokenFromPages != null) {
            if (!tokenFromPages.equals(tokenFromRequest)) {
                /** FAIL: request does not match page token **/
                throw new CsrfGuardException("request token does not match page token");
            }
        } else if (!tokenFromSession.equals(tokenFromRequest)) {
            /** FAIL: the request token does not match the session token **/
            throw new CsrfGuardException("request token does not match session token");
        }
    }

    private void verifySessionToken(MultiReadHttpServletRequest request) throws CsrfGuardException {
        CsrfGuard csrfGuard = CsrfGuard.getInstance();
        HttpSession session = request.getSession(true);
        String tokenFromSession = (String) session.getAttribute(csrfGuard.getSessionKey());
        MultipartHttpServletRequest multipartRequest = new CommonsMultipartResolver().resolveMultipart(request);
        String tokenFromRequest = multipartRequest.getParameter(csrfGuard.getTokenName());

        if (tokenFromRequest == null) {
            /** FAIL: token is missing from the request **/
            throw new CsrfGuardException("required token is missing from the request");
        } else if (!tokenFromSession.equals(tokenFromRequest)) {
            /** FAIL: the request token does not match the session token **/
            throw new CsrfGuardException("request token does not match session token");
        }
    }

    private void callActionsOnError(MultiReadHttpServletRequest request,
                                    HttpServletResponse response, CsrfGuardException csrfe) {
        CsrfGuard csrfGuard = CsrfGuard.getInstance();
        for (IAction action : csrfGuard.getActions()) {
            try {
                action.execute(request, response, csrfe, csrfGuard);
            } catch (CsrfGuardException exception) {
                csrfGuard.getLogger().log(LogLevel.Error, exception);
            }
        }
    }
    private void rotateTokens(MultiReadHttpServletRequest request) {
        CsrfGuard csrfGuard = CsrfGuard.getInstance();
        HttpSession session = request.getSession(true);

        /** rotate master token **/
        String tokenFromSession = null;

        try {
            tokenFromSession = RandomGenerator.generateRandomId(csrfGuard.getPrng(), csrfGuard.getTokenLength());
        } catch (Exception e) {
            throw new RuntimeException(String.format("unable to generate the random token - %s", e.getLocalizedMessage()), e);
        }

        session.setAttribute(csrfGuard.getSessionKey(), tokenFromSession);

        /** rotate page token **/
        if (csrfGuard.isTokenPerPageEnabled()) {
            @SuppressWarnings("unchecked")
            Map<String, String> pageTokens = (Map<String, String>) session.getAttribute(CsrfGuard.PAGE_TOKENS_KEY);

            try {
                pageTokens.put(request.getRequestURI(), RandomGenerator.generateRandomId(csrfGuard.getPrng(), csrfGuard.getTokenLength()));
            } catch (Exception e) {
                throw new RuntimeException(String.format("unable to generate the random token - %s", e.getLocalizedMessage()), e);
            }
        }
    }
}
