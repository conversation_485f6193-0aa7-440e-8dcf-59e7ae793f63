package org.oscarehr.common.dto;

import health.apps.gateway.LinkData;
import java.util.Comparator;
import java.util.Date;
import lombok.Getter;
import org.oscarehr.common.interfaces.EFormDataSummary;

@Getter
public class EFormDataMetadata implements EFormDataSummary, LinkData {

  private final Integer id;
  private final Integer formId;
  private final String formName;
  private final String subject;
  private final Integer demographicId;
  private final boolean current;
  private final Date formDate;
  private final Date formTime;
  private final String providerNo;
  private final boolean showLatestFormOnly;
  private final boolean patientIndependent;
  private final Integer appointmentNo;
  private final String roleType;
  private final String guid;
  private final Integer remoteSystemId;

  public EFormDataMetadata(Integer id, Integer formId, String formName, String subject, Integer demographicId, boolean current, Date formDate, Date formTime, String providerNo, boolean showLatestFormOnly, boolean patientIndependent, Integer appointmentNo, String roleType, String guid, Integer remoteSystemId) {
    this.id = id;
    this.formId = formId;
    this.formName = formName;
    this.subject = subject;
    this.demographicId = demographicId;
    this.current = current;
    this.formDate = formDate;
    this.formTime = formTime;
    this.providerNo = providerNo;
    this.showLatestFormOnly = showLatestFormOnly;
    this.patientIndependent = patientIndependent;
    this.appointmentNo = appointmentNo;
    this.roleType = roleType;
    this.guid = guid;
    this.remoteSystemId = remoteSystemId;
  }

  /**
   * This comparator sorts EFormData ascending based on the formDate and formTime
   */
  public static final Comparator<EFormDataMetadata> FORM_DATETIME_COMPARATOR = new Comparator<EFormDataMetadata>()
  {
    public int compare(final EFormDataMetadata o1, final EFormDataMetadata o2) {
      if (o1.formDate.equals(o2.formDate)) {
        return(o1.formTime.compareTo(o2.formTime));
      }
      return(o1.formDate.compareTo(o2.formDate));
    }
  };

  public boolean isRemote() {
    return remoteSystemId != null;
  }

  public void setRemoteSystemId(Integer remoteSystemId) {
    throw new UnsupportedOperationException("Remote system ID is immutable for eForm Metadata");
  }

}
