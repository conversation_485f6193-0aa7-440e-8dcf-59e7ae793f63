/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.common.model;

import java.time.ZonedDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.oscarehr.common.enums.FaxProviderType;

@Getter
@Setter
@Entity
@NoArgsConstructor
public class FaxAccount extends AbstractModel<Integer> {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  private String accountId;

  private String accountSecret;

  @Enumerated(value = EnumType.STRING)
  private FaxProviderType faxProviderType;

  private boolean accountEnabled;

  private boolean inboundEnabled;

  private boolean outboundEnabled;

  private String coverLetterOption;

  private String displayName;

  @Column(columnDefinition = "TIMESTAMP")
  private ZonedDateTime deletedAt;
}
