/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package org.oscarehr.common.model;

import health.apps.gateway.LinkData;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "casemgmt_note")
public class CaseManagementNoteJpa extends AbstractModel<Long> implements LinkData {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "note_id")
  private Long id;

  @Column(name = "appointmentNo")
  private int appointmentNumber;

  private boolean archived;

  @Column(name = "isAvailable")
  private boolean available;

  @Column(name = "billing_code")
  private String billingCode = "";

  @Column(name = "include_issue_innote")
  private boolean includeIssue = true;

  @Column(name = "program_no")
  private String programNumber;

  @Column(name = "demographic_no")
  private String demographicNumber;

  @Column(name = "encounter_type")
  private String encounterType = "";

  @Transient
  private Provider provider;

  @Column(name = "provider_no")
  private String providerNumber;

  private boolean signed = false;

  @Column(name = "signing_provider_no")
  private String signingProviderNo;

  @Temporal(TemporalType.TIMESTAMP)
  private Date autoSyncDate;

  @Temporal(TemporalType.TIMESTAMP)
  private Date lastSyncedDate;

  @Column(name = "observation_date")
  @Temporal(TemporalType.TIMESTAMP)
  private Date observationDate;

  @Column(name = "update_date")
  @Temporal(TemporalType.TIMESTAMP)
  private Date updateDate = new Date();

  private String note;

  private String history;

  private boolean locked;

  private String password;

  private int position = 0;

  private Integer hourOfEncounterTime;
  private Integer hourOfEncTransportationTime;
  private Integer minuteOfEncounterTime;
  private Integer minuteOfEncTransportationTime;

  @Column(name = "reporter_caisi_role")
  private String reporterCaisiRole;

  @Column(name = "reporter_program_team")
  private String reporterProgramTeam;

  private String uuid;
  private Integer remoteSystemId;

  public String getGuid() {
    return uuid;
  }

  public boolean isRemote() {
    return remoteSystemId != null;
  }
}
