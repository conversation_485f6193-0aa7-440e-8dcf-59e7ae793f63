/**
 * Copyright (c) 2023 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package org.oscarehr.common.enums;

import lombok.Getter;

@Getter
public enum FeatureFlagEnum {
  LINK_ENABLED("link.enabled", false),
  RECORD_SHARE("record_share.enabled", false),
  STATUS_ON_HOVER("link.get_link_status_on_hover.enabled", false),
  STATUS_ON_LOAD_ECHART("link.link_status_on_load.echart.enabled", false),
  STATUS_ON_LOAD_MASTER_RECORD("link.link_status_on_load.master.enabled", false),
  LABS_ENABLED("link.labs.enabled", true),
  PREVENTIONS_ENABLED("link.preventions.enabled", true),
  PREVENTIONS_FAILURE_TICKLER("link.preventions.send_tickler_on_failure", true),
  EFORM_ENABLED("link.eform.enabled", true),
  EFORM_FAILURE_TICKLER("link.eforms.send_tickler_on_failure", true),
  CPP_ENABLED("link.cpp.enabled", true),
  MEDICATIONS_ENABLED("link.medications.enabled", true),
  MEDICATIONS_FAILURE_TICKLER("link.medications.send_tickler_on_failure", true),
  ALLERGIES_ENABLED("link.allergies.enabled", true),
  DOCUMENTS_ENABLED("link.documents.enabled", true),
  HRM_ENABLED("link.hrm.enabled", true),
  QUERY_CACHE_ENABLED("query_cache.enabled", false),
  NOTE_FAILURE_TICKLER("link.notes.send_tickler_on_failure", true),
  LOG4J_JSON_LOGGING_ENABLED("log4j.json_logging.enabled", false),
  ECHART_NEW_MEASUREMENTS_DISPLAY("well.echart.use_new_measurement_display", false),
  ECHART_OPTIMIZED_NOTE_QUERIES("echart.optimized_note_queries.enabled", false),
  ECHART_STREAMLINED_NOTES("echart.streamlined_notes.enabled", true),;

  private final String key;
  private final boolean enabledByDefault;

  FeatureFlagEnum(final String key, final boolean defaultValue) {
    this.key = key;
    this.enabledByDefault = defaultValue;
  }
}
