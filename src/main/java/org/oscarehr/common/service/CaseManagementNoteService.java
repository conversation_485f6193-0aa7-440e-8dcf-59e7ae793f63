/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package org.oscarehr.common.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Nullable;
import lombok.extern.log4j.Log4j2;
import org.oscarehr.PMmodule.dao.ProgramDao;
import org.oscarehr.casemgmt.model.CaseManagementNote;
import org.oscarehr.common.dao.CaseManagementNoteJpaDao;
import org.oscarehr.common.dao.SecRoleDao;
import org.oscarehr.common.model.CaseManagementNoteJpa;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import oscar.util.StringUtils;

/**
 * This service serves as a facade for fetching and handling Case Management Note data.
 */
@Service
@Log4j2
public class CaseManagementNoteService {

  @Autowired
  private CaseManagementNoteJpaDao caseManagementNoteJpaDao;

  @Autowired
  private SecRoleDao secRoleDao;

  @Autowired
  private ProgramDao programDao;

  /**
   * Converts a list of CaseManagementNoteJpa objects to a list of CaseManagementNote objects
   *
   * @param jpaNotes The list of CaseManagementNoteJpa objects to convert
   * @return The converted list of CaseManagementNote objects
   */
  public List<CaseManagementNote> convertJpaNotesToCaseManagementNotes(
      final List<CaseManagementNoteJpa> jpaNotes) {
    if (jpaNotes == null || jpaNotes.isEmpty()) {
      return Collections.emptyList();
    }

    // Filter out notes with null or empty UUIDs to avoid processing invalid data
    List<CaseManagementNoteJpa> filteredJpaNotes = filterJpaNotesWithNullOrEmptyUuid(jpaNotes);

    List<CaseManagementNote> noteList = new ArrayList<>();

    // Batch fetch related data before entering loop
    Set<String> uuids = getUuidSet(filteredJpaNotes);
    Map<String, Integer> revisionCountMap =
        caseManagementNoteJpaDao.getRevisionCountsAsMap(uuids);
    Map<String, String> roleNameMap =
        secRoleDao.getRoleNamesAsMap(getRoleIdSet(filteredJpaNotes));
    Map<String, String> programTeamNameMap =
        programDao.getProgramTeamNamesAsMap(getProgramNumberSet(filteredJpaNotes));
    Map<String, Date> createDateMap =
        caseManagementNoteJpaDao.getCreateDatesAsMap(uuids);

    for (CaseManagementNoteJpa jpaNote : filteredJpaNotes) {
      String uuid = jpaNote.getUuid();
      CaseManagementNote note = convertJpaNoteToCaseManagementNote(
          jpaNote,
          revisionCountMap.get(uuid),
          roleNameMap.get(jpaNote.getReporterCaisiRole()),
          programTeamNameMap.get(jpaNote.getProgramNumber()),
          createDateMap.get(uuid)
      );
      if (note != null) {
        noteList.add(note);
      }
    }
    return noteList;
  }

  /**
   * Converts a CaseManagementNoteJpa object to a CaseManagementNote object
   *
   * @param jpaNote The CaseManagementNoteJpa object to convert
   * @return The converted CaseManagementNote object
   */
  @Nullable
  private CaseManagementNote convertJpaNoteToCaseManagementNote(
      final CaseManagementNoteJpa jpaNote,
      final Integer revisionCount,
      final String roleName,
      final String programName,
      final Date createDate
  ) {
    if (jpaNote == null) {
      return null;
    }

    CaseManagementNote note = new CaseManagementNote();
    Long id = jpaNote.getId();
    String uuid = jpaNote.getUuid();

    log.debug("Converting CaseManagementNoteJpa with ID: {} and UUID: {}", id, uuid);

    // Map basic fields
    note.setId(id);
    note.setAppointmentNo(jpaNote.getAppointmentNumber());
    note.setArchived(jpaNote.isArchived());
    note.setAvailable(jpaNote.isAvailable());
    note.setBilling_code(jpaNote.getBillingCode());
    note.setDemographic_no(jpaNote.getDemographicNumber());
    note.setEncounter_type(jpaNote.getEncounterType());
    note.setHistory(jpaNote.getHistory());
    note.setHourOfEncounterTime(jpaNote.getHourOfEncounterTime());
    note.setHourOfEncTransportationTime(jpaNote.getHourOfEncTransportationTime());
    note.setIncludeissue(jpaNote.isIncludeIssue());
    note.setAutoSyncDate(jpaNote.getAutoSyncDate());
    note.setLastSyncedDate(jpaNote.getLastSyncedDate());
    note.setLocked(jpaNote.isLocked());
    note.setMinuteOfEncounterTime(jpaNote.getMinuteOfEncounterTime());
    note.setMinuteOfEncTransportationTime(jpaNote.getMinuteOfEncTransportationTime());
    note.setNote(jpaNote.getNote());
    note.setObservation_date(jpaNote.getObservationDate());
    note.setPassword(jpaNote.getPassword());
    note.setPosition(jpaNote.getPosition());
    note.setProgram_no(jpaNote.getProgramNumber());
    note.setProviderNo(jpaNote.getProviderNumber());
    note.setSigned(jpaNote.isSigned());
    note.setSigning_provider_no(jpaNote.getSigningProviderNo());
    note.setUpdate_date(jpaNote.getUpdateDate());
    note.setUuid(uuid);
    note.setRemoteSystemId(jpaNote.getRemoteSystemId());
    note.setReporter_caisi_role(jpaNote.getReporterCaisiRole());
    note.setReporter_program_team(jpaNote.getReporterProgramTeam());

    // Formula fields from original CaseManagementNote Hibernate mappings
    if (revisionCount == null) {
      log.warn("Revision count not found for note with ID: {} and UUID: {}", id, uuid);
      note.setRevision(null);
    } else {
      note.setRevision(revisionCount.toString());
    }
    note.setRoleName(roleName);
    note.setProgramName(programName);
    note.setCreate_date(createDate);

    return note;
  }

  private Set<String> getUuidSet(final List<CaseManagementNoteJpa> jpaNotes) {
    Set<String> uuids = new HashSet<>();
    for (CaseManagementNoteJpa jpaNote : jpaNotes) {
      uuids.add(jpaNote.getUuid());
    }
    return uuids;
  }

  private Set<String> getRoleIdSet(final List<CaseManagementNoteJpa> jpaNotes) {
    Set<String> roleIds = new HashSet<>();
    for (CaseManagementNoteJpa jpaNote : jpaNotes) {
      roleIds.add(jpaNote.getReporterCaisiRole());
    }
    return roleIds;
  }

  /**
   * Extracts program numbers from a list of CaseManagementNoteJpa objects.
   * This method ensures that only valid program numbers are added to the set.
   * Valid program numbers are numeric and non-empty.
   * @param jpaNotes The list of CaseManagementNoteJpa objects to extract program numbers from
   * @return A set of valid program numbers as integers
   */
  private Set<Integer> getProgramNumberSet(final List<CaseManagementNoteJpa> jpaNotes) {
    Set<Integer> programNumbers = new HashSet<>();
    for (CaseManagementNoteJpa jpaNote : jpaNotes) {
      String programNumber = jpaNote.getProgramNumber();
      if (StringUtils.isNullOrEmpty(programNumber) || !StringUtils.isNumeric(programNumber)) {
        log.warn("CaseManagementNoteJpa object with ID {} has an invalid program number. "
            + "Program number will not be added to the query set.", jpaNote.getId());
        continue;
      }
      programNumbers.add(Integer.parseInt(jpaNote.getProgramNumber()));
    }
    return programNumbers;
  }

  /**
   * Filters out CaseManagementNoteJpa objects with null or empty UUIDs.
   * @param jpaNotes The list of CaseManagementNoteJpa objects to filter
   * @return A filtered list of CaseManagementNoteJpa objects with non-null and non-empty UUIDs
   */
  private List<CaseManagementNoteJpa> filterJpaNotesWithNullOrEmptyUuid(
      final List<CaseManagementNoteJpa> jpaNotes) {
    List<CaseManagementNoteJpa> filteredNotes = new ArrayList<>();
    for (CaseManagementNoteJpa jpaNote : jpaNotes) {
      if (jpaNote.getUuid() == null || jpaNote.getUuid().isEmpty()) {
        log.warn("CaseManagementNoteJpa object with ID {} has null or empty UUID. "
            + "It will not be converted to a CaseManagementNote", jpaNote.getId());
        continue;
      }
      filteredNotes.add(jpaNote);
    }
    return filteredNotes;
  }
}
