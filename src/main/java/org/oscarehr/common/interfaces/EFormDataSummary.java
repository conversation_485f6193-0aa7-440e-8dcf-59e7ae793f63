package org.oscarehr.common.interfaces;

import java.util.Date;

/**
 * Interface representing a summary of EForm data.
 * This interface is mainly used to allow similar EFormData models to be used interchangeably
 * when listing EForm data in the UI.
 */
public interface EFormDataSummary {
  Integer getId();
  Integer getFormId();
  String getFormName();
  String getSubject();
  Integer getDemographicId();
  boolean isCurrent();
  Date getFormDate();
  Date getFormTime();
  String getProviderNo();
  boolean isShowLatestFormOnly();
  boolean isPatientIndependent();
  Integer getAppointmentNo();
  String getRoleType();
  String getGuid();
  Integer getRemoteSystemId();
  boolean isRemote();
}
