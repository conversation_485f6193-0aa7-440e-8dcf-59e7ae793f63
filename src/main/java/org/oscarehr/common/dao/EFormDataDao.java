/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */

package org.oscarehr.common.dao;

import static health.apps.gateway.LinkUtility.deduplicateByGuid;

import health.apps.gateway.LinkUtility;
import health.apps.gateway.converters.DocumentReferenceConverter;
import health.apps.gateway.service.GWConfigurationService;
import health.apps.gateway.service.GatewayDao;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import javax.persistence.Query;
import lombok.val;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.hl7.fhir.r4.model.DocumentReference;
import org.oscarehr.common.dto.EFormDataMetadata;
import org.oscarehr.common.enums.FeatureFlagEnum;
import org.oscarehr.common.interfaces.EFormDataSummary;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.common.model.EFormData;
import org.oscarehr.managers.DemographicManager;
import org.oscarehr.managers.TicklerManager;
import org.oscarehr.util.MiscUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class EFormDataDao extends AbstractDao<EFormData> {

	@Autowired
  private GWConfigurationService configurationService;
	@Autowired
	private GatewayDao gatewayDao;
	@Autowired
	private TicklerManager ticklerManager;
	@Autowired
	private DocumentReferenceConverter documentReferenceConverter;
	@Autowired
	private DemographicManager demographicManager;

	private static final Logger logger = MiscUtils.getLogger();

	public EFormDataDao() {
		super(EFormData.class);
	}

	public static final String SORT_NAME = "form_name";
	public static final String SORT_SUBJECT = "subject";

	public List<EFormData> findByDemographicId(Integer demographicId) {
		Query query = entityManager.createQuery("select x from " + modelClass.getSimpleName() + " x where x.demographicId=?1");
		query.setParameter(1, demographicId);

		@SuppressWarnings("unchecked")
		List<EFormData> results = query.getResultList();

		return (results);
	}

	public List<EFormData> findByDemographicIdSinceLastDate(Integer demographicId, Date lastDate) {
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(lastDate);

		Query query = entityManager.createQuery("select x from " + modelClass.getSimpleName() + " x where x.demographicId=?1 and x.formDate > ?2 or (x.formDate= ?3 and x.formTime >= ?4)");
		query.setParameter(1, demographicId);
		query.setParameter(2, lastDate);
		query.setParameter(3, lastDate);
		query.setParameter(4, lastDate);

		@SuppressWarnings("unchecked")
		List<EFormData> results = query.getResultList();

		return (results);
	}

	//for integrator
	public List<Integer> findDemographicIdSinceLastDate(Date lastDate) {
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(lastDate);

		Query query = entityManager.createQuery("select x.demographicId from " + modelClass.getSimpleName() + " x where x.formDate > ?1 or (x.formDate= ?2 and x.formTime >= ?3)");
		query.setParameter(1, lastDate);
		query.setParameter(2, lastDate);
		query.setParameter(3, lastDate);

		@SuppressWarnings("unchecked")
		List<Integer> results = query.getResultList();

		return (results);
	}

	public EFormData findByFormDataId(Integer formDataId) {
		Query query = entityManager.createQuery("select x from " + modelClass.getSimpleName() + " x where x.id=?1");
		query.setParameter(1, formDataId);

		return this.getSingleResultOrNull(query);
	}

	public List<EFormData> findByDemographicIdCurrent(Integer demographicId, Boolean current) {
		return findByDemographicIdCurrent(demographicId, current, 0, EFormDataDao.MAX_LIST_RETURN_SIZE);
	}

	/**
	 * @param demographicId can not be null
	 * @param current       can be null for both
	 * @return list of EFormData
	 */
	public List<EFormData> findByDemographicIdCurrent(Integer demographicId, Boolean current, int startIndex, int numToReturn) {
		return findByDemographicIdCurrent(demographicId, current, startIndex, numToReturn, null);
	}

	public List<EFormData> findByDemographicIdCurrentAttachedToConsult(String consultationId) {
		String sql = "SELECT * FROM eform_data e where e.patient_independent = false " +
				"AND e.fdid IN (SELECT cd.document_no FROM consultdocs cd WHERE cd.requestId = " + consultationId + " AND cd.docType = 'E' AND cd.deleted IS NULL) " +
				"ORDER BY e.form_date DESC, e.form_time DESC";
		Query query = entityManager.createNativeQuery(sql, modelClass);

		return query.getResultList();
	}

	/**
	 * Call findByDemographidIdCurrent and configure it to return the results of the query back in the
	 * form of EFormDataMetadata objects instead of EFormData.
	 *
	 * @param demographicId - represents the demographic ID to search e-forms for
	 * @param current - represents the status of the e-forms to search for
	 * @param startIndex - the starting index of the results to return
	 * @param numToReturn - the number of results to return
	 * @param sortBy - the field to sort the results by
	 * @return a list of EFormDataMetadata objects representing the e-forms matching the above query
	 */
	public List<EFormDataMetadata> findMetadataByDemographicIdCurrent(Integer demographicId, Boolean current, int startIndex, int numToReturn, String sortBy) {
		String selectString = "select new org.oscarehr.common.dto.EFormDataMetadata(x.id, x.formId, x.formName, x.subject, x.demographicId, x.current, x.formDate, x.formTime, x.providerNo, x.showLatestFormOnly, x.patientIndependent, x.appointmentNo, x.roleType, x.guid, x.remoteSystemId)";

		@SuppressWarnings("unchecked")
		List<EFormDataMetadata> results = findByDemographicIdCurrent(demographicId, current, startIndex, numToReturn, sortBy, selectString);
		return results;
	}

	public List<EFormData> findByDemographicIdCurrent(Integer demographicId, Boolean current, int startIndex, int numToReturn, String sortBy) {

		@SuppressWarnings("unchecked")
		List<EFormData> results = findByDemographicIdCurrent(demographicId, current, startIndex, numToReturn, sortBy, "select x");
		return results;
	}

	/**
	 * Finds paginated form data for the specified demographic record and status.
	 *
	 * @param demographicId - represents the demographic ID to search e-forms for
	 * @param current - represents the status of the e-forms to search for
	 * @param startIndex - the starting index of the results to return
	 * @param numToReturn - the number of results to return
	 * @param sortBy - the field to sort the results by
	 * @param selectString - optional string to select specific fields
	 *                     by default, it selects all fields which causes the return type to
	 *                     be List<EFormData>
	 * @return List of objects representing the e-forms matching the above query
	 * 					the type of objects returned depends on the selectString parameter
	 */
	private List findByDemographicIdCurrent(Integer demographicId, Boolean current, int startIndex, int numToReturn, String sortBy, String selectString) {

		StringBuilder sb = new StringBuilder();
		sb.append(selectString);
		sb.append(" from ");
		sb.append(modelClass.getSimpleName());
		sb.append(" x where x.demographicId=?1");
		sb.append(" and x.patientIndependent=false");

		int counter = 2;

		if (current != null) {
			sb.append(" and x.current=?");
			sb.append(counter);
			counter++;
		}

		sb.append(" order by ");

		if (sortBy != null) {
			if (SORT_NAME.equals(sortBy)) sb.append(" x.formName");
			else if (SORT_SUBJECT.equals(sortBy)) sb.append(" x.subject");
			else sb.append(" x.formDate DESC, x.formTime DESC");
		}

		if (sortBy == null) {
			sb.append(" x.formDate DESC, x.formTime DESC");
		}

		String sqlCommand = sb.toString();

		logger.debug("SqlCommand=" + sqlCommand);

		Query query = entityManager.createQuery(sqlCommand);
		query.setParameter(1, demographicId);

		query.setFirstResult(startIndex);
		query.setMaxResults(numToReturn);

		counter = 2;

		if (current != null) {
			query.setParameter(counter, current);
			counter++;
		}

    return query.getResultList();
	}

	private Query getColumnsByDemographicIdAndCurrent(
			final Integer demographicId,
			final Boolean isCurrent,
			final String columns) {
		val sb = new StringBuilder();

		sb.append("select ");
		sb.append(columns);
		sb.append(" from ");
		sb.append(modelClass.getSimpleName());
		sb.append(" where demographicId=:demographicId");
		sb.append(" and patientIndependent=false");
		if (isCurrent != null) {
			sb.append(" and current=:isCurrent");
		}

		val sqlCommand = sb.toString();
		val query = entityManager.createQuery(sqlCommand);

		query.setParameter("demographicId", demographicId);
		if (isCurrent != null) {
			query.setParameter("isCurrent", isCurrent);
		}
		return query;
	}

	public int countByDemographicIdCurrent(final Integer demographicId, final Boolean isCurrent) {
		val query = getColumnsByDemographicIdAndCurrent(demographicId, isCurrent, "count(*)");

		val count = (Long) query.getSingleResult();

		return count.intValue();
	}

	public List<String> getGUIDsByDemographicIdCurrent(final Integer demographicId, final Boolean isCurrent) {
		val query = getColumnsByDemographicIdAndCurrent(demographicId, isCurrent, "guid");

		return (List<String>) query.getResultList();
	}

	/**
	 * Fetches local and remote EFormData and returns a list of maps. Each map represents an
	 * EFormData object, excluding the `formData` field.
	 * When fetching local EFormData, it retrieves all fields except `formData` due to its large size.
	 * This drastically improves query performance.
	 *
	 * @param demographicId the demographic ID to search EForms for
	 * @param current the status of the EForms to search for
	 * @return a list of maps representing EFormData objects, excluding the `formData` field
	 */
	public List<Map<String, Object>> findByDemographicIdCurrentExcludingFormData(
			final Integer demographicId,
			final Boolean current
	) {
		// Fetch local EFormDataMetadata, which does not include the `formData` field.
		List<EFormDataMetadata> eFormDataMetadataList = findMetadataByDemographicIdCurrent(
				demographicId, current, 0, EFormDataDao.MAX_LIST_RETURN_SIZE, null);

		// Fetch remote results if the system preference allows it.
		List<EFormData> remoteEFormDataList = fetchRemoteEformByDemographicId(demographicId);
		if (remoteEFormDataList != null && !remoteEFormDataList.isEmpty()) {
			addEFormDataListToMetadataList(remoteEFormDataList, eFormDataMetadataList);
			return convertEFormDataListToMapList(deduplicateByGuid(eFormDataMetadataList));
		}

		// Convert deduplicated list to a list of maps
		return convertEFormDataListToMapList(eFormDataMetadataList);
	}

	/**
	 * Add the EFormData objects' metadata to the provided metadata list.
	 * @param eFormDataList the list of EFormData objects to extract metadata from
	 * @param metaDataList the list to which the metadata will be added
	 */
	private void addEFormDataListToMetadataList(
			final List<EFormData> eFormDataList,
			final List<EFormDataMetadata> metaDataList
	) {
		for (EFormData remoteEFormData : eFormDataList) {
			metaDataList.add(remoteEFormData.getMetadata());
		}
	}

	public List<EFormData> findPatientIndependent(Boolean current) {
		StringBuilder sb = new StringBuilder();
		sb.append("select x from ");
		sb.append(modelClass.getSimpleName());
		sb.append(" x where x.patientIndependent=true");

		if (current != null) {
			sb.append(" and x.current=?1");
		}

		String sqlCommand = sb.toString();
		logger.debug("SqlCommand=" + sqlCommand);

		Query query = entityManager.createQuery(sqlCommand);

		if (current != null) {
			query.setParameter(1, current);
		}

		@SuppressWarnings("unchecked")
		List<EFormData> results = query.getResultList();

		return (results);
	}

	public List<EFormData> findByFormId(Integer formId) {

		Query query = entityManager.createQuery("select x from " + modelClass.getSimpleName() + " x where x.formId = ?1 and x.current = 1");
		query.setParameter(1, formId);

		@SuppressWarnings("unchecked")
		List<EFormData> results = query.getResultList();

		return results;
	}

	public List<Integer> findDemographicNosByFormId(Integer formId) {

		Query query = entityManager.createQuery("select x.demographicId from " + modelClass.getSimpleName() + " x where x.formId = ?1 and x.current = 1");
		query.setParameter(1, formId);

		@SuppressWarnings("unchecked")
		List<Integer> results = query.getResultList();

		return results;
	}


	public List<Integer> findAllFdidByFormId(Integer formId) {

		Query query = entityManager.createQuery("select distinct x.id from " + modelClass.getSimpleName() + " x where x.formId = ?1");
		query.setParameter(1, formId);

		@SuppressWarnings("unchecked")
		List<Integer> results = query.getResultList();

		return results;
	}

	//for EFormReportTool
	public List<Object[]> findMetaFieldsByFormId(Integer formId) {

		Query query = entityManager.createQuery("select distinct x.id, x.demographicId,x.formDate, x.formTime, x.providerNo  from " + modelClass.getSimpleName() + " x where x.formId = ?1");
		query.setParameter(1, formId);

		@SuppressWarnings("unchecked")
		List<Object[]> results = query.getResultList();

		return results;
	}


	public List<Integer> findAllCurrentFdidByFormId(Integer formId) {

		Query query = entityManager.createQuery("select distinct x.id from " + modelClass.getSimpleName() + " x where x.formId = ?1 and x.current = 1");
		query.setParameter(1, formId);

		@SuppressWarnings("unchecked")
		List<Integer> results = query.getResultList();

		return results;
	}


	public List<EFormData> findByFormIdProviderNo(List<String> providerNo, Integer formId) {

		Query query = entityManager.createQuery("select x from " + modelClass.getSimpleName() + " x where x.formId = ?1 and x.providerNo in (?2) and x.current = 1");
		//query.setParameter(1,fid);
		query.setParameter(1, formId);
		query.setParameter(2, providerNo);

		@SuppressWarnings("unchecked")
		List<EFormData> results = query.getResultList();

		return results;
	}


	/**
	 * Finds form data for the specified demographic record and form name
	 *
	 * @param demographicNo Demographic number to find the form data for
	 * @param formName      Form name to find the data for
	 * @return Returns all active matching form data, ordered by creation date and time
	 */
	@SuppressWarnings("unchecked")
	public List<EFormData> findByDemographicIdAndFormName(Integer demographicNo, String formName) {
		String queryString = "FROM EFormData e WHERE e.demographicId = :demographicNo AND e.formName LIKE :formName and status = '1' ORDER BY e.formDate, e.formTime DESC";
		Query query = entityManager.createQuery(queryString);
		query.setParameter("demographicNo", demographicNo);
		query.setParameter("formName", formName);
		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<EFormData> findByDemographicIdAndFormId(Integer demographicNo, Integer fid) {
		String queryString = "FROM EFormData e WHERE e.demographicId = :demographicNo AND e.formId = :formId and status = '1' ORDER BY e.formDate DESC, e.formTime DESC";
		Query query = entityManager.createQuery(queryString);
		query.setParameter("demographicNo", demographicNo);
		query.setParameter("formId", fid);
		return query.getResultList();
	}


	public List<EFormData> findByFidsAndDates(TreeSet<Integer> fids, Date dateStart, Date dateEnd) {
		if (fids == null || fids.isEmpty()) return null;

		Query query = entityManager.createQuery("select x from " + modelClass.getSimpleName() + " x where x.current=1 and x.formId in (?1) and x.formDate>=?2 and x.formDate<?3");
		query.setParameter(1, fids);
		query.setParameter(2, dateStart);
		query.setParameter(3, dateEnd);

		@SuppressWarnings("unchecked")
		List<EFormData> results = query.getResultList();

		return (results);
	}

	public List<EFormData> findByFdids(List<Integer> ids) {
		if (ids.size() == 0) return new ArrayList<EFormData>();

		Query query = entityManager.createQuery("select x from " + modelClass.getSimpleName() + " x where x.id in (:ids)");
		query.setParameter("ids", ids);

		@SuppressWarnings("unchecked")
		List<EFormData> results = query.getResultList();

		return results;
	}

	public boolean isLatestShowLatestFormOnlyPatientForm(Integer fdid) {
		//return true if:
		// 1) this is a ShowLatestFormOnly eform	AND
		// 2.1) the patient has only 1 eform of the same fid	OR
		// 2.2) this is the patient's latest eform of the same fid

		EFormData eformData = this.find(fdid);
		if (eformData == null) return false;
		if (!eformData.isShowLatestFormOnly()) return false;

		List<EFormData> sameEformList = this.getFormsSameFidSamePatient(fdid);
		if (sameEformList.size() == 1) return true;

		for (EFormData otherEform : sameEformList) {
			if (otherEform.getId().equals(fdid)) continue; //current eform

			Date eformDataDate = eformData.getFormDate();
			Date eformDataTime = eformData.getFormTime();
			Date otherEformDate = otherEform.getFormDate();
			Date otherEformTime = otherEform.getFormTime();

			if (eformDataDate != null && otherEformDate != null) {
				if (otherEformDate.after(eformDataDate)) {
					return false;
				}

				if (eformDataTime != null && otherEformTime != null) {
					if (eformDataDate.equals(otherEformDate) && otherEformTime.after(eformDataTime)) {
						return false;
					}
				}
			}
			if (eformDataDate.equals(otherEformDate) && eformDataTime.equals(otherEformTime) && otherEform.getId() > fdid) {
				return false;
			}
		}

		return true;
	}

	public List<EFormData> getFormsSameFidSamePatient(Integer fdid) {
		EFormData eformData = this.find(fdid);
		if (eformData == null) return new ArrayList<EFormData>(); //empty list

		List<EFormData> efmDataList = this.findByDemographicIdCurrent(eformData.getDemographicId(), true);

		for (int i = 0; i < efmDataList.size(); i++) {
			if (!eformData.getFormId().equals(efmDataList.get(i).getFormId())) {
				efmDataList.remove(i);
				i--;
			}
		}
		return efmDataList;
	}

	//for integrator
	public List<Integer> findemographicIdSinceLastDate(Date lastDate) {
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(lastDate);

		Query query = entityManager.createQuery("select x.demographicId from " + modelClass.getSimpleName() + " x where x.formDate > ?1 or (x.formDate= ?2 and x.formTime >= ?3)");
		query.setParameter(1, lastDate);
		query.setParameter(2, lastDate);
		query.setParameter(3, lastDate);

		@SuppressWarnings("unchecked")
		List<Integer> results = query.getResultList();

		return (results);
	}

	public List<EFormData> findInGroups(Boolean status, int demographicNo, String groupName, String sortBy, int offset, int numToReturn, List<String> eformPerms) {
		StringBuilder sb = new StringBuilder("SELECT e FROM EFormData e, EFormGroup g WHERE e.demographicId = :demographicNo AND e.patientIndependent = false AND e.formId = g.formId AND g.groupName = :groupName");

		if (status != null) {
			sb.append(" AND e.current = :status");
		}

		//get list of _eform.???? permissions the caller has
		if (eformPerms != null && eformPerms.size() > 0) {
			sb.append(" AND (e.roleType in (:perms) OR e.roleType IS NULL OR e.roleType = '' OR e.roleType = 'null')");
		}

		sb.append(" ORDER BY ");

		if (sortBy != null) {
			if (SORT_NAME.equals(sortBy)) sb.append(" e.formName");
			else if (SORT_SUBJECT.equals(sortBy)) sb.append(" e.subject");
			else sb.append(" e.formDate DESC, e.formTime DESC");
		} else {
			sb.append(" e.formDate DESC, e.formTime DESC");
		}

		Query query = entityManager.createQuery(sb.toString());
		query.setParameter("demographicNo", demographicNo);
		query.setParameter("groupName", groupName);
		if (status != null) {
			query.setParameter("status", status);
		}
		if (eformPerms != null && eformPerms.size() > 0) {
			query.setParameter("perms", eformPerms);
		}
		query.setFirstResult(offset);

		this.setLimit(query, numToReturn);

		return query.getResultList();
	}

	public Integer getLatestFdid(Integer fid, Integer demographicNo) {
		Query query = entityManager.createQuery("select max(x.id) from " + modelClass.getSimpleName() + " x where x.current=1 and x.formId = ? and x.demographicId = ?");
		query.setParameter(1, fid);
		query.setParameter(2, demographicNo);

		List<Integer> results = query.getResultList();
		if (results.size() == 1) {
			if (results.get(0) != null) {
				return (results.get(0).intValue());
			}
			return null;
		} else if (results.size() == 0) return (null);

		return null;
	}

	/**
	 * This method war written for BORN Kid eConnect job to figure out which eforms don't have an eform_value present
	 *
	 * @param fid
	 * @param varName
	 * @return
	 */
	public List<Integer> getDemographicNosMissingVarName(int fid, String varName) {

		Query query = entityManager.createNativeQuery("select distinct d.demographic_no from eform e,eform_data d,eform_values v where e.fid = ? and e.fid = d.fid and d.fdid = v.fdid and d.fdid not in (select distinct d.fdid from eform e,eform_data d,eform_values v where e.fid = d.fid and d.fdid = v.fdid and e.fid=? and v.var_name=?)");
		query.setParameter(1, fid);
		query.setParameter(2, fid);
		query.setParameter(3, varName);

		List<Integer> results = query.getResultList();

		return results;
	}

	public List<String> getProvidersForEforms(Collection<Integer> fdidList) {

		Query query = entityManager.createQuery("select distinct x.providerNo from " + modelClass.getSimpleName() + " x where x.id in (:ids)");
		query.setParameter("ids", fdidList);

		List<String> results = query.getResultList();

		return results;
	}

	public Date getLatestFormDateAndTimeForEforms(Collection<Integer> fdidList) {

		Query query = entityManager.createQuery("select distinct x.formDate,x.formTime from " + modelClass.getSimpleName() + " x where x.id in (:ids) order by x.formDate DESC, x.formTime DESC");
		query.setParameter("ids", fdidList);

		List<Object[]> results = query.getResultList();

		if (!results.isEmpty()) {
			Date date = (Date) results.get(0)[0];
			Date time = (Date) results.get(0)[1];

			Calendar cal = Calendar.getInstance();
			cal.setTime(time);
			int timeComponentInMillis = ((cal.get(Calendar.HOUR_OF_DAY) * 60 * 60) + (cal.get(Calendar.MINUTE) * 60) + cal.get(Calendar.SECOND)) * 1000;

			//date.setTime(date.getTime()+timeComponentInMillis);

			//	cal.setTime(date);
			//	cal.add(Calendar.MILLISECOND, timeComponentInMillis);

			Date d = new Date(date.getTime() + timeComponentInMillis);
			return d;
		}

		return null;
	}

	public List<Object> getEfromDataByDemo4(String sql, String demoId) {
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter(1, demoId);
		List<Object> results = query.getResultList();
		return results;
	}

	public List<EFormData> getSavedEfromDataByDemoId(int demoId, int requestId) {
		String sql = "select e from EFormData  e  "
				+ ", EformDocs c    where e.demographicId= ?1 "
				+ " and e.id=c.documentNo and  c.deleted is null and c.docType='E' and c.requestId=?2 ";
		Query query = entityManager.createQuery(sql);

		query.setParameter(1, demoId);
		query.setParameter(2, requestId);
		List<EFormData> results = query.getResultList();
		return results;
	}

	public Integer getFormIdByFormDataId(final Integer formDataId) {
		val query = entityManager.createQuery("select x.formId from " + modelClass.getSimpleName()
				+ " x where x.id = ?1");
		query.setParameter(1, formDataId);
		return (Integer) query.getSingleResult();
	}

	/**
	 * Fetches remote eform data using the Gateway.
	 *
	 * @param demographicNo the demographic ID to fetch eform data for
	 * @return a list of eformData objects corresponding to the given demographic ID
	 */
	public List<EFormData> fetchRemoteEformByDemographicId(final Integer demographicNo) {
		return fetchRemoteEformByGuid(demographicNo, null);
	}

	/**
	 * Fetches remote eform data by demographic ID using the Gateway.
	 *
	 * @param demographicNo the demographic ID to fetch eform data for
	 * @return a list of eformData objects corresponding to the given demographic ID
	 */
	public List<EFormData> fetchRemoteEformByGuid(
			final Integer demographicNo,
			final String guid
	) {
		val eforms = new ArrayList<EFormData>();
    // Feature flag check:
    if (!configurationService.isLinkFeatureEnabled(FeatureFlagEnum.EFORM_ENABLED)) {
      return eforms;
    }

    val converter = new DocumentReferenceConverter();

		try {
			val demographic = demographicManager.getDemographic(demographicNo);
			val parameters = LinkUtility.buildSearchParams(guid);
			parameters.put("category", "EFORM");

			val linked = gatewayDao.findAllRemoteByDemographicId(
					DocumentReference.class,
					demographic,
					parameters
			);

			if (linked == null) {
				return eforms;
			}

			for (var bundleEntryComponent : linked.getEntry()) {
				try {
					val resource = bundleEntryComponent.getResource();
					val eformData = converter.toEformData((DocumentReference) resource);
					if (eformData != null) {
						eformData.setRemoteSystemId(demographic.getRemoteSystemId());
						eforms.add(eformData);
					}
				} catch (Exception e) {
					logger.error("Error while converting DocumentReference to eformData", e);
				}
			}
		} catch (Exception e) {
			logger.error("Failed to fetch eforms from gateway", e);
		}

		return eforms;
	}

	/**
	 * Converts a list of EFormDataSummary objects to a list of maps containing field values.
	 *
	 * @param eFormDataList the list of EFormDataSummary objects to convert; may be null.
	 * @return a list of maps representing the EFormData field values, or an empty list if the input
	 * is null.
	 */
	public List<Map<String, Object>> convertEFormDataListToMapList(
			final List<? extends EFormDataSummary> eFormDataList
	) {
		if (eFormDataList == null) {
			return new ArrayList<>();
		}

		val resultList = new ArrayList<Map<String, Object>>();
		for (EFormDataSummary eFormData : eFormDataList) {
			if (eFormData == null) {
				continue;
			}
			resultList.add(mapEFormData(eFormData));
		}

		return resultList;
	}

	public HashMap<String, Object> mapEFormData(final EFormDataSummary eformData) {
		val resultMap = new HashMap<String, Object>();

		resultMap.put("id", eformData.getId());
		resultMap.put("formId", eformData.getFormId());
		resultMap.put("formName", eformData.getFormName());
		resultMap.put("subject", eformData.getSubject() != null ? eformData.getSubject() : "");
		resultMap.put("formSubject", eformData.getSubject() != null ? eformData.getSubject() : "");
		resultMap.put("demographicId", eformData.getDemographicId());
		resultMap.put("current", eformData.isCurrent());
		resultMap.put("providerNo", eformData.getProviderNo());
		resultMap.put("patientIndependent", eformData.isPatientIndependent());
		resultMap.put("roleType", eformData.getRoleType());
		resultMap.put("guid", eformData.getGuid());
		resultMap.put("remoteSystemId", eformData.getRemoteSystemId());
		resultMap.put("isRemote", eformData.isRemote());

		// for backwards compatability
		resultMap.put("formDateAsDate", eformData.getFormDate());
		resultMap.put("fdid", eformData.getId().toString());

		// for backwards compatability
		if (eformData.isRemote()) {
			resultMap.put(
					"formDate",
					new SimpleDateFormat("yyyy-MM-dd").format(eformData.getFormDate())
			);
			resultMap.put(
					"formTime",
					new SimpleDateFormat("HH:mm:ss").format(eformData.getFormTime())
			);
		} else {
			resultMap.put("fid", eformData.getFormId().toString());
			resultMap.put("formDate", eformData.getFormDate().toString());
			resultMap.put("formTime", eformData.getFormTime().toString());
		}

		return resultMap;
	}

  /**
   * if the link eForm feature flag is enabled and the demographic is remote, this method takes the
   * eFormData object and eForm content by reading the eform File and is then sent to the
   * primary/remote clinic after being converted to DocumentReference FHIR resource. Any exceptions
   * caught in this method will result in a tickler being created for the covering/local clinic if
   * the {@link FeatureFlagEnum#EFORM_FAILURE_TICKLER} feature is enabled.
   *
   * @param eFormData eForm data to be saved to primary/remote clinic.
   * @param demographic demographic to match with primary clinic.
   * @param eformPdfPrint path to eForm PDF file.
   */
  public void saveRemoteEform(
      final EFormData eFormData, final Demographic demographic, final HttpResponse eformPdfPrint) {
    if (!isValidFeatureFlagAndDemographic(demographic)) {
      return;
    }
    try {
      val filePath =
          new String(EntityUtils.toByteArray(eformPdfPrint.getEntity()), StandardCharsets.UTF_8);
      val eformPdfData = FileUtils.readFileToByteArray(new File(filePath));
      eFormData.setEformPdfBase64Data(eformPdfData);
      val documentReference = documentReferenceConverter.toFhirObject(eFormData);
      gatewayDao.saveRemote(demographic, documentReference);
    } catch (Exception e) {
      logger.error("Failed to save eForm via gateway", e);
      ticklerManager.createSendToPrimaryFailedTicklerForEform(eFormData);
    }
  }

  private boolean isValidFeatureFlagAndDemographic(Demographic demographic) {
    return configurationService.isLinkFeatureEnabled(FeatureFlagEnum.EFORM_ENABLED)
        && demographic != null
        && demographic.isRemote();
  }
}
