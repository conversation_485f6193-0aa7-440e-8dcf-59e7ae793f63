/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */
package org.oscarehr.common.dao;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import java.util.Map;
import java.util.Set;
import javax.persistence.Query;

import lombok.extern.log4j.Log4j2;
import org.oscarehr.common.model.SecRole;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional
@Log4j2
public class SecRoleDao extends AbstractDao<SecRole> {

	public SecRoleDao() {
		super(SecRole.class);
	}

    public List<SecRole> findAll()
	{
		StringBuilder sb=new StringBuilder();
		sb.append("select x from SecRole x");

		sb.append(" order by x.name");

		Query query = entityManager.createQuery(sb.toString());

		@SuppressWarnings("unchecked")
		List<SecRole> results = query.getResultList();

		return(results);
	}

    public List<String> findAllNames()
 	{
 		StringBuilder sb=new StringBuilder();
 		sb.append("select x.name from SecRole x");

 		sb.append(" order by x.name");

 		Query query = entityManager.createQuery(sb.toString());

 		@SuppressWarnings("unchecked")
 		List<String> results = query.getResultList();

 		return(results);
 	}

    public SecRole findByName(String name) {
    	Query q = entityManager.createQuery("select x from SecRole x where x.name=?");

    	q.setParameter(1, name);

    	return this.getSingleResultOrNull(q);
    }

    public List<SecRole> findAllOrderByRole()
	{
		Query query = entityManager.createQuery("select x from SecRole x order by x.name");

		@SuppressWarnings("unchecked")
		List<SecRole> results = query.getResultList();

		return(results);
	}

	/**
	 * Get the role names given a set of role IDs.
	 * @param roleIds the set of role IDs to look up
	 * @return a map where the keys are role IDs and the values are role names
	 */
	public Map<String, String> getRoleNamesAsMap(final Set<String> roleIds) {
		if (roleIds == null || roleIds.isEmpty()) {
			return Collections.emptyMap();
		}

		String sql = "SELECT r.role_no, r.role_name FROM secRole r WHERE r.role_no IN ( :roles )";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter("roles", roleIds);

		try {
			List<Object[]> results = query.getResultList();
			return convertToRoleNameMap(results);
		} catch (Exception e) {
			log.error("Error getting role names for roleIds: {}", roleIds, e);
			return Collections.emptyMap();
		}
	}

	/**
	 * Convert the results from the query into a map of role IDs to role names.
	 * Role ID and Role name are never null from the database, so we can safely assume
	 * that the first element is the ID and the second is the name.
	 *
	 * @param results the results from the query, where each result is an array of objects
	 * @return a map where the keys are role IDs and the values are role names
	 */
	private Map<String, String> convertToRoleNameMap(final List<Object[]> results) {
		Map<String, String> roleNameMap = new HashMap<>();
		for (Object[] result : results) {
			String id = result[0].toString();
			String name = (String) result[1];
			roleNameMap.put(id, name);
		}
		return roleNameMap;
	}
}
