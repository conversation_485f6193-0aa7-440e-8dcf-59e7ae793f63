/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.common.dao;

import lombok.val;
import org.oscarehr.common.model.FaxAccount;
import org.springframework.stereotype.Repository;

@Repository
public class FaxAccountDao extends AbstractDao<FaxAccount> {

  public FaxAccountDao() {
    super(FaxAccount.class);
  }

  public FaxAccount getEnabledFaxAccount(final int id) {
    val query = entityManager.createQuery(
        "SELECT a FROM FaxAccount a WHERE "
        + "a.accountEnabled IS TRUE "
        + "AND a.deletedAt IS NULL AND a.id = :accountId");
    query.setParameter("accountId", id);
    return this.getSingleResultOrNull(query);
  }
}
