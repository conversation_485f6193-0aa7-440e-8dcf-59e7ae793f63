/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package org.oscarehr.common.dao;

import ca.kai.util.MapUtils;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import java.util.Map;
import java.util.Set;
import javax.persistence.Query;

import lombok.extern.log4j.Log4j2;
import org.oscarehr.common.model.CaseManagementNoteJpa;
import org.springframework.stereotype.Repository;

/**
 * JPA implementation of CaseManagementNoteDAO
 */
@Repository
@Log4j2
public class CaseManagementNoteJpaDao extends AbstractDao<CaseManagementNoteJpa> {

  public CaseManagementNoteJpaDao() {
    super(CaseManagementNoteJpa.class);
  }

  /**
   * Get the revision counts for a set of UUIDs as a Map.
   * @param uuids UUIDs of the notes to get revision counts for
   * @return Map of UUIDs to their revision counts
   */
  public Map<String, Integer> getRevisionCountsAsMap(final Set<String> uuids) {
    if (uuids == null || uuids.isEmpty()) {
      return Collections.emptyMap();
    }

    String sql = "SELECT CAST(uuid AS CHAR(36)) AS uuid, COUNT(*) FROM casemgmt_note "
        + "WHERE uuid IN ( :uuids ) GROUP BY uuid";
    Query query = entityManager.createNativeQuery(sql);
    query.setParameter("uuids", uuids);

    try {
      List<Object[]> results = query.getResultList();
      return MapUtils.convertQueryListToMap(results, Integer.class);
    } catch (Exception e) {
      log.error("Error getting revision counts for UUIDs: {}", uuids, e);
      return Collections.emptyMap();
    }
  }


  /**
   * Get the create dates for a set of UUIDs as a Map.
   * @param uuids Set of UUIDs to get create dates for
   * @return Map of UUIDs to their create dates
   */
  public Map<String, Date> getCreateDatesAsMap(final Set<String> uuids) {
    if (uuids == null || uuids.isEmpty()) {
      return Collections.emptyMap();
    }

    String sql = "SELECT CAST(uuid AS CHAR(36)) AS uuid, MIN(update_date) FROM casemgmt_note "
        + "WHERE uuid IN ( :uuids ) GROUP BY uuid";
    Query query = entityManager.createNativeQuery(sql);
    query.setParameter("uuids", uuids);

    try {
      List<Object[]> results = query.getResultList();
      return MapUtils.convertQueryListToMap(results, Date.class);
    } catch (Exception e) {
      log.error("Error getting create dates for UUIDs: {}", uuids, e);
      return Collections.emptyMap();
    }
  }

}
