/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */

package org.oscarehr.common.dao;

import health.apps.gateway.converters.AllergyIntoleranceConverter;
import health.apps.gateway.service.GWConfigurationService;
import health.apps.gateway.service.GatewayDao;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import javax.persistence.Query;
import lombok.val;
import lombok.var;
import org.apache.log4j.Logger;
import org.hl7.fhir.r4.model.AllergyIntolerance;
import org.hl7.fhir.r4.model.Bundle;
import org.oscarehr.common.enums.FeatureFlagEnum;
import org.oscarehr.common.model.Allergy;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.managers.DemographicManager;
import org.oscarehr.util.MiscUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class AllergyDao extends AbstractDao<Allergy> {
  @Autowired
  private GWConfigurationService configurationService;

  @Autowired
  private DemographicManager demographicManager;

  @Autowired
  private GatewayDao gatewayDao;
    private static final Logger logger = MiscUtils.getLogger();

    public AllergyDao() {
        super(Allergy.class);
    }

    public List<Allergy> findAllergies(final Integer demographic_no) {
        String sql = "select x from "+modelClass.getSimpleName()+" x where x.demographicNo=?1 order by x.archived,x.severityOfReaction desc";
        Query query = entityManager.createQuery(sql);
        query.setParameter(1,demographic_no);

        @SuppressWarnings("unchecked")
        List<Allergy> allergies = query.getResultList();
        allergies.addAll(getRemoteAllergies(demographic_no));

        return allergies;
    }


    public List<Allergy> findActiveAllergies(final Integer demographic_no) {
        String sql = "select x from "+ modelClass.getSimpleName() +" x where x.demographicNo = ?1 and x.archived = 0 order by x.position, x.severityOfReaction";
        Query query = entityManager.createQuery(sql);
        query.setParameter(1, demographic_no);

        @SuppressWarnings("unchecked")
        List<Allergy> allergies = query.getResultList();
        val remoteAllergies = getRemoteAllergies(demographic_no);
        for (var allergy : remoteAllergies) {
            if (!allergy.getArchived()) {
              allergies.add(allergy);
            }
        }
        return allergies;
    }

    protected Comparator<Allergy> getSortByEntryDateComparator(final boolean reversed) {
        return new Comparator<Allergy>() {
            @Override
            public int compare(Allergy a1, Allergy a2) {
                return a1.getEntryDate().compareTo(a2.getEntryDate()) * (reversed ? -1 : 1);
            }
        };
    }

    public List<Allergy> findActiveAllergiesLimitBy(final int demographic_no, final int limit) {

      val allergyComparator = getSortByEntryDateComparator(true);

      String sql = "SELECT x FROM "+ modelClass.getSimpleName() +" x WHERE x.demographicNo = ?1 AND x.archived = 0 ORDER BY x.entryDate DESC, x.position, x.severityOfReaction";
      Query query = entityManager.createQuery(sql);
      query.setParameter(1, demographic_no);
      query.setMaxResults(limit);

      @SuppressWarnings("unchecked")
      List<Allergy> allergies = query.getResultList();
      allergies.addAll(getRemoteAllergies(demographic_no));
      allergies.sort(allergyComparator);

      return allergies.size() > limit ? allergies.subList(0, limit) : allergies;
    }

    public List<Allergy> findActiveAllergiesForPopup(final Integer demographic_no) {
        String sql = "select x from "+ modelClass.getSimpleName() +" x where x.demographicNo = ?1 and x.archived = 0 order by x.position, x.severityOfReaction";

        Query query = entityManager.createQuery(sql);
        query.setParameter(1,demographic_no);

        @SuppressWarnings("unchecked")
        List<Allergy> allergies = query.getResultList();
        return allergies;
    }

  public List<Allergy> findActiveAllergiesOrderByDescription(final Integer demographic_no) {
        String sql = "select x from "+modelClass.getSimpleName()+" x where x.demographicNo=?2 and x.archived = 0 order by x.description";
        Query query = entityManager.createQuery(sql);
        query.setParameter(2,demographic_no);

        @SuppressWarnings("unchecked")
        List<Allergy> allergies = query.getResultList();
        return allergies;
    }

    public List<Allergy> findByDemographicIdUpdatedAfterDate(final Integer demographicId, final Date updatedAfterThisDate) {
        String sqlCommand = "select x from "+modelClass.getSimpleName()+" x where x.demographicNo=?1 and x.lastUpdateDate>?2";

        Query query = entityManager.createQuery(sqlCommand);
        query.setParameter(1, demographicId);
        query.setParameter(2, updatedAfterThisDate);

        @SuppressWarnings("unchecked")
        List<Allergy> results = query.getResultList();

        return (results);
    }

    //for integrator
    public List<Integer> findDemographicIdsUpdatedAfterDate(final Date updatedAfterThisDate) {
        String sqlCommand = "select x.demographicNo from Allergy x where x.lastUpdateDate>?1";

        Query query = entityManager.createQuery(sqlCommand);
        query.setParameter(1, updatedAfterThisDate);

        @SuppressWarnings("unchecked")
        List<Integer> results = query.getResultList();

        return (results);
    }

    /**
     * @return results ordered by lastUpdateDate
     */
    public List<Allergy> findByUpdateDate(final Date updatedAfterThisDateInclusive, final int itemsToReturn) {
        String sqlCommand = "select x from "+modelClass.getSimpleName()+" x where x.lastUpdateDate>=?1 order by x.lastUpdateDate";

        Query query = entityManager.createQuery(sqlCommand);
        query.setParameter(1, updatedAfterThisDateInclusive);
        setLimit(query, itemsToReturn);

        @SuppressWarnings("unchecked")
        List<Allergy> results = query.getResultList();
        return (results);
    }

    /**
     * @return results ordered by lastUpdateDate asc
     */
    public List<Allergy> findByProviderDemographicLastUpdateDate(final String providerNo, final Integer demographicId,
                                                                 final Date updatedAfterThisDateExclusive,
                                                                 final int itemsToReturn) {
        // the providerNo field is always blank right now... we have no idea which provider did the allery entry
        // String sqlCommand = "select x from "+modelClass.getSimpleName()+" x where x.demographicNo=?1 and x.providerNo=?2 and x.lastUpdateDate>?3 order by x.lastUpdateDate";

        String sqlCommand = "select x from "+modelClass.getSimpleName()+" x where x.demographicNo=?1 and x.lastUpdateDate>?2 order by x.lastUpdateDate";

        Query query = entityManager.createQuery(sqlCommand);
        query.setParameter(1, demographicId);
        query.setParameter(2, updatedAfterThisDateExclusive);
        setLimit(query, itemsToReturn);

        @SuppressWarnings("unchecked")
        List<Allergy> results = query.getResultList();
        return (results);
    }

  /**
   * Retrieves a list of remote allergy objects for the specified demographic number using the
   * gateway service. Default parameters are used.
   *
   * @param demographicNo the demographic number of the patient.
   * @return a list of Allergy objects retrieved from the remote system.
   */
  private List<Allergy> getRemoteAllergies(final int demographicNo) {
    // Feature flag check for ALLERGIES_ENABLED
    if (!configurationService.isLinkFeatureEnabled(FeatureFlagEnum.ALLERGIES_ENABLED)) {
      return new ArrayList<>();
    }
    val demographic = demographicManager.getDemographic(demographicNo);
    return getRemoteAllergies(demographic, new HashMap<String, String>());
  }

  /**
   * Retrieves a list of remote allergy objects for the specified demographic using the gateway
   * service.
   *
   * @param demographic the demographic object containing patient information.
   * @param parameters  a map of parameters to be used in the gateway request.
   * @return a list of Allergy objects retrieved from the remote system.
   */
  private List<Allergy> getRemoteAllergies(final Demographic demographic,
      final HashMap<String, String> parameters) {
    try {
      val remoteAllergiesBundle = gatewayDao.findAllRemoteByDemographicId(AllergyIntolerance.class,
          demographic, parameters);
      return convertBundleToAllergies(remoteAllergiesBundle, demographic);
    } catch (Exception exception) {
      logger.error(exception.getMessage());
      return new ArrayList<>();
    }
  }

  /**
   * Converts a FHIR Bundle of AllergyIntolerance resources to a list of Oscar Allergy objects.
   *
   * @param bundle      the FHIR Bundle containing AllergyIntolerance resources.
   * @param demographic the demographic object associated with the retrieved allergies.
   * @return a list of Allergy objects converted from the FHIR Bundle.
   */
  private List<Allergy> convertBundleToAllergies(
      final Bundle bundle,
      final Demographic demographic
  ) {
    val allergies = new ArrayList<Allergy>();
    val converter = new AllergyIntoleranceConverter();

    for (var entry : bundle.getEntry()) {
      val allergyIntolerance = (AllergyIntolerance) entry.getResource();
      val allergy = converter.toOscarObject(allergyIntolerance);
      allergy.setRemoteSystemId(demographic.getRemoteSystemId());
      allergies.add(allergy);
    }

    return allergies;
  }
}
