/**
 * Copyright (c) 2008-2012 Indivica Inc.
 *
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "indivica.ca/gplv2"
 * and "gnu.org/licenses/gpl-2.0.html".
 */

package org.oscarehr.hospitalReportManager;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.val;
import lombok.var;
import org.apache.log4j.Logger;
import org.oscarehr.hospitalReportManager.dao.HRMDocumentDao;
import org.oscarehr.hospitalReportManager.dao.HRMDocumentSubClassDao;
import org.oscarehr.hospitalReportManager.dao.HRMSubClassDao;
import org.oscarehr.hospitalReportManager.model.HRMDocument;
import org.oscarehr.hospitalReportManager.model.HRMDocumentSubClass;
import org.oscarehr.hospitalReportManager.model.HRMSubClass;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.MiscUtils;
import org.oscarehr.util.SpringUtils;
import oscar.util.StringUtils;

public class HRMUtil {

	private static final Logger logger = MiscUtils.getLogger();
	
	public static final String DATE = "time_received";
	public static final String REPORT_DATE = "report_date";
	public static final String SUBCLASS = "subclass";
	public static final String TYPE = "report_type";
	public static final String CATEGORY = "category";
	
	private static HRMDocumentDao hrmDocumentDao = (HRMDocumentDao) SpringUtils.getBean("HRMDocumentDao");
  private static HRMSubClassDao hrmSubClassDao = (HRMSubClassDao) SpringUtils.getBean("HRMSubClassDao");
	private static HRMDocumentSubClassDao hrmDocumentSubClassDao = (HRMDocumentSubClassDao) SpringUtils.getBean("HRMDocumentSubClassDao");
	
	public HRMUtil() {
		
	}

	public static Map<String, String> getReportInformation(LoggedInInfo loggedInInfo, String hrmFileLocation) {
		HRMReport hrmReport = HRMReportParser.parseReport(loggedInInfo, hrmFileLocation);
		Map<String, String> reportInformation = new HashMap<String, String>();
		HRMSubClass hrmSubClass = null;
		String dispSubClass = "";
		String categoryName = "";
		if (hrmReport != null) {
			List<HRMDocumentSubClass> subClassList = hrmDocumentSubClassDao.getSubClassesByDocumentId(hrmReport.getHrmDocumentId());

			if (hrmReport.getFirstReportClass().equalsIgnoreCase("Diagnostic Imaging Report") || hrmReport.getFirstReportClass().equalsIgnoreCase("Cardio Respiratory Report")) {
				//Get first sub class to display on eChart
				if (subClassList != null && subClassList.size() > 0) {
					HRMDocumentSubClass firstSubClass = subClassList.get(0);
					hrmSubClass = hrmSubClassDao.findApplicableSubClassMapping(hrmReport.getFirstReportClass(), firstSubClass.getSubClass(), firstSubClass.getSubClassMnemonic(), hrmReport.getSendingFacilityId());
					dispSubClass = hrmSubClass != null ? hrmSubClass.getSubClassDescription() : "";
				}

				if (StringUtils.isNullOrEmpty(dispSubClass) && hrmReport.getAccompanyingSubclassList().size() > 0){
					// if sub class doesn't exist, display the accompanying subclass
					dispSubClass = hrmReport.getFirstAccompanyingSubClass();
				}
			} else {
				//Medical Records Report
				String[] reportSubClass = hrmReport.getFirstReportSubClass() != null ? hrmReport.getFirstReportSubClass().split("\\^") : null;
				
				if (reportSubClass != null) {
					hrmSubClass = hrmSubClassDao.findApplicableSubClassMapping(hrmReport.getFirstReportClass(), reportSubClass[0], null, hrmReport.getSendingFacilityId());
				}
				
				dispSubClass = reportSubClass != null && reportSubClass.length > 1 ? reportSubClass[1] : "";
			}

			if (hrmSubClass != null && hrmSubClass.getHrmCategory() != null) {
				categoryName = hrmSubClass.getHrmCategory().getCategoryName();
			}
		}
    reportInformation.put(CATEGORY, StringUtils.noNull(categoryName));
    reportInformation.put(SUBCLASS, StringUtils.noNull(dispSubClass));

		return reportInformation;
	}

	public static String getDisplaySubClass(Integer hrmDocumentId) {
		List<HRMDocumentSubClass> subClassList = hrmDocumentSubClassDao.getActiveSubClassesByDocumentId(hrmDocumentId);
		String dispSubClass = "HRM";
		if(subClassList != null && subClassList.size() > 0) {
			HRMDocumentSubClass firstSubClass = subClassList.get(0);
			dispSubClass = firstSubClass.getSubClassDescription();
			if (firstSubClass.getSubClass() != null && StringUtils.isNullOrEmpty(dispSubClass)){
				String[] reportSubClass = firstSubClass.getSubClass().split("\\^");
				if(reportSubClass.length > 1){
					dispSubClass = reportSubClass[1];
				}
			}
		}
		return dispSubClass;
	}
  public static ArrayList<HashMap<String, ?>> listAllHRMDocuments(
      final LoggedInInfo loggedInInfo,
			final String sortBy,
			final String demographicNo
	) {
    return listAllHRMDocuments(loggedInInfo, sortBy, demographicNo, false);
  }

  public static ArrayList<HashMap<String, ?>> listAllHRMDocuments(
      final LoggedInInfo loggedInInfo,
			final String sortBy,
			final String demographicNo,
			final boolean isDescending
	) {
    val results = new ArrayList<HashMap<String, ?>>();
    val hrmDocumentsAll = hrmDocumentDao.getAllHrmByDemographicId(Integer.parseInt(demographicNo));

    for (var hrmDocument : hrmDocumentsAll) {
      // Find subclass and category from the parsed HRMReport
      val reportInformation = getReportInformation(loggedInInfo, hrmDocument.getReportFile());
      hrmDocument.setSubclass(reportInformation.get(SUBCLASS));
      hrmDocument.setCategoryName(reportInformation.get(CATEGORY));
    }

    // Add all remote documents from linked primary demographic
    hrmDocumentsAll.addAll(
        hrmDocumentDao.findAllRemoteHrmDocumentsByDemographicId(Integer.parseInt(demographicNo)));

    sortHrmDocuments(sortBy, hrmDocumentsAll, isDescending);

    for (var hrmDocument : hrmDocumentsAll) {
      createDocumentListFromHrm(hrmDocument, results);
    }

    return results;
  }

	public static ArrayList<HashMap<String, ? extends Object>> listMappings(){
			ArrayList<HashMap<String, ? extends Object>> hrmdocslist = new ArrayList<HashMap<String, ?>>();
			
			List<HRMSubClass> hrmSubClasses = hrmSubClassDao.listAll();
			
			for (HRMSubClass hrmSubClass : hrmSubClasses) {
	
				HashMap<String, Object> curht = new HashMap<String, Object>();
				curht.put("id", hrmSubClass.getSendingFacilityId());
				curht.put("sub_class", hrmSubClass.getSubClassName());
				curht.put("class", hrmSubClass.getClassName());
				curht.put(CATEGORY, hrmSubClass.getHrmCategory());
				curht.put("mnemonic", (hrmSubClass.getSubClassMnemonic() != null ? hrmSubClass.getSubClassMnemonic() : ""));
				curht.put("description", (hrmSubClass.getSubClassDescription() != null ? hrmSubClass.getSubClassDescription() : ""));
				curht.put("mappingId", hrmSubClass.getId());
				
				hrmdocslist.add(curht);
				
			}
			
			return hrmdocslist;
			
		}
		
	/**
	 * Check the HRMSubClass for the corresponding descriptions, change the description
	 * @param subClassList
	 * @param reportType
	 * @param sendingFacilityId
	 */
	public void findCorrespondingHRMSubClassDescriptions(List<HRMDocumentSubClass> subClassList, String reportType, String sendingFacilityId, String reportSubClass ) {
		
		if (reportType == null || reportType.isEmpty()) {
			return;
		}
		
		if (sendingFacilityId == null || sendingFacilityId.isEmpty()) {
			return;
		}
		
	    for(HRMDocumentSubClass hrmDocumentSubClass: subClassList) {
	    	
	    	HRMSubClass hrmSubClass = hrmSubClassDao.findByClassNameMnemonicFacility(reportType, sendingFacilityId, hrmDocumentSubClass.getSubClassMnemonic());
	    	
	    	if (hrmSubClass != null) {
	    		hrmDocumentSubClass.setSubClassDescription(hrmSubClass.getSubClassDescription());
	    	}	    	
	    }		
	    
	    if (subClassList != null && subClassList.size() == 0 && reportType.equalsIgnoreCase("Medical Records Report") && reportSubClass != null && !reportSubClass.isEmpty()) {
	    	String[] subClassFromReport = reportSubClass.split("\\^");
	    	String subClass = "";
	    	if (subClassFromReport.length == 2) {
	    		subClass =  subClassFromReport[1];	    		
	    	}
	    	
	    	HRMSubClass hrmSubClass = hrmSubClassDao.findByClassNameSubClassNameFacility(reportType,  sendingFacilityId, subClass);
	    	if (hrmSubClass != null) {
	    		HRMDocumentSubClass hrmDocumentSubClass = new HRMDocumentSubClass();
	    		hrmDocumentSubClass.setSubClassDescription(hrmSubClass.getSubClassDescription());
	    		subClassList.add(hrmDocumentSubClass);
	    	}
	    }
		
	}

	private static void sortHrmDocuments(
			final String sortBy,
			final List<HRMDocument> hrmDocumentsAll
	) {
		if (SUBCLASS.equals(sortBy)) {
			hrmDocumentsAll.sort(HRMDocument.HRM_SUBCLASS_COMPARATOR);
		} else if (TYPE.equals(sortBy)) {
			hrmDocumentsAll.sort(HRMDocument.HRM_TYPE_COMPARATOR);
		} else if (DATE.equals(sortBy)) {
			hrmDocumentsAll.sort(HRMDocument.HRM_DATE_COMPARATOR);
		} else {
			// Default to sorting by report date
			hrmDocumentsAll.sort(HRMDocument.HRM_REPORT_DATE_COMPARATOR);
		}
	}

	protected static void createDocumentListFromHrm(
			final HRMDocument hrmDocument,
			final ArrayList<HashMap<String, ?>> hrmResults
	) {
		// Use user-friendly format without seconds for display
		val displayDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
		var formattedDate = "";
		if (hrmDocument.getTimeReceived() != null) {
			formattedDate = hrmDocument.isRemote()
					? displayDateFormat.format(hrmDocument.getTimeReceived())
					: displayDateFormat.format(hrmDocument.getTimeReceived());
		}

		var formattedReportDate = "";
		if (hrmDocument.getReportDate() != null) {
			formattedReportDate = hrmDocument.isRemote()
					? displayDateFormat.format(hrmDocument.getReportDate())
					: displayDateFormat.format(hrmDocument.getReportDate());
		}

		val hrmMap = new HashMap<String, Object>();
		hrmMap.put("id", hrmDocument.getId());
		hrmMap.put("time_received", formattedDate);
		hrmMap.put(REPORT_DATE, formattedReportDate);
		hrmMap.put("report_type", hrmDocument.getReportType());
		hrmMap.put("report_status", hrmDocument.getReportStatus());
    hrmMap.put(
        CATEGORY,
				hrmDocument.getCategoryName() != null ? hrmDocument.getCategoryName() : ""
		);
		hrmMap.put("description", hrmDocument.getDescription());
		hrmMap.put(SUBCLASS, hrmDocument.getSubclass());
		hrmMap.put("remoteSystemId", hrmDocument.getRemoteSystemId());
		hrmMap.put("isRemote", hrmDocument.isRemote());
		hrmMap.put("guid", hrmDocument.getGuid());

		hrmResults.add(hrmMap);
	}
}
