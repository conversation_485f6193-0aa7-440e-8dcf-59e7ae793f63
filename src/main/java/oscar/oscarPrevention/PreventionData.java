/**
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version. 
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for the
 * Department of Family Medicine
 * McMaster University
 * Hamilton
 * Ontario, Canada
 */

package oscar.oscarPrevention;

import ca.kai.datasharing.DataSharingService;
import ca.kai.datasharing.PatientPortalData;
import health.apps.gateway.service.GWConfigurationService;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Nullable;
import lombok.val;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.log4j.Logger;
import org.oscarehr.PMmodule.caisi_integrator.CaisiIntegratorManager;
import org.oscarehr.PMmodule.caisi_integrator.IntegratorFallBackManager;
import org.oscarehr.PMmodule.caisi_integrator.RemotePreventionHelper;
import org.oscarehr.caisi_integrator.ws.CachedDemographicPrevention;
import org.oscarehr.caisi_integrator.ws.CachedFacility;
import org.oscarehr.common.dao.DemographicDao;
import org.oscarehr.common.dao.PartialDateDao;
import org.oscarehr.common.dao.PreventionDao;
import org.oscarehr.common.dao.PreventionExtDao;
import org.oscarehr.common.enums.FeatureFlagEnum;
import org.oscarehr.common.model.DHIRSubmissionLog;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.common.model.PartialDate;
import org.oscarehr.common.model.Prevention;
import org.oscarehr.common.model.PreventionExt;
import org.oscarehr.managers.DHIRSubmissionManager;
import org.oscarehr.managers.DemographicManager;
import org.oscarehr.managers.PreventionManager;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.MiscUtils;
import org.oscarehr.util.SpringUtils;
import oscar.oscarProvider.data.ProviderData;
import oscar.util.DateUtils;
import oscar.util.UtilDateUtilities;

public class PreventionData {

	private static Logger log = MiscUtils.getLogger();
	private static PreventionDao preventionDao = (PreventionDao) SpringUtils.getBean(PreventionDao.class);
	private static PreventionExtDao preventionExtDao = (PreventionExtDao) SpringUtils.getBean(PreventionExtDao.class);
	private static DemographicManager demographicManager =
			SpringUtils.getBean(DemographicManager.class);
	private static PartialDateDao partialDateDao = SpringUtils.getBean(PartialDateDao.class);

	private static final int GUID_LENGTH = 36;

	private PreventionData() {
		// prevent instantiation
	}

	private static Date stringToDate(String date) {
		if(date == null)
			return null;
		Date ret = UtilDateUtilities.StringToDate(date, "yyyy-MM-dd HH:mm");
		if(ret != null) {
			return ret;
		}
		ret = UtilDateUtilities.StringToDate(date, "yyyy-MM-dd");

		return ret;

	}

	private static PartialDate setPreventionDate(Prevention prevention, String date) {
		PartialDate pd = null;
		if(date.length() == 4) {
			pd = new PartialDate();
			pd.setTableName(PartialDate.PREVENTION);
			pd.setTableId(prevention.getId());
			pd.setFieldName(PartialDate.PREVENTION_DATE);
			pd.setFormat("YYYY");
			//partialDateDao.persist(pd);
			date = date + "-01-01 00:00";
		} else if(date.length() == 7) {
			pd = new PartialDate();
			pd.setTableName(PartialDate.PREVENTION);
			pd.setTableId(prevention.getId());
			pd.setFieldName(PartialDate.PREVENTION_DATE);
			pd.setFormat("YYYY-MM");
			//partialDateDao.persist(pd);
			date = date + "-01 00:00";
		}
		prevention.setPreventionDate(stringToDate(date));
		return pd;
	}

	public static Integer insertPreventionData(String creator, String demoNo, String date,
			String providerNo, String location, String providerName, String preventionType, String refused,
			String nextDate, String neverWarn, ArrayList<Map<String, String>> list, String snomedId, String din) {
		Integer insertId = -1;
		try {
			Prevention prevention = new Prevention();
			prevention.setCreatorProviderNo(creator);
			prevention.setDemographicId(Integer.valueOf(demoNo));
			PartialDate pd = setPreventionDate(prevention,date);
			prevention.setProviderName(providerName);
			prevention.setProviderNo(providerNo);
			prevention.setLocation(location);
			prevention.setPreventionType(preventionType);
			prevention.setNextDate(UtilDateUtilities.StringToDate(nextDate, "yyyy-MM-dd"));
			prevention.setNever(neverWarn.trim().equals("1"));
			if (refused.trim().equals("1")) {
        prevention.setBooleanRefused(true);
      } else if (refused.trim().equals("2")) {
        prevention.setIneligible(true);
      } else if (refused.trim().equals("3")) {
        prevention.setCompletedExternally(true);
      }
			prevention.setSnomedId(snomedId);

			preventionDao.persist(prevention);

			if(pd != null) {
				pd.setTableId(prevention.getId());
				partialDateDao.persist(pd);
			}
			if (prevention.getId() == null) {
				return insertId;
			}

			insertId = prevention.getId();
			for (int i = 0; i < list.size(); i++) {
				Map<String, String> h = list.get(i);
				for (Map.Entry<String, String> entry : h.entrySet()) {
					if (entry.getKey() != null && entry.getValue() != null) {
						addPreventionKeyValue("" + insertId, entry.getKey(), entry.getValue());
					}
				}
			}

			preventionDao.saveToPrimary(prevention, demographicManager.getDemographic(
					Integer.valueOf(demoNo)));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return insertId;
	}

	public static Integer insertPreventionDataReturnError(String creator, String demoNo, Date preventionDate, String providerNo, String providerName, String preventionType, String refused, String nextDate, String neverWarn, ArrayList<Map<String, String>> list) throws Exception {
		Integer insertId = -1;
		Prevention prevention = new Prevention();
		prevention.setCreatorProviderNo(creator);
		prevention.setDemographicId(Integer.valueOf(demoNo));
		prevention.setPreventionDate(preventionDate);
		prevention.setProviderName(providerName);
		prevention.setProviderNo(providerNo);
		prevention.setPreventionType(preventionType);
		prevention.setNextDate(UtilDateUtilities.StringToDate(nextDate, "yyyy-MM-dd"));
		prevention.setNever(neverWarn.trim().equals("1"));
		if (refused.trim().equals("1")) {
      prevention.setBooleanRefused(true);
    } else if (refused.trim().equals("2")) {
      prevention.setIneligible(true);
    }

		preventionDao.persist(prevention);
		if (prevention.getId() == null) {
      return insertId;
    }

		insertId = prevention.getId();
		for (int i = 0; i < list.size(); i++) {
			Map<String, String> h = list.get(i);
			for (Map.Entry<String, String> entry : h.entrySet()) {
				if (entry.getKey() != null && entry.getValue() != null) {
					addPreventionKeyValue("" + insertId, entry.getKey(), entry.getValue());
				}
			}
		}

		return insertId;
	}

	public static void addPreventionKeyValue(String preventionId, String keyval, String val) {
		try {
			PreventionExt preventionExt = new PreventionExt();
			preventionExt.setPreventionId(Integer.valueOf(preventionId));
			preventionExt.setKeyval(keyval);
			preventionExt.setVal(val);

			preventionExtDao.persist(preventionExt);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * Gets the prevention exts for a given prevention.
	 * If a prevention is remote, then the prevention is one of two cases:
	 * 1. The prevention has been fetched from Gateway, which in turn means it is
	 * 		one of the preventions in linkPreventions
	 * 2. The prevention has been saved locally but marked as a remote prevention,
	 * 		meaning its extensions can be found from the local database.
	 * @param preventionData The prevention data
	 * @param localPreventionExts The local prevention extensions
	 * @param linkPreventions The preventions fetched from Gateway
	 * @return The prevention extensions, or an empty map if not found
	 */
	public static Map<String, String> getPreventionExtMap(
			final Map<String,Object> preventionData,
			final Map<String, Map<String, String>> localPreventionExts,
			final List<Prevention> linkPreventions
	) {
		// If prevention is remote, check if fetched from Gateway.
		if ((Boolean) preventionData.get("isRemote")) {
			for (Prevention linkPrevention : linkPreventions) {
				if (linkPrevention.getGuid().equals(preventionData.get("guid"))) {
					return getPreventionKeyValues(linkPrevention);
				}
			}
		}

		// If not found, prevention may still be saved locally but marked as remote.
		// This can occur if the current clinic is the Primary clinic.
		Map<String, String> preventionExtMap = localPreventionExts.get(preventionData.get("id"));
		if (preventionExtMap != null) {
			return preventionExtMap;
		}

		// If there are no extensions found and preventionExtMap is null, return empty map.
		return Collections.emptyMap();
	}

	public static Map<String, String> getPreventionKeyValues(
			List<PreventionExt> preventionExtensions) {
		Map<String, String> extensionsMap = new HashMap<String, String>();
		if (preventionExtensions == null) {
			return extensionsMap;
		}
		for (PreventionExt preventionExt : preventionExtensions) {
			extensionsMap.put(preventionExt.getkeyval(), preventionExt.getVal());
		}
		return extensionsMap;
	}

	public static Map<String, String> getPreventionKeyValues(final Prevention prevention) {
		if (prevention == null) {
			return Collections.emptyMap();
		}
		if (prevention.isRemote() && !prevention.getPreventionExts().isEmpty()) {
			return getPreventionKeyValues(prevention.getPreventionExts());
		}
		return getPreventionKeyValues(prevention.getId().toString());
	}

	public static Map<String, String> getPreventionKeyValues(String preventionId) {
		Map<String, String> h = new HashMap<String, String>();

		if( preventionId == null || preventionId.isEmpty()) {
			return h;
		}
		try {
			List<PreventionExt> preventionExts = preventionExtDao.findByPreventionId(Integer.valueOf(preventionId));
			h = getPreventionKeyValues(preventionExts);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return h;
	}

	public static void deletePreventionData(String id) {
		try {
			Prevention prevention = preventionDao.find(Integer.valueOf(id));
			prevention.setDeleted(true);

			preventionDao.merge(prevention);
			preventionDao.saveToPrimary(prevention, demographicManager.getDemographic(
					prevention.getDemographicId()));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	public static void setNextPreventionDate(String date, String id) {
		try {
			Prevention prevention = preventionDao.find(Integer.valueOf(id));
			prevention.setNextDate(UtilDateUtilities.StringToDate(date, "yyyy-MM-dd"));

			preventionDao.merge(prevention);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	public static String getProviderName(Map<String, Object> hash) {
		String name = "";
		if (hash != null) {
			String proNum = (String) hash.get("provider_no");
			if (proNum == null || proNum.equals("-1")) {
				name = (String) hash.get("provider_name");
			} else {
				name = ProviderData.getProviderName(proNum);
			}
		}
		return name;
	}

	public static void updatetPreventionData(String id, String creator, String demoNo, String date,
			String providerNo, String location, String providerName, String preventionType, String refused,
			String nextDate, String neverWarn, ArrayList<Map<String, String>> list, String snomedId) {
		deletePreventionData(id);
		insertPreventionData(creator, demoNo, date, providerNo, location, providerName, preventionType,
				refused, nextDate, neverWarn, list, snomedId, null);
	}

	public static ArrayList<Map<String, Object>> getPreventionDataFromExt(String extKey, String extVal) {
		ArrayList<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		try {
			List<PreventionExt> preventionExts = preventionExtDao.findByKeyAndValue(extKey, extVal);
			for (PreventionExt preventionExt : preventionExts) {
				Map<String, Object> hash = getPreventionById(preventionDao.find(preventionExt.getPreventionId()).getId().toString());
				if (hash.get("deleted") != null && ((String) hash.get("deleted")).equals("0")) {
					list.add(hash);
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return list;
	}

	/*
	 * Fetch one extended prevention key
	 * Requires prevention id and keyval to return
	 */
	public static String getExtValue(String id, String keyval) {
		try {
			List<PreventionExt> preventionExts = preventionExtDao.findByPreventionIdAndKey(Integer.valueOf(id), keyval);
			for (PreventionExt preventionExt : preventionExts) {
				return preventionExt.getVal();
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return "";
	}

	// //////
	/**
	 *Method to get a list of (demographic #, prevention dates, and key values) of a certain type <injectionTppe> from a start Date to an end Date with a Ext key value EG get all
	 * Rh injection's product #, from 2006-12-12 to 2006-12-18
	 *
	 */
	public static ArrayList<Map<String, Object>> getExtValues(String injectionType, Date startDate, Date endDate, String keyVal) {
		ArrayList<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		List<Prevention> preventions = preventionDao.findByTypeAndDate(injectionType, startDate, endDate);
		for (Prevention prevention : preventions) {

			List<PreventionExt> preventionExts = preventionExtDao.findByPreventionIdAndKey(prevention.getId(), keyVal);
			try {
				for (PreventionExt preventionExt : preventionExts) {
					Map<String, Object> h = new HashMap<String, Object>();
					h.put("preventions_id", prevention.getId().toString());
					h.put("demographic_no", prevention.getDemographicId().toString());
					h.put("val", preventionExt.getVal());
					h.put("prevention_date", prevention.getPreventionDate());
					list.add(h);
					break;
				}
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			}
		}
		return list;
	}

	public static Date getDemographicDateOfBirth(LoggedInInfo loggedInInfo, Integer demoNo) {
		DemographicManager demographicManager = SpringUtils.getBean(DemographicManager.class);
		Demographic dd = demographicManager.getDemographic(loggedInInfo,demoNo);
		if (dd == null) {
      return (null);
    }
		Calendar bday = dd.getBirthDay();
		if (bday == null) {
      return (null);
    }
		return (bday.getTime());
	}

	public static ArrayList<Map<String, Object>> getPreventionData(LoggedInInfo loggedInInfo, Integer demoNo) {
		return getPreventionData(loggedInInfo, null, demoNo);
	}

	public static Map<String, String> getLastPreventionDateForDemographics (String preventionType, String[] demographicNos) {
		Map<String, String> preventionDates = new HashMap<>();
		if (preventionType != null) {

			Integer[] parsedDemographicNos = new Integer[demographicNos.length];
			for (int i = 0; i < demographicNos.length; i++) {
				parsedDemographicNos[i] = Integer.valueOf(demographicNos[i]);
			}
			List<Integer> demographicNoList = new ArrayList<Integer>(Arrays.asList(parsedDemographicNos));

			List<Prevention> preventions = preventionDao.findByTypeAndDemoNos(preventionType, demographicNoList);
			
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			for (Prevention prevention : preventions) {
				String preventionDate = partialDateDao.getDatePartial(sdf.format(prevention.getPreventionDate()), PartialDate.PREVENTION, prevention.getId(), PartialDate.PREVENTION_DATE);
				preventionDates.put(prevention.getDemographicId().toString(), preventionDate);
			}
		}
		return preventionDates;
	}

	public static List<Prevention> getPrevention(LoggedInInfo loggedInInfo, String preventionType, Integer demographicId) {
		return preventionDao.findByTypeAndDemoNo(preventionType, demographicId);
	}

	/**
	 * Check that if remote preventions is disabled, then filter the input list of preventions out of
	 * all remote preventions.
	 *
	 * @param preventions the list of preventions to filter
	 * @return a subset of preventions where all remote preventions are removed if the feature is disabled
	 */
	public static List<Prevention> filterRemotePreventions(List<Prevention> preventions) {
		GWConfigurationService gatewayConfigurationService = SpringUtils.getBean(GWConfigurationService.class);
		if (gatewayConfigurationService.isLinkFeatureEnabled(
				FeatureFlagEnum.PREVENTIONS_ENABLED)) {
			return preventions;
		}

		List<Prevention> filteredPreventions = new ArrayList<>();
		for (var prevention : preventions) {
			if (!prevention.isRemote()) {
				filteredPreventions.add(prevention);
			}
		}

		return filteredPreventions;
	}

	/*
	 * Get all locally stored preventions for a demographic and return them grouped by prevention type.
	 * @param loggedInInfo LoggedInInfo fetched from request session used to fetch demographic DOB
	 * @param demographicId Demographic ID to fetch preventions for
	 * @return Map of prevention type to list of preventions
	 * 				eg. {"Flu" -> [{id: 1, type: "Flu", prevention_date: "2025-03-06 17:57 ...}, ...], ...}
	 */
	public static Map<String, ArrayList<Map<String, Object>>> getAllPreventionData(final LoggedInInfo loggedInInfo, final Integer demographicId) {
		val allPreventionsList = getPreventionData(loggedInInfo, null, demographicId);
		val allPreventionsMap = new HashMap<String, ArrayList<Map<String, Object>>>();

		for (val prevention : allPreventionsList) {
			val preventionType = (String) prevention.get("type");
			allPreventionsMap.putIfAbsent(preventionType, new ArrayList<>());
			allPreventionsMap.get(preventionType).add(prevention);
		}

		return allPreventionsMap;
	}

	public static Map<String, Map<String, String>> getPreventionExtensionDataForDemographic(final Integer demographicId) {

		val allPreventionExts = preventionExtDao.findByDemographicId(demographicId);
		val allPreventionExtsAsListMap = new HashMap<String, List<PreventionExt>>();

		for (val preventionExt : allPreventionExts) {
			val preventionId = preventionExt.getPreventionId().toString();
			allPreventionExtsAsListMap.putIfAbsent(preventionId, new ArrayList<>());
			allPreventionExtsAsListMap.get(preventionId).add(preventionExt);
		}

		val allPreventionExtsMap = new HashMap<String, Map<String, String>>();
		for (val entry : allPreventionExtsAsListMap.entrySet()) {
			val preventionId = entry.getKey();
			val preventionExts = entry.getValue();
			allPreventionExtsMap.put(preventionId, getPreventionKeyValues(preventionExts));
		}

		return allPreventionExtsMap;
	}

	public static ArrayList<Map<String, Object>> getPreventionData(LoggedInInfo loggedInInfo, String preventionType, Integer demographicId) {
		Date dob = getDemographicDateOfBirth(loggedInInfo, demographicId);
		List<Prevention> preventions =
				preventionType == null ? preventionDao.findNotDeletedByDemographicId(demographicId)
						: preventionDao.findByTypeAndDemoNo(preventionType, demographicId);

		preventions = filterRemotePreventions(preventions);

		return convertPreventionsToMap(new ArrayList<>(), preventions, preventionType, dob);
	}

	public static ArrayList<Map<String, Object>> convertPreventionsToMap(
			final ArrayList<Map<String, Object>> preventionMap,
			final List<Prevention> preventions,
			final String preventionType,
			final Date dob) {
		return convertPreventionsToMap(preventionMap, preventions, preventionType, dob, true);
	}

  public static ArrayList<Map<String, Object>> convertPreventionsToMap(
      final ArrayList<Map<String, Object>> preventionMap,
      final List<Prevention> preventions,
      final String preventionType,
      final Date dob,
      final boolean isLocallySavedPreventions) {

		try {
			for (Prevention prevention : preventions) {

				/*
				 * force case sensitive comparison of name; MySQL by default does case INsensitive
				 * DTaP and dTap are considered the same by MySQL
				 */
				if (preventionType != null && !prevention.getPreventionType().equals(preventionType)) {
					continue;
				}

				val h = new HashMap<String, Object>();
				h.put("id", prevention.getId().toString());
				h.put("refused", prevention.isRefused() ? "1" : prevention.isIneligible() ? "2" : prevention.isCompletedExternally() ? "3" : "0");
				h.put("type", prevention.getPreventionType());
				h.put("provider_no", prevention.getProviderNo());
				h.put("provider_name", prevention.getProviderName()!=null&&prevention.getProviderName().trim().length()>0?prevention.getProviderName():ProviderData.getProviderName(prevention.getProviderNo()));
				h.put("creator", prevention.getCreatorProviderNo());
				h.put("creator_name", ProviderData.getProviderName(prevention.getCreatorProviderNo()));
				h.put("isRemote", prevention.isRemote());
                h.put("isLocallySavedRecord", isLocallySavedPreventions);
				h.put("guid", prevention.getGuid());


				Date pDate = prevention.getPreventionDate();
				String d1 = UtilDateUtilities.DateToString(pDate, "yyyy-MM-dd HH:mm");
				String d2 = UtilDateUtilities.DateToString(pDate, "yyyy-MM-dd");

				d1 = partialDateDao.getDatePartial(d1, PartialDate.PREVENTION,  prevention.getId(), PartialDate.PREVENTION_DATE);
				d2 = partialDateDao.getDatePartial(d2, PartialDate.PREVENTION,  prevention.getId(), PartialDate.PREVENTION_DATE);


				h.put("prevention_date", blankIfNull(d1));
				h.put("prevention_date_asDate", pDate);
				h.put("prevention_date_no_time", blankIfNull(d2));

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				h.put("next_date", (prevention.getNextDate() != null ? sdf.format(prevention.getNextDate()) : null));

				Map<String, String> preventionExt = getPreventionKeyValues(prevention);
				String exportPastHealthValue = preventionExt.containsKey("export_past_health") && !preventionExt.get("export_past_health").trim().equals("") ? preventionExt.get("export_past_health") : "false";
				h.put("export_past_health", exportPastHealthValue);

				if (preventionExt.containsKey("result")) {
					h.put("isImmunization", "false");
				} else {
					h.put("isImmunization", "true");
				}
				
				h.put("comments", "");
				String age = "N/A";
				if (pDate != null) {
					age = UtilDateUtilities.calcAgeAtDate(dob, pDate);
				}
				h.put("age", age);
				DataSharingService dataSharingService = SpringUtils.getBean(DataSharingService.class);
				h.put(
						"syncStatus",
						dataSharingService.getPortalStatus(PatientPortalData.Type.PREVENTION, prevention)
				);
				preventionMap.add(h);
			}
			Collections.sort(preventionMap, new PreventionsComparator());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return preventionMap;
	}

	public static ArrayList<HashMap<String, Object>> getLinkedRemotePreventionData(LoggedInInfo loggedInInfo, String preventionType, Integer localDemographicId) {
		ArrayList<HashMap<String, Object>> allResults = RemotePreventionHelper.getLinkedPreventionDataMap(loggedInInfo, localDemographicId);
		ArrayList<HashMap<String, Object>> filteredResults = new ArrayList<HashMap<String, Object>>();

		for (HashMap<String, Object> temp : allResults) {
			if (preventionType.equals(temp.get("type"))) {
				filteredResults.add(temp);
			}
		}

		return (filteredResults);
	}

	public static String getPreventionComment(String id) {
		log.debug("Calling getPreventionComment " + id);
		String comment = null;

		try {
			List<PreventionExt> preventionExts = preventionExtDao.findByPreventionIdAndKey(Integer.valueOf(id), "comments");
			for (PreventionExt preventionExt : preventionExts) {
				comment = preventionExt.getVal();
				if (comment != null && comment.trim().equals("")) {
          comment = null;
        }
				break;
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return comment;
	}

	public static oscar.oscarPrevention.Prevention getLocalandRemotePreventions(LoggedInInfo loggedInInfo, Integer demographicId) {
		oscar.oscarPrevention.Prevention prevention = getPrevention(loggedInInfo, demographicId);

		List<CachedDemographicPrevention> cachedPreventions = getRemotePreventions(loggedInInfo, demographicId);

		if (cachedPreventions != null) {
			for (CachedDemographicPrevention cdp : cachedPreventions) {
				PreventionItem pi = new PreventionItem(cdp);
				prevention.addPreventionItem(pi);
			}
		}

		return prevention;
	}

	public static oscar.oscarPrevention.Prevention getPrevention(LoggedInInfo loggedInInfo, Integer demoNo) {
		DemographicManager demographicManager = SpringUtils.getBean(DemographicManager.class);
		Demographic dd = demographicManager.getDemographic(loggedInInfo, demoNo);

		java.util.Date dob = null;
		String sex = null;
		if (dd != null) {
			Calendar temp = dd.getBirthDay();
			if (temp != null) {
        dob = temp.getTime();
      }
			sex = dd.getSex();
		}

		val dao = (PreventionDao) SpringUtils.getBean("preventionDaoImpl");

		val preventionList = new ArrayList<Prevention>();
		getLocalAndRemotePreventions(dd, preventionList, dao);

		val oscarPrevention = new oscar.oscarPrevention.Prevention(sex, dob);
		addPreventionsToPrevention(oscarPrevention, preventionList);
		return oscarPrevention;
	}

	private static void addPreventionsToPrevention(
			final oscar.oscarPrevention.Prevention oscarPrevention,
			final List<Prevention> preventionList) {
		for (val prevention : preventionList) {
			val preventionItem = new PreventionItem(prevention);
			oscarPrevention.addPreventionItem(preventionItem);
		}
	}

	private static void getLocalAndRemotePreventions(final Demographic demographic,
			final List<Prevention> preventions, final PreventionDao preventionDao) {
		if (demographic == null) {
			return;
		}
		// Get local preventions
		getLocalPreventions(demographic, preventions, preventionDao);
		// Use previously obtained local preventions to filter and add remote preventions
		getAndFilterRemotePreventions(demographic, preventions, preventionDao);
	}

	/**
	 * Get the local prevention data for a demographic.
	 * @param demographic the demographic to get the local prevention data for
	 * @param preventions the list of preventions to add to
	 * @param preventionDao the dao to use to fetch the local prevention data
	 */
	private static void getLocalPreventions(final Demographic demographic,
			final List<Prevention> preventions, final PreventionDao preventionDao) {
		if (demographic == null) {
			return;
		}
		preventions.addAll(preventionDao.findActiveByDemoId(demographic.getDemographicNo()));
	}

	public static List<Prevention> getAndFilterRemotePreventions(final Demographic demographic) {
		val preventions = new ArrayList<Prevention>();
		val dao = (PreventionDao) SpringUtils.getBean("preventionDaoImpl");
		getLocalPreventions(demographic, preventions, dao);
		return getAndFilterRemotePreventions(demographic, preventions, dao);
	}

	/**
	 * Get the remote prevention data for a demographic.
	 * Remote preventions with the same guid as a local prevention will not be added to the list.
	 * @param demographic the demographic to get the remote prevention data for
	 * @param preventions the list of preventions to add to
	 * @param preventionDao the dao to use to fetch the remote prevention data
	 */
	private static List<Prevention> getAndFilterRemotePreventions(final Demographic demographic,
			final List<Prevention> preventions, final PreventionDao preventionDao) {
		val gatewayConfigurationService =
				(GWConfigurationService) SpringUtils.getBean(GWConfigurationService.class);
		if (demographic == null
				|| !demographic.isLinked()
				|| !gatewayConfigurationService.isLinkEnabled()) {
			return new ArrayList<>();
		}

		val remotePreventions = new ArrayList<Prevention>();
		val preventionGuidSet = getSetOfPreventionGuids(preventions);
		for (val prevention : preventionDao.fetchRemotePreventions(demographic.getDemographicNo())) {
			if (isGuidInPreventionSet(preventionGuidSet, prevention.getGuid())) {
				preventions.add(prevention);
				remotePreventions.add(prevention);
			}
		}
		return remotePreventions;
	}

	private static boolean isGuidInPreventionSet(final Set<String> preventionGuidSet,
			final String guid) {
		return guid != null && !preventionGuidSet.contains(guid);
	}

	public static Set<String> getSetOfPreventionGuids(final List<Prevention> preventionItemList) {
		val preventionGuidSet = new HashSet<String>();
		for (val prevention : preventionItemList) {
			preventionGuidSet.add(prevention.getGuid());
		}
		return preventionGuidSet;
	}

	public static List<CachedDemographicPrevention> getRemotePreventions(LoggedInInfo loggedInInfo, Integer demographicId) {

		List<CachedDemographicPrevention> remotePreventions = null;
		if (loggedInInfo.getCurrentFacility().isIntegratorEnabled()) {

			try {
				if (!CaisiIntegratorManager.isIntegratorOffline(loggedInInfo.getSession())) {
					remotePreventions = CaisiIntegratorManager.getLinkedPreventions(loggedInInfo, demographicId);
				}
			} catch (Exception e) {
				MiscUtils.getLogger().error("Unexpected error.", e);
				CaisiIntegratorManager.checkForConnectionError(loggedInInfo.getSession(), e);
			}

			if (CaisiIntegratorManager.isIntegratorOffline(loggedInInfo.getSession())) {
				remotePreventions = IntegratorFallBackManager.getRemotePreventions(loggedInInfo, demographicId);
			}
		}
		if(remotePreventions == null) {
			remotePreventions = Collections.emptyList();
		}

		return remotePreventions;
	}

	public static oscar.oscarPrevention.Prevention addRemotePreventions(LoggedInInfo loggedInInfo, oscar.oscarPrevention.Prevention prevention, Integer demographicId) {
		List<CachedDemographicPrevention> remotePreventions = getRemotePreventions(loggedInInfo, demographicId);

		for (CachedDemographicPrevention cachedDemographicPrevention : remotePreventions) {
			Date preventionDate = DateUtils.toDate(cachedDemographicPrevention.getPreventionDate());

			PreventionItem pItem = new PreventionItem(cachedDemographicPrevention.getPreventionType(), preventionDate);
			pItem.setRemoteEntry(true);
			prevention.addPreventionItem(pItem);
		}

		return (prevention);
	}

	public static List<Prevention> addRemotePreventions(LoggedInInfo loggedInInfo, List<Prevention> preventions, Integer demographicId, String preventionType, Date demographicDateOfBirth) {
		List<CachedDemographicPrevention> remotePreventions = getRemotePreventions(loggedInInfo, demographicId);
		for (CachedDemographicPrevention cachedDemographicPrevention : remotePreventions) {
			if (preventionType.equals(cachedDemographicPrevention.getPreventionType())) {
				Prevention prevention = new Prevention();
				prevention.setPreventionDate(cachedDemographicPrevention.getPreventionDate().getTime());
				prevention.setPreventionType(preventionType);
				prevention.setCompletedExternally(true);
				prevention.setProviderNo("r:"+cachedDemographicPrevention.getCaisiProviderId());
				preventions.add(prevention);
			}

			Collections.sort(preventions, new PreventionComparator());
		}
		return preventions;
	}

	public static ArrayList<Map<String, Object>> addRemotePreventions(LoggedInInfo loggedInInfo, ArrayList<Map<String, Object>> preventions, Integer demographicId, String preventionType, Date demographicDateOfBirth) {
		List<CachedDemographicPrevention> remotePreventions = getRemotePreventions(loggedInInfo, demographicId);
		return addRemotePreventions(loggedInInfo, remotePreventions, preventions, preventionType, demographicDateOfBirth);
	}

	public static ArrayList<Map<String, Object>> addRemotePreventions(LoggedInInfo loggedInInfo, List<CachedDemographicPrevention> remotePreventions, ArrayList<Map<String, Object>> preventions, String preventionType, Date demographicDateOfBirth) {
		for (CachedDemographicPrevention cachedDemographicPrevention : remotePreventions) {
			if (preventionType.equals(cachedDemographicPrevention.getPreventionType())) {

				Map<String, Object> h = new HashMap<String, Object>();
				h.put("integratorFacilityId", cachedDemographicPrevention.getFacilityPreventionPk().getIntegratorFacilityId());
				h.put("integratorPreventionId", cachedDemographicPrevention.getFacilityPreventionPk().getCaisiItemId());
				String remoteFacilityName = "N/A";
				CachedFacility remoteFacility = null;
				try {
					remoteFacility = CaisiIntegratorManager.getRemoteFacility(loggedInInfo, loggedInInfo.getCurrentFacility(), cachedDemographicPrevention.getFacilityPreventionPk().getIntegratorFacilityId());
				} catch (Exception e) {
					log.error("Error", e);
				}

				if (remoteFacility != null)
				{
					remoteFacilityName = remoteFacility.getName();
				}

				h.put("remoteFacilityName", remoteFacilityName);
				h.put("integratorDemographicId", cachedDemographicPrevention.getCaisiDemographicId());
				h.put("type", cachedDemographicPrevention.getPreventionType());
				h.put("provider_no", "remote:" + cachedDemographicPrevention.getCaisiProviderId());
				h.put("provider_name", "remote:" + cachedDemographicPrevention.getCaisiProviderId());
				h.put("prevention_date", DateFormatUtils.ISO_DATE_FORMAT.format(cachedDemographicPrevention.getPreventionDate()) + " 00:00");
				h.put("prevention_date_asDate", cachedDemographicPrevention.getPreventionDate());
				h.put("prevention_date_no_time", blankIfNull(DateFormatUtils.ISO_DATE_FORMAT.format(cachedDemographicPrevention.getPreventionDate())));

				if (demographicDateOfBirth != null) {
					String age = UtilDateUtilities.calcAgeAtDate(demographicDateOfBirth, DateUtils.toDate(cachedDemographicPrevention.getPreventionDate()));
					h.put("age", age);
				} else {
					h.put("age", "N/A");
				}

				preventions.add(h);
			}

			Collections.sort(preventions, new PreventionsComparator());
		}

		return preventions;
	}

	public static class PreventionsComparator implements Comparator<Map<String, Object>> {
		@Override
    public int compare(Map<String, Object> o1, Map<String, Object> o2) {
			Comparable date1 = (Comparable) o1.get("prevention_date_asDate");
			Comparable date2 = (Comparable) o2.get("prevention_date_asDate");

			if (date1 != null && date2 != null) {
				if (date1 instanceof Calendar) {
					date1 = ((Calendar) date1).getTime();
				}

				if (date2 instanceof Calendar) {
					date2 = ((Calendar) date2).getTime();
				}

				return (date1.compareTo(date2));
			} else {
				return (0);
			}
		}
	}

	public static class PreventionComparator implements Comparator<Prevention> {
		public int compare(Prevention o1, Prevention o2) {
			Date date1 =  o1.getPreventionDate();
			Date date2 =  o2.getPreventionDate();

			if (date1 != null && date2 != null) {

				return (date1.compareTo(date2));
			} else {
				return (0);
			}
		}
	}

	public static Prevention getRemotePreventionById(String guid, String demographicNo) {
		val demographicDao = (DemographicDao) SpringUtils.getBean(DemographicDao.class);
		val demographic = demographicDao.getDemographic(demographicNo);
		return preventionDao.findRemoteById(guid, demographic);
	}

	public static Map<String, Object> getPreventionById(String id) {
		Prevention prevention = null;
		if (id.length() == GUID_LENGTH) {
			prevention = preventionDao.findByGuid(id);
		}
		if (prevention == null) {
			prevention = preventionDao.find(Integer.valueOf(id));
		}
		return convertPreventionToMap(prevention);
	}

	public static Map<String, Object> convertPreventionToMap(final Prevention prevention) {

		Map<String, Object> h = null;

		try {
			if (prevention != null) {
				Map<String, String> ext = getPreventionKeyValues(prevention);

				h = new HashMap<String, Object>();
				String providerName = null;
				if(!"-1".equals(prevention.getProviderNo())) {
					providerName = ProviderData.getProviderName(prevention.getProviderNo());
				} else {
					providerName = ext.get("providerName") != null ? ext.get("providerName") : "";
				}

				String preventionDate = UtilDateUtilities.DateToString(prevention.getPreventionDate(), "yyyy-MM-dd HH:mm");
				String lastUpdateDate = UtilDateUtilities.DateToString(prevention.getLastUpdateDate(), "yyyy-MM-dd");
        val creatorName = prevention.isRemote()
                ? prevention.getCreatorName()
                : ProviderData.getProviderName(prevention.getCreatorProviderNo());

				addToHashIfNotNull(h, "id", prevention.getId().toString());
				addToHashIfNotNull(h, "demographicNo", prevention.getDemographicId().toString());
				addToHashIfNotNull(h, "provider_no", prevention.getProviderNo());
				addToHashIfNotNull(h, "site", prevention.getLocation());
				addToHashIfNotNull(h, "providerName", providerName);
				addToHashIfNotNull(h, "creationDate", UtilDateUtilities.DateToString(prevention.getCreationDate(), "yyyy-MM-dd"));
				addToHashIfNotNull(h, "preventionDate", preventionDate);
				addToHashIfNotNull(h, "prevention_date_asDate", prevention.getPreventionDate());
				addToHashIfNotNull(h, "preventionType", prevention.getPreventionType());
				addToHashIfNotNull(h, "deleted", prevention.isDeleted() ? "1" : "0");
				addToHashIfNotNull(h, "refused", prevention.isRefused() ? "1" : prevention.isIneligible() ? "2" : prevention.isCompletedExternally() ? "3" : "0");
				addToHashIfNotNull(h, "next_date", UtilDateUtilities.DateToString(prevention.getNextDate(), "yyyy-MM-dd"));
				addToHashIfNotNull(h, "never", prevention.isNever() ? "1" : "0");
				addToHashIfNotNull(h, "creator", prevention.getCreatorProviderNo());
				addToHashIfNotNull(h, "snomedId", prevention.getSnomedId());
				addToHashIfNotNull(h, "isRemote", Boolean.toString(prevention.isRemote()));
				addToHashIfNotNull(h, "guid", prevention.getGuid());
				String summary = "Prevention " + prevention.getPreventionType() + " provided by " + providerName + " on " + preventionDate;
				summary = summary + " entered by " + creatorName + " on " + lastUpdateDate;


				String exportPastHealthValue = ext.containsKey("export_past_health") && !ext.get("export_past_health").trim().equals("") ? ext.get("export_past_health") : "false";
				h.put("export_past_health", exportPastHealthValue);

				addToHashIfNotNull(h, "brandSnomedId", ext.get("brandSnomedId"));

				if (ext.containsKey("result")) { //This is a preventive Test
					addToHashIfNotNull(h, "result", ext.get("result"));
					summary += "\nResult: " + ext.get("result");
					if (ext.containsKey("reason") && !ext.get("reason").equals("")) {
						addToHashIfNotNull(h, "reason", ext.get("reason"));
						summary += "\nReason: " + ext.get("reason");
					}
				} else { //This is an immunization
					if (ext.containsKey("name") && !ext.get("name").equals("")) {
						addToHashIfNotNull(h, "name", ext.get("name"));
						summary += "\nName: " + ext.get("name");
					}
					if (ext.containsKey("location") && !ext.get("location").equals("")) {
						addToHashIfNotNull(h, "location", ext.get("location"));
						if(ext.containsKey("locationDisplay") && !ext.get("locationDisplay").equals("")) {
							summary += "\nLocation: " + ext.get("locationDisplay");
						} else {
							summary += "\nLocation: " + ext.get("location");
						}
					}
					if (ext.containsKey("expiryDate") && !ext.get("expiryDate").equals("")) {
						addToHashIfNotNull(h, "expiryDate", ext.get("expiryDate"));
						summary += "\nExpiryDate: " + ext.get("expiryDate");
					}
					if (ext.containsKey("route") && !ext.get("route").equals("")) {
						addToHashIfNotNull(h, "route", ext.get("route"));
						if(ext.containsKey("routeDisplay") && !ext.get("routeDisplay").equals("")) {
							summary += "\nRoute: " + ext.get("routeDisplay");
						} else {
							summary += "\nRoute: " + ext.get("route");
						}
					}
					if (ext.containsKey("dose") && !ext.get("dose").equals("")) {
						addToHashIfNotNull(h, "dose", ext.get("dose"));
						summary += "\nDose: " + ext.get("dose");
					}
					if (ext.containsKey("lot") && !ext.get("lot").equals("")) {
						addToHashIfNotNull(h, "lot", ext.get("lot"));
						summary += "\nLot: " + ext.get("lot");
					}
					if (ext.containsKey("manufacture") && !ext.get("manufacture").equals("")) {
						addToHashIfNotNull(h, "manufacture", ext.get("manufacture"));
						summary += "\nManufacturer: " + ext.get("manufacture");
					}
					if (ext.containsKey("din") && !ext.get("din").equals("")) {
						addToHashIfNotNull(h, "din", ext.get("din"));
						summary += "\nDIN: " + ext.get("din");
					}
				}
				if (!StringUtils.isBlank(ext.get("instructions"))) {
					addToHashIfNotNull(h, "instructions", ext.get("instructions"));
					summary += "\nInstructions: " + ext.get("instructions");
				}
				if (ext.containsKey("comments") && !ext.get("comments").equals("")) {
					addToHashIfNotNull(h, "comments", ext.get("comments"));
					summary += "\nComments: " + ext.get("comments");
				}

				DHIRSubmissionManager dhirSubmissionManager = SpringUtils.getBean(DHIRSubmissionManager.class);
				List<DHIRSubmissionLog> dhirLogs =  dhirSubmissionManager.findByPreventionId(prevention.getId());
				if(!dhirLogs.isEmpty()) {
					summary += "\n\nDHIR Submission Transaction ID: " + dhirLogs.get(0).getTransactionId();
					summary += "\nDHIR Submission Location ID: " + dhirLogs.get(0).getBundleId();
				}
				addToHashIfNotNull(h, "summary", summary);
				log.debug("1" + h.get("preventionType") + " " + h.size());
				log.debug("id" + h.get("id"));

			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return h;
	}

	private static void addToHashIfNotNull(Map<String, Object> h, String key, String val) {
		if (val != null && !val.equalsIgnoreCase("null")) {
			h.put(key, val);
		}
	}

	private static void addToHashIfNotNull(Map<String, Object> h, String key, Date val) {
		if (val != null) {
			h.put(key, val);
		}
	}

	private static String blankIfNull(String s) {
		if (s == null) {
      return "";
    }
		return s;
	}
}
