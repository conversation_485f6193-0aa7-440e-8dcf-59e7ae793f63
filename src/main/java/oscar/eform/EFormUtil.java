/**
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version. 
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for the
 * Department of Family Medicine
 * McMaster University
 * Hamilton
 * Ontario, Canada
 */

package oscar.eform;

import com.lowagie.text.DocumentException;
import com.quatro.model.security.Secobjprivilege;
import freemarker.template.TemplateException;
import health.apps.gateway.LinkUtility;
import health.apps.gateway.service.GWConfigurationService;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.UUID;
import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.persistence.PersistenceException;
import javax.servlet.http.HttpServletRequest;
import lombok.val;
import lombok.var;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.log4j.Logger;
import org.apache.tika.io.FilenameUtils;
import org.oscarehr.PMmodule.dao.ProviderDao;
import org.oscarehr.PMmodule.model.ProgramProvider;
import org.oscarehr.casemgmt.dao.CaseManagementNoteLinkDAO;
import org.oscarehr.casemgmt.model.CaseManagementIssue;
import org.oscarehr.casemgmt.model.CaseManagementNote;
import org.oscarehr.casemgmt.model.CaseManagementNoteLink;
import org.oscarehr.casemgmt.model.Issue;
import org.oscarehr.casemgmt.service.CaseManagementManager;
import org.oscarehr.common.dao.ConsultationRequestDao;
import org.oscarehr.common.dao.DemographicDao;
import org.oscarehr.common.dao.EFormDao;
import org.oscarehr.common.dao.EFormDao.EFormSortOrder;
import org.oscarehr.common.dao.EFormDataDao;
import org.oscarehr.common.dao.EFormGroupDao;
import org.oscarehr.common.dao.EFormValueDao;
import org.oscarehr.common.dao.OscarAppointmentDao;
import org.oscarehr.common.dao.ProfessionalSpecialistDao;
import org.oscarehr.common.dao.PropertyDao;
import org.oscarehr.common.dao.SecRoleDao;
import org.oscarehr.common.dao.SystemPreferencesDao;
import org.oscarehr.common.dao.TicklerDao;
import org.oscarehr.common.dto.EFormDataMetadata;
import org.oscarehr.common.enums.FeatureFlagEnum;
import org.oscarehr.common.model.AbstractModel;
import org.oscarehr.common.model.Appointment;
import org.oscarehr.common.model.ConsultationRequest;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.common.model.EFormData;
import org.oscarehr.common.model.EFormGroup;
import org.oscarehr.common.model.EFormValue;
import org.oscarehr.common.model.OscarMsgType;
import org.oscarehr.common.model.Prevention;
import org.oscarehr.common.model.ProfessionalSpecialist;
import org.oscarehr.common.model.Provider;
import org.oscarehr.common.model.SecRole;
import org.oscarehr.common.model.SystemPreferences;
import org.oscarehr.common.model.Tickler;
import org.oscarehr.managers.PreventionManager;
import org.oscarehr.managers.ProgramManager2;
import org.oscarehr.managers.SecurityInfoManager;
import org.oscarehr.util.EFormConstants;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.MiscUtils;
import org.oscarehr.util.SpringUtils;
import org.oscarehr.util.WKHtmlToPdfUtils;
import org.owasp.encoder.Encode;
import org.xhtmlrenderer.pdf.ITextRenderer;
import oscar.OscarProperties;
import oscar.dms.EDoc;
import oscar.dms.EDocUtil;
import oscar.eform.actions.DisplayImageAction;
import oscar.eform.data.EForm;
import oscar.eform.data.EFormBase;
import oscar.oscarClinic.ClinicData;
import oscar.oscarDB.DBHandler;
import oscar.oscarMessenger.data.MsgMessageData;
import oscar.util.ConversionUtils;
import oscar.util.OscarRoleObjectPrivilege;
import oscar.util.TempFileManager;
import oscar.util.TemplateUtil;
import oscar.util.UtilDateUtilities;

public class EFormUtil {
	private static final Logger logger = MiscUtils.getLogger();

	// for sorting....
	public static final String NAME = "form_name";
	public static final String SUBJECT = "subject";
	public static final String DATE = "form_date DESC, form_time DESC";
	public static final String FILE_NAME = "file_name";
	public static final String PROVIDER = "form_provider";
	// -----------
	public static final String DELETED = "deleted";
	public static final String CURRENT = "current";
	public static final String ALL = "all";

	private static final CaseManagementManager cmm = SpringUtils.getBean(CaseManagementManager.class);
	private static final CaseManagementNoteLinkDAO cmDao = SpringUtils.getBean(CaseManagementNoteLinkDAO.class);
	private static EFormDataDao eFormDataDao = SpringUtils.getBean(EFormDataDao.class);
	private static GWConfigurationService configurationService =
			SpringUtils.getBean(GWConfigurationService.class);
	private static final EFormValueDao eFormValueDao = SpringUtils.getBean(EFormValueDao.class);
	private static final EFormGroupDao eFormGroupDao = SpringUtils.getBean(EFormGroupDao.class);
	private static final ProviderDao providerDao = SpringUtils.getBean(ProviderDao.class);
	private static final TicklerDao ticklerDao = SpringUtils.getBean(TicklerDao.class);
	private static final PreventionManager preventionManager = SpringUtils.getBean(PreventionManager.class);
	private static final ConsultationRequestDao consultationRequestDao = SpringUtils.getBean(ConsultationRequestDao.class);
	private static final ProfessionalSpecialistDao professionalSpecialistDao = SpringUtils.getBean(ProfessionalSpecialistDao.class);
	private static final SystemPreferencesDao systemPreferencesDao = SpringUtils.getBean(SystemPreferencesDao.class);

	EFormUtil() {}

	public static String saveEForm(EForm eForm) {
		return saveEForm(eForm.getFormName(), eForm.getFormSubject(), eForm.getFormFileName(),
				eForm.getFormHtml(), eForm.getFormCreator(), eForm.isShowLatestFormOnly(),
				eForm.isPatientIndependent(), eForm.isAttachmentsEnabled(), eForm.getRoleType());
	}

	public static String saveEForm(String formName, String formSubject, String fileName,
			String htmlStr, boolean showLatestFormOnly, boolean patientIndependent, String roleType) {
		return saveEForm(formName, formSubject, fileName, htmlStr, null, showLatestFormOnly,
				patientIndependent, true, roleType);
	}

	public static String saveEForm(String formName, String formSubject, String fileName,
			String htmlStr, boolean showLatestFormOnly, boolean patientIndependent,
			final boolean isAttachmentsEnabled, String roleType) {
		return saveEForm(formName, formSubject, fileName, htmlStr, null, showLatestFormOnly,
				patientIndependent, isAttachmentsEnabled, roleType);
	}

	public static String saveEForm(String formName, String formSubject, String fileName,
			String htmlStr, String creator, boolean showLatestFormOnly, boolean patientIndependent,
			final boolean isAttachmentsEnabled, String roleType) {
		// called by the upload action, puts the uploaded form into DB
		org.oscarehr.common.model.EForm eform = new org.oscarehr.common.model.EForm();
		eform.setFormName(formName);
		eform.setFileName(fileName);
		eform.setSubject(formSubject);
		eform.setCreator(creator);
		eform.setCurrent(true);
		eform.setFormHtml(htmlStr);
		eform.setShowLatestFormOnly(showLatestFormOnly);
		eform.setPatientIndependent(patientIndependent);
		eform.setAttachmentsEnabled(isAttachmentsEnabled);
		eform.setRoleType(roleType);

		EFormDao dao = SpringUtils.getBean(EFormDao.class);
		dao.persist(eform);

		return eform.getId().toString();
	}

	public static ArrayList<HashMap<String, ? extends Object>> listEForms(String sortBy, String deleted) {

		// sends back a list of forms that were uploaded (those that can be added to the patient)
		EFormDao dao = SpringUtils.getBean(EFormDao.class);
		List<org.oscarehr.common.model.EForm> eforms = null;
		Boolean status = null;
		if (deleted.equals("deleted")) {
			status = false;
		} else if (deleted.equals("current")) {
			status = true;
		} else if (deleted.equals("all")) {
			status = null;
		}

		EFormSortOrder sortOrder = null;
		if (NAME.equals(sortBy)) sortOrder = EFormSortOrder.NAME;
		else if (SUBJECT.equals(sortBy)) sortOrder = EFormSortOrder.SUBJECT;
		else if (FILE_NAME.equals(sortBy)) sortOrder = EFormSortOrder.FILE_NAME;
		else if (DATE.equals(sortBy)) sortOrder = EFormSortOrder.DATE;

		eforms = dao.findByStatus(status, sortOrder);

		ArrayList<HashMap<String, ? extends Object>> results = new ArrayList<HashMap<String, ? extends Object>>();
		for (org.oscarehr.common.model.EForm eform : eforms) {
			HashMap<String, Object> curht = new HashMap<String, Object>();
			curht.put("fid", eform.getId().toString());
			curht.put("formName", eform.getFormName());
			curht.put("formSubject", eform.getSubject());
			curht.put("formFileName", eform.getFileName());
			curht.put("formDate", ConversionUtils.toDateString(eform.getFormDate()));
			curht.put("formDateAsDate", eform.getFormDate());
			curht.put("formTime", ConversionUtils.toTimeString(eform.getFormTime()));
			curht.put("roleType", eform.getRoleType());
			results.add(curht);
		}
		return (results);
	}

	public static ArrayList<HashMap<String, ? extends Object>> listEForms(String sortBy, String deleted, String userRoles) {
		ArrayList<HashMap<String, ? extends Object>> results = new ArrayList<HashMap<String, ? extends Object>>();
		ArrayList<HashMap<String, ? extends Object>> eForms = listEForms(sortBy, deleted);
		if (eForms.size() > 0) {
			for (int i = 0; i < eForms.size(); i++) {
				HashMap<String, ? extends Object> curForm = eForms.get(i);
				// filter eform by role type
				if (curForm.get("roleType") != null && !curForm.get("roleType").equals("")) {
					// ojectName: "_admin,_admin.eform"
					// roleName: "doctor,admin"
					String objectName = "_eform." + curForm.get("roleType");
					Vector v = OscarRoleObjectPrivilege.getPrivilegeProp(objectName);
					if (!OscarRoleObjectPrivilege.checkPrivilege(userRoles, (Properties) v.get(0), (Vector) v.get(1))) {
						continue;
					}
				}
				results.add(curForm);
			}
		}
		return (results);
	}

	public static ArrayList<String> listSecRole() {
		SecRoleDao dao = (SecRoleDao) SpringUtils.getBean(SecRoleDao.class);
		ArrayList<String> results = new ArrayList<String>();
		for (SecRole role : dao.findAll())
			results.add(Encode.forHtmlContent(role.getName()));

		return (results);
	}

  public static List<String> listImages() {
    String imagePath = OscarProperties.getInstance().getProperty("eform_image");
    logger.debug("Img Path: " + imagePath);
    File dir = new File(imagePath);
    return listImages(dir);
  }

  protected static List<String> listImages(File directory) {
    String[] files = directory.list();
    List<String> fileList;
    if (files != null) {
      Arrays.sort(files);
      fileList = Arrays.asList(files);
    }
    else {
      fileList = new ArrayList<>();
    }

    return fileList;
  }

	/**
	 * Given a list of EFormData, this method will add remote eForms to the list for the given
	 * demographic
	 *
	 * @param demographicNo - demographic number to query remote systems for
	 * @param eformData - list of EFormData to add remote eForms to
	 * @return - the updated list of EFormData with remote eForms added
	 */
	private static List<EFormData> addRemoteEforms(final Integer demographicNo, List<EFormData> eformData) {
		val remotes = eFormDataDao.fetchRemoteEformByDemographicId(demographicNo);

		if (remotes != null && !remotes.isEmpty()) {
			eformData.addAll(remotes);
			return LinkUtility.deduplicateByGuid(eformData);
		}

		return eformData;
	}

	/**
	 * Given a list of EFormDataMetadata, this method will add remote eForm metadata to the list
	 * for the given demographic
	 *
	 * @param demographicNo - demographic number to query remote systems for
	 * @param eformDataMetadata - list of EFormDataMetadata to add remote eForm metadata to
	 * @return - the updated list of EFormDataMetadata with remote eForm metadata added
	 */
	private static List<EFormDataMetadata> addRemoteEformDataMetadata(
			final Integer demographicNo,
			List<EFormDataMetadata> eformDataMetadata
	) {
		val remotes = eFormDataDao.fetchRemoteEformByDemographicId(demographicNo);

		if (remotes != null && !remotes.isEmpty()) {
			for (var remote : remotes) {
				eformDataMetadata.add(remote.getMetadata());
			}
			return LinkUtility.deduplicateByGuid(eformDataMetadata);
		}

		return eformDataMetadata;
	}

	/**
	 * Fetches the current eForms for a given patient demographic number. This method specifically
	 * returns the e-forms in the form of EFormDataMetadata.
	 *
	 * @param demographicNo - demographic number to find e-forms for
	 * @param current - status of e-forms to search for
	 * @param startIndex - the starting index for pagination
	 * @param numToReturn - the number of e-forms to return
	 * @return - a list of EFormDataMetadata objects representing the current e-forms
	 */
	public static List<EFormDataMetadata> listPatientEformsMetadataCurrent(
			final Integer demographicNo,
			final Boolean current,
			final int startIndex,
			final int numToReturn
	) {
		val currentEforms = eFormDataDao.findMetadataByDemographicIdCurrent(
				demographicNo, current, startIndex, numToReturn, null);
		return addRemoteEformDataMetadata(demographicNo, currentEforms);
	}

	/**
	 * Fetches the current eForms for a given patient demographic number.
	 * @param demographicNo - demographic number to find e-forms for
	 * @param current - status of e-forms to search for
	 * @param startIndex - the starting index for pagination
	 * @param numToReturn - the number of e-forms to return
	 * @return - a list of EFormData objects representing the current e-forms
	 */
	public static List<EFormData> listPatientEformsCurrent(
      final Integer demographicNo,
      final Boolean current,
      final int startIndex,
      final int numToReturn
  ) {
	  val currentEforms = eFormDataDao.findByDemographicIdCurrent(
			  demographicNo, current, startIndex, numToReturn);
		return addRemoteEforms(demographicNo, currentEforms);
  }

	public static List<EFormData> listPatientEformsCurrentAttachedToConsult(String consultationId) {
		return eFormDataDao.findByDemographicIdCurrentAttachedToConsult(consultationId);
	}

	public static ArrayList<Map<String, ?>> listPatientEForms(String sortBy, String deleted, String demographic_no, String userRoles, int offset, int itemsToReturn) {
		Boolean current = null;
		if ("deleted".equals(deleted)) {
			current = false;
		} else if ("current".equals(deleted)) {
			current = true;
		}

		val demographicNo = Integer.parseInt(demographic_no);
		List<EFormData> allEformDatas = eFormDataDao.findByDemographicIdCurrent(demographicNo, current, offset, itemsToReturn, sortBy);

		// list of guids we don't work
		// val remoteEforms = eFormDataDao.fetchRemoteEformByDemographicId(demographicNo);
		// TODO: get a list of local eforms and compare them

		ArrayList<Map<String, ?>> results = new ArrayList<>();
		try {
			for (EFormData eFormData : allEformDatas) {
				// filter eform by role type
				String tempRole = StringUtils.trimToNull(eFormData.getRoleType());
				if (userRoles != null && tempRole != null) {
					// objectName: "_admin,_admin.eform"
					// roleName: "doctor,admin"
					String objectName = "_eform." + tempRole;
					Vector<Object> v = OscarRoleObjectPrivilege.getPrivilegeProp(objectName);
					if (!OscarRoleObjectPrivilege.checkPrivilege(userRoles, (Properties) v.get(0), (Vector) v.get(1))) {
						continue;
					}
				}

				 val mappedEformData = eFormDataDao.mapEFormData(eFormData);

				 results.add(mappedEformData);
			}

			// NOTE: we only want to add remote results to the list IF we run out of local items. Local is priority.
			if (results.size() < itemsToReturn
					&& configurationService.isLinkFeatureEnabled(FeatureFlagEnum.EFORM_ENABLED)) {
				results.addAll(fetchRemoteEformsAfterLocal(current, offset, itemsToReturn, demographicNo));
			}
		} catch (Exception sqe) {
			logger.error("Error", sqe);
		}

		return results;
	}

	/**
	 * Adds remote eForms to the results list if the number of local eForms is insufficient.
	 *
	 * This method handles fetching remote eForms based on the provided pagination parameters
	 * (offset and itemsToReturn). It prioritizes local eForms, only fetching remote ones when
	 * necessary to meet the itemsToReturn requirement.
	 *
	 * Offset calculation logic:
	 * -  offset: The starting point for pagination, considering both local and remote eForms.
	 * - totalLocalEforms: The total number of local eForms available.
	 * - remoteOffset: The starting point for fetching remote eForms if the local eForms are insufficient.
	 * - remoteItemsToReturn: The total number of remote eForms to return, ensuring the total items do not exceed itemsToReturn.
	 *
	 * Examples:
	 * offset, max results, local, remote, remote offset
	 * 0,      25,          10,   15,       0
	 * 25,     25,          0,    25,       15
	 * 50,     25,          0,    25,       40
	 * 75,     25,          0,    25,       65
	 *
	 * @param demographicNo The demographic ID for which eForms are being fetched.
	 * @param offset The offset for pagination, combining local and remote counts.
	 * @param itemsToReturn The maximum number of items to fetch.
	 *
	 * @return The list of maps to which the fetched remote eForms will be added.
	 */
	static List<Map<String, Object>> fetchRemoteEformsAfterLocal(Boolean current, int offset, int itemsToReturn, int demographicNo) {
		try {
			val totalLocalEforms = eFormDataDao.countByDemographicIdCurrent(demographicNo, current);
			val localEformGUIDs = eFormDataDao.getGUIDsByDemographicIdCurrent(demographicNo, current);

			val remoteOffset = Math.max(offset - totalLocalEforms, 0);
			val remoteItemsToReturn = Math.min(offset + itemsToReturn - totalLocalEforms, itemsToReturn);

			// TODO: implement offset in fetchRemoteEformByDemographicId
			val remoteEforms = eFormDataDao.fetchRemoteEformByDemographicId(demographicNo);
			val chosenRemoteEForms = new ArrayList<EFormData>();
			if (remoteEforms == null) {
				return new ArrayList<>();
			}

			while (!remoteEforms.isEmpty()) {
				val removed = remoteEforms.remove(remoteOffset);
				if (localEformGUIDs.contains(removed.getGuid())) {
					continue;
				}

				chosenRemoteEForms.add(removed);
				if (chosenRemoteEForms.size() == remoteItemsToReturn) {
					return eFormDataDao.convertEFormDataListToMapList(chosenRemoteEForms);
				}
			}

			return eFormDataDao.convertEFormDataListToMapList(chosenRemoteEForms);
			// we want to ignore the exception as we can simply display the current eforms
		} catch (Exception exception) {
			logger.error("Error fetching remote eForms for demographic ID: " + demographicNo, exception);
		}

		return new ArrayList<>();
	}

	@Deprecated
	public static ArrayList<HashMap<String, ? extends Object>> listPatientEForms(String sortBy, String deleted, String demographic_no, String userRoles) {

		Boolean current = null;
		if (deleted.equals("deleted")) current = false;
		else if (deleted.equals("current")) current = true;
		
		List<EFormData> allEformDatas = eFormDataDao.findByDemographicIdCurrent(Integer.parseInt(demographic_no), current);

		if (NAME.equals(sortBy)) Collections.sort(allEformDatas, EFormData.FORM_NAME_COMPARATOR);
		else if (SUBJECT.equals(sortBy)) Collections.sort(allEformDatas, EFormData.FORM_SUBJECT_COMPARATOR);
		else Collections.sort(allEformDatas, EFormData.FORM_DATE_COMPARATOR);

		ArrayList<HashMap<String, ? extends Object>> results = new ArrayList<HashMap<String, ? extends Object>>();
		try {
			for (EFormData eFormData : allEformDatas) {
				// filter eform by role type
				String tempRole = StringUtils.trimToNull(eFormData.getRoleType());
				if (userRoles != null && tempRole != null) {
					// ojectName: "_admin,_admin.eform"
					// roleName: "doctor,admin"
					String objectName = "_eform." + tempRole;
					Vector v = OscarRoleObjectPrivilege.getPrivilegeProp(objectName);
					if (!OscarRoleObjectPrivilege.checkPrivilege(userRoles, (Properties) v.get(0), (Vector) v.get(1))) {
						continue;
					}
				}
				HashMap<String, Object> curht = new HashMap<String, Object>();
				curht.put("fdid", eFormData.getId().toString());
				curht.put("fid", eFormData.getFormId().toString());
				curht.put("formName", eFormData.getFormName());
				curht.put("formSubject", eFormData.getSubject());
				curht.put("formDate", eFormData.getFormDate().toString());
				curht.put("formTime", eFormData.getFormTime().toString());
				curht.put("formDateAsDate", eFormData.getFormDate());
				curht.put("roleType", eFormData.getRoleType());
				curht.put("providerNo", eFormData.getProviderNo());
				results.add(curht);
			}
		} catch (Exception sqe) {
			logger.error("Error", sqe);
		}
		return (results);
	}

	public static ArrayList<HashMap<String, ? extends Object>> listPatientIndependentEForms(String sortBy, String deleted) {

		Boolean current = null;
		if (deleted.equals("deleted")) current = false;
		else if (deleted.equals("current")) current = true;

		List<EFormData> allEformDatas = eFormDataDao.findPatientIndependent(current);

		if (NAME.equals(sortBy)) Collections.sort(allEformDatas, EFormData.FORM_NAME_COMPARATOR);
		else if (SUBJECT.equals(sortBy)) Collections.sort(allEformDatas, EFormData.FORM_SUBJECT_COMPARATOR);
		else if (PROVIDER.equals(sortBy)) sortByProviderName(allEformDatas);
		else Collections.sort(allEformDatas, EFormData.FORM_DATE_COMPARATOR);

		ArrayList<HashMap<String, ? extends Object>> results = new ArrayList<HashMap<String, ? extends Object>>();
		try {
			for (EFormData eFormData : allEformDatas) {
				HashMap<String, Object> curht = new HashMap<String, Object>();
				curht.put("fdid", eFormData.getId().toString());
				curht.put("fid", eFormData.getFormId().toString());
				curht.put("formName", eFormData.getFormName());
				curht.put("formSubject", eFormData.getSubject());
				curht.put("formDate", eFormData.getFormDate().toString());
				curht.put("formTime", eFormData.getFormTime().toString());
				curht.put("formDateAsDate", eFormData.getFormDate());
				curht.put("roleType", eFormData.getRoleType());
				curht.put("providerNo", eFormData.getProviderNo());
				results.add(curht);
			}
		} catch (Exception sqe) {
			logger.error("Error", sqe);
		}
		return (results);
	}

	public static ArrayList<HashMap<String, ? extends Object>> listPatientEFormsNoData(String demographic_no, String userRoles) {

		Boolean current = true;

		List<Map<String, Object>> allEformDatas = eFormDataDao.findByDemographicIdCurrentExcludingFormData(Integer.parseInt(demographic_no), current);

		ArrayList<HashMap<String, ? extends Object>> results = new ArrayList<HashMap<String, ? extends Object>>();
		try {
			for (Map<String, Object> eFormData : allEformDatas) {
				// filter eform by role type
				String tempRole = StringUtils.trimToNull((String) eFormData.get("roleType"));
				if (userRoles != null && tempRole != null) {
					// ojectName: "_admin,_admin.eform"
					// roleName: "doctor,admin"
					String objectName = "_eform." + tempRole;
					Vector v = OscarRoleObjectPrivilege.getPrivilegeProp(objectName);
					if (!OscarRoleObjectPrivilege.checkPrivilege(userRoles, (Properties) v.get(0), (Vector) v.get(1))) {
						continue;
					}
				}
				HashMap<String, Object> curht = new HashMap<String, Object>();
				curht.put("fdid", String.valueOf(eFormData.get("id")));
				curht.put("fid", String.valueOf(eFormData.get("formId")));
				curht.put("formName", eFormData.get("formName"));
				curht.put("formSubject", eFormData.get("subject"));
				curht.put("formDate", String.valueOf(eFormData.get("formDate")));
				curht.put("formTime", String.valueOf(eFormData.get("formTime")));
				curht.put("formDateAsDate", eFormData.get("formDate"));
				curht.put("roleType", eFormData.get("roleType"));
				curht.put("providerNo", eFormData.get("providerNo"));
				val isRemote = eFormData.get("remoteSystemId") != null;
				curht.put("isRemote", isRemote);
				curht.put("guid", eFormData.get("guid"));
				results.add(curht);
			}
		} catch (Exception sqe) {
			logger.error("Error", sqe);
		}
		return (results);
	}

	public static ArrayList<HashMap<String, ? extends Object>> loadEformsByFdis(List<Integer> ids) {

		List<EFormData> allEformDatas = eFormDataDao.findByFdids(ids);

		ArrayList<HashMap<String, ? extends Object>> results = new ArrayList<HashMap<String, ? extends Object>>();
		try {
			for (EFormData eFormData : allEformDatas) {
				HashMap<String, Object> curht = new HashMap<String, Object>();
				curht.put("fdid", eFormData.getId().toString());
				curht.put("fid", eFormData.getFormId().toString());
				curht.put("formName", eFormData.getFormName());
				curht.put("formSubject", eFormData.getSubject());
				curht.put("formDate", eFormData.getFormDate().toString());
				curht.put("formTime", eFormData.getFormTime().toString());
				curht.put("formDateAsDate", eFormData.getFormDate());
				curht.put("roleType", eFormData.getRoleType());
				curht.put("providerNo", eFormData.getProviderNo());
				results.add(curht);
			}
		} catch (Exception sqe) {
			logger.error("Error", sqe);
		}
		return (results);
	}

	public static HashMap<String, Object> loadEForm(String fid) {
		EFormDao dao = SpringUtils.getBean(EFormDao.class);
		Integer id = Integer.valueOf(fid);
		org.oscarehr.common.model.EForm eform = dao.find(id);
		HashMap<String, Object> curht = new HashMap<String, Object>();
		if (eform == null) {
			logger.error("Unable to find EForm with ID = " + fid);
			curht.put("formName", "");
			curht.put("formHtml", "No Such Form in Database");
			return curht;
		}

		// must have FID and form_name otherwise throws null pointer on the hashtable
		curht.put("fid", eform.getId());
		curht.put("formName", eform.getFormName());
		curht.put("formSubject", eform.getSubject());
		curht.put("formFileName", eform.getFileName());
		curht.put("formDate", eform.getFormDate().toString());
		curht.put("formTime", eform.getFormTime().toString());
		curht.put("formCreator", eform.getCreator());
		curht.put("formHtml", eform.getFormHtml());
		curht.put("showLatestFormOnly", eform.isShowLatestFormOnly());
		curht.put("patientIndependent", eform.isPatientIndependent());
		curht.put(EFormConstants.IS_ATTACHMENTS_ENABLED, eform.isAttachmentsEnabled());
		curht.put("roleType", eform.getRoleType());

		return (curht);
	}

	public static void updateEForm(EFormBase updatedForm) {
		// Updates the form - used by editForm
	
		EFormDao dao = SpringUtils.getBean(EFormDao.class);
		org.oscarehr.common.model.EForm eform = dao.find(Integer.parseInt(updatedForm.getFid()));
		if (eform == null) {
			logger.error("Unable to find eform for update: " + updatedForm);
			return;
		}
		
		eform.setFormName(updatedForm.getFormName());
		eform.setFileName(updatedForm.getFormFileName());
		eform.setSubject(updatedForm.getFormSubject());
		eform.setFormDate(ConversionUtils.fromDateString(updatedForm.getFormDate()));
		eform.setFormTime(ConversionUtils.fromTimeString(updatedForm.getFormTime()));
		eform.setFormHtml(updatedForm.getFormHtml());
		eform.setShowLatestFormOnly(updatedForm.isShowLatestFormOnly());
		eform.setPatientIndependent(updatedForm.isPatientIndependent());
		eform.setAttachmentsEnabled(updatedForm.isAttachmentsEnabled());
		eform.setRoleType(updatedForm.getRoleType());
		
		dao.merge(eform);
	}

	/*
	 * +--------------+--------------+------+-----+---------+----------------+ 
	 * | Field        | Type         | Null | Key | Default | Extra          | 
	 * +--------------+--------------+------+-----+---------+----------------+ 
	 * | fid          | int(8)       |      | PRI | NULL    | auto_increment | 
	 * | form_name    | varchar(255) | YES  |     | NULL    |                | 
	 * | file_name    | varchar(255) | YES  |     | NULL    |                | 
	 * | subject      | varchar(255) | YES  |     | NULL    |                | 
	 * | form_date    | date         | YES  |     | NULL    |                | 
	 * | form_time    | time         | YES  |     | NULL    |                | 
	 * | form_creator | varchar(255) | YES  |     | NULL    |                | 
	 * | status       | tinyint(1)   |      |     | 1       |                | 
	 * | form_html    | text         | YE S |     | NULL    |                | 
	 * +--------------+--------------+------+-----+---------+----------------+
	 */

	public static String getEFormParameter(String fid, String fieldName) {
		EFormDao dao = SpringUtils.getBean(EFormDao.class); 
		org.oscarehr.common.model.EForm eform = dao.find(ConversionUtils.fromIntString(fid));
		if (eform == null) {
			logger.error("Unable to find EForm for ID = " + fid);
			return "";
		}
		
		if (fieldName.equalsIgnoreCase("formName"))
			return eform.getFormName();
		else if (fieldName.equalsIgnoreCase("formSubject"))
			return eform.getSubject();
		else if (fieldName.equalsIgnoreCase("formFileName"))
			return eform.getFileName();
		else if (fieldName.equalsIgnoreCase("formDate"))
			return ConversionUtils.toDateString(eform.getFormDate());
		else if (fieldName.equalsIgnoreCase("formTime"))
			return ConversionUtils.toTimeString(eform.getFormTime());
		else if (fieldName.equalsIgnoreCase("formStatus"))
			return ConversionUtils.toBoolString(eform.isCurrent());
		else if (fieldName.equalsIgnoreCase("formHtml"))
			return eform.getFormHtml();
		else if (fieldName.equalsIgnoreCase("showLatestFormOnly"))
			return ConversionUtils.toBoolString(eform.isShowLatestFormOnly());
		else if (fieldName.equalsIgnoreCase("patientIndependent")) 
			return ConversionUtils.toBoolString(eform.isPatientIndependent());
		else if (fieldName.equalsIgnoreCase("roleType"))
			return eform.getRoleType();
		
		logger.warn("Invalid field name: " + fieldName + ". Please use one of formName, formSubject, formFileName, formDate, formTime, formStatus, formHtml, showLatestFormOnly, patientIndependent or roleType.");
		
		return null;
	}

	public static String getEFormIdByName(String name) {		
		EFormDao dao = SpringUtils.getBean(EFormDao.class);		
		logger.debug("EFORM NAME '" + name + "'");
		Integer maxId = dao.findMaxIdForActiveForm(name);
		
		return (maxId == null ? null : maxId.toString());
	}
	
	public static void delEForm(String fid) {
		setFormStatus(fid, false);
	}

	public static void restoreEForm(String fid) {
		setFormStatus(fid, true);
	}

	@Deprecated
	public static ArrayList<String> getValues(ArrayList<String> names, String sql) {
		// gets the values for each column name in the sql (used by DatabaseAP)
		if (EFormUtil.getSQL(sql) == null) {
			return null;
		}
		ResultSet rs = getSQL(sql);
		ArrayList<String> values = new ArrayList<String>();
		try {
			while (rs.next()) {
				values = new ArrayList<String>();
				for (int i = 0; i < names.size(); i++) {
					try {
						values.add(oscar.Misc.getString(rs, names.get(i)));
						logger.debug("VALUE ====" + rs.getObject(names.get(i)) + "|");
					} catch (Exception sqe) {
						values.add("<(" + names.get(i) + ")NotFound>");
						logger.error("Error", sqe);
					}
				}
			}
			rs.close();
		} catch (SQLException sqe) {
			logger.error("Error", sqe);
		}
		return (values);
	}
	
	public static JSONArray getJsonValues(ArrayList<String> names, String sql) {
		// gets the values for each column name in the sql (used by DatabaseAP)
		ResultSet rs = getSQL(sql);
		JSONArray values = new JSONArray();
		try {
			while (rs.next()) {
				JSONObject value = new JSONObject();
				for (int i = 0; i < names.size(); i++) {
					try {
						value.element(names.get(i), oscar.Misc.getString(rs, names.get(i)));
					} catch (Exception sqe) {
						value.element(names.get(i), "<(" + names.get(i) + ")NotFound>");
						logger.error("Error", sqe);
					}
				}
				values.add(value);
			}
			rs.close();
		} catch (SQLException sqe) {
			logger.error("Error", sqe);
		}
		return values;
	}

	// used by addEForm for escaping characters
	public static String charEscape(String S, char a) {
		if (null == S) {
			return S;
		}
		int N = S.length();
		StringBuilder sb = new StringBuilder(N);
		for (int i = 0; i < N; i++) {
			char c = S.charAt(i);
			// escape the escape characters
			if (c == '\\') {
				sb.append("\\\\");
			} else if (c == a) {
				sb.append("\\" + a);
			} else {
				sb.append(c);
			}
		}
		return sb.toString();
	}

	public static void addEFormValues(ArrayList<String> names, ArrayList<String> values, Integer fdid, Integer fid, Integer demographic_no) {
		// adds parsed values and names to DB
		// names.size and values.size must equal!
		try {
			if (names.size() != values.size()) { throw new IllegalArgumentException("names and values Lists must be the same size!"); }
			List<AbstractModel<?>> valuesList = new ArrayList<>();
			for (int i = 0; i < names.size(); i++) {
				EFormValue eFormValue = new EFormValue();
				eFormValue.setFormId(fid);
				eFormValue.setFormDataId(fdid);
				eFormValue.setDemographicId(demographic_no);
				eFormValue.setVarName(names.get(i));
				eFormValue.setVarValue(values.get(i));
				valuesList.add(eFormValue);
			}
            eFormValueDao.batchPersist(valuesList, 50);
		} catch (PersistenceException ee) {
			logger.error("Unexpected Error", ee);
		} catch (IllegalArgumentException e) {
            logger.error("Unexpected Error", e); 
		}
	}

	public static boolean formExistsInDB(String eFormName) {
		EFormDao dao = SpringUtils.getBean(EFormDao.class);
		org.oscarehr.common.model.EForm eform = dao.findByName(eFormName);
		return eform != null;
	}

	public static int formExistsInDBn(String formName, String fid) {
		EFormDao dao = SpringUtils.getBean(EFormDao.class);
		Long result = dao.countFormsOtherThanSpecified(formName, ConversionUtils.fromIntString(fid));
		return result.intValue();
	}

	// --------------eform groups---------
	public static ArrayList<HashMap<String, String>> getEFormGroups() {
		String sql;
		sql = "SELECT DISTINCT eform_groups.group_name, count(*)-1 AS 'count' FROM eform_groups " 
				+ "LEFT JOIN eform ON eform.fid=eform_groups.fid WHERE eform.status=1 OR eform_groups.fid=0 " 
				+ "GROUP BY eform_groups.group_name;";
		ArrayList<HashMap<String, String>> al = new ArrayList<HashMap<String, String>>();
		try {
			ResultSet rs = getSQL(sql);
			while (rs.next()) {
				HashMap<String, String> curhash = new HashMap<String, String>();
				curhash.put("groupName", oscar.Misc.getString(rs, "group_name"));
				curhash.put("count", oscar.Misc.getString(rs, "count"));
				al.add(curhash);
			}
		} catch (SQLException sqe) {
			logger.error("Error", sqe);
		}
		return al;
	}

	public static ArrayList<HashMap<String, String>> getEFormGroups(String demographic_no) {
		String sql;
		sql = "SELECT eform_groups.group_name, count(*)-1 AS 'count' FROM eform_groups " 
				+ "LEFT JOIN eform_data ON eform_data.fid=eform_groups.fid " 
				+ "WHERE (eform_data.status=1 AND eform_data.demographic_no=" + demographic_no 
				+ ") OR eform_groups.fid=0 " + "GROUP BY eform_groups.group_name";
		ArrayList<HashMap<String, String>> al = new ArrayList<HashMap<String, String>>();
		try {
			ResultSet rs = getSQL(sql);
			while (rs.next()) {
				HashMap<String, String> curhash = new HashMap<String, String>();
				curhash.put("groupName", oscar.Misc.getString(rs, "group_name"));
				curhash.put("count", oscar.Misc.getString(rs, "count"));
				al.add(curhash);
			}
		} catch (SQLException sqe) {
			logger.error("Error", sqe);
		}
		return al;
	}

	public static void delEFormGroup(String name) {
		EFormGroupDao dao = SpringUtils.getBean(EFormGroupDao.class);
		dao.deleteByName(name);
	}

	public static void addEFormToGroup(String groupName, String fid) {
		try {

			String sql1 = "SELECT eform_groups.fid FROM eform_groups, eform WHERE eform_groups.fid=" + fid 
					+ " AND eform_groups.fid=eform.fid AND eform.status=1 AND eform_groups.group_name='" + groupName + "'";
			ResultSet rs = DBHandler.GetSQL(sql1);
			if (!rs.next()) {
				EFormGroup eg = new EFormGroup();
				eg.setFormId(Integer.parseInt(fid));
				eg.setGroupName(groupName);
				eFormGroupDao.persist(eg);	
			}
		} catch (SQLException sqe) {
			logger.error("Error", sqe);
		}
	}

	public static void remEFormFromGroup(String groupName, String fid) {
		EFormGroupDao dao = SpringUtils.getBean(EFormGroupDao.class);
		dao.deleteByNameAndFormId(groupName, ConversionUtils.fromIntString(fid));
	}

	public static ArrayList<HashMap<String, ? extends Object>> listEForms(String sortBy, String deleted, String group, String userRoles) {
		// sends back a list of forms that were uploaded (those that can be added to the patient)
		String sql = "";
		if (deleted.equals("deleted")) {
			sql = "SELECT * FROM eform, eform_groups where eform.status=0 AND eform.fid=eform_groups.fid AND eform_groups.group_name='" + group + "' ORDER BY " + sortBy;
		} else if (deleted.equals("current")) {
			sql = "SELECT * FROM eform, eform_groups where eform.status=1 AND eform.fid=eform_groups.fid AND eform_groups.group_name='" + group + "' ORDER BY " + sortBy;
		} else if (deleted.equals("all")) {
			sql = "SELECT * FROM eform AND eform.fid=eform_groups.fid AND eform_groups.group_name='" + group + "' ORDER BY " + sortBy;
		}
		ResultSet rs = getSQL(sql);
		ArrayList<HashMap<String, ? extends Object>> results = new ArrayList<HashMap<String, ? extends Object>>();
		try {
			while (rs.next()) {
				HashMap<String, String> curht = new HashMap<String, String>();
				curht.put("fid", rsGetString(rs, "fid"));
				curht.put("formName", rsGetString(rs, "form_name"));
				curht.put("formSubject", rsGetString(rs, "subject"));
				curht.put("formFileName", rsGetString(rs, "file_name"));
				curht.put("formDate", rsGetString(rs, "form_date"));
				curht.put("formTime", rsGetString(rs, "form_time"));
				curht.put("roleType", rsGetString(rs, "roleType"));
                                
                                // filter eform by role type
				if (curht.get("roleType") != null && !curht.get("roleType").equals("")) {
					// ojectName: "_admin,_admin.eform"
					// roleName: "doctor,admin"
					String objectName = "_eform." + curht.get("roleType");
					Vector v = OscarRoleObjectPrivilege.getPrivilegeProp(objectName);
					if (!OscarRoleObjectPrivilege.checkPrivilege(userRoles, (Properties) v.get(0), (Vector) v.get(1))) {
						continue;
					}
				}
				results.add(curht);
			}
			rs.close();
		} catch (Exception sqe) {
			logger.error("Error", sqe);
		}
		return (results);
	}

	public static ArrayList<HashMap<String, ? extends Object>> listEForms(String sortBy, String deleted, String group, String userRoles, String systemPreferenceKey) {
		SystemPreferences systemPreference = systemPreferencesDao.findPreferenceByName(systemPreferenceKey);
		ArrayList<HashMap<String, ? extends Object>> eFormListing = new ArrayList<HashMap<String, ? extends Object>>();
		
		if (systemPreference != null && StringUtils.trimToNull(systemPreference.getValue()) == null) {
			// system preference exists, no eForms selected
			return eFormListing;
		}
		
		String sql = "SELECT * FROM eform";
		boolean includeGroupFilter = StringUtils.isNotBlank(group) && !group.equals("default");
		
		if (includeGroupFilter){
			sql += ", eform_groups";
		}
		
		if (deleted.equals("deleted")) {
			sql += " WHERE eform.status=0  ";
		} else if (deleted.equals("current")) {
			sql += " WHERE eform.status=1 ";
		}
		
		if (systemPreference != null) {
			sql += (sql.contains("WHERE") ? " AND " : " WHERE ") + " eform.fid IN (" + systemPreference.getValue() + ") ";
		}
		
		if (includeGroupFilter) {
			sql +=  (sql.contains("WHERE") ? " AND " : " WHERE ") + " eform.fid=eform_groups.fid AND eform_groups.group_name='" + group + "' ";
		}
		
		sql += " ORDER BY " + sortBy;
		
		ResultSet rs = getSQL(sql);
		
		
		try {
			while (rs.next()) {
				HashMap<String, String> curht = new HashMap<String, String>();
				curht.put("fid", rsGetString(rs, "fid"));
				curht.put("formName", rsGetString(rs, "form_name"));
				curht.put("formSubject", rsGetString(rs, "subject"));
				curht.put("formFileName", rsGetString(rs, "file_name"));
				curht.put("formDate", rsGetString(rs, "form_date"));
				curht.put("formTime", rsGetString(rs, "form_time"));
				curht.put("roleType", rsGetString(rs, "roleType"));

				// filter eform by role type
				if (curht.get("roleType") != null && !curht.get("roleType").equals("")) {
					// ojectName: "_admin,_admin.eform"
					// roleName: "doctor,admin"
					String objectName = "_eform." + curht.get("roleType");
					Vector v = OscarRoleObjectPrivilege.getPrivilegeProp(objectName);
					if (!OscarRoleObjectPrivilege.checkPrivilege(userRoles, (Properties) v.get(0), (Vector) v.get(1))) {
						continue;
					}
				}
				eFormListing.add(curht);
			}
			rs.close();
		} catch (Exception sqe) {
			logger.error("Error", sqe);
		}
		return (eFormListing);
	}

	public static ArrayList<Map<String, ?>> listPatientEForms(LoggedInInfo loggedInInfo, String sortBy, String deleted, String demographic_no, String groupName, int offset, int numToReturn) {
		SecurityInfoManager secInfoManager = SpringUtils.getBean(SecurityInfoManager.class);
		List<String> privs = new ArrayList<String>();
		for(Secobjprivilege p: secInfoManager.getSecurityObjects(loggedInInfo)) {
			if(p.getObjectname_code().startsWith("_eform.")) {
				privs.add(p.getObjectname_code());
			}
		}
		
		Boolean current = true;
		if(deleted.equals("deleted")) {
			current=false;
		} else if(deleted.equals("all")) {
			current=null;
		}
		
		
		List<EFormData> results1 = eFormDataDao.findInGroups(current, Integer.valueOf(demographic_no), groupName, sortBy, offset, numToReturn, privs);
		ArrayList<Map<String, ? extends Object>> results = new ArrayList<>();
		
		for(EFormData x:results1) {
			HashMap<String, String> curht = new HashMap<String, String>();
			curht.put("fdid", String.valueOf(x.getId()));
			curht.put("fid", x.getId().toString());
			curht.put("formName",x.getFormName());
			curht.put("formSubject", x.getSubject());
			curht.put("formDate",DateFormatUtils.ISO_DATE_FORMAT.format(x.getFormDate()));
			curht.put("formTime",DateFormatUtils.ISO_TIME_NO_T_FORMAT.format(x.getFormTime()));
			curht.put("roleType", x.getRoleType());
			results.add(curht);
		}
		
		return results;
	}
	
	@Deprecated
	public static ArrayList<HashMap<String, ? extends Object>> listPatientEForms(String sortBy, String deleted, String demographic_no, String groupName, String userRoles) {
		// sends back a list of forms added to the patient
		String sql = "";
		if (deleted.equals("deleted")) {
			sql = "SELECT * FROM eform_data, eform_groups WHERE eform_data.status=0 AND eform_data.patient_independent=0 AND eform_data.demographic_no=" + demographic_no + " AND eform_data.fid=eform_groups.fid AND eform_groups.group_name='" + groupName + "' ORDER BY " + sortBy;
		} else if (deleted.equals("current")) {
			sql = "SELECT * FROM eform_data, eform_groups WHERE eform_data.status=1 AND eform_data.patient_independent=0 AND eform_data.demographic_no=" + demographic_no + " AND eform_data.fid=eform_groups.fid AND eform_groups.group_name='" + groupName + "' ORDER BY " + sortBy;
		} else if (deleted.equals("all")) {
			sql = "SELECT * FROM eform_data, eform_groups WHERE eform_data.patient_independent=0 AND eform_data.demographic_no=" + demographic_no + " AND eform_data.fid=eform_groups.fid AND eform_groups.group_name='" + groupName + "' ORDER BY " + sortBy;
		}
		ResultSet rs = getSQL(sql);
		ArrayList<HashMap<String, ? extends Object>> results = new ArrayList<HashMap<String, ? extends Object>>();
		try {
			while (rs.next()) {
				// filter eform by role type
				if (rsGetString(rs, "roleType") != null && !rsGetString(rs, "roleType").equals("") && !rsGetString(rs, "roleType").equals("null")) {
					// ojectName: "_admin,_admin.eform"
					// roleName: "doctor,admin"
					String objectName = "_eform." + rsGetString(rs, "roleType");
					Vector v = OscarRoleObjectPrivilege.getPrivilegeProp(objectName);
					if (!OscarRoleObjectPrivilege.checkPrivilege(userRoles, (Properties) v.get(0), (Vector) v.get(1))) {
						continue;
					}
				}
				HashMap<String, String> curht = new HashMap<String, String>();
				curht.put("fdid", oscar.Misc.getString(rs, "fdid"));
				curht.put("fid", rsGetString(rs, "fid"));
				curht.put("formName", rsGetString(rs, "form_name"));
				curht.put("formSubject", rsGetString(rs, "subject"));
				curht.put("formDate", rsGetString(rs, "form_date"));
				curht.put("formTime", rsGetString(rs, "form_time"));
				curht.put("roleType", rsGetString(rs, "roleType"));
				results.add(curht);
			}
			rs.close();
		} catch (Exception sqe) {
			logger.error("Error", sqe);
		}
		return (results);
	}

	public static void writeEformTemplate(LoggedInInfo loggedInInfo, ArrayList<String> paramNames, ArrayList<String> paramValues, EForm eForm, String fdid, String programNo, String context_path) {
		String text = eForm != null ? eForm.getTemplate() : null;
		if (StringUtils.isBlank(text)) return;

		text = putTemplateValues(paramNames, paramValues, text);
		text = putTemplateEformValues(eForm, fdid, context_path, text);
		String[] template_echart = { "EncounterNote", "SocialHistory", "FamilyHistory", "MedicalHistory", "OngoingConcerns", "RiskFactors", "Reminders", "OtherMeds" };
		String[] code = { "", "SocHistory", "FamHistory", "MedHistory", "Concerns", "RiskFactors", "Reminders", "OMeds" };
		ArrayList<String> templates = new ArrayList<String>();
		
		/* write to echart
		 * <EncounterNote {or another template_echart}>
		 * 		content to write to echart
		 * </EncounterNote>
		 */
		for (int i = 0; i < template_echart.length; i++) {
			templates = getWithin(template_echart[i], text);
			for (String template : templates) {
				if (StringUtils.isBlank(template)) continue;

				template = putTemplateEformHtml(eForm.getFormHtml(), template);
				saveCMNote(eForm, fdid, programNo, code[i], template);
			}
		}

		/* write to document
		 * <document {optional:belong=provider/patient}>
		 * 		<docdesc>{optional:documentDescription}</docdesc>
		 * 		<docowner>{optional:provider_no/demographic_no}</docowner>
		 * 		<content>
		 * 			content to write to document
		 * 		</content>
		 * </document>
		 */
		templates = getWhole("document", text);
		for (String template : templates) {
			if (StringUtils.isBlank(template)) continue;

			String belong = getAttribute("belong", getBeginTag("document", template));
			if (!"patient".equalsIgnoreCase(belong)) belong = "provider";
			else belong = "demographic";
			
			String docOwner = getContent("docowner", template, null);
			if (docOwner == null) {
				if (belong.equals("demographic")) docOwner = eForm.getDemographicNo();
				else docOwner = eForm.getProviderNo(); 
			}
			
			String docDesc = getContent("docdesc", template, eForm.getFormName());
			String docText = getContent("content", template, "");
			docText = putTemplateEformHtml(eForm.getFormHtml(), docText);

			if (NumberUtils.isDigits(docOwner)) {
				EDoc edoc = new EDoc(docDesc, "forms", "html", docText, docOwner, eForm.getProviderNo(), "", 'H', eForm.getFormDate().toString(), "", null, belong, docOwner);
				edoc.setContentType("text/html");
				edoc.setDocPublic("0");
				EDocUtil.addDocumentSQL(edoc);
			}
		}
		
		/* write to prevention
		 * <prevention>
		 * 		<type>{preventionType: must be identical to Oscar prevention types}</type>
		 * 		<provider>{optional:providerNo}</provider>
		 * 		<date>{optional:preventionDate}</date>
		 * 		<status>{optional:completed/refused/ineligible}</status>
		 * 		<name>{optional}</name>
		 * 		<dose>{optional}</dose>
		 * 		<manufacture>{optional}</manufacture>
		 * 		<route>{optional}</route>
		 * 		<lot>{optional}</lot>
		 * 		<location>{optional}</location>
		 * 		<comments>{optional}</comments>
		 * 		<reason>{optional}</reason>
		 * 		<result>{optional:pending/normal/abnormal/other}</result>
		 * </prevention>
		 */
		
		templates = getWithin("prevention", text);
		for (String template : templates) {
			if (StringUtils.isBlank(template)) continue;

			String preventionType = getEqualIgnoreCase(preventionManager.getPreventionTypeList(), getContent("type", template, null));
			if (preventionType == null) continue;
			
			String preventionProvider = getContent("provider", template, eForm.getProviderNo());
			String preventionDate = getContent("date", template, eForm.getFormDate());
			String preventionStatus = getContent("status", template, "completed"); //completed(0)/refused(1)/ineligible(2)
			
			Prevention prevention = new Prevention();
			prevention.setPreventionType(preventionType);
			prevention.setPreventionDate(UtilDateUtilities.StringToDate(preventionDate, "yyyy-MM-dd"));
			prevention.setProviderNo(preventionProvider);
			prevention.setDemographicId(Integer.valueOf(eForm.getDemographicNo()));
			prevention.setCreatorProviderNo(eForm.getProviderNo());
			if (preventionStatus.equalsIgnoreCase("refused")) prevention.setBooleanRefused(true);
			else if (preventionStatus.equalsIgnoreCase("ineligible")) prevention.setIneligible(true);
			
			HashMap<String, String> extHash = new HashMap<String, String>();
			String extData = null;
			if ((extData = getContent("name", template, null)) != null) extHash.put("name", extData);
			if ((extData = getContent("dose", template, null)) != null) extHash.put("dose", extData);
			if ((extData = getContent("manufacture", template, null)) != null) extHash.put("manufacture", extData);
			if ((extData = getContent("route", template, null)) != null) extHash.put("route", extData);
			if ((extData = getContent("lot", template, null)) != null) extHash.put("lot", extData);
			if ((extData = getContent("location", template, null)) != null) extHash.put("location", extData);
			if ((extData = getContent("comments", template, null)) != null) extHash.put("comments", extData);
			if ((extData = getContent("reason", template, null)) != null) extHash.put("reason", extData);
			if ((extData = getContent("result", template, null)) != null) {
				extData = extData.toLowerCase();
				if (extData.equals("pending") || extData.equals("normal") || extData.equals("abnormal") || extData.equals("other")) {
					extHash.put("result", extData);
				}
			}
			preventionManager.addPreventionWithExts(prevention, extHash);
		}

		/* write to message
		 * <message>
		 * 		<subject>{optional}</subject>
		 * 		<sendto>{list of providerNo to receive message, separated by comma}</sendto>
		 * 		<content>
		 * 			content of message
		 * 		</content>
		 * </message>
		 */
		templates = getWithin("message", text);
		for (String template : templates) {
			if (StringUtils.isBlank(template)) continue;

			String subject = getContent("subject", template, eForm.getFormName());
			String sentWho = getSentWho(template);
			String[] sentList = getSentList(template);
			String userNo = eForm.getProviderNo();
			String userName = providerDao.getProviderName(eForm.getProviderNo());
			String message = getContent("content", template, "");
			message = putTemplateEformHtml(eForm.getFormHtml(), message);

			MsgMessageData msg = new MsgMessageData();
			msg.sendMessage2(message, subject, userName, sentWho, userNo, msg.getProviderStructure(sentList), null, null, OscarMsgType.GENERAL_TYPE);
		}
		
		/* write to ticklers
		 * <tickler>
		 * 		<taskAssignedTo>{providerNo}</taskAssignedTo>
		 * 		<tickMsg>
		 * 			message of the tickler
		 * 		</tickMsg>
		 * </tickler>
		 */
		templates = getWithin("tickler", text);
		for (String template : templates) {
			if (StringUtils.isBlank(template)) continue;
			
			String taskAssignedTo = getContent("taskAssignedTo", template, null);
			if (taskAssignedTo==null) continue; //no assignee
			if (providerDao.getProvider(taskAssignedTo.trim())==null) continue; //assignee provider no not exists
			
			String message = getContent("tickMsg", template, "");
			Tickler tickler = new Tickler();
			tickler.setTaskAssignedTo(taskAssignedTo);
			tickler.setMessage(message);
			tickler.setDemographicNo(Integer.valueOf(eForm.getDemographicNo()));
			tickler.setCreator(eForm.getProviderNo());
			
			ProgramManager2 programManager = SpringUtils.getBean(ProgramManager2.class);
			ProgramProvider pp = programManager.getCurrentProgramInDomain(loggedInInfo,loggedInInfo.getLoggedInProviderNo());
			
			if(pp != null) {
				tickler.setProgramId(pp.getProgramId().intValue());
			}
			
			ticklerDao.persist(tickler);
		}
		
		/* write to consult request
		 * <consultRequest>
		 * 		<referredToService></referredToService>
		 * 		<referredToSpecialist></referredToSpecialist>
		 * 		<urgency></urgency>
		 * 		<referredBy></referredBy>
		 * 		<referralDate></referralDate>
		 * 		<reason>
		 * 			reason
		 * 		</reason>
		 * 		<clinicalInfo>
		 * 			clinicalInfo
		 * 		</clinicalInfo>
		 * 		<currentMeds>
		 * 			currentMeds
		 * 		</currentMeds>
		 * 		<allergies>
		 * 			allergies
		 * 		</allergies>
		 * 		<concurrentProblems>
		 * 			concurrentProblems
		 * 		</concurrentProblems>
		 *		<patientWillBook></patientWillBook>
		 *		<letterheadName></letterheadName>
		 *		<letterheadAddresss></letterheadAddresss>
		 *		<letterheadPhone></letterheadPhone>
		 *		<letterheadFax></letterheadFax>
		 *		<status></status>
		 *		<source></source>
		 * </consultRequest>
		 */
		templates = getWithin("consultRequest", text);
		for (String template : templates) {
			if (StringUtils.isBlank(template)) continue;
			
			String referredToService = getContent("referredToService", template, null);
			String referredToSpecialist = getContent("referredToSpecialist", template, null);
			String referredBy = getContent("referredBy", template, null);
			
			String urgency = getContent("urgency", template, null);
			String referralDate = getContent("referralDate", template, null);
			String reason = getContent("reason", template, "");
			String clinicalInfo = getContent("clinicalInfo", template, "");
			String currentMeds = getContent("currentMeds", template, "");
			String allergies = getContent("allergies", template, "");
			String concurrentProblems = getContent("concurrentProblems", template, "");
			
			String patientWillBook = getContent("patientWillBook", template, null);
			String letterheadName = getContent("letterheadName", template, null);
			String letterheadAddress = getContent("letterheadAddress", template, null);
			String letterheadPhone = getContent("letterheadPhone", template, null);
			String letterheadFax = getContent("letterheadFax", template, null);
			String status = getContent("urgency", template, null);
			String source = getContent("source",template,"");
			
			ConsultationRequest consult = new ConsultationRequest();
			
			ProfessionalSpecialist ps = professionalSpecialistDao.find(Integer.parseInt(referredToSpecialist));
			if(ps == null) continue;
			if(referredToService == null || referredBy == null) continue;
			
			consult.setServiceId(Integer.parseInt(referredToService));
			consult.setDemographicId(Integer.parseInt(eForm.getDemographicNo()));
			consult.setProviderNo(referredBy);
			consult.setFdid(Integer.parseInt(fdid));
			
			if(referralDate != null) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				try {
					consult.setReferralDate(sdf.parse(referralDate));
				}catch(ParseException e) {}
			} else {
				consult.setReferralDate(new Date());
			}
			
			consult.setReasonForReferral(reason);
			consult.setClinicalInfo(clinicalInfo);
			consult.setCurrentMeds(currentMeds);
			consult.setAllergies(allergies);
			consult.setConcurrentProblems(concurrentProblems);
			
			consult.setUrgency(urgency!=null?urgency:"2");
			consult.setStatus(status != null?status:"1");
			consult.setSendTo("-1");
			consult.setPatientWillBook(patientWillBook!=null?Boolean.valueOf(patientWillBook):false);
	
			consult.setSource(source);
			
			ClinicData clinic = new ClinicData();
			
			consult.setLetterheadName(letterheadName!=null?letterheadName:clinic.getClinicName());
			consult.setLetterheadAddress(letterheadAddress!=null?letterheadAddress:clinic.getClinicAddress() + " " + clinic.getClinicCity() + " " + clinic.getClinicProvince() + " " + clinic.getClinicPostal());
			consult.setLetterheadPhone(letterheadPhone!=null?letterheadPhone:clinic.getClinicPhone());
			consult.setLetterheadFax(letterheadFax!=null?letterheadFax:clinic.getClinicFax());
			
			
			consultationRequestDao.persist(consult);
			
			consult.setProfessionalSpecialist(ps);
			consultationRequestDao.merge(consult);
			
			
		}
		
	}

	public static int findIgnoreCase(String phrase, String text, int start) {
		if (StringUtils.isBlank(phrase) || StringUtils.isBlank(text)) return -1;

		text = text.toLowerCase();
		phrase = phrase.toLowerCase();
		return text.indexOf(phrase, start);
	}

	public static String removeQuotes(String s) {
		if (StringUtils.isBlank(s)) return s;

		s = s.trim();
		if (StringUtils.isBlank(s)) return s;

		if (s.charAt(0) == '"' && s.charAt(s.length() - 1) == '"') s = s.substring(1, s.length() - 1);
		if (StringUtils.isBlank(s)) return s;

		if (s.charAt(0) == '\'' && s.charAt(s.length() - 1) == '\'') s = s.substring(1, s.length() - 1);
		return s.trim();
	}

	public static String getAttribute(String key, String htmlTag) {
		return getAttribute(key, htmlTag, false);
	}

	public static String getAttribute(String key, String htmlTag, boolean startsWith) {
		if (StringUtils.isBlank(key) || StringUtils.isBlank(htmlTag)) return null;

		Matcher m = getAttributeMatcher(key, htmlTag, startsWith);
		if (m == null) return null;

		String value = m.group();
		int keysplit = m.group().indexOf("=");
		if (!startsWith && keysplit >= 0) value = m.group().substring(keysplit + 1, m.group().length());

		return value.trim();
	}

	public static int getAttributePos(String key, String htmlTag) {
		int pos = -1;
		if (StringUtils.isBlank(key) || StringUtils.isBlank(htmlTag)) return pos;

		Matcher m = getAttributeMatcher(key, htmlTag, false);
		if (m == null) return pos;

		return m.start();
	}

	public static ArrayList<String> listRichTextLetterTemplates() {
		String imagePath = OscarProperties.getInstance().getProperty("eform_image");
		MiscUtils.getLogger().debug("Img Path: " + imagePath);
		File dir = new File(imagePath);
		String[] files = DisplayImageAction.getRichTextLetterTemplates(dir);
		ArrayList<String> fileList;
		if (files != null) fileList = new ArrayList<String>(Arrays.asList(files));
		else fileList = new ArrayList<String>();

		return fileList;
	}
	
	public static ArrayList<HashMap<String, ? extends Object>> getFormsSameFidSamePatient(String fdid, String sortBy, String userRoles)
	{
		List<EFormData> allEformDatas =  eFormDataDao.getFormsSameFidSamePatient(Integer.valueOf(fdid));

		if (SUBJECT.equals(sortBy)) Collections.sort(allEformDatas, EFormData.FORM_SUBJECT_COMPARATOR);
		else Collections.sort(allEformDatas, EFormData.FORM_DATE_COMPARATOR);

		ArrayList<HashMap<String, ? extends Object>> results = new ArrayList<HashMap<String, ? extends Object>>();
		try {
			for (EFormData eFormData : allEformDatas) {
				// filter eform by role type
				String tempRole = StringUtils.trimToNull(eFormData.getRoleType());
				if (userRoles != null && tempRole != null) {
					// ojectName: "_admin,_admin.eform"
					// roleName: "doctor,admin"
					String objectName = "_eform." + tempRole;
					Vector v = OscarRoleObjectPrivilege.getPrivilegeProp(objectName);
					if (!OscarRoleObjectPrivilege.checkPrivilege(userRoles, (Properties) v.get(0), (Vector) v.get(1))) {
						continue;
					}
				}
				HashMap<String, Object> curht = new HashMap<String, Object>();
				curht.put("fdid", eFormData.getId().toString());
				curht.put("fid", eFormData.getFormId().toString());
				curht.put("formName", eFormData.getFormName());
				curht.put("formSubject", eFormData.getSubject());
				curht.put("formDate", eFormData.getFormDate().toString());
				curht.put("formTime", eFormData.getFormTime().toString());
				curht.put("formDateAsDate", eFormData.getFormDate());
				curht.put("roleType", eFormData.getRoleType());
				curht.put("providerNo", eFormData.getProviderNo());
				results.add(curht);
			}
		} catch (Exception sqe) {
			logger.error("Error", sqe);
		}
		return (results);
	}


	public static List<EFormData> listPatientEFormsShowLatestOnly(String demographicNo) {
		//return all current eforms belonging to patient
		//if eform is showLatestFormOnly, return only the latest one
		
		List<EFormData> list = new ArrayList<EFormData>();
		List<EFormData> currentEForms = eFormDataDao.findByDemographicIdCurrent(NumberUtils.toInt(demographicNo), true);
		if (currentEForms==null) return list;
		
		for (EFormData eform : currentEForms) {
			if (eform.isShowLatestFormOnly()) {
				if (EFormUtil.isLatestShowLatestFormOnlyPatientForm(eform.getId())) {
					list.add(eform);
				}
			} else {
				list.add(eform);
			}
		}
		return list;
	}
	
    public static boolean isLatestShowLatestFormOnlyPatientForm(Integer fdid)
    {
    	return eFormDataDao.isLatestShowLatestFormOnlyPatientForm(fdid);
    }

	
	/**
	 * Prints the RTL with the custom template that allows for headers and footers
	 *
	 * @param eformData The eform record that will be used to generate the print version of the RTL
	 * @param tempFile The temporary file that will be used to save the generated PDF
	 * @throws DocumentException Thrown when the pdf could not be generated
	 * @throws RuntimeException Thrown when data is missing for the rtl content, the provider, or the demographic
	 * @throws IOException Thrown when an error occurs creating the output stream for the pdf or when preparing the template
	 */
	public static void printRtlWithTemplate(EFormData eformData, File tempFile, boolean skipAddToChart, HttpServletRequest request) throws DocumentException, RuntimeException, IOException {
		// Gets the required DAOs and provider and demographic objects
		DemographicDao demographicDao = SpringUtils.getBean(DemographicDao.class);
		ProviderDao providerDao = SpringUtils.getBean(ProviderDao.class);
		EFormValueDao eFormValueDao = SpringUtils.getBean(EFormValueDao.class);
		SystemPreferencesDao systemPreferencesDao = SpringUtils.getBean(SystemPreferencesDao.class);
		Demographic demographic = demographicDao.getDemographic(eformData.getDemographicId().toString());

		// Gets the contents of the RTL
		EFormValue rtlContentData  = eFormValueDao.findByFormDataIdAndKey(eformData.getId(), "Letter");
		EFormValue providerNoData = eFormValueDao.findByFormDataIdAndKey(eformData.getId(), "providerNo");

		if (rtlContentData != null) {
			// Gets the values of the rtlContent and providerNo extensions
			String rtlContent = rtlContentData.getVarValue();
			// Gets the provider associated with the stored provider number
			Provider provider = null;
			if (providerNoData != null && providerNoData.getVarValue() != null) {
				String providerNo = providerNoData.getVarValue();
				provider = providerDao.getProvider(providerNo);
			} else {
				String errorMessage = "The providerNo property associated with eform " + eformData.getId() + " does not exist. The printed template will not be routed to a patient";
				logger.warn(errorMessage);
			}
			
			if (demographic != null) {
				// Creates a new output stream for writing the pdf to
				try (OutputStream os = new FileOutputStream(tempFile)) {
					// Prepares the RTL template
					String templateUri = prepareTemplate(eformData, rtlContent, provider, demographic);
					if (!templateUri.isEmpty()) {
						// Creates the renderer and creates the PDF using the populated template at the given uri
						ITextRenderer renderer = new ITextRenderer();
						renderer.setDocument(templateUri);
						renderer.layout();
						renderer.createPDF(os);
						if (!skipAddToChart) {
							
							SystemPreferences documentTypePreference = systemPreferencesDao.findPreferenceByName("rtl_template_document_type");
							String documentType = "";
							if (documentTypePreference != null && documentTypePreference.getValue() != null) {
								documentType = documentTypePreference.getValue();
							}
                            // Saves the RTL to the patient and adds a routes the pdf to the provider
                            EDocUtil.saveDocumentToPatient(LoggedInInfo.getLoggedInInfoFromSession(request), provider, demographic.getDemographicNo(), tempFile,  eformData.getAppointmentNo(), 
                                    documentType, null);
                        }
					}
				} catch (DocumentException e) {
					logger.error("Could not generate the rich text letter with the id " + eformData.getId(), e);
					throw e;
				}
			} else {
				String errorMessage = "The demographic associated with the id " + eformData.getDemographicId() + " for the eform id " + eformData.getId() + " could not be found";
				throw new RuntimeException(errorMessage);
			}
		} else {
			String errorMessage = "Eform " + eformData.getId() + " must have an associated 'Letter' value in the eform_values table";
			throw new RuntimeException(errorMessage);
		}
	}

	/**
	 * Prints an eform and saves it to the patient chart
	 * @param loggedInInfo The logged in info
	 * @param eformData The data for the eform that will be printed
	 * @param printUrl The generated URL that will print the eform (for WKHtmlToPdf)
	 */
	public static void printEformToEchart(LoggedInInfo loggedInInfo, EFormData eformData, String printUrl) {
		try {
			DemographicDao demographicDao = SpringUtils.getBean(DemographicDao.class);
			Demographic demographic = demographicDao.getDemographicById(eformData.getDemographicId());
			
			// Creates a file for the form to be printed to
			File printedEform = new File(OscarProperties.getInstance().getProperty("DOCUMENT_DIR") + "/" + FilenameUtils.normalize(eformData.getFormName()) + "." + eformData.getId() + ".pdf");
			// Prints the eform to the file
			WKHtmlToPdfUtils.convertToPdf(printUrl, printedEform);
			// Saves the eform to the patient's chart
			EDocUtil.saveDocumentToPatient(loggedInInfo, loggedInInfo.getLoggedInProvider(), demographic.getDemographicNo(), printedEform, -1, "", eformData.getFormName());
		} catch (IOException e) {
			logger.error("Eform " + eformData.getId() + " could not be printed", e);
		}
	}

	/**
	 * Creates a Patient Intake letter by formatting a provided template with information provided by the patient intake eform and determined by the values in the patient intake letter field table
	 * @param intakeFormData The eform data of the patient intake form
	 */
	public static void createPatientIntakeLetter(EFormData intakeFormData) {
		SystemPreferences systemPreference = systemPreferencesDao.findPreferenceByName("patient_intake_letter_template_url");
		if (systemPreference != null && StringUtils.isNotEmpty(systemPreference.getValue())) {
			// Gets the path for the patient intake letter template file
			File templateFile = new File(systemPreference.getValue());
			try {
				// Gets the contents of the template as a string so that special tags can be replaces with appropriate data
				String intakeLetterTemplate = IOUtils.toString(new FileInputStream(templateFile));

				Map<String, String> intakeValueMap = getEformValuesAsMap(intakeFormData.getId());
				Map<String, String> templateParameterMap = PatientIntakeUtil.createTemplateParameterMap(intakeValueMap);
				PatientIntakeUtil.populateAdditionaleLetterData(templateParameterMap, intakeFormData.getDemographicId());
				try {
					String formattedTemplate = TemplateUtil.performTemplateReplace(intakeLetterTemplate, templateParameterMap);
					PatientIntakeUtil.savePatientIntakeLetter(formattedTemplate, intakeFormData.getDemographicId(), intakeFormData.getProviderNo());
				} catch (IOException | TemplateException e) {
					logger.error("An error occurred when creating the patient intake letter", e);
				}
			} catch(FileNotFoundException e) {
				logger.error("Patient intake letter template does not exist in the given location: " + systemPreference.getValue(), e);
			} catch(IOException e) {
				logger.error("Could not convert the patient intake letter template to a String", e);
			}
		} else {
			logger.error("The SystemPreference for patient_intake_letter_template_url is not set");
		}
	}
	
	/**
	 * Prepares the template for RTL printing using FlyingSaucer's XML to PDF generation. 
	 * Sets various predetermined tags to their associated data
	 *
	 * @param eformData eformData for the eform that is being printed
	 * @param rtl Content of the RTL that was created
	 * @param provider Provider associated with the RTL
	 * @param demographic Demographic associated with the RTL
	 * @return Uri of the populated uri's temporary file
	 * @throws IOException Thrown when the populated template could not be saved as a file
	 */
	private static String prepareTemplate(EFormData eformData, String rtl, Provider provider, Demographic demographic) throws IOException {
		PropertyDao propertyDao = SpringUtils.getBean(PropertyDao.class);
        OscarAppointmentDao appointmentDao = SpringUtils.getBean(OscarAppointmentDao.class);
		String letterUri = "";

		// Gets the path for the template file
		String templateUrl = OscarProperties.getInstance().getProperty("rtl_template_url", "");
		File templateFile = new File(templateUrl);
		// Gets the contents of the template as a string so that special tags can be replaces with appropriate data
		String rtlTemplate = IOUtils.toString(new FileInputStream(templateFile));
		// Fixes any <br> elements to be XHTML valid
		rtl = rtl.replaceAll("<br>", "<br />");
		// Replaces the body tag with the saved RTL contents
		rtlTemplate = rtlTemplate.replaceAll("\\$\\{rtlBody}", Matcher.quoteReplacement(rtl));

		// Replaces field in the header with the appropriate data
		if (demographic != null) {
			rtlTemplate = rtlTemplate.replaceAll("\\$\\{formattedPatientName}", Matcher.quoteReplacement(demographic.getFormattedName()));
			rtlTemplate = rtlTemplate.replaceAll("\\$\\{formattedDob}", Matcher.quoteReplacement(demographic.getFormattedDob()));
		}
		if (provider != null) {
			rtlTemplate = rtlTemplate.replaceAll("\\$\\{fullProviderName}", Matcher.quoteReplacement(provider.getFullName()));
			rtlTemplate = rtlTemplate.replaceAll("\\$\\{credentials}", Matcher.quoteReplacement(provider.getCredentials()));
			rtlTemplate = rtlTemplate.replaceAll("\\$\\{specialty}", Matcher.quoteReplacement(provider.getSpecialty()));
		}
		
		// If the template has a placeholder for the appointment date, 
		if (rtlTemplate.contains("${appointment_date}")) {
		    // Gets the appointment related to the eform
            Appointment appointment = appointmentDao.find(eformData.getAppointmentNo());
            if (appointment != null && appointment.getAppointmentDate() != null) {
				// Replaces the appointment date placeholder in the RTL template
				rtlTemplate = rtlTemplate.replaceAll("\\$\\{appointment_date}", appointment.getAppointmentDate().toString());
			}
        }

		// Removes any remaining tags that haven't been populated
		rtlTemplate = rtlTemplate.replaceAll("\\$\\{.*?}", "");
		
		// Creates a temporary file for the populated template
		File populatedTemplate = TempFileManager.createTempFile("eform." + eformData.getId() + ".header", ".html");

		// Creates a writer to write the pdf to in UTF-8 encoding
		try (Writer tempFile = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(populatedTemplate), StandardCharsets.UTF_8))) {
			tempFile.write(rtlTemplate);
			letterUri = populatedTemplate.toURI().toURL().toString();
		}

		return letterUri;
	}
    
	
	@Deprecated
	private static ResultSet getSQL(String sql) {
		ResultSet rs = null;
		try {

			rs = DBHandler.GetSQL(sql);
		} catch (SQLException sqe) {
			logger.error("Error", sqe);
		}
		return (rs);
	}

	private static void setFormStatus(String fid, boolean status) {
		EFormDao dao = SpringUtils.getBean(EFormDao.class);
		org.oscarehr.common.model.EForm eform = dao.find(ConversionUtils.fromIntString(fid));
		if (eform == null) {
			logger.error("Unable to find EForm for " + fid);
			return;
		}
		eform.setCurrent(status);
		dao.merge(eform);
	}

	private static String rsGetString(ResultSet rs, String column) throws SQLException {
		// protects agianst null values;
		String thisStr = oscar.Misc.getString(rs, column);
		if (thisStr == null) return "";
		return thisStr;
	}

	private static Matcher getAttributeMatcher(String key, String htmlTag, boolean startsWith) {
		Matcher m_return = null;
		if (StringUtils.isBlank(key) || StringUtils.isBlank(htmlTag)) return m_return;

		Pattern p = Pattern.compile("\\b[^\\s'\"=>]+[ ]*=[ ]*\"[^\"]*\"|\\b[^\\s'\"=>]+[ ]*=[ ]*'[^']*'|\\b[^\\s'\"=>]+[ ]*=[ ]*[^ >]*|\\b[^\\s>]+", Pattern.CASE_INSENSITIVE);
		Matcher m = p.matcher(htmlTag);

		while (m.find()) {
			int keysplit = m.group().indexOf("=");
			if (keysplit < 0) keysplit = m.group().length();

			String keypart = m.group().substring(0, keysplit).trim().toLowerCase();
			key = key.trim().toLowerCase();
			if ((keypart.equals(key)) || (startsWith && keypart.startsWith(key))) {
				m_return = m;
				break;
			}
		}
		return m_return;
	}

	private static String putTemplateValues(ArrayList<String> paramNames, ArrayList<String> paramValues, String template) {
		if (StringUtils.isBlank(template)) return template;

		String tag = "$t{";
		String nwTemplate = "";
		int pointer = 0;
		ArrayList<Integer> fieldBeginList = getFieldIndices(tag, template);
		for (int fieldBegin : fieldBeginList) {
			nwTemplate += template.substring(pointer, fieldBegin);
			int fieldEnd = template.indexOf("}", fieldBegin);
			pointer = fieldEnd + 1;
			String field = template.substring(fieldBegin + tag.length(), fieldEnd);
			if (paramNames.contains(field)) {
				nwTemplate += paramValues.get(paramNames.indexOf(field));
			} else {
				nwTemplate += "";
				logger.debug("Cannot find input name {" + field + "} in eform");
			}
		}
		nwTemplate += template.substring(pointer, template.length());
		return nwTemplate;
	}

	private static String putTemplateEformValues(EForm eForm, String fdid, String path, String template) {
		if (eForm == null || StringUtils.isBlank(template)) return template;

		String[] efields = { "name", "subject", "patient", "provider", "link" };
		String[] eValues = { eForm.getFormName(), eForm.getFormSubject(), eForm.getDemographicNo(), eForm.getProviderNo(), "<a href='" + path + "/eform/efmshowform_data.jsp?fdid=" + fdid + "' target='_blank'>" + eForm.getFormName() + "</a>" };

		String tag = "$te{";
		String nwTemplate = "";
		int pointer = 0;
		ArrayList<Integer> fieldBeginList = getFieldIndices(tag, template);
		for (int fieldBegin : fieldBeginList) {
			nwTemplate += template.substring(pointer, fieldBegin);
			int fieldEnd = template.indexOf("}", fieldBegin);
			pointer = fieldEnd + 1;
			String field = template.substring(fieldBegin + tag.length(), fieldEnd);
			if (field.equalsIgnoreCase("eform.html")) {
				nwTemplate += "$te{eform.html}";
				continue;
			}
			boolean match = false;
			for (int i = 0; i < efields.length; i++) {
				if (field.equalsIgnoreCase("eform." + efields[i])) {
					nwTemplate += eValues[i];
					match = true;
					break;
				}
			}
			if (!match) {
				nwTemplate += "";
				logger.debug("Cannot find input name {" + field + "} in eform");
			}
		}
		nwTemplate += template.substring(pointer, template.length());
		return nwTemplate;
	}

	private static String putTemplateEformHtml(String html, String template) {
		if (StringUtils.isBlank(html) || StringUtils.isBlank(template)) return "";

		html = removeAction(html);
		String tag = "$te{eform.html}";
		String nwTemplate = "";
		int pointer = 0;
		ArrayList<Integer> fieldBeginList = getFieldIndices(tag, template);
		for (int fieldBegin : fieldBeginList) {
			nwTemplate += template.substring(pointer, fieldBegin);
			nwTemplate += html;
			pointer = fieldBegin + tag.length();
		}
		nwTemplate += template.substring(pointer, template.length());
		return nwTemplate;
	}

	private static String removeAction(String html) {
		if (StringUtils.isBlank(html)) return html;

		Pattern p = Pattern.compile("<form[^<>]*>", Pattern.CASE_INSENSITIVE);
		Matcher m = p.matcher(html);
		String nwHtml = "";
		int pointer = 0;
		while (m.find()) {
			nwHtml += html.substring(pointer, m.start());
			String formTag = m.group();
			pointer += m.start() + formTag.length();
			p = Pattern.compile("\\baction[ ]*=[ ]*\"[^>\"]*\"|\\b[a-zA-Z]+[ ]*=[ ]*'[^>']*'|\\b[a-zA-Z]+[ ]*=[^ >]*", Pattern.CASE_INSENSITIVE);
			m = p.matcher(formTag);
			nwHtml += m.replaceAll("");
		}
		nwHtml += html.substring(pointer, html.length());
		return nwHtml;
	}

	private static void saveCMNote(EForm eForm, String fdid, String programNo, String code, String note) {
		if (StringUtils.isBlank(note)) return;

		CaseManagementNote cNote = createCMNote(eForm.getDemographicNo(), eForm.getProviderNo(), programNo, note);
		if (!StringUtils.isBlank(code)) {
			Set<CaseManagementIssue> scmi = createCMIssue(eForm.getDemographicNo(), code);
			cNote.setIssues(scmi);
		}
		cmm.saveNoteSimple(cNote);
		CaseManagementNoteLink cmLink = new CaseManagementNoteLink(CaseManagementNoteLink.EFORMDATA, Long.valueOf(fdid), cNote.getId());
		cmDao.save(cmLink);
	}

	private static CaseManagementNote createCMNote(String demographicNo, String providerNo, String programNo, String note) {
		CaseManagementNote cmNote = new CaseManagementNote();
		cmNote.setUpdate_date(new Date());
		cmNote.setObservation_date(new Date());
		cmNote.setDemographic_no(demographicNo);
		cmNote.setProviderNo(providerNo);
		cmNote.setSigning_provider_no(providerNo);
		cmNote.setSigned(true);
		cmNote.setHistory("");

		SecRoleDao secRoleDao = (SecRoleDao) SpringUtils.getBean("secRoleDao");
		SecRole doctorRole = secRoleDao.findByName("doctor");
		cmNote.setReporter_caisi_role(doctorRole.getId().toString());

		cmNote.setReporter_program_team("0");
		cmNote.setProgram_no(programNo);
		cmNote.setUuid(UUID.randomUUID().toString());
		cmNote.setNote(note);

		return cmNote;
	}

	private static Set<CaseManagementIssue> createCMIssue(String demographicNo, String code) {
		Issue isu = cmm.getIssueInfoByCode(code);
		CaseManagementIssue cmIssu = cmm.getIssueById(demographicNo, isu.getId().toString());
		if (cmIssu == null) {
			cmIssu = new CaseManagementIssue();
			cmIssu.setDemographic_no(demographicNo);
			cmIssu.setIssue_id(isu.getId());
			cmIssu.setType(isu.getType());
			cmm.saveCaseIssue(cmIssu);
		}

		Set<CaseManagementIssue> sCmIssu = new HashSet<CaseManagementIssue>();
		sCmIssu.add(cmIssu);
		return sCmIssu;
	}

	private static ArrayList<Integer> getFieldIndices(String fieldtag, String s) {
		ArrayList<Integer> fieldIndexList = new ArrayList<Integer>();
		if (StringUtils.isBlank(fieldtag) || StringUtils.isBlank(s)) return fieldIndexList;

		fieldtag = fieldtag.toLowerCase();
		s = s.toLowerCase();

		int from = 0;
		int fieldAt = s.indexOf(fieldtag, from);
		while (fieldAt >= 0) {
			fieldIndexList.add(fieldAt);
			from = fieldAt + 1;
			fieldAt = s.indexOf(fieldtag, from);
		}
		return fieldIndexList;
	}

	private static String getContent(String tag, String template, String dflt) {
		ArrayList<String> contents = getWithin(tag, template);
		if (contents.isEmpty()) return dflt;

		String content = contents.get(0).trim();
		
		while (content.length()>0 && Character.isWhitespace(content.charAt(0))) {
			content = content.substring(1);
			content = content.trim();
		}
		while (content.length()>0 && Character.isWhitespace(content.charAt(content.length()-1))) {
			content = content.substring(0, content.length()-1);
			content = content.trim();
		}
		return content;
	}

	private static ArrayList<String> getWithin(String tag, String s) {
		ArrayList<String> within = new ArrayList<String>();
		if (StringUtils.isBlank(tag) || StringUtils.isBlank(s)) return within;

		ArrayList<String> w = getWhole(tag, s);
		for (String whole : w) {
			int begin = getBeginTag(tag, whole).length();
			int end = whole.length() - ("</" + tag + ">").length();
			within.add(whole.substring(begin, end));
		}
		return within;
	}

	private static ArrayList<String> getWhole(String tag, String s) {
		ArrayList<String> whole = new ArrayList<String>();
		if (StringUtils.isBlank(tag) || StringUtils.isBlank(s)) return whole;

		String sBegin = "<" + tag;
		String sEnd = "</" + tag + ">";
		int begin = -1;
		boolean search = true;
		while (search) {
			begin = findIgnoreCase(sBegin, s, begin + 1);
			if (begin == -1) {
				search = false;
				break;
			}
			int end = findIgnoreCase(sEnd, s, begin);

			// search for middle begin tags; if found, extend end tag to include complete begin-end within
			int mid_begin = findIgnoreCase(sBegin, s, begin + 1);
			int count_mbegin = 0;
			while (mid_begin >= 0 && mid_begin < end) {
				count_mbegin++;
				mid_begin = findIgnoreCase(sBegin, s, mid_begin + 1);
			}
			while (count_mbegin > 0) {
				end = findIgnoreCase(sEnd, s, end + 1);
				if (end >= 0) count_mbegin--;
				else count_mbegin = -1;
			}
			// end search

			if (begin >= 0 && end >= 0) {
				whole.add(s.substring(begin, end + sEnd.length()));
			}
		}
		return whole;
	}

	private static String getBeginTag(String tag, String template) {
		if (StringUtils.isBlank(tag) || StringUtils.isBlank(template)) return "";

		Pattern p = Pattern.compile("<" + tag + "[^<>]*>", Pattern.CASE_INSENSITIVE);
		Matcher m = p.matcher(template);
		return m.find() ? m.group() : "";
	}

	private static String[] getSentList(String msgtxt) {
		String sentListTxt = getContent("sendto", msgtxt, "");
		String[] sentList = sentListTxt.split(",");
		for (int i = 0; i < sentList.length; i++) {
			sentList[i] = sentList[i].trim();
		}
		return sentList;
	}

	private static String getSentWho(String msgtxt) {
		String[] sentList = getSentList(msgtxt);
		String sentWho = "";
		for (String sent : sentList) {
			sent = providerDao.getProviderName(sent);
			if( !StringUtils.isBlank(sent) ) {
				if (!StringUtils.isBlank(sentWho) ) {
					sentWho += ", " + sent;
				} else {
					sentWho += sent;
				}
			}
		}
		return sentWho;
	}

	private static void sortByProviderName(List<EFormData> allEformDatas) {
		if (allEformDatas == null || allEformDatas.isEmpty()) return;
		
		for (int i=allEformDatas.size()-1; i>0; i--) {
			
			boolean swapped = false;
			for (int j=0; j<i; j++) {
				String providerName = providerDao.getProviderNameLastFirst(allEformDatas.get(j).getProviderNo());
				String providerNamePlus = providerDao.getProviderNameLastFirst(allEformDatas.get(j+1).getProviderNo());
				
				if (providerName.compareToIgnoreCase(providerNamePlus)>0) {
					EFormData tmp = allEformDatas.get(j);
					allEformDatas.set(j, allEformDatas.get(j+1));
					allEformDatas.set(j+1, tmp);
					swapped = true;
				}
			}
			if (!swapped) break;
		}
	}
	
	private static String getEqualIgnoreCase(ArrayList<String> lst, String str) {
		if (lst==null || lst.isEmpty() || StringUtils.isBlank(str)) return null;
		
		for (String strLst : lst) {
			if (str.trim().equalsIgnoreCase(strLst.trim())) return strLst.trim();
		}
		return null;
	}
	private static Map<String, String> getEformValuesAsMap(Integer formDataId) {
		Map<String, String> eformValueMap = new HashMap<>();
		List<EFormValue> patientIntakeValues = eFormValueDao.findByFormDataId(formDataId);
		for(EFormValue value : patientIntakeValues) {
			eformValueMap.put(value.getVarName(), value.getVarValue());
		}

		return eformValueMap;
	}

	public static String replaceAllFields(
			String sql, String demographicNumber, String providerNumber, String appointmentNumber) {
		return EForm.replaceAllFields(sql, demographicNumber, providerNumber, appointmentNumber);
	}
}
