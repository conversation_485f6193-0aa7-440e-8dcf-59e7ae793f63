/*
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package oscar.oscarEncounter.util;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import oscar.util.StringUtils;

public class EChartDisplayUtil {

  private static final String NUMBER_TO_DISPLAY = "numToDisplay";

  /**
   * Retrieves the number of items to display on an eChart module from the request parameter.
   * Returns the integer value plus one if the parameter is valid.
   * This increment is mainly to display the dropdown with one more item than the user requested.
   *
   * @param request the HttpServletRequest containing the parameter
   * @return the number to display, or null if not specified or invalid
   */
  @Nullable
  public static Integer getNumberToDisplayWithIncrement(final HttpServletRequest request) {
    if (request == null) {
      return null;
    }

    String numberToDisplayAsString = request.getParameter(NUMBER_TO_DISPLAY);

    if (StringUtils.isNullOrEmpty(numberToDisplayAsString)
        || !StringUtils.isNumeric(numberToDisplayAsString)) {
      return null;
    }

    Integer numberToDisplay = StringUtils.parseInt(numberToDisplayAsString);

    if (numberToDisplay == null) {
      return null;
    }

    // Increment the result to display the potential dropdown arrow
    return numberToDisplay + 1;
  }

}
