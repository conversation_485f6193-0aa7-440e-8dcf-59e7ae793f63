/**
 * Copyright (c) 2008-2012 Indivica Inc.
 *
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "indivica.ca/gplv2"
 * and "gnu.org/licenses/gpl-2.0.html".
 */


package oscar.oscarEncounter.pageUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import lombok.val;
import lombok.var;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.log4j.Logger;
import org.apache.struts.util.MessageResources;
import org.oscarehr.common.dao.ReadLabDao;
import org.oscarehr.common.dao.UserPropertyDAO;
import org.oscarehr.common.model.UserProperty;
import org.oscarehr.hospitalReportManager.HRMReportParser;
import org.oscarehr.hospitalReportManager.dao.HRMDocumentDao;
import org.oscarehr.hospitalReportManager.dao.HRMDocumentSubClassDao;
import org.oscarehr.hospitalReportManager.dao.HRMDocumentToDemographicDao;
import org.oscarehr.hospitalReportManager.dao.HRMSubClassDao;
import org.oscarehr.hospitalReportManager.model.HRMDocument;
import org.oscarehr.hospitalReportManager.model.HRMDocumentSubClass;
import org.oscarehr.hospitalReportManager.model.HRMDocumentToDemographic;
import org.oscarehr.hospitalReportManager.model.HRMSubClass;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.MiscUtils;
import org.oscarehr.util.PropertyUtils;
import org.oscarehr.util.SpringUtils;
import oscar.OscarProperties;
import oscar.util.DateUtils;
import oscar.util.StringUtils;

public class EctDisplayHRMAction extends EctDisplayAction {

  private static final Logger logger = MiscUtils.getLogger();
  private static final String cmd = "HRM";
  private HRMDocumentToDemographicDao hrmDocumentToDemographicDao = (HRMDocumentToDemographicDao) SpringUtils.getBean("HRMDocumentToDemographicDao");
  private HRMDocumentDao hrmDocumentDao = (HRMDocumentDao) SpringUtils.getBean("HRMDocumentDao");
  private HRMDocumentSubClassDao hrmDocumentSubClassDao = (HRMDocumentSubClassDao) SpringUtils.getBean("HRMDocumentSubClassDao");
  private HRMSubClassDao hrmSubClassDao = (HRMSubClassDao) SpringUtils.getBean("HRMSubClassDao");
  private ReadLabDao readLabDao = SpringUtils.getBean(ReadLabDao.class);

  public boolean getInfo(EctSessionBean bean, HttpServletRequest request, NavBarDisplayDAO Dao, MessageResources messages) {
    LoggedInInfo loggedInInfo=LoggedInInfo.getLoggedInInfoFromSession(request);

    if(!securityInfoManager.hasPrivilege(loggedInInfo, "_hrm", "r", null)) {
      return true; //Prevention link won't show up on new CME screen.
    } else {
      try {
        String winName = "docs" + bean.demographicNo;
        String url = "popupPage(500,1115,'" + winName + "', '" + request.getContextPath() + "/hospitalReportManager/displayHRMDocList.jsp?demographic_no=" + bean.demographicNo + "')";
        Dao.setLeftURL(url);
        Dao.setLeftHeading(messages.getMessage(request.getLocale(), "oscarEncounter.Index.msgHRMDocuments"));

        Dao.setRightHeadingID(cmd); //no menu so set div id to unique id for this action

        StringBuilder javascript = new StringBuilder("<script type=\"text/javascript\">");
        String js = "";
        List<HRMDocumentToDemographic> hrmDocListDemographic = hrmDocumentToDemographicDao.findByDemographicNo(bean.demographicNo);
        String serviceDateStr = "";
        String key;
        String title;
        int hash;
        String BGCOLOUR = request.getParameter("hC");
        Date date;

        List<HRMDocument> allHrmDocsForDemo = new LinkedList<HRMDocument>();
        for (HRMDocumentToDemographic hrmDemoDocResult : hrmDocListDemographic) {
          List<HRMDocument> hrmDoc = hrmDocumentDao.findById(hrmDemoDocResult.getHrmDocumentId());
          allHrmDocsForDemo.addAll(hrmDoc);
        }

        // Add remote HRM documents
        val remoteHrms = hrmDocumentDao.findAllRemoteHrmDocumentsByDemographicId(
            Integer.parseInt(bean.demographicNo));
        allHrmDocsForDemo.addAll(remoteHrms);

        String user = (String) request.getSession().getAttribute("user");

        for (HRMDocument hrmDocument : allHrmDocsForDemo) {
          // filter duplicate reports
          val hrmReport = hrmDocument.isRemote()
              ? HRMReportParser.parseReportFromXmlString(hrmDocument.getFileData())
              : HRMReportParser.parseReport(loggedInInfo, hrmDocument.getReportFile());
          if (hrmReport == null) continue;
          hrmReport.setHrmDocumentId(hrmDocument.getId());

          List<HRMDocumentSubClass> hrmDocumentSubClassList = hrmDocumentSubClassDao.getSubClassesByDocumentId(hrmDocument.getId());
          String dispFilename = hrmDocument.getReportType();
          String dispSubClass ="";
          HRMSubClass subClass;

          if (hrmReport.getFirstReportClass().equalsIgnoreCase("Diagnostic Imaging Report") || hrmReport.getFirstReportClass().equalsIgnoreCase("Cardio Respiratory Report")) {
            //Get first sub class to display on eChart
            if (hrmDocumentSubClassList != null && hrmDocumentSubClassList.size()>0) {
              HRMDocumentSubClass firstSubClass = hrmDocumentSubClassList.get(0);
              subClass = hrmSubClassDao.findApplicableSubClassMapping(hrmReport.getFirstReportClass(), firstSubClass.getSubClass(), firstSubClass.getSubClassMnemonic(), hrmReport.getSendingFacilityId());
              dispSubClass = subClass!=null?subClass.getSubClassDescription():"";
            }

            if ((StringUtils.isNullOrEmpty(dispSubClass)) && hrmReport.getAccompanyingSubclassList().size()>0){
              // if sub class doesn't exist, display the accompanying subclass
              dispSubClass = hrmReport.getFirstAccompanyingSubClass();
            }
          } else {
            //Medical Records Report
            String[] reportSubClass = hrmReport.getFirstReportSubClass().split("\\^");
            dispSubClass = reportSubClass!=null&&reportSubClass.length>1?reportSubClass[1]:"";
          }

          title = StringUtils.maxLenString(getEchartTextDisplay(hrmDocument, dispSubClass),
              MAX_LEN_TITLE, CROP_LEN_TITLE, ELLIPSES);
          date = getHrmDocumentDate(hrmDocument);
          serviceDateStr = date != null
              ? DateUtils.formatDate(date, request.getLocale())
              : "Error";

          NavBarDisplayDAO.Item item = NavBarDisplayDAO.Item();
          item.setDate(date);
          hash = Math.abs(winName.hashCode());

          url = getHrmViewer(request.getContextPath(), hrmDocument, user, hash, bean.demographicNo);
          // Default the labRead marker to '*' to indicate "unread" by default
          var labRead = "*";
          // If the HRM is not remote and marked as "read" in the local database, remove the "unread" marker.
          // Remote HRMs are not checked against the local database and retain the default marker.
          if (hrmDocument.isRemote()
              || readLabDao.isRead(loggedInInfo.getLoggedInProviderNo(),
              "HRM", hrmDocument.getId())) {
            labRead = ""; // Indicate "read."
          }

          item.setLinkTitle(title + serviceDateStr);
          item.setTitle(labRead+title+labRead);
          key = StringUtils.maxLenString(dispFilename, MAX_LEN_KEY, CROP_LEN_KEY, ELLIPSES) + "(" + serviceDateStr + ")";
          key = StringEscapeUtils.escapeJavaScript(key);


          js = "itemColours['" + key + "'] = '" + BGCOLOUR + "'; autoCompleted['" + key + "'] = \"" + url + "\"; autoCompList.push('" + key + "');";
          javascript.append(js);
          url += "return false;";
          item.setURL(url);
          item.setRemote(hrmDocument.isRemote());
          Dao.addItem(item);

        }
        javascript.append("</script>");

        Dao.setJavaScript(javascript.toString());
        Dao.sortItems(NavBarDisplayDAO.DATESORT_ASC);
      } catch (Exception e) {
        logger.error("OSCARPRO-5468 - Error in EctDisplayHRMAction: ", e);
        return false;
      }
      return true;
    }
  }


  /**
   * Gets the URL to display the HRM document in the classic view.
   *
   * @param contextPath the context path to create url with
   * @param hash the hash code of the window name
   * @param hrmDocumentNo the HRM document number
   * @param extras the extra parameters for remote HRM documents (should start with '&')
   * @return the URL to display the HRM document in the classic view
   */
  protected static String getClassicDisplayUrl(
      final String contextPath,
      final int hash,
      final String hrmDocumentNo,
      final String extras
  ) {
    return "popupPage(700,800,'" + hash + "', '" + contextPath + "/hospitalReportManager/Display.do"
        + "?id=" + hrmDocumentNo
        + "&segmentID=" + hrmDocumentNo
        + "&duplicateLabIds=" // duplicateLabIds is always blank/empty from this page
        + extras + "');";
  }

  /**
   * Gets the URL to display the Pro Inbox for the HRM document.
   *
   * @param hrmDocumentNo the HRM document number
   * @param user the provider number
   * @return the URL to display the Pro Inbox for the HRM document
   */
  protected static String getProDisplayUrl(final String hrmDocumentNo, final String user) {
    return "popupPage(900, 1250, 'HRM " + hrmDocumentNo + "', '/"
        + OscarProperties.getKaiemrDeployedContext() + "/#/hrm/?providerNo=" + user + "&documentNo="
        + hrmDocumentNo + "&view=eChart', 10);";
  }

  /**
   * Determines if the Pro Inbox is enabled for the current user.
   *
   * @param currentUserNo the current user number
   * @return true if the Pro Inbox is enabled, false otherwise
   */
  protected static boolean isProInboxEnabled(final String currentUserNo) {
    val userPropertyDao = (UserPropertyDAO) SpringUtils.getBean(UserPropertyDAO.class);
    val proInboxEnabled = userPropertyDao.getProp(currentUserNo, UserProperty.PRO_INBOX_ENABLED);
    if (Objects.nonNull(proInboxEnabled)) {
      return PropertyUtils.isPropertyEnabledWithDefaultFalse(proInboxEnabled.getValue());
    }
    return false;
  }

  /**
   * Determines the text to display on the eChart for the HRM document. The text is determined by
   * the description, subclass, or report class of the HRM document (depending on which is
   * available).
   *
   * @param hrmDocument the HRM document
   * @return the text to display on the eChart
   */
  protected static String getEchartTextDisplay(final HRMDocument hrmDocument, final String subclass) {
    String display;
    if (!StringUtils.isNullOrEmpty(hrmDocument.getDescription())) {
      display = hrmDocument.getDescription(); //custom label
    } else if (!StringUtils.isNullOrEmpty(subclass)) {
      display = subclass; // subclass
    } else {
      display = hrmDocument.getReportType(); // report class
    }
    val reportStatus = hrmDocument.getReportStatus();
    if (reportStatus != null && reportStatus.equalsIgnoreCase("C")) {
      return "(Cancelled) " + display;
    }
    return display;
  }

  /**
   * Gets the date of the HRM document based on whether it is remote or not.
   * Uses report date if available, falls back to timeReceived.
   *
   * @param hrmDocument the HRM document
   * @return the date of the HRM document
   */
  protected static Date getHrmDocumentDate(final HRMDocument hrmDocument) {
    // Use report date if available, otherwise fall back to timeReceived
    Date dateToUse = hrmDocument.getReportDate() != null
        ? hrmDocument.getReportDate()
        : hrmDocument.getTimeReceived();

    if (dateToUse == null) {
      return null;
    }

    val remoteFormat = "EEE MMM dd HH:mm:ss zzz yyyy";
    val dbFormat = "yyyy-MM-dd";
    val formatter = hrmDocument.isRemote()
        ? new SimpleDateFormat(remoteFormat)
        : new SimpleDateFormat(dbFormat);
    val dateString = dateToUse.toString();
    try {
      return formatter.parse(dateString);
    } catch (ParseException e) {
      MiscUtils.getLogger().debug("EctDisplayHRMAction: Error creating date " + e.getMessage());
    }
    return null;
  }

  protected static String getHrmViewer(
      final String contextPath,
      final HRMDocument hrm,
      final String user,
      final int hash,
      final String demographicNo
  ) {
    val hrmDocumentNo = hrm.getId().toString();
    val isRemote = hrm.isRemote();
    // only use classic view for remote HRM documents
    if (isProInboxEnabled(user) && !isRemote) {
      return getProDisplayUrl(hrmDocumentNo, user);
    }
    // when remote also add the guid, remote and demographic_no to the url
    return getClassicDisplayUrl(
        contextPath, hash, hrmDocumentNo,
        isRemote ? "&guid=" + hrm.getGuid() + "&remote=true&demographic_no=" + demographicNo : "");
  }

  @Override
  public String getCmd() {
    return cmd;
  }

}
