/**
 * Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version. 
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for the
 * Department of Family Medicine
 * McMaster University
 * Hamilton
 * Ontario, Canada
 */


package oscar.oscarEncounter.pageUtil;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.ResourceBundle;
import javax.servlet.http.HttpServletRequest;
import lombok.val;
import org.apache.log4j.Logger;
import org.apache.struts.util.MessageResources;
import org.oscarehr.PMmodule.caisi_integrator.CaisiIntegratorManager;
import org.oscarehr.PMmodule.caisi_integrator.IntegratorFallBackManager;
import org.oscarehr.caisi_integrator.ws.CachedDemographicAllergy;
import org.oscarehr.common.dao.UserPropertyDAO;
import org.oscarehr.common.model.Allergy;
import org.oscarehr.common.model.UserProperty;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.MiscUtils;
import org.oscarehr.util.PropertyUtils;
import org.oscarehr.util.SpringUtils;
import oscar.OscarProperties;
import oscar.oscarRx.data.RxPatientData;
import oscar.oscarRx.data.RxPatientData.Patient;
import oscar.util.DateUtils;
import oscar.util.StringUtils;

/**
 * retrieves info to display Disease entries for demographic
 */
public class EctDisplayAllergyAction extends EctDisplayAction {
  private static final Logger LOGGER = MiscUtils.getLogger();
  private static final String CMD = "allergies";

	public boolean getInfo(EctSessionBean bean, HttpServletRequest request, NavBarDisplayDAO Dao, MessageResources messages) {

		LoggedInInfo loggedInInfo=LoggedInInfo.getLoggedInInfoFromSession(request);

		if (!securityInfoManager.hasPrivilege(loggedInInfo, "_allergy", "r", null)) {
			return true; // Allergies link won't show up on new CME screen.
		} else {
			try {
			// set lefthand module heading and link
				UserPropertyDAO userPropertyDao = SpringUtils.getBean(UserPropertyDAO.class);
				String curUser_no = request.getSession().getAttribute("user").toString();
				UserProperty property = userPropertyDao.getProp(curUser_no, UserProperty.PRO_PRESCRIPTION_ENABLED);
				boolean isProPrescriptionEnabled = false;
				if (Objects.nonNull(property)) {
					isProPrescriptionEnabled = PropertyUtils.isPropertyEnabledWithDefaultFalse(property.getValue());
				}
			String winName = "Allergy" + bean.demographicNo;
        val oscarContext = "/" + OscarProperties.getInstance().getProperty("oscar.deployed.context", "oscar");
        val kaiemrContext = OscarProperties.getInstance().getProperty("kaiemr.deployed.context", "kaiemr");
			String url = (isProPrescriptionEnabled && OscarProperties.getInstance().isPropertyActive("use_fdb")) ? "popupPage(755,1200,'" + winName + "','/" + kaiemrContext + "/app/components/rx/?providerNo=" + bean.providerNo + "&demographicNo=" + bean.demographicNo + "&openAllergies=true')" : "popupPage(755,1200,'" + winName + "','" + oscarContext + "/oscarRx/showAllergy.do?demographicNo=" + bean.demographicNo + "')";
      // set text for lefthand module title

        ResourceBundle messageResources = ResourceBundle.getBundle("oscarResources", Locale.getDefault());
        Dao.setLeftHeading(messageResources.getString("oscarEncounter.NavBar.Allergy"));
			Dao.setLeftURL(url);

			// set righthand link to same as left so we have visual consistency with other modules
			url += "; return false;";
			Dao.setRightURL(url);
			Dao.setRightHeadingID(CMD); // no menu so set div id to unique id for this action

			// grab all of the diseases associated with patient and add a list item for each
			Integer demographicId = Integer.parseInt(bean.demographicNo);
        String numToDisplay = request.getParameter("numToDisplay");
        int limitNumber = Integer.MAX_VALUE;
        Allergy[] allergies;
        Patient patient = RxPatientData.getPatient(loggedInInfo, demographicId);
        if (numToDisplay != null && !numToDisplay.isEmpty()) {
          limitNumber = Integer.parseInt(numToDisplay);
          allergies = patient.findActiveAllergiesWithLimit(limitNumber + 1); // +1 to allow down arrow appears
        } else {
          allergies = patient.getActiveAllergies();
        }

			// --- get local allergies ---
        for (int idx = 0; idx < allergies.length && isDaoItemListUnderLimit(Dao, limitNumber); ++idx) {
				Date date = allergies[idx].getEntryDate();
        NavBarDisplayDAO.Item item =
            makeItem(date, allergies[idx].getDescription(),
            allergies[idx].getSeverityOfReaction(), allergies[idx].isRemote());
				Dao.addItem(item);
			}

			// --- get integrator allergies ---
			if (loggedInInfo.getCurrentFacility().isIntegratorEnabled() && isDaoItemListUnderLimit(Dao, limitNumber)) {
				try {
					List<CachedDemographicAllergy> remoteAllergies  = null;
					try {
						if (!CaisiIntegratorManager.isIntegratorOffline(loggedInInfo.getSession())){
							remoteAllergies = CaisiIntegratorManager.getDemographicWs(loggedInInfo, loggedInInfo.getCurrentFacility()).getLinkedCachedDemographicAllergies(demographicId);
							MiscUtils.getLogger().debug("remoteAllergies retrieved "+remoteAllergies.size());
						}
					} catch (Exception e) {
						MiscUtils.getLogger().error("Unexpected error.", e);
						CaisiIntegratorManager.checkForConnectionError(loggedInInfo.getSession(),e);
					}
					
					if(CaisiIntegratorManager.isIntegratorOffline(loggedInInfo.getSession())){
						remoteAllergies = IntegratorFallBackManager.getRemoteAllergies(loggedInInfo,demographicId);	
						MiscUtils.getLogger().debug("fallBack Allergies retrieved "+remoteAllergies.size());
					}

          sortRemoteAllergies(remoteAllergies);
					
					for (CachedDemographicAllergy remoteAllergy : remoteAllergies)
					{
						Date date=null;
						if (remoteAllergy.getEntryDate()!=null) date=remoteAllergy.getEntryDate().getTime();
            NavBarDisplayDAO.Item item = makeItem(date, remoteAllergy.getDescription(),
                remoteAllergy.getSeverityCode(), false);
						Dao.addItem(item);
            if (!isDaoItemListUnderLimit(Dao, limitNumber)) {
              break; // stop if we have reached the limit
            }
					}
				} catch (Exception e) {
					LOGGER.error("error getting remote allergies", e);
				}
			}

			// --- sort all results ---
			Dao.sortItems(NavBarDisplayDAO.DATESORT_ASC);
		} catch (Exception e) {
			LOGGER.error("OSCARPRO-5489 -  Error in EctDisplayAllergyAction: ", e);
			return false;
		}
			return true;
		}
	}

  private boolean isDaoItemListUnderLimit(NavBarDisplayDAO Dao, int limitNumber) {
    return Dao.numItems() <= limitNumber;
  }

  private void sortRemoteAllergies(final List<CachedDemographicAllergy> remoteAllergies) {
    remoteAllergies.sort(new Comparator<CachedDemographicAllergy>() {
      @Override
      public int compare(CachedDemographicAllergy o1, CachedDemographicAllergy o2) {
        return o2.getEntryDate().compareTo(o1.getEntryDate());
      }
    });
  }

	private static NavBarDisplayDAO.Item makeItem(
			final Date entryDate,
			final String description,
			final String severity,
			final boolean isRemote
	) {
		val item = NavBarDisplayDAO.Item();

		item.setDate(entryDate);
		if (severity != null && severity.equals("3")) {
			item.setColour("red");
		} else if (severity != null && severity.equals("2")) {
			item.setColour("orange");
		}

		item.setTitle(StringUtils.maxLenString(description, MAX_LEN_TITLE, CROP_LEN_TITLE, ELLIPSES));
		item.setLinkTitle(description + " " + DateUtils.formatDate(entryDate, Locale.getDefault()));
		item.setURL("return false;");
		// Set the remote flag
		item.setRemote(isRemote);

		return(item);
	}

	public String getCmd() {
		return CMD;
	}
}
