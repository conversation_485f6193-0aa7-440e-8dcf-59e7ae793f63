/*
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package oscar.oscarLab.ca.all;

import lombok.Getter;
import org.oscarehr.util.LoggedInInfo;

/**
 * Immutable parameters for fetching lab results.
 */
@Getter
public class LabResultsParameters {

  private final LoggedInInfo loggedInInfo;
  private final String providerNumber;

  // The demographic number of the patient
  private final String originalDemographicNumber;
  private final String patientFirstName;
  private final String patientLastName;
  private final String patientHealthNumber;

  // The lab status to query for
  private final String labStatus;

  // Indicates if the results are for display in EChart. A different query may be used for
  // EChart display.
  private final boolean isForEChartDisplay;
  private final Integer numberToDisplay;

  public LabResultsParameters(final LoggedInInfo loggedInInfo, final String providerNumber,
      final String originalDemographicNumber, final String patientFirstName, final String patientLastName,
      final String patientHealthNumber, final String labStatus, final boolean isForEChartDisplay,
      final Integer numberToDisplay) {
    this.loggedInInfo = loggedInInfo;
    this.providerNumber = providerNumber;
    this.originalDemographicNumber = originalDemographicNumber;
    this.patientFirstName = patientFirstName;
    this.patientLastName = patientLastName;
    this.patientHealthNumber = patientHealthNumber;
    this.labStatus = labStatus;
    this.isForEChartDisplay = isForEChartDisplay;
    this.numberToDisplay = numberToDisplay;
  }

}
