package oscar.schedule;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
public class ScheduleSlot {
  /*
  Represents a timeslot in the schedule in a given day. Each time slot can possibly contain multiple
  appointments.
   */

  private int hourCursor; // Hour that slot starts on
  private StringBuffer hourmin;
  private int minuteCursor; //Minute that slot starts on
  private String timeDisplay;

  private List<ScheduleAppointment> appointments;

  public ScheduleSlot(int hourCursor, int minuteCursor, String timeDisplay, StringBuffer hourmin,
      List<ScheduleAppointment> appointments) {
    this.hourCursor = hourCursor;
    this.minuteCursor = minuteCursor;
    this.timeDisplay = timeDisplay;
    this.hourmin = hourmin;
    this.appointments = appointments;
  }

  public boolean startsOnTheHour() {
    return minuteCursor == 0;
  }

}
