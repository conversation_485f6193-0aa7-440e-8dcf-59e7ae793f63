<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd

                http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
                http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
                http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd"
	default-autowire="no">

	<!--
		load properties from additional property files; oscar.properties is
		already loaded
	-->
	<bean id="propertyConfigurer" class="org.oscarehr.common.OscarPropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:oscar_mcmaster.properties</value>
			</list>
		</property>
	</bean>

	<!-- setup JPA -->
	<import resource="classpath:spring_jpa.xml" />

	<!-- setup hibernate -->
	<import resource="classpath:spring_hibernate.xml" />

	<!-- setup managers -->
	<import resource="classpath:spring_managers.xml" />

	<!-- setup CXF -->
	<import resource="classpath:spring_ws.xml" />

	<bean id="hl7HandlerMSHMappingDao" class="org.oscarehr.common.dao.HL7HandlerMSHMappingDao" autowire="byName" />
	<!-- hibernated DAO -->
        <bean id="gstControlDao" class="org.oscarehr.billing.CA.dao.GstControlDao" autowire="byName" />
	<bean id="programDao" class="org.oscarehr.PMmodule.dao.ProgramDao" autowire="byName" />
	<bean id="programQueueDao" class="org.oscarehr.PMmodule.dao.ProgramQueueDao" autowire="byName" />
	<bean id="clientReferralDAO" class="org.oscarehr.PMmodule.dao.ClientReferralDAO" autowire="byName" />
	<bean id="agencyDao" class="org.oscarehr.PMmodule.dao.AgencyDao" autowire="byName" />
	<!-- bean id="admissionDao" class="org.oscarehr.common.dao.AdmissionDao" autowire="byName" /-->
	<bean id="providerDao" class="org.oscarehr.PMmodule.dao.ProviderDao" autowire="byName" />
	<bean id="streetHealthDao" class="org.oscarehr.PMmodule.dao.StreetHealthDao" autowire="byName" />
	<bean id="otherIdDao" class="org.oscarehr.common.dao.OtherIdDAO" autowire="byName" />
	<bean id="appsHealthGatewayConfigurationService"
		class="health.apps.gateway.service.GWConfigurationService"/>
	<bean id="stringEncryptionService"
		class="health.apps.gateway.service.StringEncryptionService" autowire="byName"/>
	<bean id="gatewayDao" class="health.apps.gateway.service.GatewayDaoImpl" autowire="byName"/>
	<bean id="eformService" class="oscar.eform.service.EformService" autowire="byName"/>
	<bean id="documentReferenceConverter" class="health.apps.gateway.converters.DocumentReferenceConverter" autowire="byName"/>
	<bean id="CaseManagementIssueDAO" class="org.oscarehr.casemgmt.dao.CaseManagementIssueDAO" autowire="byName" />
	<bean id="caseManagementIssueDAO" class="org.oscarehr.casemgmt.dao.CaseManagementIssueDAO" autowire="byName" />
	<bean id="CaseManagementNoteDAO" class="org.oscarehr.casemgmt.dao.CaseManagementNoteDAO" autowire="byName" />
	<bean id="caseManagementNoteDAO" class="org.oscarehr.casemgmt.dao.CaseManagementNoteDAO" autowire="byName" />
	<bean id="CaseManagementNoteExtDAO" class="org.oscarehr.casemgmt.dao.CaseManagementNoteExtDAO" autowire="byName" />
	<bean id="CaseManagementNoteLinkDAO" class="org.oscarehr.casemgmt.dao.CaseManagementNoteLinkDAO" autowire="byName" />
	<bean id="CaseManagementCPPDAO" class="org.oscarehr.casemgmt.dao.CaseManagementCPPDAO" autowire="byName" />
	<bean id="CaseManagementDxLinkDao" class="org.oscarehr.casemgmt.dao.CaseManagementDxLinkDao" autowire="byName" />
	<bean id="demoExpListDao" class="org.oscarehr.common.dao.DemographicToExportPCDS_Dao" autowire="byName" />
	<bean id="diseaseRegistryPCDSDao" class="org.oscarehr.common.dao.DiseaseRegistryPCDS_Dao" autowire="byName" />
	
	
	
	<bean id="AppointmentArchiveDao" class="org.oscarehr.common.dao.AppointmentArchiveDao" autowire="byName" />
	<bean id="DxDao" class="org.oscarehr.common.dao.DxDao" autowire="byName" />
	<bean id="CasemgmtNoteLockDao" class="org.oscarehr.common.dao.CasemgmtNoteLockDao" autowire="byName" />
	
	<bean id="AllergyDao" class="org.oscarehr.common.dao.AllergyMergedDemographicDao" autowire="byName" />
	<bean id="allergyDao" class="org.oscarehr.common.dao.AllergyMergedDemographicDao" autowire="byName" />
	
	<bean id="IssueDAO" class="org.oscarehr.casemgmt.dao.IssueDAO" autowire="byName" />
	<bean id="secroleDao" class="com.quatro.dao.security.SecroleDao" autowire="byName"/>
	<bean id="RoleProgramAccessDAO" class="org.oscarehr.casemgmt.dao.RoleProgramAccessDAO" autowire="byName" />
	<bean id="clientImageDAO" class="org.oscarehr.casemgmt.dao.ClientImageDAO" autowire="byName" />
	<bean id="programProviderDAO" class="org.oscarehr.PMmodule.dao.ProgramProviderDAO" autowire="byName" />
	<bean id="programFunctionalUserDAO" class="org.oscarehr.PMmodule.dao.ProgramFunctionalUserDAO" autowire="byName" />
	<bean id="programAccessDAO" class="org.oscarehr.PMmodule.dao.ProgramAccessDAO" autowire="byName" />
	<bean id="defaultRoleAccessDAO" class="org.oscarehr.PMmodule.dao.DefaultRoleAccessDAO" autowire="byName" />
	<bean id="programClientStatusDAO" class="org.oscarehr.PMmodule.dao.ProgramClientStatusDAO" autowire="byName" />
	<bean id="programTeamDAO" class="org.oscarehr.PMmodule.dao.ProgramTeamDAO" autowire="byName" />
	<bean id="bedProgramDao" class="org.caisi.dao.BedProgramDao" autowire="byName" />
	<bean id="demographicDao" class="org.oscarehr.common.dao.DemographicDao" autowire="byName" />
	<bean id="patientTypeDao" class="org.oscarehr.common.dao.PatientTypeDao" autowire="byName" />
	<bean id="demographicGenderDao" class="org.oscarehr.common.dao.DemographicGenderDao" autowire="byName" />
	<bean id="DemographicFirstNationDao" class="org.oscarehr.common.dao.DemographicFirstNationDao" autowire="byName" />
	<bean id="demographicGroupDao" class="org.oscarehr.common.dao.DemographicGroupDao" autowire="byName" />
	<bean id="demographicPronounDao" class="org.oscarehr.common.dao.DemographicPronounDao" autowire="byName" />
	<bean id="secUserRoleDao" class="org.oscarehr.PMmodule.dao.SecUserRoleDao" autowire="byName" />
	<bean id="programSignatureDao" class="org.oscarehr.PMmodule.dao.ProgramSignatureDao" autowire="byName" />
	<bean id="programClientRestrictionDAO" class="org.oscarehr.PMmodule.dao.ProgramClientRestrictionDAO" autowire="byName" />
	<bean id="UserPropertyDAO" class="org.oscarehr.common.dao.UserPropertyDAO" autowire="byName" />
	<bean id="clinicDAO" class="org.oscarehr.common.dao.ClinicDAO" autowire="byName" />
	<bean id="dsConfigDao" class="org.oscarehr.common.dao.DaySheetConfigurationDao" autowire="byName" />
	<bean id="dataExportDAO" class="org.oscarehr.common.dao.DataExportDao" autowire="byName" />
	<bean id="siteDao" class="org.oscarehr.common.dao.SiteDao" autowire="byName" />
	<bean id="BillingmasterDAO" class="oscar.oscarBilling.ca.bc.data.BillingmasterDAO" autowire="byName" />
	<bean id="BillingPreferencesDAO" class="oscar.oscarBilling.ca.bc.data.BillingPreferencesDAO" autowire="byName" />
	<bean id="PrivateBillTransactionDAO" class="oscar.oscarBilling.ca.bc.data.PrivateBillTransactionsDAO" autowire="byName" />
	<bean id="SupServiceCodeAssocDAO" class="oscar.oscarBilling.ca.bc.data.SupServiceCodeAssocDAO" autowire="byName" />
	<bean id="BillingreferralDAO" class="org.oscarehr.common.dao.BillingreferralDao" autowire="byName" />
	<bean id="DxresearchDAO" class="org.oscarehr.common.dao.DxresearchDAO" autowire="byName" />
	<bean id="Icd9DAO" class="org.oscarehr.common.dao.Icd9Dao" autowire="byName" />
	<bean id="IncomingLabRulesDao" class="org.oscarehr.common.dao.IncomingLabRulesDao" autowire="byName" />
	<bean id="defaultIssueDao" class="org.caisi.dao.DefaultIssueDao" autowire="byName" />
	
    <bean id="MacroDAO" class="org.oscarehr.eyeform.dao.MacroDao" autowire="byName" />
	<bean id="rourke2009Dao" class="oscar.form.dao.Rourke2009DAO" autowire="byName" />
	<bean id="ocanSubmissionLogDao" class="org.oscarehr.PMmodule.dao.OcanSubmissionLogDao" autowire="byName" />

	<bean id="lookupDao" class="com.quatro.dao.LookupDao" autowire="byName" />
	<bean id="documentDao" class="org.oscarehr.document.dao.DocumentMergeDemographicDAO" autowire="byName" />
	<bean id="userAccessDao" class="com.quatro.dao.security.UserAccessDao" autowire="byName" />
	<bean id="providerInboxRoutingDAO" class="org.oscarehr.common.dao.ProviderInboxRoutingDao" autowire="byName"/>
	<bean id="ProviderLabRoutingFavoritesDao" class="org.oscarehr.common.dao.ProviderLabRoutingFavoritesDao" autowire="byName"/>

        <bean id="queueDocumentLinkDAO" class="org.oscarehr.common.dao.QueueDocumentLinkDao" autowire="byName"/>
	<bean id="eformDocsDao" class="org.oscarehr.common.dao.EformDocsDao" autowire="byName" />
	<bean id="caseloadDao" class="org.oscarehr.common.dao.CaseloadDao" autowire="byName" />
	<bean id="inboxResultsDao" class="org.oscarehr.common.dao.InboxResultsDao" autowire="byName" />
    <bean id="inboxResultsRepository" class="org.oscarehr.common.dao.InboxResultsRepository" autowire="byName" />

	<bean id="FhirCheckSubscriptionRequestDao" class="org.oscarehr.common.dao.FhirCheckSubscriptionRequestDao" autowire="byName" />
	
	<!-- Dao classes that implement OscarSuperDao -->
	<bean id="providerSuperDao" class="oscar.dao.ProviderDao" autowire="byName" />
	
	<bean id="smtpConfigDao" class="org.oscarehr.common.dao.SMTPConfigDao" autowire="byName" />
	<bean id="emailLogDao" class="org.oscarehr.common.dao.EmailLogDao" autowire="byName" />
	<bean id="emailUtil" class="org.oscarehr.util.EmailUtil" autowire="byName" />
	
	<bean id="eChartDao" class="org.oscarehr.common.dao.EChartDao" autowire="byName" />
	<bean id="encounterWindowDao" class="org.oscarehr.common.dao.EncounterWindowDao" autowire="byName" />
	<bean id="oneIdSessionBean" class="org.oscarehr.integration.OneIdSessionDao" autowire="byName" />
	<bean id="oneIdViewletDao" class="org.oscarehr.integration.OneIdViewletDao" autowire="byName" />

	<!-- OSCAR Desicion Support -->
	<bean id="dsService" class="org.oscarehr.decisionSupport.service.DSServiceMyDrugref">
	</bean>
	<bean id="progressSheetDataSource" class="org.oscarehr.util.OscarTrackingBasicDataSource" destroy-method="close">
		<property name="driverClassName" value="${db_driver}" />
		<property name="url" value="${db_uri}${progress_sheet_db_name}" />
		<property name="defaultAutoCommit" value="true" />

		<property name="username" value="${db_username}" />
		<property name="password" value="${db_password}" />

		<property name="maxActive" value="${db_max_active}" />
		<property name="maxIdle" value="${db_max_idle}" />
		<property name="maxWait" value="${db_max_wait}" />

		<property name="testOnBorrow" value="${db_testOnBorrow}" />
		<property name="validationQuery" value="${db_validationQuery}" />

		<property name="maxOpenPreparedStatements" value="320" />
		<!-- bug in the connector 3.11 driver which prevents this from being used, we need to upgrade drivers before re-enabling -->
		<property name="poolPreparedStatements" value="false" />
		<!-- abandoned features are deprecated and have no effect, don't expect the below to do anything -->

		<property name="removeAbandoned" value="${db_remove_abandoned}" />
		<property name="removeAbandonedTimeout" value="${db_remove_abandoned_timeout}" />
		<property name="logAbandoned" value="${db_log_abandoned}" />

	</bean>
	<bean id="progressSheetHibernate4AnnotatedSessionFactory" class="org.oscarehr.util.SpringHibernateLocalSessionFactoryBean">
		<property name="dataSource" ref="progressSheetDataSource" />
		<property name="annotatedClasses">
			<list>
				<value>org.progressSheet.model.ProgressSheet</value>
				<value>org.progressSheet.model.FormFieldPK</value>
				<value>org.progressSheet.model.FormField</value>
				<value>org.progressSheet.model.PainDiagram</value>
				<value>org.progressSheet.model.BillingSheet</value>
				<value>org.progressSheet.model.BillingFormFieldPK</value>
				<value>org.progressSheet.model.BillingFormField</value>
				<value>org.progressSheet.model.DrugRecord</value>
			</list>
		</property>
		<property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.MySQLDialect</prop>
				<prop key="hibernate.show_sql">false</prop>
			</props>
		</property>
	</bean>
	<bean id="progressSheetService" class="org.progressSheet.dao.ProgressSheetService" autowire="byName" >
	</bean>
	<bean id="progressSheetDao" class="org.progressSheet.dao.ProgressSheetDao" autowire="byName" >
		<property name="sessionFactory" ref="progressSheetHibernate4AnnotatedSessionFactory" />
	</bean>
	<bean id="formFieldDao" class="org.progressSheet.dao.FormFieldDao" autowire="byName" >
		<property name="sessionFactory" ref="progressSheetHibernate4AnnotatedSessionFactory" />
	</bean>
	<bean id="painDiagramDao" class="org.progressSheet.dao.PainDiagramDao" autowire="byName" >
		<property name="sessionFactory" ref="progressSheetHibernate4AnnotatedSessionFactory" />
	</bean>
	<bean id="billingSheetDao" class="org.progressSheet.dao.BillingSheetDao" autowire="byName" >
		<property name="sessionFactory" ref="progressSheetHibernate4AnnotatedSessionFactory" />
	</bean>
	<bean id="billingFormFieldDao" class="org.progressSheet.dao.BillingFormFieldDao" autowire="byName" >
		<property name="sessionFactory" ref="progressSheetHibernate4AnnotatedSessionFactory" />
	</bean>
	<bean id="drugRecordDao" class="org.progressSheet.dao.DrugRecordDao" autowire="byName" >
		<property name="sessionFactory" ref="progressSheetHibernate4AnnotatedSessionFactory" />
	</bean>
	
	<!--Container for holding preventions -->
	<bean id="preventionMgr" class="org.oscarehr.provider.model.PreventionManager"></bean>
	<bean id="chartPreventionService" class="com.well.services.ChartPreventionService"
		autowire="byName"/>

	<!-- OcanDataProcessor is used to create the OCAN xml submission file based on intake data -->
	<bean id="ocanDataProcessor" class="oscar.ocan.service.OcanDataProcessor">
		<property name="serviceOrganizationNumber" value="1001" />
		<property name="submissionFileLocation" value="C:/TEMP/ocan_test" />
	</bean>

	<!-- OscarSuperManager manager provides business logic facade for jsp layer -->
	<bean id="oscarSuperManager" class="oscar.service.OscarSuperManager" autowire="byName" init-method="init"/>

	<bean id="lookupManager" class="com.quatro.service.LookupManager" autowire="byName">
		<property name="lookupDao" ref="lookupDao" />
	</bean>
	<bean id="userAccessManager" class="com.quatro.service.security.UserAccessManager" autowire="byName">
		<property name="userAccessDao" ref="userAccessDao" />
	</bean>

	<!-- configure the available measurement flow sheets -->
	<bean id="measurementTemplateFlowSheet" class="oscar.oscarEncounter.oscarMeasurements.MeasurementTemplateFlowSheetConfig">
		<property name="flowSheets">
			<list>
				<!--value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/diabetesFlowsheet.xml</value-->
				<!--value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/hypertensionFlowsheet.xml</value-->
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/hivFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/inrFlowsheet.xml</value>
				<!--value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/omdChf.xml</value-->
				<!--value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/inrFlowsheet2.xml</value-->


				<!--<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/physicalFunctionFlowsheet.xml</value -->
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/omdAsthmaFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/omdCOPDFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/omdChf.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/omdHypertensionFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/omdDiabetesFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/diabetesQueensFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/ckdFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/painAssistant.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/periodicHealthVisit.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/healthTracker.xml</value>
				
			</list>
		</property>
	</bean>

	<!-- managers  -->
	<bean id="agencyManager" class="org.oscarehr.PMmodule.service.AgencyManager" autowire="byName" />
	<bean id="facilityMessageManager" class="org.caisi.service.FacilityMessageManager">
	</bean>

	<!-- Manager classes -->
	<bean id="programQueueManager" class="org.oscarehr.PMmodule.service.ProgramQueueManager" autowire="byName" />
	<bean id="admissionManager" class="org.oscarehr.PMmodule.service.AdmissionManager" autowire="byType" />
	
	<bean id="caseManagementManager" class="org.oscarehr.casemgmt.service.CaseManagementManager" autowire="byName">
		<property name="caseManagementNoteDAO" ref="CaseManagementNoteDAO" />
		<property name="caseManagementNoteExtDAO" ref="CaseManagementNoteExtDAO" />
		<property name="caseManagementNoteLinkDAO" ref="CaseManagementNoteLinkDAO" />
		<property name="caseManagementIssueDAO" ref="CaseManagementIssueDAO" />
		<property name="caseManagementCPPDAO" ref="CaseManagementCPPDAO" />
		<property name="issueDAO" ref="IssueDAO" />
		<property name="demographicDao" ref="demographicDao" />
		<property name="roleProgramAccessDAO" ref="RoleProgramAccessDAO" />
		
		<property name="userPropertyDAO" ref="UserPropertyDAO" />
		<property name="rolesManager" ref="rolesManager" />
		<property name="admissionManager" ref="admissionManager" />
		<property name="enabled" value="${casemgmt.note.password.enabled}" />
		<property name="programManager" ref="programManager" />
		<property name="dxresearchDAO" ref="dxresearchDAO" />
		<property name="programProviderDao" ref="programProviderDAO" />
		<property name="programAccessDAO" ref="programAccessDAO" />
		<property name="eChartDao" ref="eChartDao"/>
		<property name="encounterWindowDao" ref="encounterWindowDao"/>
		
		<property name="appointmentArchiveDao" ref="AppointmentArchiveDao" />
		<property name="dxDao" ref="DxDao" />
	</bean>

	<bean id="ClientImageManager" class="org.oscarehr.casemgmt.service.ClientImageManager" autowire="byName" />
	<bean id="programManager" class="org.oscarehr.PMmodule.service.ProgramManager" autowire="byName">
		<property name="enabled" value="${pmm.refer.temporaryAdmission.enabled}" />
	</bean>
	<bean id="clientManager" class="org.oscarehr.PMmodule.service.ClientManager" autowire="byName">
		<property name="demographicDao" ref="demographicDao" />
		<property name="clientReferralDAO" ref="clientReferralDAO" />
		<property name="programQueueManager" ref="programQueueManager" />
		<property name="admissionManager" ref="admissionManager" />
		<property name="clientRestrictionManager" ref="clientRestrictionManager" />
		<property name="outsideOfDomainEnabled" value="${pmm.client.search.outside.of.domain.enabled}" />
	</bean>
	<bean id="infirmBedProgramManager" class="org.caisi.service.InfirmBedProgramManager" autowire="byName">
		<property name="programProviderDAOT" ref="programProviderDAO" />
		<property name="demographicDao" ref="demographicDao" />
	</bean>

	
 	<bean id="demographicManagerT" class="org.caisi.service.DemographicManagerTickler">
		<property name="demographicDao" ref="demographicDao" />
	</bean>
	<bean id="preparedTicklerManagerT" class="org.caisi.tickler.prepared.PreparedTicklerManager" scope="singleton" />
	<bean id="providerManager" class="org.oscarehr.PMmodule.service.ProviderManager" autowire="byName" />
	<bean id="clientRestrictionManager" class="org.oscarehr.PMmodule.service.ClientRestrictionManager" autowire="byName" />
	
	<!-- Admin Start -->
	<bean id="secobjprivilegeDao" class="com.quatro.dao.security.SecobjprivilegeDao" autowire="byName" />
	<bean id="secProviderDao" class="com.quatro.dao.security.SecProviderDao" autowire="byName" />
	<bean id="secuserroleDao" class="com.quatro.dao.security.SecuserroleDao" autowire="byName" />
        <bean id="secObjectNameDao" class="com.quatro.dao.security.SecObjectNameDao" autowire="byName"/>

	<bean id="rolesManager" class="com.quatro.service.security.RolesManager" autowire="byName">
		<property name="secroleDao" ref="secroleDao" />
		<property name="secobjprivilegeDao" ref="secobjprivilegeDao" />
	</bean>

	<bean id="drilldownQueryHandler" class="org.oscarehr.dashboard.handler.DrilldownQueryHandler" autowire="byName" />
	<bean id="indicatorQueryHandler" class="org.oscarehr.dashboard.handler.IndicatorQueryHandler" autowire="byName" />
	<bean id="exportQueryHandler" class="org.oscarehr.dashboard.handler.ExportQueryHandler" autowire="byName" />
		
	<bean id="extPrintMeasurements" class="org.oscarehr.casemgmt.service.MeasurementPrint" autowire="byName"/>
	<bean id="extPrintOcularProcedures" class="org.oscarehr.eyeform.web.OcularProcPrint" autowire="byName"/>
	<bean id="extPrintSpecsHistory" class="org.oscarehr.eyeform.web.SpecsHistoryPrint" autowire="byName"/>

	<!-- Encounter Form DAO -->
	<bean id="Rourke2009DAO" class="oscar.form.dao.Rourke2009DAO" autowire="byName"/>
    <bean id="Rourke2017Dao" class="oscar.form.dao.Rourke2017Dao" autowire="byName"/>
    <bean id="Rourke2020Dao" class="oscar.form.dao.Rourke2020Dao" autowire="byName"/>
    <bean id="FormBooleanValueDao" class="oscar.form.dao.FormBooleanValueDao" autowire="byName"/>
    <bean id="FormStringValueDao" class="oscar.form.dao.FormStringValueDao" autowire="byName"/>
	<bean id="Perinatal2017Dao" class="oscar.form.dao.ONPerinatal2017Dao" autowire="byName"/>
	<bean id="Perinatal2017CommentDao" class="oscar.form.dao.ONPerinatal2017CommentDao" autowire="byName"/>
    <bean id="FormSmartEncounterDao" class="oscar.form.dao.FormSmartEncounterDao" autowire="byName"/>
    <bean id="SmartEncounterTemplateDao" class="oscar.form.dao.SmartEncounterTemplateDao" autowire="byName"/>
    <bean id="SmartEncounterTemplateImagesDao" class="oscar.form.dao.SmartEncounterTemplateImagesDao" autowire="byName"/>
    <bean id="SmartEncounterShortCodeDao" class="oscar.form.dao.SmartEncounterShortCodeDao" autowire="byName"/>
    <bean id="SmartEncounterProviderPreferenceDao" class="oscar.form.dao.SmartEncounterProviderPreferenceDao" autowire="byName"/>
    <bean id="SmartEncounterHeaderDao" class="oscar.form.dao.SmartEncounterHeaderDao" autowire="byName"/>
    <bean id="SmartEncounterFooterDao" class="oscar.form.dao.SmartEncounterFooterDao" autowire="byName"/>
	<bean id="FormBCAR2020Dao" class="oscar.form.dao.FormBCAR2020Dao" autowire="byName"/>
	<bean id="FormBCAR2020DataDao" class="oscar.form.dao.FormBCAR2020DataDao" autowire="byName"/>
	<bean id="FormBCAR2020TextDao" class="oscar.form.dao.FormBCAR2020TextDao" autowire="byName"/>
	<bean id="OscarOAuthDataProvider" class="oscar.login.OscarOAuthDataProvider" autowire="byName"/>
	<bean id="FormBCAR2012Dao" class="oscar.form.dao.FormBCAR2012Dao" autowire="byName"/>
	<bean id="FormONAR2017Dao" class="oscar.form.dao.FormONAR2017Dao" autowire="byName"/>

        <!--Email Support -->
        <context:component-scan base-package="oscar.service"/>

        <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor" p:corePoolSize="5"
          p:maxPoolSize="10" p:queueCapacity="100" p:waitForTasksToCompleteOnShutdown="true"/>

        <bean id="asyncMailSender" class="oscar.service.AsyncMailSender" autowire="byName">
           <property name="taskExecutor" ref="taskExecutor" />
        </bean>

        <bean id="mailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
            <property name="host" value="${email.host}"/>
            <property name="port" value="${email.port}" />
            <property name="protocol" value="${email.protocol}" />
            <property name="username" value="${email.username}" />
            <property name="password" value="${email.password}" />

            <property name="javaMailProperties">
               <props>
                  <prop key="mail.smtp.auth">true</prop>
                  <prop key="mail.smtp.starttls.enable">true</prop>
               </props>
            </property>
        </bean>
		
		<bean id="eventService" class="org.oscarehr.event.EventService"/> 
		<bean id="applicationEventMulticaster" class="org.springframework.context.event.SimpleApplicationEventMulticaster">  
              <property name="taskExecutor">
                  <bean class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor"/>
              </property>  
		</bean>
		
	<!-- Bean References -->
	<bean id="teleplanPwdJobBean"
		class="org.oscarehr.common.service.TeleplanPasswordRenewJob" />
	<bean id="teleplanRemitJobBean"
		class="org.oscarehr.common.service.TeleplanRemitDownloadJob" />

	<!-- Scheduler Job -->
	<bean id="teleplanPwdScheduledJob"
		class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
		<property name="targetObject" ref="teleplanPwdJobBean" />
		<property name="targetMethod" value="run" />
	</bean>
	<bean id="teleplanRemitScheduledJob"
		class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
		<property name="targetObject" ref="teleplanRemitJobBean" />
		<property name="targetMethod" value="run" />
	</bean>

	<!-- Recommend using http://www.cronmaker.com/ for generating quartz cron expressions -->
	<bean id="teleplanPwdCronTrigger"
		class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="teleplanPwdScheduledJob" />
		<property name="cronExpression" value="0 0 0 * * ? *" />
	</bean>
	<bean id="teleplanRemitCronTrigger"
		class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="teleplanRemitScheduledJob" />
		<property name="cronExpression" value="0 0/1 * 1/1 * ? *" />
	</bean>

	<!-- Scheduler Factory -->
	<bean
		class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
		<property name="jobDetails">
			<list>
				<ref bean="teleplanPwdScheduledJob" />
				<ref bean="teleplanRemitScheduledJob" />
			</list>
		</property>
		<property name="triggers">
			<list>
				<ref bean="teleplanPwdCronTrigger" />
				<ref bean="teleplanRemitCronTrigger" />
			</list>
		</property>
	</bean>

		<!-- Excelleris eOrder integration -->

		<bean id="orderService" class="org.oscarehr.integration.excelleris.eorder.OrderServiceImpl"/>	
		<bean id="orderGateway" class="org.oscarehr.integration.excelleris.eorder.OrderGatewayImpl"/>		
	
		<!-- YourCare Integration  -->
		<bean id="dataSyncService" class="org.oscarehr.integration.yourcare.DataSyncService"/>

		<bean id="excellerisEOrder" class="org.oscarehr.integration.excelleris.eorder.ExcellerisEOrderImpl"/>		
    
	<!-- For storing pdf during document split -->
		<bean id="pdDocumentBean" class="org.oscarehr.document.web.PDDocumentBean"/>		

		
		<!-- Dynacare eOrder integration -->
		
		<bean id="dynacareOrderService" class="org.oscarehr.integration.dynacare.eorder.DynacareOrderServiceImpl"/>	
		<bean id="dynacareOrderGateway" class="org.oscarehr.integration.dynacare.eorder.DynacareOrderGatewayImpl"/>
		<bean id="orderConverter" class="org.oscarehr.integration.dynacare.eorder.converter.OrderConverter"/>

		<task:scheduler id="taskScheduler" />

		<!-- SigningKeyResolverService bean -->
		<bean id="signingKeyResolverService" class="oscar.login.SigningKeyResolverService"/>

	<!-- OscarProConnectorService bean -->
	<bean id="oscarProConnectorService" class="ca.oscarpro.service.OscarProConnectorService"/>

	<!-- CookieUtils bean -->
	<bean id="cookieUtils" class="ca.oscarpro.utils.CookieUtils"/>

	<!-- OutboundFaxApiConnector bean -->
	<bean id="outboundFaxApiConnector" class="ca.oscarpro.fax.OutboundFaxApiConnector"/>

	<!-- PdfCoverPageCreator bean -->
	<bean id="pdfCoverPageCreator" class="org.oscarehr.fax.util.PdfCoverPageCreator"/>

	<bean id="logService" class="oscar.log.LogService"/>
</beans>
