<?xml version="1.0" encoding="UTF-8"?>
	<!--
		Copyright (c) 2001-2002. Centre for Research on Inner City Health, St.
		Michael's Hospital, Toronto. All Rights Reserved. This software is
		published under the GPL GNU General Public License. This program is
		free software; you can redistribute it and/or modify it under the
		terms of the GNU General Public License as published by the Free
		Software Foundation; either version 2 of the License, or (at your
		option) any later version. This program is distributed in the hope
		that it will be useful, but WITHOUT ANY WARRANTY; without even the
		implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
		PURPOSE. See the GNU General Public License for more details. You
		should have received a copy of the GNU General Public License along
		with this program; if not, write to the Free Software Foundation,
		Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA. OSCAR
		TEAM This software was written for Centre for Research on Inner City
		Health, St. Michael's Hospital, Toronto, Ontario, Canada
	-->
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="org.oscarehr.PMmodule.model">
	<class name="ProgramSignature" table="programSignature">
		<id column="id" name="id" type="integer" unsaved-value="0">
			<generator class="native" />
		</id>
		<property name="programId" type="integer" not-null="true"
			length="10" />
		<property name="programName" type="string" not-null="true"
			length="70" />
		<property name="providerId" type="string" not-null="true"
			length="6" />
		<property name="providerName" type="string" not-null="true"
			length="60" />
		<property name="caisiRoleName" type="string" not-null="false"
			length="255" />
		<property name="updateDate" type="timestamp" not-null="false"
			length="19" />
	</class>
</hibernate-mapping>
