<?xml version="1.0" encoding="UTF-8"?>
	<!--
		Copyright (c) 2001-2002. Centre for Research on Inner City Health, St.
		Michael's Hospital, Toronto. All Rights Reserved. This software is
		published under the GPL GNU General Public License. This program is
		free software; you can redistribute it and/or modify it under the
		terms of the GNU General Public License as published by the Free
		Software Foundation; either version 2 of the License, or (at your
		option) any later version. This program is distributed in the hope
		that it will be useful, but WITHOUT ANY WARRANTY; without even the
		implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
		PURPOSE. See the GNU General Public License for more details. You
		should have received a copy of the GNU General Public License along
		with this program; if not, write to the Free Software Foundation,
		Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA. OSCAR
		TEAM This software was written for Centre for Research on Inner City
		Health, St. Michael's Hospital, Toronto, Ontario, Canada
	-->
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd" >
<hibernate-mapping>
	<class name="org.oscarehr.casemgmt.model.CaseManagementNote"
		table="casemgmt_note">
		<id name="id" column="note_id" unsaved-value="0">
			<generator class="native" />
		</id>
		<property name="update_date" type="java.util.Date" />
		<property name="observation_date" type="java.util.Date" />
		<property name="demographic_no" />
		<property name="providerNo" column="provider_no" />
		<property name="note" />
		<property name="signed" />
		<property name="includeissue" column="include_issue_innote" />
		<property name="signing_provider_no" />
		<property name="encounter_type" />
		<property name="billing_code" />
		<property name="program_no" />
		<property name="reporter_caisi_role" />
		<property name="reporter_program_team" />
		<property name="history" />
		<property name="uuid" />
		<many-to-one name="provider" class="org.oscarehr.common.model.Provider"
			column="provider_no" update="false" not-found='ignore' insert="false"
			lazy="false" />
		<set name="issues" table="casemgmt_issue_notes" lazy="false">
			<key column="note_id" />
			<many-to-many column="id"
				class="org.oscarehr.casemgmt.model.CaseManagementIssue" />
		</set>
                <set name="extend" table="casemgmt_note_ext" lazy="false">
                        <key column="note_id" update="false" />
                        <one-to-many class="org.oscarehr.casemgmt.model.CaseManagementNoteExt"/>
                </set>
		<property name="roleName" type="string"
			formula="(select r.role_name from secRole r where r.role_no = reporter_caisi_role)" />
		<property name="programName" type="string"
			formula="(select p.name from program p where p.id = program_no)" />
		<property name="revision" type="string"
			formula="(select count(cmn.uuid) from casemgmt_note cmn where cmn.uuid = uuid)" />
		<property name="create_date" type="java.util.Date"
			formula="(select min(cmn.update_date) from casemgmt_note cmn where cmn.uuid = uuid)" />

		<property name="password" type="string" column="password" />
		<property name="locked" type="boolean" column="locked" />
                <property name="archived" type="boolean" column="archived" />
                <property name="position" type="integer" column="position" />

		<property name="appointmentNo" type="integer" column="appointmentNo"/>

		<property name="hourOfEncounterTime" type="integer" column="hourOfEncounterTime"/>
		<property name="minuteOfEncounterTime" type="integer" column="minuteOfEncounterTime"/>
		<property name="hourOfEncTransportationTime" type="integer" column="hourOfEncTransportationTime"/>
		<property name="minuteOfEncTransportationTime" type="integer" column="minuteOfEncTransportationTime"/>
		<property name="remoteSystemId" type="integer" column="remoteSystemId" />

	</class>

	<sql-query name="searchDemographicNotes">
		<return alias="cmn" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		select high_priority c1.note_id as {cmn.id}, c1.update_date as
		{cmn.update_date}, c1.observation_date as {cmn.observation_date},
		c1.demographic_no as {cmn.demographic_no}, c1.provider_no as
		{cmn.providerNo}, c1.note as {cmn.note},
		c1.signed as {cmn.signed}, c1.include_issue_innote as {cmn.includeissue},
		c1.signing_provider_no as {cmn.signing_provider_no},
		c1.encounter_type as {cmn.encounter_type}, c1.billing_code as {cmn.billing_code},
		c1.program_no as {cmn.program_no},
		c1.reporter_caisi_role as {cmn.reporter_caisi_role}, c1.reporter_program_team as
		{cmn.reporter_program_team}, c1.history as {cmn.history},
		c1.uuid as {cmn.uuid}, c1.password as {cmn.password}, c1.locked as {cmn.locked}, c1.archived as {cmn.archived},
		c1.position as {cmn.position}, c1.appointmentNo as {cmn.appointmentNo},
		c1.hourOfEncounterTime as {cmn.hourOfEncounterTime}, c1.minuteOfEncounterTime as {cmn.minuteOfEncounterTime},
		c1.hourOfEncTransportationTime as {cmn.hourOfEncTransportationTime},
		c1.minuteOfEncTransportationTime as {cmn.minuteOfEncTransportationTime},
		(select r.role_name from secRole r where r.role_no =
		c1.reporter_caisi_role) as {cmn.roleName},
		(select p.name from program p where p.id = c1.program_no) as {cmn.programName},
		(select count(casemgmt_note.uuid) from casemgmt_note where
		casemgmt_note.uuid = {cmn.uuid}) as {cmn.revision}, (select
		min(casemgmt_note.update_date)
		from casemgmt_note where casemgmt_note.uuid = {cmn.uuid}) as
		{cmn.create_date} from casemgmt_note c1
		left join casemgmt_note AS c2 ON (c1.uuid = c2.uuid AND c1.note_id &lt; c2.note_id)
		WHERE c1.demographic_no = ? and c1.note like ? and c2.note_id IS NULL
	</sql-query>
	<sql-query name="mostRecent">
		<return alias="cmn" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		select high_priority casemgmt_note.note_id as {cmn.id}, update_date as
		{cmn.update_date}, observation_date as {cmn.observation_date},
		demographic_no as {cmn.demographic_no}, provider_no as
		{cmn.providerNo}, note as {cmn.note},
		signed as {cmn.signed}, include_issue_innote as {cmn.includeissue},
		signing_provider_no as {cmn.signing_provider_no},
		encounter_type as {cmn.encounter_type}, billing_code as {cmn.billing_code},
		program_no as {cmn.program_no},
		reporter_caisi_role as {cmn.reporter_caisi_role}, reporter_program_team as
		{cmn.reporter_program_team}, history as {cmn.history},
		uuid as {cmn.uuid}, password as {cmn.password}, locked as {cmn.locked}, archived as {cmn.archived},
                position as {cmn.position}, appointmentNo as {cmn.appointmentNo},
        hourOfEncounterTime as {cmn.hourOfEncounterTime}, minuteOfEncounterTime as {cmn.minuteOfEncounterTime},
        hourOfEncTransportationTime as {cmn.hourOfEncTransportationTime},
        minuteOfEncTransportationTime as {cmn.minuteOfEncTransportationTime},
		(select r.role_name from secRole r where r.role_no =
		reporter_caisi_role) as {cmn.roleName},
		(select p.name from program p where p.id = program_no) as {cmn.programName},
		(select count(casemgmt_note.uuid) from casemgmt_note where
		casemgmt_note.uuid = {cmn.uuid}) as {cmn.revision}, (select
		min(casemgmt_note.update_date)
		from casemgmt_note where casemgmt_note.uuid = {cmn.uuid}) as
		{cmn.create_date} from casemgmt_note
		left join (select max(note_id) as note_id from casemgmt_note where
		demographic_no = ? group by uuid) recent
		using(note_id) where recent.note_id = casemgmt_note.note_id order by
		{cmn.observation_date} asc
	</sql-query>
	<sql-query name="mostRecentTime">
		<return alias="cmn" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		select high_priority casemgmt_note.note_id as {cmn.id}, update_date as
		{cmn.update_date}, observation_date as {cmn.observation_date},
		demographic_no as {cmn.demographic_no}, provider_no as
		{cmn.providerNo}, note as {cmn.note},
		signed as {cmn.signed}, include_issue_innote as {cmn.includeissue},
		signing_provider_no as {cmn.signing_provider_no},
		encounter_type as {cmn.encounter_type}, billing_code as {cmn.billing_code},
		program_no as {cmn.program_no},
		reporter_caisi_role as {cmn.reporter_caisi_role}, reporter_program_team as
		{cmn.reporter_program_team}, history as {cmn.history},
		uuid as {cmn.uuid}, password as {cmn.password}, locked as {cmn.locked}, archived as {cmn.archived},
                position as {cmn.position}, appointmentNo as {cmn.appointmentNo},
        hourOfEncounterTime as {cmn.hourOfEncounterTime}, minuteOfEncounterTime as {cmn.minuteOfEncounterTime},
        hourOfEncTransportationTime as {cmn.hourOfEncTransportationTime},
        minuteOfEncTransportationTime as {cmn.minuteOfEncTransportationTime},
		(select r.role_name from secRole r where r.role_no =
		reporter_caisi_role) as {cmn.roleName},
		(select p.name from program p where p.id = program_no) as {cmn.programName},
		(select count(casemgmt_note.uuid) from casemgmt_note where
		casemgmt_note.uuid = {cmn.uuid}) as {cmn.revision}, (select
		min(casemgmt_note.update_date)
		from casemgmt_note where casemgmt_note.uuid = {cmn.uuid}) as
		{cmn.create_date} from casemgmt_note
		left join (select max(note_id) as note_id from casemgmt_note where
		demographic_no = ? and observation_date &gt;= ? group by uuid) recent
		using(note_id) where recent.note_id = casemgmt_note.note_id order by
		{cmn.observation_date} asc
	</sql-query>
	<sql-query name="mostRecentLimit">
		<return alias="cmn" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		select high_priority casemgmt_note.note_id as {cmn.id}, update_date as
		{cmn.update_date}, observation_date as {cmn.observation_date},
		demographic_no as {cmn.demographic_no}, provider_no as
		{cmn.providerNo}, note as {cmn.note},
		signed as {cmn.signed}, include_issue_innote as {cmn.includeissue},
		signing_provider_no as {cmn.signing_provider_no},
		encounter_type as {cmn.encounter_type}, billing_code as {cmn.billing_code},
		program_no as {cmn.program_no},
		reporter_caisi_role as {cmn.reporter_caisi_role}, reporter_program_team as
		{cmn.reporter_program_team}, history as {cmn.history},
		uuid as {cmn.uuid}, password as {cmn.password}, locked as {cmn.locked}, archived as {cmn.archived},
                position as {cmn.position}, appointmentNo as {cmn.appointmentNo},
		hourOfEncounterTime as {cmn.hourOfEncounterTime}, minuteOfEncounterTime as {cmn.minuteOfEncounterTime},
        hourOfEncTransportationTime as {cmn.hourOfEncTransportationTime},
        minuteOfEncTransportationTime as {cmn.minuteOfEncTransportationTime},
		(select r.role_name from secRole r where r.role_no =
		reporter_caisi_role) as {cmn.roleName},
		(select p.name from program p where p.id = program_no) as {cmn.programName},
		(select count(cmn3.uuid) from casemgmt_note cmn3 where
		cmn3.uuid = {cmn.uuid}) as {cmn.revision}, (select
		min(cmn3.update_date)
		from casemgmt_note cmn3 where cmn3.uuid = {cmn.uuid}) as
		{cmn.create_date} from casemgmt_note
		left join (select max(note_id) as note_id from casemgmt_note where
		demographic_no = ? group by uuid) recent
		using(note_id) where recent.note_id = casemgmt_note.note_id order by
		{cmn.observation_date} desc limit ?, ?
	</sql-query>
	<sql-query name="appointmentNotes">
		<return alias="cmn" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		select high_priority casemgmt_note.note_id as {cmn.id}, update_date as
		{cmn.update_date}, observation_date as {cmn.observation_date},
		demographic_no as {cmn.demographic_no}, provider_no as
		{cmn.providerNo}, note as {cmn.note},
		signed as {cmn.signed}, include_issue_innote as {cmn.includeissue},
		signing_provider_no as {cmn.signing_provider_no},
		encounter_type as {cmn.encounter_type}, billing_code as {cmn.billing_code},
		program_no as {cmn.program_no},
		reporter_caisi_role as {cmn.reporter_caisi_role}, reporter_program_team as
		{cmn.reporter_program_team}, history as {cmn.history},
		uuid as {cmn.uuid}, password as {cmn.password}, locked as {cmn.locked}, archived as {cmn.archived},
                position as {cmn.position}, appointmentNo as {cmn.appointmentNo},
		hourOfEncounterTime as {cmn.hourOfEncounterTime}, minuteOfEncounterTime as {cmn.minuteOfEncounterTime},
        hourOfEncTransportationTime as {cmn.hourOfEncTransportationTime},
        minuteOfEncTransportationTime as {cmn.minuteOfEncTransportationTime},
		(select r.role_name from secRole r where r.role_no =
		reporter_caisi_role) as {cmn.roleName},
		(select p.name from program p where p.id = program_no) as {cmn.programName},
		(select count(cmn3.uuid) from casemgmt_note cmn3 where
		cmn3.uuid = {cmn.uuid}) as {cmn.revision}, (select
		min(cmn3.update_date)
		from casemgmt_note cmn3 where cmn3.uuid = {cmn.uuid}) as
		{cmn.create_date} from casemgmt_note
		left join (select max(note_id) as note_id from casemgmt_note where
		appointmentNo = ? group by uuid) recent
		using(note_id) where recent.note_id = casemgmt_note.note_id and casemgmt_note.appointmentNo = ? order by
		{cmn.observation_date} asc
	</sql-query>
	<sql-query name="issueNotes">
		<return alias="cmn" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		select high_priority distinct casemgmt_note.note_id as {cmn.id}, casemgmt_note.update_date as
		{cmn.update_date}, casemgmt_note.observation_date as {cmn.observation_date},
		casemgmt_note.demographic_no as {cmn.demographic_no}, casemgmt_note.provider_no as
		{cmn.providerNo}, casemgmt_note.note as {cmn.note},
		casemgmt_note.signed as {cmn.signed}, casemgmt_note.include_issue_innote as {cmn.includeissue},
		casemgmt_note.signing_provider_no as {cmn.signing_provider_no},
		casemgmt_note.encounter_type as {cmn.encounter_type}, casemgmt_note.billing_code as {cmn.billing_code},
		casemgmt_note.program_no as {cmn.program_no},
		casemgmt_note.reporter_caisi_role as {cmn.reporter_caisi_role}, casemgmt_note.reporter_program_team as
		{cmn.reporter_program_team}, casemgmt_note.history as {cmn.history},
		casemgmt_note.uuid as {cmn.uuid}, casemgmt_note.password as {cmn.password}, casemgmt_note.locked as {cmn.locked},
		casemgmt_note.archived as {cmn.archived}, casemgmt_note.position as {cmn.position},
		casemgmt_note.appointmentNo as {cmn.appointmentNo},
		casemgmt_note.hourOfEncounterTime as {cmn.hourOfEncounterTime}, casemgmt_note.minuteOfEncounterTime as {cmn.minuteOfEncounterTime},
        casemgmt_note.hourOfEncTransportationTime as {cmn.hourOfEncTransportationTime},
        casemgmt_note.minuteOfEncTransportationTime as {cmn.minuteOfEncTransportationTime},
		(select r.role_name from secRole r where r.role_no =
		reporter_caisi_role) as {cmn.roleName},
		(select p.name from program p where p.id = program_no) as {cmn.programName},
		(select count(cmn3.uuid) from casemgmt_note cmn3 where
		cmn3.uuid = {cmn.uuid}) as {cmn.revision}, (select
		min(cmn3.update_date)
		from casemgmt_note cmn3 where cmn3.uuid = {cmn.uuid}) as
		{cmn.create_date}
		from casemgmt_issue_notes as cmin
		left join casemgmt_note using (note_id)
		left join casemgmt_issue as cmi using (id)
		where cmi.issue_id in (?) and
		casemgmt_note.demographic_no = ? and casemgmt_note.note_id in
		(select max(cmn2.note_id)
                from casemgmt_issue_notes as cmin2
		left join casemgmt_note cmn2 USE INDEX (demographic_no) using (note_id)
		left join casemgmt_issue as cmi2 using (id)
		where cmn2.note_id = cmin2.note_id and cmin2.id = cmi2.id
		and cmi2.issue_id in (?) and cmn2.demographic_no = ? group by cmn2.uuid)
	</sql-query>
	<sql-query name="mostRecentOra">
		<return alias="cmn" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		select note_id as {cmn.id}, update_date as {cmn.update_date},
		observation_date as {cmn.observation_date}, demographic_no as
		{cmn.demographic_no}, provider_no as {cmn.providerNo}, note as
		{cmn.note},
		signed as {cmn.signed}, include_issue_innote as {cmn.includeissue},
		signing_provider_no as {cmn.signing_provider_no},
		encounter_type as {cmn.encounter_type}, billing_code as {cmn.billing_code},
		program_no as {cmn.program_no},
		reporter_caisi_role as {cmn.reporter_caisi_role}, reporter_program_team as
		{cmn.reporter_program_team}, history as {cmn.history},
		uuid as {cmn.uuid}, password as {cmn.password}, locked as {cmn.locked}, archived as {cmn.archived},
		appointmentNo as {cmn.appointmentNo},
		hourOfEncounterTime as {cmn.hourOfEncounterTime}, minuteOfEncounterTime as {cmn.minuteOfEncounterTime},
        hourOfEncTransportationTime as {cmn.hourOfEncTransportationTime},
        minuteOfEncTransportationTime as {cmn.minuteOfEncTransportationTime},
		(select r.role_name from secRole r where r.role_no =
		reporter_caisi_role) as {cmn.roleName},
		(select p.name from program p where p.id = program_no) as {cmn.programName},
		(select count(n1.uuid) from casemgmt_note n1 where casemgmt_note.uuid
		= n1.uuid) as {cmn.revision},
		(select min(n2.update_date) from casemgmt_note n2 where casemgmt_note.uuid =
		n2.uuid) as {cmn.create_date} from casemgmt_note
		inner join (select max(note_id) as note_id from casemgmt_note where
		demographic_no = ? group by uuid) recent
		using(note_id) order by {cmn.observation_date} asc
	</sql-query>
	<sql-query name="mostRecentTimeOra">
		<return alias="cmn" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		select note_id as {cmn.id}, update_date as {cmn.update_date},
		observation_date as {cmn.observation_date}, demographic_no as
		{cmn.demographic_no}, provider_no as {cmn.providerNo}, note as
		{cmn.note},
		signed as {cmn.signed}, include_issue_innote as {cmn.includeissue},
		signing_provider_no as {cmn.signing_provider_no},
		encounter_type as {cmn.encounter_type}, billing_code as {cmn.billing_code},
		program_no as {cmn.program_no},
		reporter_caisi_role as {cmn.reporter_caisi_role}, reporter_program_team as
		{cmn.reporter_program_team}, history as {cmn.history},
		uuid as {cmn.uuid}, password as {cmn.password}, locked as {cmn.locked}, archived as {cmn.archived},
		hourOfEncounterTime as {cmn.hourOfEncounterTime}, minuteOfEncounterTime as {cmn.minuteOfEncounterTime},
        hourOfEncTransportationTime as {cmn.hourOfEncTransportationTime},
        minuteOfEncTransportationTime as {cmn.minuteOfEncTransportationTime},
		(select r.role_name from secRole r where r.role_no =
		reporter_caisi_role) as {cmn.roleName},
		(select p.name from program p where p.id = program_no) as {cmn.programName},
		(select count(n1.uuid) from casemgmt_note n1 where casemgmt_note.uuid
		= n1.uuid) as {cmn.revision},
		(select min(n2.update_date) from casemgmt_note n2 where casemgmt_note.uuid =
		n2.uuid) as {cmn.create_date} from casemgmt_note
		inner join (select max(note_id) as note_id from casemgmt_note where
		demographic_no = ? and observation_date &gt;= ? group by uuid) recent
		using(note_id) order by {cmn.observation_date} asc
	</sql-query>

	<sql-query name="mostRecentDateRangeForDemographic">
		<return alias="c" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		SELECT DISTINCT c.note_id as {c.id}, c.update_date as {c.update_date}, c.observation_date as {c.observation_date}, c.demographic_no as {c.demographic_no},
		c.provider_no as {c.providerNo}, c.note as {c.note}, c.signed as {c.signed}, c.include_issue_innote as {c.includeissue}, c.signing_provider_no as {c.signing_provider_no},
		c.encounter_type as {c.encounter_type}, c.billing_code as {c.billing_code}, c.program_no as {c.program_no}, c.reporter_caisi_role as {c.reporter_caisi_role},
		c.reporter_program_team as {c.reporter_program_team}, c.history as {c.history}, c.uuid as {c.uuid}, c.password as {c.password}, c.locked as {c.locked}, c.archived as {c.archived},
		c.position as {c.position}, c.appointmentNo as {c.appointmentNo}, c.hourOfEncounterTime as {c.hourOfEncounterTime}, c.minuteOfEncounterTime as {c.minuteOfEncounterTime},
		c.hourOfEncTransportationTime as {c.hourOfEncTransportationTime}, c.minuteOfEncTransportationTime as {c.minuteOfEncTransportationTime},
		c.remoteSystemId as {c.remoteSystemId},
		r.role_name as {c.roleName}, p.name as {c.programName},
		(select count(c3.uuid) from casemgmt_note c3 where c3.uuid = c.uuid) as {c.revision},
		(select min(c4.update_date) from casemgmt_note c4 where c4.uuid = c.uuid) as {c.create_date}
		FROM casemgmt_note c
		LEFT JOIN casemgmt_note c2 ON (c.uuid = c2.uuid AND c.note_id &lt; c2.note_id)
		LEFT JOIN secRole r ON r.role_no = c.reporter_caisi_role
		LEFT JOIN program p ON p.id = c.program_no
		WHERE c.demographic_no = :demographicNo
		AND DATE(c.observation_date) &gt;= :startDate
		AND DATE(c.observation_date) &lt;= :endDate
		AND c.provider_no &gt; 0
		AND c2.note_id IS NULL
		ORDER BY {c.observation_date} DESC
	</sql-query>
	<!--
		generated sql select casemgmt_note.note_id as note1_82_0_, update_date
		as update2_82_0_, observation_date as observat3_82_0_, demographic_no
		as demograp4_82_0_, provider_no as provider5_82_0_, note as note82_0_,
		signed as signed82_0_, include_issue_innote as include8_82_0_,
		signing_provider_no as signing9_82_0_, encounter_type as
		encounter10_82_0_, billing_code as billing11_82_0_, program_no as
		program12_82_0_, reporter_caisi_role as reporter14_82_0_,
		reporter_program_team as reporter15_82_0_, history as history82_0_,
		uuid as uuid82_0_, password as password82_0_, locked as locked82_0_,
		(select r.name from caisi_role r where r.role_id =
		reporter_caisi_role) as formula16_0_, (select p.name from program p
		where p.id = program_no) as formula17_0_, (select
		count(casemgmt_note.uuid) from casemgmt_note where casemgmt_note.uuid
		= uuid82_0_) as formula18_0_, (select min(casemgmt_note.update_date)
		from casemgmt_note where casemgmt_note.uuid = uuid82_0_) as
		formula19_0_ from casemgmt_note left join (select max(note_id) as
		note_id from casemgmt_note where demographic_no = ? group by uuid)
		recent using(note_id) where recent.note_id = casemgmt_note.note_id
		order by observat3_82_0_ asc <sql-query name="mostRecent"> <return
		alias="cmn" class="org.oscarehr.casemgmt.model.CaseManagementNote" />
		select note_id as {cmn.id}, update_date as {cmn.update_date},
		observation_date as {cmn.observation_date}, demographic_no as
		{cmn.demographic_no}, provider_no as {cmn.providerNo}, note as
		{cmn.note}, signed as {cmn.signed}, include_issue_innote as
		{cmn.includeissue}, signing_provider_no as {cmn.signing_provider_no},
		encounter_type as {cmn.encounter_type}, billing_code as
		{cmn.billing_code}, program_no as {cmn.program_no},
		reporter_caisi_role as {cmn.reporter_caisi_role},
		reporter_program_team as {cmn.reporter_program_team}, history as
		{cmn.history}, uuid as {cmn.uuid}, password as {cmn.password}, locked
		as {cmn.locked}, (select r.role_name from secRole r where r.role_no =
		reporter_caisi_role) as {cmn.roleName}, (select p.name from program p
		where p.id = program_no) as {cmn.programName}, (select count(n1.uuid)
		from casemgmt_note n1 where casemgmt_note.uuid = n1.uuid) as
		{cmn.revision}, (select min(n2.update_date) from casemgmt_note n2
		where casemgmt_note.uuid = n2.uuid) as {cmn.create_date} from
		casemgmt_note inner join (select max(note_id) as note_id from
		casemgmt_note where demographic_no = ? group by uuid) recent
		using(note_id) order by {cmn.observation_date} asc </sql-query>
		<sql-query name="mostRecentTime"> <return alias="cmn"
		class="org.oscarehr.casemgmt.model.CaseManagementNote" /> select
		note_id as {cmn.id}, update_date as {cmn.update_date},
		observation_date as {cmn.observation_date}, demographic_no as
		{cmn.demographic_no}, provider_no as {cmn.providerNo}, note as
		{cmn.note}, signed as {cmn.signed}, include_issue_innote as
		{cmn.includeissue}, signing_provider_no as {cmn.signing_provider_no},
		encounter_type as {cmn.encounter_type}, billing_code as
		{cmn.billing_code}, program_no as {cmn.program_no},
		reporter_caisi_role as {cmn.reporter_caisi_role},
		reporter_program_team as {cmn.reporter_program_team}, history as
		{cmn.history}, uuid as {cmn.uuid}, password as {cmn.password}, locked
		as {cmn.locked}, (select r.role_name from secRole r where r.role_no =
		reporter_caisi_role) as {cmn.roleName}, (select p.name from program p
		where p.id = program_no) as {cmn.programName}, (select count(n1.uuid)
		from casemgmt_note n1 where casemgmt_note.uuid = n1.uuid) as
		{cmn.revision}, (select min(n2.update_date) from casemgmt_note n2
		where casemgmt_note.uuid = n2.uuid) as {cmn.create_date} from
		casemgmt_note inner join (select max(note_id) as note_id from
		casemgmt_note where demographic_no = ? and observation_date >= ? group
		by uuid) recent using(note_id) order by {cmn.observation_date} asc
		</sql-query>
	-->
</hibernate-mapping>
