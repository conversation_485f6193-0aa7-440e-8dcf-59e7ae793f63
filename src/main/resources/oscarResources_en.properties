#*
#* Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved. *
#* This software is published under the GPL GNU General Public License.
#* This program is free software; you can redistribute it and/or
#* modify it under the terms of the GNU General Public License
#* as published by the Free Software Foundation; either version 2
#* of the License, or (at your option) any later version. *
#* This program is distributed in the hope that it will be useful,
#* but WITHOUT ANY WARRANTY; without even the implied warranty of
#* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
#* GNU General Public License for more details. * * You should have received a copy of the GNU General Public License
#* along with this program; if not, write to the Free Software
#* Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA. *
#*
#* <OSCAR TEAM>
#*
#* This software was written for the
#* Department of Family Medicine
#* McMaster University
#* Hamilton,
#* Ontario, Canada
#*
message.custom={0}

index.btnSignIn=Sign in
index.OscarPro=OSCAR Pro
index.OSCARClassic=OSCAR Classic
index.formPIN=2nd Level Passcode
loginApplication.propertyFile=oscar_mcmaster
loginApplication.title=OSCAR McMaster
loginApplication.formLabel=Login
loginApplication.formFailedLabel=Login Incorrect
loginApplication.formUserName=User Name:
loginApplication.formPwd=Password:
loginApplication.formCmt=for external Wide Area Network access
loginApplication.leftRmk1=Most browsers will probably work with OSCAR but the development team has been primarily using the latest versions of Firefox and Internet Explorer. The user interface assumes that the resolution of the screen is at least 1024x768.<p></p>
loginApplication.leftRmk2=OSCAR McMaster is copyrighted and trade-marked by <a href="http://oscarmcmaster.org" target="_blank">McMaster University</a>.<P>You can redistribute it and/or modify it under the terms of the GNU General Public License version 2 as published by the Free Software Foundation.
loginApplication.gplLink=For more details about the General Public Licence.
loginApplication.gplLink2=
loginApplication.mobileMsg=You are using OSCAR Mobile (Beta).
loginApplication.fullSite=Full Site
loginApplication.gpltext=gpl.txt
loginApplication.alert=OSCAR McMaster Trunk
loginApplication.alert2=QuatroShelter Ver 1.2

loginApplication.image.i18n=images/CA.png
loginApplication.image.i18nTitle=CA
loginApplication.image.i18nAlt=OSCAR is available in several language versions
loginApplication.i18nText=Canadian English Version

loginApplication.image.logo=images/OSCAR-LOGO.gif
loginApplication.image.logoText=OSCAR McMaster <br>For more information, visit <a href='http://www.oscarcanada.org'>www.oscarcanada.org</a><br>
loginApplication.image.logoText2=<br><br><br><br><br><br>Powered By <br><img src="images/CAISIlogoBIG.bmp"><br><img src="images/OSCARLogo.jpg"><br> version 2.4 02-15-2008
msgApplication.title=Messenger
msgBackToOscar.link=Exit Messenger

errors.missingLogonInfo=Malformed Password and/or Username
errors.badLogon=Username and password do not match please try again

app.title=oscarRx
app.top1=Help
app.top2=About
app.top3=Disclaimer

global.phoneformat1= (x-xxx-xxx-xxxx)

ChooseDrug.title=Step 2 Select the Desired Drug
ChooseDrug.section1Title=Search for New Drug
ChooseDrug.section2Title=Drug Search Results
ChooseDrug.searchAgain=Drug Name:
ChooseDrug.genericDrugBox=Generic Name
ChooseDrug.brandDrugBox=Brand Name

ChooseDrug.msgSearch=Search
ChooseDrug.msgReset=Reset
ChooseDrug.msgCustomDrug=Custom Drug
ChooseDrug.msgDrugNotFound=I can't find the drug I'm looking for...
ChooseDrug.msgSearchNoResults=Search returned no results. Revise your search and try again.
ChooseDrug.msgInfo=Info
ChooseDrug.title.DrugSearchResults=Drug Search Results
ChooseDrug.msgDrugOfChoice=Drug of Choice
ChooseDrug.msgCustomWarning=This feature will allow you to manually enter a drug.\\n Warning\: Only use this feature if absolutely necessary, as you will lose the following functionality\: \\n  *  Known Dosage Forms / Routes \\n  *  Drug Allergy Information\\n  *  Drug-Drug Interaction Information\\n  *  Drug Information' \\n\\nAre you sure you wish to use this feature?


ChoosePatient.title=Step 2 Choose a Patient
ChoosePatient.choose=Patient Search Results
ChoosePatient.searchAgain=Search Again
ChoosePatient.textBox=Surname:
ChoosePatient.emptySearch=Name search yielded no results please try again

EditAllergies.title=Edit Allergy/Adverse Reaction Profile
EditAllergies.section1Title=Patient Demographics
EditAllergies.section2Title=Edit Allergy/Adverse Reaction Profile
EditAllergies.section3Title=Add an Allergy/Adverse Reaction

ChooseAllergy.title=Add an Allergy/Adverse Reaction
ChooseAllergy.section1Title=Search for Allergy/Adverse Reaction
ChooseAllergy.section2Title=Allergy/Adverse Reaction Search Results

EditDiseases.title=Edit Allergy/Adverse Reaction Profile
EditDiseases.section1Title=Patient Demographics
EditDiseases.section2Title=Edit Allergy/Adverse Reaction Profile
EditDiseases.availableDiseases=Drug Allergies/Adverse Reactions in Database
EditDiseases.actualDiseases=Current Drug Allergy/Adverse Reaction Profile

EditDrugs.title=Edit Allergy/Adverse Reaction Profile
EditDrugs.section1Title=Patient Demographic
EditDrugs.section2Title=Edit Allergy/Adverse Reaction Profile
EditDrugs.availableDrugs=Drug Allergies/Adverse Reactions in Database
EditDrugs.actualDrugs=Current Drug Allergy/Adverse Reactions Profile

AddReaction.title=Add Reaction
EditReaction.title=Edit Reaction 

Logon.title=oscarRx Secure Logon
Logon.section1Title=Logon
Logon.userName=Username
Logon.passWord=Password
Logon.submit=Logon
Logon.reset=Reset

ScratchPad.title = Scratch Pad

SearchDrug.title=Step 1 Search For Drug
SearchDrug.section1Title=Patient Demographics
SearchDrug.section2Title=Patient Drug Profile
SearchDrug.section3Title=Drug Search
SearchDrug.nameTextBox=Name:
SearchDrug.ageTextBox=Age:
SearchDrug.sexTextBox=Sex:
SearchDrug.drugTextBox=Drug Name:
SearchDrug.nameText=Patient Name:
SearchDrug.ageText=Age:
SearchDrug.OHIPText=OHIP:
SearchDrug.sexText=Sex:
SearchDrug.drugProfileText=Drug Profile:
SearchDrug.diseaseProfileText=Disease Profile:
SearchDrug.allergyProfileText=Drug Allergy/Adverse Reaction Profile:
SearchDrug.drugSearchTextBox=Drug Name:
SearchDrug.drugSearchRouteLabel=Administration Route:
SearchDrug.radio1BrandName=Brand Name
SearchDrug.radio1GenericName=Generic Name
SearchDrug.radio1Both=Both
SearchDrug.PreferedPharmacy=Preferred Pharmacy
SearchDrug.Print=Print
SearchDrug.Reprint=Reprint
SearchDrug.title.CopyFavorites=Copy Favorites

SearchDrug.msgRxDate=Start Date
SearchDrug.msgPrescription=Medication
SearchDrug.msgReprescribe=Represcribe
SearchDrug.msgReprescribeLongTermMed=Represcribe Long Term Meds
SearchDrug.msgDelete=Delete
SearchDrug.msgEdit=Edit
SearchDrug.msgLocationPrescribed=Location Prescribed
SearchDrug.msgDiscontinue=Discontinue
SearchDrug.msgReason=Reason
SearchDrug.msgHideCPP=Hide from CPP
SearchDrug.msgDispense=Dispense

SearchDrug.msgHideCPP_help=Check this to have this medication not display in echart under 'Medications' tab
SearchDrug.msgReason_help=Associate this medication with a disease


SearchDrug.msgProfileLegend=Profile Legend
SearchDrug.msgShowCurrent=Current
SearchDrug.msgShowCurrentDesc=Displays Active and Expired medications.
SearchDrug.msgShowAll=All
SearchDrug.msgShowAllDesc=Displays Active, Expired, Deleted and Discontinued.
SearchDrug.msgActive=Active
SearchDrug.msgActiveDesc=Displays Active ONLY.
SearchDrug.msgInactive=Expired
SearchDrug.msgInactiveDesc=Displays Expired ONLY.

SearchDrug.msgAll=All

SearchDrug.msgLongTermAcute=Longterm/Acute
SearchDrug.msgLongTermAcuteDesc=Displays Active and Expired medications grouped by Longterm and Acute.
SearchDrug.msgLongTermAcuteInactiveExternal=Longterm/Acute/Inactive/External
SearchDrug.msgLongTermAcuteInactiveExternalDesc=Displays Active and Expired medications grouped by Longterm, Acute, Inactive and External.

SearchDrug.msgSaveAndPrint=Save And Print
SearchDrug.msgDrugOfChoice=Drug of Choice
SearchDrug.msgViewEditAllergies=View / Edit Allergies
SearchDrug.msgInfo=Info
SearchDrug.msgSaveAndPrescribe=Save & Prescribe
SearchDrug.msgSaveOnly=Save

SearchDrug.pharmacy.msgName=Name
SearchDrug.pharmacy.msgAddress=Address
SearchDrug.pharmacy.msgCity=City
SearchDrug.pharmacy.msgProvince=Province
SearchDrug.pharmacy.msgPostalCode=PostalCode
SearchDrug.pharmacy.msgPhone1=Phone 1
SearchDrug.pharmacy.msgPhone2=Phone 2
SearchDrug.pharmacy.msgFax=Fax
SearchDrug.pharmacy.msgEmail=Email
SearchDrug.pharmacy.msgNotes=Notes

SearchDrug.msgSearch=Search
SearchDrug.msgReset=Reset
SearchDrug.msgCustomDrug=Custom Drug
SearchDrug.msgOMDLookup=OMD Lookup
SearchDrug.msgResetPrescriptionRx3=Reset
SearchDrug.msgDrugOfChoiceRx3=DrugOfChoice
SearchDrug.msgNoteRx3=Note
SearchDrug.msgCustomDrugRx3=CustomDrug
SearchDrug.SendToPHR=Send To PHR
SearchDrug.msgPastMed=Past Med

SearchDrug.help.Search=See search results (breaks up results into generic and brand)
SearchDrug.help.CustomDrug=Create a custom drug if drug is not in the drug database
SearchDrug.help.CustomNote=Create a custom note (no quantity/repeats/duration)
SearchDrug.help.DrugOfChoice=Queries MyDrugRef for treatments
SearchDrug.help.OMD=OntarioMD lookup for treatments (requires login)
SearchDrug.help.SaveAndPrint=Save medication, and generate a prescription
SearchDrug.help.Save=Save medication only

oscarRx.DisplayRxRecord.title=Display Rx Record
oscarRx.chartDrugProfile.title=Timeline Drug Profile
oscarRx.Prescription.changeDrugLongTerm=Change Drug to Long Term Med
oscarRx.Prescription.changeDrugShortTerm=Change Drug to Short Term Med
oscarRx.Prescription.changeDrugLongTermConfirm=Change Drug to Long Term Med?
oscarRx.Prescription.changeDrugShortTermConfirm=Change Drug to Short Term Med?
oscarRx.Preview.EditRx=Edit Rx
oscarRx.sideLinks.msgSpecial=Special
oscarRx.sideLinks.msgEditPharmacy=Edit Pharmacy
oscarRx.sideLinks.msgEditFavorites=Edit Favorites
oscarRx.sideLinks.msgCopyFavorites=Copy Favorites
oscarRx.sideLinks.msgAllergies=Active Allergies/Adverse Reactions
oscarRx.sideLinks.msgFavorites=Favorites
oscarRx.sideLinks.msgDiseases=Diseases
oscarRx.sideLinks.msgMedHistory=Medical History

oscarRx.interactions.msgAugmentsNoClinical=augments (no clinical effect)
oscarRx.interactions.msgAugments=augments
oscarRx.interactions.msgInhibitsNoClinical=inhibits  (no clinical effect)
oscarRx.interactions.msgInhibits=inhibits
oscarRx.interactions.msgNoEffect=has no effect on
oscarRx.interactions.msgUnknownEffect=unknown effect on

oscarRx.timelineDrugProfile=Chronologic Profile

oscarRx.BackToTop=Back To Top
oscarRx.discontinuedReason.msgComment =Comment:
oscarRx.discontinuedReason.msgReason =Reason
oscarRx.discontinuedReason.DoseChange =Dose change
oscarRx.discontinuedReason.EnteredInError=Entered in Error
oscarRx.discontinuedReason.Unknown=Unknown
oscarRx.discontinuedReason.AdverseReaction =Adverse reaction
oscarRx.discontinuedReason.Allergy =Allergy
oscarRx.discontinuedReason.IneffectiveTreatment =Ineffective treatment
oscarRx.discontinuedReason.PrescribingError =Prescribing error
oscarRx.discontinuedReason.NoLongerNecessary =No longer necessary
oscarRx.discontinuedReason.SimplifyingTreatment =Simplifying treatment
oscarRx.discontinuedReason.PatientRequest =Patient request
oscarRx.discontinuedReason.NewScientificEvidence =New scientific evidence
oscarRx.discontinuedReason.IncreasedRiskBenefitRatio =Increased risk:benefit ratio
oscarRx.discontinuedReason.DiscontinuedByAnotherPhysician =Discontinued by another physician
oscarRx.discontinuedReason.Cost=Cost
oscarRx.discontinuedReason.DrugInteraction =Drug interaction
oscarRx.discontinuedReason.Other=Other


oscarRx.printPharmacyInfo.selectPharmacyInfo=Select a pharmacy:
oscarRx.printPharmacyInfo.paperSizeWarning=Use A4 size or larger paper to print pharmacy info properly.
oscarRx.printPharmacyInfo.addPharmacyButton=Add Pharmacy Info

SearchPatient.title=Step 1 Search for a Patient
SearchPatient.section1Title=Surname Search
SearchPatient.surname=Surname:
SearchPatient.firstName=First Name:
SearchPatient.button1Label=Search

StaticScript.title=View Medication
StaticScript.title.EditFavorites=Edit Favorites
StaticScript.options=Options

WriteScript.msgComponents=Components

WriteScript.msgDays=Days
WriteScript.msgWeeks=Weeks
WriteScript.msgMonths=Months

WriteScript.msgCalculated=Calculated
WriteScript.msgRepeats=Repeats
WriteScript.msgOther=Other

WriteScript.msgLastRefillDate=Last Refill Date
WriteScript.msgLongTermMedication=Long Term Medication
WriteScript.msgPastMedication=Past Medication
WriteScript.msgComment=Comment
WriteScript.msgUnknown=Unknown
WriteScript.msgPatientCompliance=Patient Compliance
WriteScript.msgYes=Yes
WriteScript.msgNo=No
WriteScript.msgUnset=Unknown
WriteScript.msgNonAuthoritative=Non-Authoritative
WriteScript.msgPickUpDate=Pickup Date
WriteScript.msgPickUpTime=Pickup Time
WriteScript.msgETreatmentType=eTreatment Type
WriteScript.msgETreatment.Continuous=Continuous/chronic
WriteScript.msgETreatment.Acute=Acute
WriteScript.msgETreatment.OneTime=One Time
WriteScript.msgETreatment.LongTermPRN=Long-term - As needed
WriteScript.msgETreatment.ShortTermPRN=Short-term - As needed
WriteScript.msgRxStatus=Prescription Status
WriteScript.msgRxStatus.New=New
WriteScript.msgRxStatus.Active=Active
WriteScript.msgRxStatus.Suspended=Suspended
WriteScript.msgRxStatus.Aborted=Aborted
WriteScript.msgRxStatus.Completed=Completed
WriteScript.msgRxStatus.Obsolete=Obsolete
WriteScript.msgRxStatus.Nullified=Nullified

WriteScript.msgWarning=warning
WriteScript.msgPrescribedByOutsideProvider=Prescribed by Outside Provider
WriteScript.msgDispenseInternal=Dispense Internally
WriteScript.msgName=Name
WriteScript.msgOHIPNO=OHIP No
WriteScript.msgRxWrittenDate=Prescription Written Date
WriteScript.msgUpdate=Update
WriteScript.msgUpdateAndGetNewDrug=Update and Get New Drug
WriteScript.msgUpdatePrintAndSave=Update, Print and Save
WriteScript.msgAnnotation=Annotation
WriteScript.msgEdit=Edit
WriteScript.msgDelete=Delete
WriteScript.msgInfo=Info
WriteScript.msgAddtoFavorites=Add to Favorites
WriteScript.msgFor=For
WriteScript.msgPrescribedRefill=Refill
WriteScript.msgPrescribedRefillDuration=Duration
WriteScript.msgPrescribedRefillDurationDays=days
WriteScript.msgPrescribedRefillQuantity=Quantity
WriteScript.msgPrescribedDispenseInterval=Dispense Interval
WriteScript.msgDrugForm=Drug Form

WriteScript.method=Method
WriteScript.route=Route
WriteScript.drugForm=Drug Form
WriteScript.amount=Amount

WriteScript.title=Step 3 Write the Prescription
WriteScript.section1Title=Patient Name
WriteScript.section11Title=Drug Details
WriteScript.genericNameText=Generic Name
WriteScript.brandNameText=Brand Name
WriteScript.dFRText=D/F/R:
WriteScript.section2Title=Prescription Details
WriteScript.startDate=Start Date
WriteScript.endDate=End Date
WriteScript.take=Take
WriteScript.frequency=Frequency
WriteScript.duration=Duration
WriteScript.durationUnit=Duration Unit
WriteScript.quantity=Quantity
WriteScript.repeat=Repeat
WriteScript.noSubs=No Substitutions
WriteScript.prn=PRN
WriteScript.special=Special Instructions
WriteScript.msgCustomInstructions=Custom Instructions

WriteScript.section3Title=Drug Allergy and Drug Drug Interaction Details
WriteScript.section4Title=Final Options
WriteScript.section5Title=Pending Prescriptions
WriteScript.section6Title=Drug Drug Interactions
WriteScript.section1Text1=Generic Name:
WriteScript.section1Text2=Brand Name:
WriteScript.section1Text3=D/F/R:
WriteScript.section2Text1=Take:
WriteScript.section2Text2=Frequency:
WriteScript.section2Text3=Duration:
WriteScript.section2Text4=Special Instructions:
WriteScript.section2Text5=Quantity:
WriteScript.section2Text6=Repeat:
WriteScript.section2Text7=No Substitutes:
WriteScript.section2OrOther=or other
WriteScript.section2AndOrOther=and/or other
WriteScript.section4Button1=Save
WriteScript.section4Button2=Print to Local Printer
WriteScript.section4Button3=Fax to Pharmacy
WriteScript.section4Button4=Add to Favorites
WriteScript.msgQuantity=Please enter a number for quantity

WriteScript.name=Name:
WriteScript.instructions=Instructions:
WriteScript.enterSpecialInstructions=Enter Special Instruction
WriteScript.longTermMed=Long term
WriteScript.lastRefillDate=Last renew:
WriteScript.writtenDate=Writted on:
WriteScript.addToFavorite=add to favorites
Send2Indivo.prescription.Instruction=See Instructions


ViewScript.title=Step 6 Print the Prescription
ViewScript.section1Title=Final Prescription
ViewScript.section2Title=Options


ViewScript.msgHelp=Help
ViewScript.msgAbout=About
ViewScript.msgDisclaimer=Disclaimer
ViewScript.msgRightClick=To print,right click on prescription and select "print" from the menu.
ViewScript.msgAddress=Address
ViewScript.msgActions=Actions
ViewScript.msgCreateNewRx=Create New Prescription
ViewScript.msgBackToOscar=Close Window
ViewScript.msgPrintPasteEmr=Print & Paste into EMR
ViewScript.msgAddNotesRx=Additional Notes to add to Rx
ViewScript.msgDrugInfo=Drug Information

ViewScript.msgPrint=Print
ViewScript.msgAddToRx=Add to Rx



RxPreview.title=Print Preview
RxPreview.msgDOB=DOB
RxPreview.msgTel=Tel
RxPreview.msgFax=Fax
RxPreview.msgSignature=Signature
RxPreview.digitallySign=Sign
RxPreview.msgReprintBy=Reprint by
RxPreview.msgOrigPrinted=Originally Printed
RxPreview.msgTimesPrinted=Times Printed
RxPreview.PractNo=Pract. No.


SelectPharmacy.title=Select Pharmacy
SelectPharmacy.instructions=Click on the Pharmacy you would like to associate with this patient.
SelectPharmacy.table.pharmacyName=Pharmacy Name
SelectPharmacy.table.address=Address
SelectPharmacy.table.city=City
SelectPharmacy.table.postalCode=Postal Code
SelectPharmacy.table.phone=Phone
SelectPharmacy.table.fax=Fax
SelectPharmacy.editLink=Edit
SelectPharmacy.deleteLink=Delete
SelectPharmacy.addLink=Add Pharmacy


SelectReason.table.codingSystem=Coding System
SelectReason.table.code=Code
SelectReason.table.description=Description
SelectReason.table.comments=Comments
SelectReason.table.primaryReasonFlag=Primary Reason
SelectReason.table.provider=Provider
SelectReason.table.dateCoded=Date Coded
SelectReason.error.codeEmpty=Code can not be empty
SelectReason.error.codeValid=Code must be valid
SelectReason.msg.archived=Reason has been archived
SelectReason.error.duplicateCode=Code is already active for this Drug

ManagePharmacy.title=Manage Pharmacy
ManagePharmacy.subTitle.add=Add Pharmacy
ManagePharmacy.subTitle.update=Update Pharmacy
ManagePharmacy.txtfld.label.pharmacyName=Pharmacy Name
ManagePharmacy.txtfld.label.address=Address
ManagePharmacy.txtfld.label.city=City
ManagePharmacy.txtfld.label.province=Province
ManagePharmacy.txtfld.label.postalCode=Postal Code
ManagePharmacy.txtfld.label.phone1=Phone 1
ManagePharmacy.txtfld.label.phone2=Phone 2
ManagePharmacy.txtfld.label.fax=Fax
ManagePharmacy.txtfld.label.email=Email
ManagePharmacy.txtfld.label.serviceLocationIdentifier=Service Location Identifier
ManagePharmacy.txtfld.label.notes=Notes
ManagePharmacy.submitBtn.label.submit=Submit

wrongmsg.heading=Wrong!
index.title=Strutshehe2345345345345 Starter Application
index.heading=Hello World!
createMessage.title=Create a Message
DisplayMessage.title=Display Messages
ViewMessage.title=View Messages
index.message=To get started on your own application, copy the struts-blank.war to a new WAR file using the name for your application. Place it in your container's "webapp" folder (or equivalent), and let your container auto-deploy the application. Edit the skeleton configuration files as needed, reload Struts or restart your container, and you are on your way! (You can find the ApplicationResources file with this message in the classes folder.)
error.message.missing=<li>Message or Subject is required</li>
error.provider.missing=<li>No provider Selected</li>
message="yo"

application.title=Messenger
application.title.admin=Messenger Group Configurator

backToOscar.link=Exit Messenger

search.title=Demographic Search

errors.header=<h3><font color="red">Validation Error<ul>
errors.footer=</ul></font></h3>

Errors.service.noServiceSelected=<li>You must Select a service for the Consultation Request</li>

Errors.Firstname=<li>Please enter First Name</li>
Errors.FirstnameValidation=<li>No digits allowed in First Name</li>
Errors.Lastname=<li>Please enter Last Name</li>
Errors.LastnameValidation=<li>No digits allowed in Last Name</li>
Errors.Phone=<li>Please enter Phone</li>
Errors.PhoneValidation=<li>Please use the phone format xxx-xxx-xxxx</li>
Errors.Address=<li>Please enter Address</li>
Errors.Fax=<li>Please enter a fax number</li>
Errors.FaxValidation=<li>Please use the fax format xxx-xxx-xxxx</li>
Errors.PrivatePhoneNumberValidation=<li>Please use the private phone format xxx-xxx-xxxx</li>
Errors.CellPhoneNumberValidation=<li>Please use the cell phone format xxx-xxx-xxxx</li>
Errors.ReferralNo=<li>Please enter a referral Number</li>
Errors.ReferralNoValidation=<li>Please use the referral number format xxxxxx</li>
Errors.PagerNumberValidation=<li>Please use the pager number format xxx-xxx-xxxx</li>
Errors.EmailValidation=<li>Please use the correct Email format (<EMAIL>)</li>
Errors.WebsiteValidation=<li>Please enter a valid website http(s)://xyz.com</li>
Errors.City=<li>Please enter City</li>
Errors.ZipOrPostal=<li>Please enter Zip or Postal Code</li>
Errors.Country=<li>Please enter Country</li>
Errors.StateOrProvince=<li>Please enter State or Province</li>

Error.numCols.missing=<li>You must enter a Column number</li>
Error.numRows.missing=<li>You must enter a Row Number</li>
Error.setName.missing=<li>You must enter a Set Name</li>
Error.numCols.below.zero=<li>Column value must be greater than 0</li>
Error.numRows.below.zero=<li>Row value must be greater than 0</li>
Error.numRows.non.numeric=<li>You must enter a numeric value for the Rows</li>
Error.numCols.non.numeric=<li>You must enter a numeric value for the Columns</li>

#Messages for appointment form
Appointment.msgFillNameField=Please type in name in the Name field and then click 'Search' button.
Appointment.msgFillTimeField=You must type in a number in the field.
Appointment.msgFillValidTimeField=You must type in a right number in the field.
Appointment.msgCheckDuration=Please check Start Time/duration!!!
Appointment.msgInvalidDateFormat=Please use the date format: xx:xx !
Appointment.formDate=Date
Appointment.formStatus=Status
Appointment.formStartTime=Start Time
Appointment.formType=Type
Appointment.formDoctor=Doctor
Appointment.formDuration=Duration <font size='-2'>(min)</font>
Appointment.formName=Name
Appointment.formReason=Reason
Appointment.formNotes=Notes
Appointment.formLocation=Location
Appointment.formResources=Resources
Appointment.formCreator=Creator
Appointment.formDateTime=Date Time
Appointment.formAlert=Alert
Appointment.formChartNo=Chart No.
Appointment.formLastCreator=Last Creator
Appointment.formLastTime=Last Time
Appointment.msgPatientStatus=Patient Status
Appointment.msgRosterStatus=Roster Status
Appointment.msgTelephone=Tel
Appointment.formCritical=Critical
Appointment.formSeeReceptionist=See Receptionist at Check-in
Appointment.formEmailReminder=Email Reminder

#Messages for appointment add form
appointment.addappointment.title=ADD APPOINTMENT
appointment.addappointment.msgMainLabel=MAKE AN APPOINTMENT
appointment.addappointment.msgMainLabelMobile=Make
appointment.addappointment.btnSearch=Search
appointment.addappointment.btnGroupAppt=Group Appt
appointment.addappointment.btnAddAppointment=Add Appointment
appointment.addappointment.btnAddAppointmentMobile=Add
appointment.addappointment.btnAddApptPrintPreview=Add Appt & PrintPreview
appointment.addappointment.btnPrintReceipt= Add & Receipt 
appointment.addappointment.btnCancel=Cancel
appointment.addappointment.btnRepeat=R
appointment.addappointment.msgDoubleBooking=Double Booking
appointment.addappointment.msgBooking=Booking
appointment.addappointment.msgAddSuccess=Successful Addition of an appointment Record.
appointment.addappointment.msgAddFailure=Sorry, addition has failed.
appointment.addappointment.formName=Name
appointment.addappointment.msgOverview=Appointment Overview
appointment.addappointment.msgProvider=Provider
appointment.addappointment.msgStatus=Status
appointment.addappointment.msgNotes=Notes
appointment.addappointment.msgComments=Comments
appointment.addappointment.titleMultipleGroupDayBooking=Multiple Same Day Group Booking
appointment.addappointment.MultipleGroupDayBooking=Cannot book more than one group appointment per patient per day
appointment.addappointment.msgDemgraphics=Demographics
appointment.addappointment.btnEdit=edit
appointment.addappointment.msgSex=Sex
appointment.addappointment.msgDOB=DOB
appointment.addappointment.msgHin=Hin
appointment.addappointment.msgAddress=Address
appointment.addappointment.msgPhone=Phone
appointment.addappointment.msgH=H
appointment.addappointment.msgW=W
appointment.addappointment.msgC=C
appointment.addappointment.msgEmail=Email
appointment.addappointment.msgFamDoc=Family Doctor
appointment.addappointment.msgRefDoc=Referral Doctor
appointment.addappointment.msgFormsSaved=Form(s) Completed
appointment.addappointment.msgFormNotCompleted=No
appointment.addappointment.msgFormCompleted=Yes

#Messages for appointment delete form
appointment.appointmentdeletearecord.msgLabel=DELETE AN APPOINTMENT RECORD
appointment.appointmentdeletearecord.msgDeleteSuccess=Successful Deletion of an appointment Record.
appointment.appointmentdeletearecord.msgDeleteFailure=Sorry, deletion has failed.

#Messages for add appointment group form
appointment.appointmentgrouprecords.title=Recurring Appt
appointment.appointmentgrouprecords.msgAddSuccess=Successful Addition of a Recurring Record.
appointment.appointmentgrouprecords.msgAddFailure=Sorry, addition has failed.
appointment.appointmentgrouprecords.msgExitConfirmation=Are you sure you want to exit without any recurring action?
appointment.appointmentgrouprecords.msgDeleteConfirmation=Are you sure to DELETE the appointment?
appointment.appointmentgrouprecords.btnGroupUpdate=Recurring Update
appointment.appointmentgrouprecords.btnGroupCancel=Recurring Cancel
appointment.appointmentgrouprecords.btnGroupDelete=Recurring Delete
appointment.appointmentgrouprecords.btnAddGroupAppt=Add Recurring Appointment
appointment.appointmentgrouprecords.msgLabel=RECURRING APPOINTMENT
appointment.appointmentgrouprecords.msgProviderName=Provider Name
appointment.appointmentgrouprecords.msgFirstAppointment=1st Appt
appointment.appointmentgrouprecords.msgSecondAppointment=2nd Appt
appointment.appointmentgrouprecords.msgExistedAppointment=Existing Appt

#Messages for appointment edit form
appointment.editappointment.title=EDIT APPOINTMENTS
appointment.editappointment.msgMainLabel=EDIT AN APPOINTMENT
appointment.editappointment.msgMainLabelMobile=Edit
appointment.editappointment.msgTime=Time
appointment.editappointment.btnView=View
appointment.editappointment.btnSearch=Search
appointment.editappointment.msgNoSuchAppointment=failed!!! No such appointment to edit! Select Back button.
appointment.editappointment.btnGroupAction=Recurring Action
appointment.editappointment.btnUpdateAppointment=Update Appt
appointment.editappointment.btnDeleteAppointment=Delete Appt
appointment.editappointment.btnCancelAppointment=Cancel Appt
appointment.editappointment.btnNoShow=No show
appointment.editappointment.btnLabelPrint=Label
appointment.editappointment.btnPrintReceipt=Update & Receipt
appointment.appointmentupdatearecord.msgMainLabel=UPDATE AN APPOINTMENT RECORD
appointment.appointmentupdatearecord.msgUpdateSuccess=Successful Update of an Appointment Record.
appointment.appointmentupdatearecord.msgUpdateFailure=Sorry, update has failed.
appointment.editappointment.msgDeleteConfirmation=Are you sure to DELETE the appointment?
appointment.editappointment.msgDeleteBilledConfirmation=Appointment has been billed.  Are you sure you want to delete appointment?
appointment.editappointment.msgCanceledBilledConfirmation=Appointment has been Billed.  Are you sure you want to cancel appointment?
appointment.editappointment.msgNotesTooBig=Please enter no more than 255 characters for notes
appointment.editappointment.appointmentLocationRequired=Appointment location has not been selected

#messages for appointment type form
appointment.type.oper.error=Operation type is not correct
appointment.type.number.error=Appointment type number is not correct
appointment.type.name.error=Appointment type name is not correct
appointment.type.location.error=Appointment type location should be on of site locations
appointment.type.notfound.error=Appointment type does not exist


caseload.msgNotes=Notes:
caseload.msgSearch=Search
caseload.msgProvider=Provider:
caseload.msgAllPrograms=All Programs
caseload.msgAllProviders=All Providers
caseload.msgRostered=Rostered:
caseload.msgDxReg=DxReg:
caseload.msgSchedule=Schedule
caseload.msgResults=results retrieved
caseload.msgDemographic=Demographic
caseload.msgLoading=Loading results...
caseload.msgTryAgain=No results found. Please try a different search.
caseload.msgAge=Age
caseload.msgSex=Sex
caseload.msgLastAppt=Last Appt
caseload.msgNextAppt=Next Appt
caseload.msgApptsLYTD=Appts LYTD
caseload.msgLab=Lab
caseload.msgDoc=Doc
caseload.msgTickler=Tickler
caseload.msgMsg=Msg
caseload.msgBMI=BMI
caseload.msgBP=BP
caseload.msgWT=WT
caseload.msgSMK=SMK
caseload.msgA1C=A1C
caseload.msgACR=ACR
caseload.msgSCR=SCR
caseload.msgLDL=LDL
caseload.msgHDL=HDL
caseload.msgTCHD=TCHD
caseload.msgEGFR=EGFR
caseload.msgEYEE=EYEE
caseload.msgLastEncounterDate=Last Encounter Date
caseload.msgLastEncounterType=Last Encounter Type
caseload.msgDisplayMode=Display Mode
caseload.msgCASHAdmissionDate=[CASH]AdmissionDate
caseload.msgACCESS1AdmissionDate=[ACCESS1]AdmissionDate

healthtracker.noActiveMeds.warning=No Active Medications

#Messages for add provider status form
provider.provideraddstatus.msgAddFailure=Sorry, addition has failed.

#Messages for report day sheet
report.reportdaysheet.title=DAY SHEET
report.reportdaysheet.msgMainLabel=DAY SHEET
report.reportdaysheet.btnPrint=Print
report.reportdaysheet.msgAppointmentDate=Appt Date
report.reportdaysheet.msgAppointmentTime=Appt Time
report.reportdaysheet.msgPatientLastName=Patient's Last Name
report.reportdaysheet.msgPatientFirstName=Patient's First Name
report.reportdaysheet.msgChartNo=Chart No.
report.reportdaysheet.msgComments=Comments
report.reportdaysheet.msgRosterStatus=Roster Status
report.reportdaysheet.msgBookingStatus=Booking Status
report.reportdaysheet.msgSelfBookedCheck=Show Only Self Booked
report.reportdaysheet.msgSelfBooked=Self

provider.rxChangeProfileViewMessage=Change what kind of prescriptions to see in this drug profile
provider.rxChangeProfileView=*
provider.appointmentProviderAdminDay.apptProvider=apptProvider
provider.appointmentProviderAdminDay.provider=Provider
provider.appointmentProviderAdminDay.btnB=B
provider.appointmentProviderAdminDay.chGrpNo=Change your Group No.
provider.appointmentProviderAdminDay.confirm=You are about to delete the previous billing, are you sure?
provider.appointmentProviderAdminDay.confirmBooking=Are you sure you would like to book an apointment here
provider.appointmentProviderAdminDay.btnE=E
provider.appointmentProviderAdminDay.btnEncounter=Encounter
provider.appointmentProviderAdminDay.btnI=I
provider.appointmentProviderAdminDay.btnIntake=Intake
provider.appointmentProviderAdminDay.flipView=Flip view
provider.appointmentProviderAdminDay.grpView=Group View
provider.appointmentProviderAdminDay.notes=notes
provider.appointmentProviderAdminDay.onUnbilled=You are about to delete the previous billing, are you sure?
provider.appointmentProviderAdminDay.reason=reason
provider.appointmentProviderAdminDay.Reason=Reason
provider.appointmentProviderAdminDay.expandreason=Expand Reason
provider.appointmentProviderAdminDay.sameDay=Sorry, this appointment can only be booked the same day.
provider.appointmentProviderAdminDay.sameWeek=Sorry, this appointment can only be booked within one week of the appointment time.
provider.appointmentProviderAdminDay.schedView=Schedule
provider.appointmentProviderAdminDay.SelfBookedMarker=[Self Booked]
provider.appointmentProviderAdminDay.study=Study
provider.appointmentProviderAdminDay.ticklerMsg=Tickler Msg
provider.appointmentProviderAdminDay.title=Appointment Access
provider.appointmentProviderAdminDay.viewAllProv=View all providers in the group
provider.appointmentProviderAdminDay.viewAll=All
provider.appointmentProviderAdminDay.viewConReq=View Consultation Requests
provider.appointmentProviderAdminDay.viewDaySched=View your daily schedule
provider.appointmentProviderAdminDay.viewEdoc=View e-Document
provider.appointmentProviderAdminDay.viewLabReports=View lab reports
provider.appointmentProviderAdminDay.viewMonthSched=View your monthly template
provider.appointmentProviderAdminDay.viewNextDay=View Next DAY
provider.appointmentProviderAdminDay.viewPrevDay=View Previous DAY
provider.appointmentProviderAdminDay.viewProvAval=View providers available
provider.appointmentProviderAdminDay.viewResources=View Resources
provider.appointmentProviderAdminDay.zoomView=zoom view
provider.appointmentProviderAdminDay.msgMasterFile=Master Record
provider.appointmentProviderAdminDay.btnM=M
provider.appointmentProviderAdminDay.msgNotOnSched=not on Schedule
provider.appointmentProviderAdminDay.search=<u>S</u>earch
provider.appointmentProviderAdminDay.msgSettings=Edit your personal setting
provider.appointmentProviderAdminDay.daySheet=Day Sheet
provider.appointmentProviderAdminDay.daySheetLetter=DS
provider.appointmentProviderAdminDay.week=Week
provider.appointmentProviderAdminDay.weekLetter=W
provider.appointmentProviderAdminDay.weekView=Week view
provider.appointmentProviderAdminDay.searchLetter=S
provider.appointmentProviderAdminDay.searchView=Search
provider.appointmentProviderAdminDay.doctor=Doctor
provider.appointmentProviderAdminDay.label=Label
provider.appointmentProviderAdminDay.btnL=L
provider.appointmentProviderAdminDay.provder=Provider
provider.appointmentProviderAdminDay.hcv=HCV
provider.SetDefaultPrescriptionQuantity=Set Default Quantity in RX3
provider.SetDefaultQueueForUploadedDocument=Set Default Queue For Uploaded Document
provider.setRxDefaultQuantity.title=Set Rx Default Quantity
provider.setRxDefaultQuantity.msgPrefs=Preferences
provider.setRxDefaultQuantity.msgDefaultQuantity=Rx Default Quantity
provider.setRxDefaultQuantity.msgEdit=Enter your desired default quantity
provider.setRxDefaultQuantity.btnSubmit=Save
provider.setRxDefaultQuantity.msgSuccess=Rx Default Quantity saved

provider.setDefaultDocumentQueue.title=Set Default Document Queue
provider.setDefaultDocumentQueue.msgPrefs=Preferences
provider.setDefaultDocumentQueue.msgProfileView=Default Document Queue
provider.setDefaultDocumentQueue.msgEditFromExisting=Choose a default queue from existing queues
provider.setDefaultDocumentQueue.msgEditSaveNew=Save a new default queue
provider.setDefaultDocumentQueue.btnSubmit=Save
provider.setDefaultDocumentQueue.msgSuccess=Default Document Queue saved
provider.btnCaisiBillPreferenceNotDelete=Do Not Delete Previous Billing

provider.btnSetIntegratorPreferences=Integrator Preferences
provider.integratorPreferences.preferences=Integrator Preferences
provider.integratorPreferences.chooseDataSets=Choose Data Sets
provider.integratorPreferences.enabled=Enabled
provider.integratorPreferences.disabled=Disabled
provider.integratorPreferences.save=Save


#Global messages. These messages are used on many pages of the system.
global.abnormal=Abnormal
global.caseload=Caseload
global.loggedIn=Logged in as
global.normal=Normal
global.hl7=HL7
global.Alberta = Alberta
global.BC = British Columbia
global.Manitoba = Manitoba
global.NewBrun = New Brunswick
global.Nflnd = New Foundland & Labrador
global.NWTerr = Northwest Territory
global.Nova = Nova Scotia
global.Nunavut = Nunavut
global.Ontario = Ontario
global.PEI = Prince Edward Island
global.Quebec = Quebec
global.Sask = Saskatchewan
global.Yukon = Yukon
global.US = US resident
global.other = Other
global.admin=<u>A</u>dministration
global.adminShortcut=65
global.allergies=allergies
global.btnClose=Close
global.btnExit=Exit
global.btnPrint=Print
global.btnSave=Save
global.about=About
global.billing=<u>B</u>illing
global.billingtag=Billing
global.billingShortcut=66
global.calendar=<u>C</u>alendar
global.calendarShortcut=67
global.con=C<u>o</u>nsultations
global.conShortcut=79
global.consultations=consultations
global.today=<u>T</u>oday
global.day=Day
global.dayShortcut=84
global.year=Year
global.default=default
global.disclaimer=Disclaimer
global.Document=Document
global.documents=documents
global.edoc=e<u>D</u>oc
global.inbox=inboxManager
global.edocShortcut=68
global.eForms=eForms
global.VaAndIop=VA IOP
global.decisionSupportAlerts=Decision Support Alerts
global.encounter=Encounter
global.remoteReferral=Remote Referral Intake
global.genBillReport=Generate a billing report
global.genReport=Generate a report
global.google=Google
global.group=Group
global.harmony.echart = Harmony
global.hello=Hello
global.help=<u>H</u>elp
global.helpShortcut=72
global.immunizations=immunizations
global.lab=<u>I</u>nbox
global.labShortcut=76
global.license=License
global.master=Master
global.menu=<u>M</u>enu
global.messenger=Messenger
global.monthShortcut=78
global.phr=PHR
global.btnphr=P<u>H</u>R
global.phrShortcut=72
global.btnSendToPHR = Send to PHR
global.personalHealthRecord = Personal Health Record
global.msg=<u>M</u>sg
global.msgShortcut=77
global.renewal=<u>P</u>rescribeIT Msgs
global.month=Mo<u>n</u>th
global.oscarComm=oscarComm
global.oscarRx=oscarRx
global.pref=<u>P</u>references 
global.prefShortcut=80
global.prescriptions=Prescriptions
global.cycles=Cycles
global.pubmed=Pubmed
global.report=<u>R</u>eport
global.reportShortcut=82
global.resources=Clinical Resources
global.resourcesShortcut=69
global.rx=Rx
global.c=C
global.cl=CL
global.searchPatientRecords=Search for patient records
global.search=Search
global.searchShortcut=83
global.tickler=Tickler
global.btntickler=T<u>i</u>ckler
global.ticklerShortcut=73
global.unbil=Unbil
global.viewShortcut=86
global.viewTickler=Tickler
global.btnworkflow=<u>W</u>orkFlow
global.workflow=WorkFlow
global.workflowShortcut=87
global.btnSubmit=Submit
global.disease=Disease Registry
global.years=years
global.days=days
global.weeks=weeks
global.months=months
global.seconds=seconds
global.btnDelete=Delete
global.btnRestore=Restore
global.btnDeleteList=Delete List
global.btnAdd=Add
global.javascript.calendar=calendar-en.js
global.courseview=Courses
global.btncourseview=Courses
global.episode=Episodes
global.pregnancy=Pregnancies
global.middleware=ZEISS FORUM Viewer
global.parcs=PACS
global.contacts=Contacts
global.no.phr.account.registered=Patient does not have a PHR Account registered
global.aua=Acceptable Use Agreement
global.showhide=Show/Hide
global.completed=Completed
global.templates=Templates
global.module=Module
global.expandall=Expand All
global.collapseall=Collapse All
global.close=Close
global.update=Update
global.reset=Reset
global.manageReferrals=Ref
global.yes=Yes
global.no=No
global.clear=Clear
global.enddate=End Date
global.warning=Warning!
global.freeDrawing=Free Drawing
mobile.menu = Menu
mobile.notes = Notes

oscarEncounter.Index.chronicBilling=Chronic Billing
oscarEncounter.Index.inboxManager=Inbox Manager
oscarEncounter.Index.addForm=add new form
oscarEncounter.Index.addTickler=Add
oscarEncounter.Index.bodyMass=Body Mass Index
oscarEncounter.Index.by=by
oscarEncounter.Index.calculators=calculators
oscarEncounter.Index.clinicalModules=Clinical Modules
oscarEncounter.Index.clinicalResources=R<u>e</u>sources
oscarEncounter.Index.closeEncounterWindowConfirm=Are you sure you wish to exit the Encounter without saving?
oscarEncounter.Index.confirmExit=Are you sure you wish to exit the Encounter without saving?
oscarEncounter.Index.confirmSplit=Are you sure you want to Split the Chart?
oscarEncounter.Index.coronary=Coronary Artery
oscarEncounter.Index.createForm=Create a new form for
oscarEncounter.Index.currentForms=current forms
oscarEncounter.Index.encounterTable=encounterTable
oscarEncounter.Index.encounterTemplate=Encounter Templates
oscarEncounter.Index.f=F
oscarEncounter.Index.generalConversions=General Conversions
oscarEncounter.Index.kidneyFailureRiskCalculator=Kidney Failure Risk Calculator
oscarEncounter.Index.DASCalculator=Disease Activity Score Calculator (Rheumatoid Arthritis)
oscarEncounter.Index.goToSearchConfirm=Entering search without first saving encounter to lost date, are you sure?
oscarEncounter.Index.insertTemplateConfirm=Adding a template without saving your changes may results in lost data. Are you sure?
oscarEncounter.Index.insertTemplate=insert template
oscarEncounter.Index.internetResources=Internet Resources
oscarEncounter.Index.l=L
oscarEncounter.Index.masterFile=Master Record
oscarEncounter.Index.medHist=Medical History
oscarEncounter.Index.n=N
oscarEncounter.Index.onUnbilledConfirm=You are about to delete the previous billing, are you sure?
oscarEncounter.Index.oscarSearch=OSCAR Search
oscarEncounter.Index.otherMed=Other Medications
#oscarEncounter.Index.otherMed=Medical Alerts
oscarEncounter.Index.popupPage2Window=apptProviderSearch
oscarEncounter.Index.popupPageAlert=hi this is a null for self!
oscarEncounter.Index.popupPageKWindow=apptProviderSearch
oscarEncounter.Index.popupPageWindow=EncProvider
oscarEncounter.Index.popupSearchPageWindow=searchpage
oscarEncounter.Index.pregnancy=Pregnancy Calculator
oscarEncounter.Index.resource=resource
oscarEncounter.Index.r=R
oscarEncounter.Index.searchFor=search for...
oscarEncounter.Index.signed=Signed on
oscarEncounter.Index.simpleCalculator=Simple Calculator
oscarEncounter.Index.socialFamHist=Social & Family History
oscarEncounter.Index.s=S
oscarEncounter.Index.title=Encounter
oscarEncounter.Index.using=using...
oscarEncounter.Index.viewResource=View Resource
oscarEncounter.Index.x=X
oscarEncounter.Index.msgOscarConsultation=Consultation
oscarEncounter.Index.msgEncounter=Encounter
oscarEncounter.Index.msgMRP=MRP
oscarEncounter.Index.msgAppoint=Appointments
oscarEncounter.Index.msgForms=Forms
oscarEncounter.Index.msgOldForms=old forms
oscarEncounter.Index.msgConcerns=Ongoing Concerns
oscarEncounter.Index.msgReminders=Reminders
oscarEncounter.warnings.title=Warnings
oscarEncounter.Index.msgLocked=Locked
oscarEncounter.Index.btnNew=New Note
oscarEncounter.Index.btnGroupNote=Group
oscarEncounter.Index.btnAttachNote=Attach
oscarEncounter.Index.btnSave=Save
oscarEncounter.Index.btnFreeDraw=New Free Drawing
oscarEncounter.Index.btnSearch = Search
oscarEncounter.Index.btnSignSave=Sign & Save
oscarEncounter.Index.btnBill=Sign Save & Bill
oscarEncounter.Index.btnArchive=Archive
oscarEncounter.Index.btnAnnotation = Annotation
oscarEncounter.Index.btnCopy = Copy to Current Note
oscarEncounter.Index.btnWorking = Working ...
oscarEncounter.Index.btnPosition=Insert Position
oscarEncounter.Index.btnSignSaveBill=Sign, Save & Bill
oscarEncounter.Index.btnSign=Verify & Sign
oscarEncounter.Index.btnLock=Lock
oscarEncounter.Index.btnUnLock=Unlock
oscarEncounter.Index.btnGo= Go
oscarEncounter.Index.btnPrint=Print Notes
oscarEncounter.Index.btnDisplayIssues = Display Issues
oscarEncounter.Index.btnDisplayResolvedIssues = Display Resolved Issues
oscarEncounter.Index.btnDisplayUnresolvedIssues = Display Unresolved Issues
oscarEncounter.Index.msgDocuments=Documents
oscarEncounter.Index.msgConsentDocuments=Consent
oscarEncounter.Index.msgHRMDocuments=HRM Documents
oscarEncounter.Index.msgOsteoporotic=Osteoporotic Fracture
oscarEncounter.Index.saveFeedbackText=Saved
oscarEncounter.Index.assnIssue=Assign Issues
oscarEncounter.Index.PrintDialog = Print Dialog
oscarEncounter.Index.PrintSelect = Selected
oscarEncounter.Index.PrintAll = All
oscarEncounter.Index.PrintDates = Dates
oscarEncounter.Index.PrintToday = Today
oscarEncounter.Index.PrintFrom = From
oscarEncounter.Index.PrintTo = To
oscarEncounter.Index.BrowseNotes= Browse Notes
oscarEncounter.Index.ConsultOutstanding=This patient has outstanding consultations over 1 month old
oscarEncounter.Index.startTime=Start Time
oscarEncounter.Index.endTime=End Time
oscarEncounter.Index.pasteTimer=Paste timer data
oscarEncounter.Index.toggleTimer=Start/Pause timer

oscarEncounter.Header.nextAppt = Next Appt
oscarEncounter.Header.nextApptMsg = Click to see appointment history
oscarEncounter.Header.Calculators = Calculators
oscarEncounter.Header.OntMD = OntarioMD
oscarEncounter.Header.Templates =Templates

oscarEncounter.LeftNavBar.Labs=Lab Result
oscarEncounter.LeftNavBar.AllLabs=All
oscarEncounter.LeftNavBar.LabMenuHeading=Lab Displays
oscarEncounter.LeftNavBar.LabMenuItem1=Grid Display
oscarEncounter.LeftNavBar.LabMenuItem2=Row Display
oscarEncounter.LeftNavBar.ErrorH3=Error Loading module
oscarEncounter.LeftNavBar.Messages=Messenger
oscarEncounter.LeftNavBar.AddFrm=Add Form
oscarEncounter.LeftNavBar.AddEForm=Add eForm
oscarEncounter.LeftNavBar.InputGrps=Input Groups
oscarEncounter.LeftNavBar.Consult=Consultations
oscarEncounter.LeftNavBar.DxRegistry=Disease Registry
oscarEncounter.LeftNavBar.Prevent=Preventions
oscarEncounter.LeftNavBar.ProgressSheet=Progress Sheets
oscarEncounter.LeftNavBar.ProgressSheetPrint=Progress Sheets printout
oscarEncounter.LeftNavBar.BillingSheetPrint=Billing Sheets printout
oscarEncounter.LeftNavBar.Relations=Relations
oscarEncounter.LeftNavBar.msgLoading = Loading ...
oscarEncounter.NavBar.Allergy=Allergies
oscarEncounter.NavBar.Prescriptions=Prescriptions
oscarEncounter.NavBar.Medications=Medications
oscarEncounter.NavBar.OtherMeds=Other Meds
oscarEncounter.NavBar.ChronicBilling=Chronic Billing
oscarEncounter.NavBar.Reports=Reports
oscarEncounter.NavBar.unresolvedIssues=Unresolved Issues
oscarEncounter.NavBar.resolvedIssues=Resolved Issues
oscarEncounter.LeftNavBar.Diagrams=Diagrams
oscarEncounter.LeftNavBar.DiagramsMenuHeading=Diagrams
oscarEncounter.NavBar.OcularMeds=Ocular Meds
oscarEncounter.LeftNavBar.PHR=PHR
oscarEncounter.LeftNavBar.Tracker=Health Tracker
oscarEncounter.LeftNavBar.rDoc=Referral Doctor
oscarEncounter.LeftNavBar.fDoc=Family Doctor

oscarEncounter.accessDenied = You have no rights to access the data!
oscarEncounter.concurrencyError.title=Error Saving Encounter
oscarEncounter.concurrencyError.errorMsg=Somebody else is working with the same encounter.  Copy your work into a text editor, close OSCAR, and restart it when nobody else is working with the encounter.  Then copy your work back into the encounter.
oscarEncounter.pdfPrint.title=Documentation for
oscarEncounter.pdfPrint.gender=Gender:
oscarEncounter.pdfPrint.dob=Date of Birth:
oscarEncounter.pdfPrint.age=Age:
oscarEncounter.pdfPrint.city=City:
oscarEncounter.pdfPrint.bandNumber=Band Number:
oscarEncounter.pdfPrint.mrp=MRP:
oscarEncounter.pdfPrint.mrn=MRN:
oscarEncounter.pdfPrint.hin=HIN:
oscarEncounter.pdfPrint.homePhone=Home Phone:
oscarEncounter.pdfPrint.workPhone=Work Phone:
oscarEncounter.pdfPrint.cellPhone=Cell Phone:

oscarEncounter.socHistory.title =Social History
oscarEncounter.medHistory.title =Medical History
oscarEncounter.onGoing.title =Ongoing Concerns
oscarEncounter.reminders.title =Reminders
oscarEncounter.oMeds.title = Other Meds
oscarEncounter.famHistory.title = Family History
oscarEncounter.riskFactors.title = Risk Factors
oscarEncounter.change.title = Change
oscarEncounter.assign.title = Assign
oscarEncounter.startdate.title = Start Date
oscarEncounter.resolutionDate.title = Resolution Date
oscarEncounter.ageAtOnset.title = Age at Onset
oscarEncounter.treatment.title = Treatment
oscarEncounter.procedureDate.title = Procedure Date
oscarEncounter.problemStatus.title = Problem Status
oscarEncounter.exposureDetail.title = Exposure Details
oscarEncounter.relationship.title = Relationship
oscarEncounter.lifestage.title = Life Stage
oscarEncounter.hidecpp.title = Hide CPP
oscarEncounter.hideFromPrint.title=Hide from Print
oscarEncounter.addFromDxReg.title=Add Issue From Disease Registry
oscarEncounter.problemdescription.title = Problem Description
oscarEncounter.lifestage.opt.notset = Not Set
oscarEncounter.lifestage.opt.newborn = Newborn: Birth to 28 days
oscarEncounter.lifestage.opt.infant = Infant: 29 days to 2 years
oscarEncounter.lifestage.opt.child = Child: 2 years to 15 years
oscarEncounter.lifestage.opt.adolescent = Adolescent: 16 to 17
oscarEncounter.lifestage.opt.adult = Adult: 18 years
oscarEncounter.closeWithoutSave.msg = Are you sure you wish to close the encounter? Any unsaved data WILL BE LOST
oscarEncounter.templateError.msg = Inserting template failed
oscarEncounter.unsavedNoteWarning.msg = Your current note has not been saved.  Click OK to save it or Cancel to continue editing the current note.
oscarEncounter.sessionExpiredError.msg = Session Expired
oscarEncounter.unlockNoteError.msg = An error occurred while unlocking note
oscarEncounter.filterError.msg = An error occurred while performing your filter request
oscarEncounter.pastObservationDateError.msg = Observation date must be in the past
oscarEncounter.encounterTimeMandatory.msg = Encounter time and transportation time must be specified
oscarEncounter.encounterTimeError.msg = Encounter time and transportation time must be digital number only
oscarEncounter.assignIssueError.msg = At least one issue must be assigned to your note
oscarEncounter.assignObservationDateError.msg = An observation date must be set for your note
oscarEncounter.assignEncTypeError.msg = An encounter type must be set for your note
oscarEncounter.savingNoteError.msg = Failed to save note!
oscarEncounter.updateIssueError.msg = An error occurred while updating issues. Most likely your session has expired. \\nLog in again.  If problem persists, contact support
oscarEncounter.pickIssue.msg = Please select an issue from the auto completion menu
oscarEncounter.unsavedNote.msg = There is an unsaved note for this client.  Click Ok to edit it.
oscarEncounter.printDate.msg = Both start date and end date must be specified
oscarEncounter.printDateOrder.msg = Beginning date must precede end date
oscarEncounter.draftSaved.msg = Draft Saved
oscarEncounter.nothingToPrint.msg = Nothing selected to print
oscarEncounter.editUnsignedNote.msg = This note has not been signed by the owner.  Are you sure you want to continue to edit?
oscarEncounter.problemStatusExample.msg = (active/resolved/...)
oscarEncounter.providers.title = Providers
oscarEncounter.provider.title = Provider
oscarEncounter.program.title = Program
oscarEncounter.roles.title = Roles
oscarEncounter.role.title = Role
oscarEncounter.sort.title = Sort
oscarEncounter.issues.title = Issues
oscarEncounter.sortAll.title = All
oscarEncounter.sortDateAsc.title = Encounter Date Asc
oscarEncounter.sortDateDesc.title = Encounter Date Desc
oscarEncounter.showView.title = Show View
oscarEncounter.resetFilter.title = Reset Filter
oscarEncounter.viewFilter.title = View Filter
oscarEncounter.Filter.title = Filter
oscarEncounter.print.title = Print
oscarEncounter.noteFrom.label = Note from
oscarEncounter.togglePrintCPP.title = Toggle Print CPP
oscarEncounter.cpp.title = CPP
oscarEncounter.togglePrintRx.title = Toggle Print Rx
oscarEncounter.togglePrintLabs.title = Toggle Print Labs
oscarEncounter.togglePrintPreventions.title = Toggle Print Preventions
oscarEncounter.Rx.title = Rx
oscarEncounter.Labs.title = Labs
oscarEncounter.Preventions.title = Preventions
oscarEncounter.Documents.title = Documents
oscarEncounter.Hrm.title = HRM
oscarEncounter.togglePrintNote.title = Toggle Print Note
oscarEncounter.MinDisplay.title = Minimize Display
oscarEncounter.MaxDisplay.title = Maximize Display
oscarEncounter.edit.msgEdit = Edit
oscarEncounter.eSend = eSend
oscarEncounter.eSendTitle = Send Electronically
oscarEncounter.view.docView = View Document
oscarEncounter.view.rxView = View
oscarEncounter.view.eformView = View eForm
oscarEncounter.view = View
oscarEncounter.noteRev.title = Rev
oscarEncounter.editors.title = Editors
oscarEncounter.assignedIssues.title = Assigned Issues
oscarEncounter.date.title = Date
oscarEncounter.encounterDate.title = Encounter Date
oscarEncounter.encounterTime.title = Encounter Time (hour:min)
oscarEncounter.encounterTransportation.title = Encounter Transportation Time (hour:min)
oscarEncounter.encType.title = Enc Type
oscarEncounter.faceToFaceEnc.title = face to face encounter with client
oscarEncounter.telephoneEnc.title = telephone encounter with client
oscarEncounter.emailEnc.title = email encounter with client
oscarEncounter.noClientEnc.title = encounter without client
oscarEncounter.groupFaceEnc.title = group face to face encounter
oscarEncounter.groupTelephoneEnc.title = group telephone encounter
oscarEncounter.groupNoClientEnc.title = group encounter without group of clients
oscarEncounter.referenceIssues.title = Reference Issues
oscarEncounter.referenceUnresolvedIssues.title = Reference Unresolved Issues
oscarEncounter.referenceResolvedIssues.title = Reference Resolved Issues
oscarEncounter.removedIssue.Msg = Removed following issue(s)
oscarEncounter.futureDate.Msg = Observation Date set in future, rolled back to current time
oscarEncounter.noteReason.TelProgress = Tel-Progress Note
oscarEncounter.noteHistory.title = Note Revision History
oscarEncounter.history.title = History
oscarEncounter.quickChart.msg = View Quick Chart
oscarEncounter.fullChart.msg = View Full Chart
oscarEncounter.integrator.NA=Note: Integrator is not available at this time
oscarEncounter.integrator.outOfSync=Note: Integrated Community is not synced
oscarEncounter.noteLockError.Msg = This note was already saved in another window.\\nPlease close the encounter and open it again if you wish to edit this note.
oscarEncounter.blankNoteError.Msg = Note cannot be blank.


oscarEncounter.noteBrowser.title=Note Browser
oscarEncounter.noteBrowser.msgView=View
oscarEncounter.noteBrowser.msgViewStatus=View Status
oscarEncounter.noteBrowser.msgAll=All
oscarEncounter.noteBrowser.msgDeleted=Deleted
oscarEncounter.noteBrowser.msgPublished=Published
oscarEncounter.noteBrowser.msgOnlyPDFCanBeCombined= Only PDFs can be combined.
oscarEncounter.noteBrowser.msgSortDate=Sort Date
oscarEncounter.noteBrowser.msgUpdate=Update
oscarEncounter.noteBrowser.msgObservation=Observation
oscarEncounter.noteBrowser.msgContent= Content
oscarEncounter.noteBrowser.msgFileButNotAcknowledgedOn=Filed but not Acknowledged on
oscarEncounter.noteBrowser.msgNotAcknowledgeSince=Not Acknowledged since
oscarEncounter.noteBrowser.msgAcknowledgedOn=Acknowledged on
oscarEncounter.noteBrowser.DocumentUpdated=Document Updated
oscarEncounter.noteBrowser.ObservationDate=Observation Date
oscarEncounter.noteBrowser.Type=Type
oscarEncounter.noteBrowser.Class=Class
oscarEncounter.noteBrowser.Subclass=Subclass
oscarEncounter.noteBrowser.Description=Description
oscarEncounter.noteBrowser.Creator=Creator
oscarEncounter.noteBrowser.Responsible=Responsible
oscarEncounter.noteBrowser.Reviewer=Reviewer
oscarEncounter.noteBrowser.Source=Source
oscarEncounter.noteBrowser.ObservationTypeDescription=&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Observ.&nbsp;&nbsp;&nbsp;[type] Description
oscarEncounter.noteBrowser.msgNoteLocked=Note Locked
oscarEncounter.noteBrowser.encounterNote=Encounter Note
oscarEncounter.noteBrowser.accessDenied = You have no rights to access the data!
oscarEncounter.noteBrowser.msgAddTickler=Add Tikler
oscarEncounter.noteBrowser.msgDelete=Delete
oscarEncounter.noteBrowser.msgUndelete=Undelete
oscarEncounter.noteBrowser.msgAnnotate=Annotate
oscarEncounter.noteBrowser.msgEdit=Edit
oscarEncounter.noteBrowser.msgRefile=Refile

admin.admin.page.title=Administration Panel
admin.admin.UserManagement=User Management
admin.admin.LabsInbox=Labs/Inbox
admin.admin.FormsEforms=Forms/eForms
admin.admin.eChart=eChart
admin.admin.SystemManagement=System Management
admin.admin.SystemReports=System Reports
admin.admin.ScheduleManagement=Schedule Management
admin.admin.Integration=Integration
admin.admin.Status=Status
admin.admin.DataManagement=Data Management
admin.admin.SelectForms=Select Forms
admin.admin.ImportFormData=Import Form Data
admin.admin.DiseaseRegistry=Disease Registry
admin.admin.CreateProvider=Create Provider
admin.admin.EditDeleteProvider=Edit/Delete Provider
admin.admin.CreateLogin=Create Login
admin.admin.EditDeleteLogin=Edit/Delete Login
admin.admin.RoleAssignments=Role Assignments
admin.admin.SystemStatus=System Status
admin.admin.FaxStatus=Fax Status
admin.admin.OSCARBackup=OSCAR Backup
admin.admin.MigrateContacts=Migrate Contacts
admin.admin.MyOSCAR=MyOSCAR
admin.admin.DrugRef=DrugRef
admin.admin.bcQuickBilling=BC MSP Quick Billing
admin.admin.bcMethadoneBilling=BC Methadone Billing
admin.admin.bcMethadoneBillingPrivate=BC Methadone Billing - Private
admin.admin.bcMethadoneBillingMSP=BC Methadone Billing - MSP
admin.admin.PrivateBillingStatement=Private Billing Statement

admin.admin.oscarEncounter.consult.appointmentIntructions=Customize Consult Appointment Instructions
admin.admin.lookUpLists=Manage Lookup Lists
admin.admin.lookuplists.title=Lookup List Manager
admin.admin.manageRx=Rx Settings
admin.admin.managaeReferrelSources=Referrel Settings
admin.admin.mandatoryMasterFileFields=Mandatory Demographic Fields
admin.admin.echartDisplaySettings=eChart Settings
admin.admin.manageEchartSettings=Manage eChart Settings
admin.admin.echartDisplaySettings.caisiNoteFilter=Remove Notes Filter
admin.admin.manageEchartSettingsPasteFaxNote=Automatically paste note into encounter upon triggering fax from a Consultation, Prescription, and eForm
admin.admin.echartDisplaySettings.displayAppointmentTime=Display Appointment Time
admin.admin.echartDisplaySettings.displayRosterStatus=Display Roster Status
admin.admin.echartDisplaySettings.sortMeasurementsInEChart=Sort Measurements alphabetically in eChart and Measurement Groups
admin.admin.echartDisplaySettings.displayOnlyOverduePreventions=Display Only Overdue Preventions
admin.admin.echartDisplaySettings.displayRecordedOutsideUse=Display Recorded Outside Use in Encounter Roll
admin.admin.echartDisplaySettings.displayLargerFontSize=Display Larger Font Size
admin.admin.echartDisplaySettings.hideEformNotes=Hide E-Form Notes
admin.admin.echartDisplaySettings.hideDocumentNotes=Hide Document Notes
admin.admin.echartDisplaySettings.hideInvoiceNotes=Hide Invoice Notes
admin.admin.echartDisplaySettings.hideFormNotes=Hide Form Notes
admin.admin.echartDisplaySettings.hideCppNotes=Hide CPP Notes
admin.admin.echartDisplaySettings.groupDocumentByType=Group Documents by Document Type
admin.admin.echartDisplaySettings.displayHealthInsuranceNumber=Display Health Insurance Number
admin.admin.customHcTypes=Custom healthcard Types
admin.admin.masterFileFields=Customize Masterfile Fields
admin.admin.generalSettings=Edit General Settings
admin.admin.lookuplists.nonfound=No List(s) Found.
admin.admin.richTextLetter=Rich Text Letter
admin.admin.title=ADMIN PAGE
admin.admin.DocumentCategories = Document Categories
admin.admin.DocumentDescriptionTemplate= Document Description Template
global.msgInputKeyword=You forgot to input a keyword!
admin.admin.description=ADMINISTRATIVE PAGE
global.btnLogout=Log Out
global.btnLogoutShortcut=81
admin.admin.addRole = Add A Role
admin.admin.assignRole = Assign Role to Provider
admin.admin.assignRightsObject = Assign Role/Rights to Object
admin.admin.securityLogReport = Security Log Report
admin.admin.traceabilityReport = Generate Traceability Report
admin.admin.downloadTraceabilityData = Download Traceability Data from this OSCAR
admin.admin.downloadEmpty = Please Enter Traceability Data File
admin.admin.unlockAcct = Unlock Account
admin.admin.titleFactorAuth = 2 Factor Authentication
admin.admin.ipFilter = Set IP filter (no super certificate)
admin.admin.setCert = Set super certificate
admin.admin.genCert = Generate super certificate
admin.admin.clearCookie = Clear user cookie and super-cert cookie
admin.admin.adminSecQuestions = Administrate security questions
admin.admin.adminSecPolicies = Administrate security policies
admin.admin.removeBans = Remove bans
admin.admin.genMatrixCards = Generate matrix cards
admin.admin.FacilitiesMsgs = Facility Messages
admin.admin.LookupFieldEditor = Lookup Field Editor
admin.admin.linkTrackingRpt=Link Tracking Report
admin.admin.ManageBillFrm = Manage Billing Form
admin.admin.ManagePrivFrm = Manage Private Bill
admin.admin.ManageBillCodes = Manage Billing Codes
admin.admin.ManageServiceDiagnosticCodeAssoc = Manage Service/Diagnostic Code Associations
admin.admin.ManageProcedureFeeCodeAssoc = Manage Procedure/Fee Code Associations
admin.admin.ManageReferralDoc = Manage Referral Doctors
admin.admin.SimulateSubFile = Simulate Submission File
admin.admin.genTeleplanFile = Generate Teleplan File
admin.admin.simulateSubFile2 = Simulate Submission File2
admin.admin.genTeleplanFile2 = Generate Teleplan File2
admin.admin.manageTeleplan = Manage Teleplan
admin.admin.uploadRemittance = Upload Remittance Files
admin.admin.reconciliationReports = MSP Reconcilliation Reports
admin.admin.AccountingRpts = Accounting Reports
admin.admin.editInvoices = Edit Invoices
admin.admin.settlePaidClaims = Settle Over/Under Paid Claims
admin.admin.MSPFacilityMapping = MSP Facility Mapping
admin.admin.MSPFacilityMappingBlackWhite = MSP Facility Mapping Provider List
admin.admin.monerisUpload = Moneris upload
admin.admin.scheduleOfBenefits = Upload Schedule Of Benefits
admin.admin.manageBillingServiceCode = Manage Billing Service Code
admin.admin.managePrivBillingCode = Manage Private Billing Code
admin.admin.manageCodeStyles=Manage Service Code Display Styles
admin.admin.manageGSTControl = Manage GST Control
admin.admin.manageHSTControl = Manage HST Control
admin.admin.gstReport = GST Report
admin.admin.hstReport = HST Report
admin.admin.manageFeeSplit = Manage Provider Fee Splits
admin.admin.mcedt=MCEDT Interface
admin.admin.mcedt.mailbox=MCEDT Mailbox
admin.admin.uploadMOHFile = Upload MOH files
admin.admin.viewMOHFiles = View MOH files
admin.admin.invoiceRpts = Invoice Reports
admin.admin.endYearStatement = End Year Statement
admin.admin.managePaymentType = Manage Payment Type
admin.admin.manageAutoBillingRules = Manage Rules
admin.admin.pendingAutoBilling = Manage Pending Billings
admin.admin.editBillPaymentList = Edit payment list
admin.admin.rptbyTemplate = Report by Template
admin.admin.psRxPrintout = Rx Printout
admin.admin.psBillingPrintout = Billing Sheet Printout
admin.admin.psUsageReport = Usage Report
admin.admin.rehabStudy = Rehab Study
admin.admin.exportPatientbyAppt = Patient List by Appointment Time
admin.admin.activityRpt = Activity Report
admin.admin.providerServiceRpt = Provider Service Report
admin.admin.popRpt = Population Report
admin.admin.cdsRpt = CDS Report
admin.admin.misRpt = MIS Report
admin.admin.ocanRpt = OCAN Export
admin.admin.ocanIarRpt = OCAN/IAR
admin.admin.ocanReporting=OCAN Reports
admin.admin.cbiSubmit=CBI Submit Manually
admin.admin.cbi.reportlink=CBI Upload Report
admin.admin.cbiRpt=CBI CAISI Report
admin.admin.usageRpt = Usage Report
admin.admin.serverLog = Server Logging
admin.admin.frmGroups = eForm Groups
admin.admin.frmIndependent = Patient-independent eForm
admin.admin.billingreferralAdmin = Referral Doctors Admin
admin.admin.consultationSettings = Consultation Settings
admin.admin.professionalSpecialistAdmin = Professional Specialist/External Providers Admin
admin.admin.referralSearchName = Name
admin.admin.referralNo = Referral No
admin.admin.clinicAdmin = Clinic/Agency Address
admin.admin.sitesAdmin = Satellite-sites Admin
admin.admin.DemoExport = Demographic Export
admin.admin.DemoImport = Import New Demographic
admin.admin.manageDemographicGroups = Manage Demographic Groups
admin.admin.manageFacilities = Manage Facilities
admin.admin.sendOruR01 = Send data electronically to another OSCAR
admin.admin.mergeRec = Merge Patient Records
admin.admin.PHCP = PHCP
admin.admin.dx = dx
admin.admin.oldLabUpload = Lab Upload
admin.admin.hl7LabUpload = HL7 Lab Upload
admin.admin.uploadDocument = Doc Upload
admin.admin.keyPairGen = Key Pair Generator
admin.admin.labFwdRules = Lab Forwarding Rules
admin.admin.hsfoSubmit = schedule HSFO XML resubmit
admin.admin.provider=Provider
admin.admin.btnAddProvider=Add a Provider Record
admin.admin.btnSearchProvider=Search/Edit/Delete Provider Records
admin.admin.groupNo=Group No
admin.admin.btnAddGroupNoRecord=Add a Group
admin.admin.btnSearchGroupNoRecords=Manage Groups
admin.admin.btnCustomTemporaryGroup=Custom Temporary Group
admin.admin.btnGroupNoAcl=Access Control
admin.admin.btnGroupPreference=Add/Edit Group Preferences
admin.admin.preference=Preference
admin.admin.btnAddPreference=Add a Preference Record for a User
admin.admin.btnSearchPreference=Search/Edit/Delete Preference Records
admin.admin.security=Security
admin.admin.btnAddLogin=Add a Login Record
admin.admin.btnSearchLogin=Search/Edit/Delete Security Records
admin.admin.schedule=Schedule
admin.admin.scheduleSettingTitle=Holiday and Schedule Setting
admin.admin.scheduleSetting=Schedule Setting
admin.admin.daySheetConfiguration=Day Sheet Configuration
admin.admin.billing=Billing
admin.admin.btnAddBillingLocation=Add Billing Location
admin.admin.btnManageBillingForm=Manage Billing Form
admin.admin.btnSimulationOHIPDiskette=Simulation OHIP File
admin.admin.btnGenerateOHIPDiskette=Generate OHIP File
admin.admin.btnBillingCorrection=Billing Correction
admin.admin.btnINRBatchBilling=INR Batch Billing
admin.admin.btnBatchBilling=Batch Billing
admin.admin.btnCodeBatchBilling=Batch Billing by Code
admin.admin.btnBillingReconciliation=Billing Reconciliation
admin.admin.btnBillingReconciliationSettings=Billing Reconciliation Settings
admin.admin.btnEDTBillingReportGenerator=EDT Billing Report Generator
admin.admin.demographic=Demographic
admin.admin.btnAddDemographicRecord=Add a Demographic Record
admin.admin.btnSearchDemographicRecord=Search/Edit/Delete Demographic Records
admin.admin.resource=Resource
admin.admin.baseURLSettingTitle=Base URL Setting
admin.admin.btnBaseURLSetting=Help Link Setting
admin.admin.oscarReport=Reports
admin.admin.btnQueryByExample=Query By Example
admin.admin.btnAgeSexReport=Age-Sex Report
admin.admin.btnVisitReport=Visit Report
admin.admin.btnPCNCatchmentReport=PCN Catchment Report
admin.admin.btnFluBillingReport=Flu Billing Report
admin.admin.btnOvernightChecking=Overnight Batch Eligibility Checking
admin.admin.btnOBECGenerator=OBEC Response Report Generator
admin.admin.oscarBackup=Backup
admin.admin.btnAdminBackupDownload=Database/Document Download
admin.admin.oscarMessenger=Messenger
admin.admin.btnMessengerAdmin=Messenger Group Admin
admin.admin.eForms=eForms
admin.admin.fieldNoteReport=Field Note Report & Management
admin.admin.btnUploadForm=Upload eForm
admin.admin.btnUploadImage=Upload an Image
admin.admin.btnUpdatePatientProvider=Update Patient Provider
admin.admin.btnInsertTemplate=Insert a Template
admin.admin.btnechartAlert=eChart Alert
admin.admin.allergyAlert=Allergy Alert
admin.admin.btnStudy=Study
admin.admin.btnEaaps=Electronic Asthma Management Export
admin.admin.report.SurveillanceReport=Surveillance Report
admin.admin.diseaseRegister=Disease Registry Report
admin.admin.caisi=CAISI
admin.admin.systemMessage=System Messages
admin.admin.issueEditor=Issue Editor
admin.admin.caisiRole=Roles
admin.admin.surveyManager=User Created Forms
admin.admin.defaultEncounterIssue=Default Encounter Issue
admin.admin.defaultEncounterIssue.issuenotempty=Issues can't be empty!
admin.admin.caisiTicklerSettings=Tickler Settings
admin.admin.AddNewQueue=Add New Queue
admin.admin.changeInboxDateSearch=Change Inbox Date Search
admin.admin.changeInboxAutoFlagSettings=Auto Flag Setting
admin.admin.incomingLabCreateDemographicSettings=Incoming Lab Demographic Creation Settings
admin.admin.triggerSettings=Trigger Settings
admin.admin.triggerLogs=Trigger Log
admin.admin.triggerList=Trigger List
admin.admin.changeInboxAutoFlagAlwaysToMrpOnDocuments=Auto Flag MRP When Assigning Patient Documents
admin.admin.UpdateDrugref=Update Drugref
admin.admin.TiaHealth=YourCare
admin.admin.AuthServiceConfig=Pro Auth Settings
admin.admin.SecurityRecordIntegration=Set Security Record Integration Password
admin.admin.Know2ActConfig=Know2Act Configuration
admin.admin.Know2ActNotifications=Notifications from Know2Act
admin.admin.Know2ActNotifications.readmore=Read More
admin.admin.Know2ActNotifications.archive=Archive
admin.phr.initbtn=Initialize
admin.phr.clinicUsername=Clinic PHR Username
admin.phr.clinicPassword=Clinic PHR Password
admin.phr.active=PHR is active
admin.admin.flowsheetManager=Manage Flowsheets
admin.admin.phrconfig=PHR Configuration
admin.admin.oscar_phr_sync_config=Oscar PHR Sync Configuration
admin.admin.oscar_phr_clinic_config=Oscar PHR Clinic Configuration
admin.admin.oscar_phr_sync_config_must_be_logged_in=Please login to PHR first.
admin.admin.preventionNotification.title=Prevention Notification Settings
admin.admin.migrate_contacts=Migrate Contacts
admin.admin.setProviderAvailabilities=Set Provider Availabilities
admin.admin.eformReportTool=EForm Report Tool
admin.admin.born=BORN
admin.admin.ClinicalConnectConfig=Clinical Connect Configuration
admin.admin.dynacareEorder=Dynacare eOrder Ontario
admin.admin.encounterType=Customize Encounter Types
admin.admin.ConfigureCareConnect=Configure CareConnect
admin.systemManagement.editGeneralSettings.allowDemographicsToBeAssignedToSites=Allow Demographics to be Assigned to Site/Location (multisite only)
admin.systemManagement.editGeneralSettings.enableValidationOnSpecialist=Enable Additional Validation when creating or editing a Specialist entry
admin.systemManagement.customizeMasterfileFields.populateChartNoWithDemographicNo=Automatically Populate Demographic Number in Chart Number Field on Demographic Creation
admin.systemManagement.editGeneralSettings.requireCpsidAndDoctorForMrp=Display only providers with CPSID under MRP in Master Record:
admin.admin.cvc=Configure/Update CVC
admin.integration.well.ai.voice.menu=WELL AI Voice
admin.integration.well.ai.voice.2.menu=Nexus AI
admin.integration.well.ai.voice.title=WELL AI Voice
admin.integration.well.ai.voice.2.title=Nexus AI
admin.integration.well.ai.voice.1.0.enable=Enable WELL AI Voice Widget (WAIV 1.0)
admin.integration.well.ai.voice.2.0.enable=Enable WELL AI Voice Widget (Nexus AI)
admin.integration.well.ai.voice.clinic.enable=Enable Nexus AI Scribe
admin.integration.well.ai.voice.2.0.url=WELL AI Voice URL (Nexus AI)

admin.systemManagement.editGeneralSettings.search_button_during_patient_search=When searching with empty search field during Patient Search:
admin.admin.insig=Insig
admin.k2a.active=Know2act is active in this system
admin.k2a.clinicName=Clinic Name 
admin.k2a.clinicName.reason=This will show in the users know2act profile
admin.k2a.initbtn=Initialize Know2Act
admin.k2a.preventionsListTitle=Preventions Available from Know2Act
admin.k2a.LUCodes=Limited Use Codes Available from Know2Act
admin.k2a.table.filename=Prevention File Name
admin.k2a.table.luCodeFilename=Limited Use Codes Filename
admin.k2a.table.dateCreated=Date Created
admin.k2a.table.createdBy=Created By
admin.k2a.load=Load
admin.k2a.loadMore=Load More
admin.k2a.confirmation=The Knowledge2Action service is designed to offer users a place to access and post general health information for educational purposes only, for themselves and anyone within their trusted network. The general health information furnished on this site is not intended to replace a formal consultation with a qualified health care professional. Knowledge2Action does not give medical advice, nor do we provide medical or diagnostic services. Knowledge2Action does not guarantee that the content covers all possible uses, direction, precautions, drug interactions, or adverse effects that may be associated with any therapeutic treatments. You may not rely on the application of any information on the Knowledge2Action Service as being applicable to your specific circumstances. Knowledge2Action does not assume any liability or responsibility for damages or injury to you, other persons, or property arising from any use of any information, idea, or instruction contained in the Knowledge2Action Service.\\n\\nKnowledge2Action does not endorse, support, represent or guarantee the completeness, truthfulness, accuracy, or reliability of any Content or communications obtained via the service or endorse any opinions expressed via the service. You understand that by using the Services, you may be exposed to content that might be offensive, harmful, inaccurate or otherwise inappropriate, or in some cases, postings that have been mislabeled or are otherwise deceptive. Under no circumstances will McMaster or Knowledge2Action be liable in any way for any content accessed, including, but not limited to, any errors or omissions in any Content, or any loss or damage of any kind incurred as a result of the use of any Content posted, emailed, transmitted or otherwise made available via the Services or broadcast elsewhere.\\n\\nBy agreeing to these terms you state that you hold all liability for content you access and download into your EMR.

admin.admin.ConfigureSMTPServer=Configure SMTP Server
admin.email.enableemail=Enable eOrder email notifications 
admin.email.servername=Server Name
admin.email.username=Username
admin.email.password=Password
admin.email.usetlsssl=Use TLS/SSL
admin.email.portnumber=Port Number
admin.email.replytoemail=Reply to Email
admin.email.displayname=Display Name
admin.email.testmsg=* Test <em>will send a test message to the Reply to Email address

admin.admin.appointmentSearchConfig=Appointment Search Configuration
admin.appointmentSearchConfig.provider=Provider
admin.appointmentSearchConfig.team=Team  		
admin.appointmentSearchConfig.add=Add
admin.appointmentSearchConfig.main=Main
admin.appointmentSearchConfig.apptCodes=Appointment Codes
admin.appointmentSearchConfig.apptTypes=Appointment Types


admin.admin.surveillanceConfig=Surveillance Configuration
admin.surveillance.config.loaded=Surveys Loaded
admin.surveillance.table.surveyName=Name
admin.surveillance.table.active=Active
admin.surveillance.heading.surveyDetails=Survey Details
admin.surveillance.config.loadedk2aSurveillanceConfigs=Available from K2A
admin.surveillance.table.author=Author
admin.k2a.save=Save
admin.k2a.update=Update	 

admin.admin.consentConfig=Consent Configuration

admin.fieldNote.addEform=Add this eForm as a Field Note
admin.fieldNote.back=Back
admin.fieldNote.close=Close
admin.fieldNote.getFieldNotes=Get Field Notes
admin.fieldNote.download=Download
admin.fieldNote.downloadReport=Download report as Word document
admin.fieldNote.eformSelected=eForms selected as Field Notes
admin.fieldNote.endDate=End Date
admin.fieldNote.enterCustomName=Enter custom eForm name for field note selection
admin.fieldNote.noEformAssigned=No eForm assigned as Field Note. Press [Select eForms] to assign.
admin.fieldNote.noFieldNote=No Field Note selected
admin.fieldNote.report=Field Note Report
admin.fieldNote.reset=Reset
admin.fieldNote.resetDates=Reset dates to default
admin.fieldNote.selectEformsButton=Select eForms
admin.fieldNote.selectEforms=Select eForms as Field Notes
admin.fieldNote.startDate=Start Date
admin.fieldNote.submit=Submit
admin.fieldNote.unselect=UnSelect
admin.fieldNote.unselectEform=Unselect this eForm from Field Notes
admin.fieldNote.view=View
admin.fieldNote.viewReport=View report online
admin.fieldNote.observerNoteCount=Observer/Supervisor Field Note count
admin.fieldNote.residentReports=Resident Reports
admin.fieldNote.observerSupervisor=Observer/Supervisor
admin.fieldNote.resident=Resident
admin.fieldNote.count=Field Note count
admin.fieldNote.total=Total

admin.jobs.title=Jobs Management
admin.jobtypes.title=Job Type Management
admin.admin.add_lot_nr.title=Add Prevention Lot number
admin.admin.add_lot_nr.description=Add Prevention Lot number
admin.admin.add_lot_nr.prevention=Prevention
admin.admin.add_lot_nr.lotnr=Lot Number
admin.admin.add_lot_nr.expdate=Expiry Date
admin.lotaddrecordhtm.btnlotAddRecord=Add
admin.lotaddrecord.title=ADD PREVENTION LOT NUMBER
admin.lotaddrecord.description=Add Prevention Lot number
admin.lotaddrecord.msgAdditionSuccess=Successful Addition of a Log number for selected prevention.
admin.lotaddrecord.msgAdditionFailure=Sorry, addition has failed.
admin.lotaddrecord.msgDuplicateLotnr=Sorry, the entered lot number already exists for that prevention.
admin.lotnrsearchrecordshtm.title=Search lot number by prevention
admin.lotnrsearchrecordshtm.description=Search lot number by prevention
admin.lotnrsearch.prevention=Prevention
admin.lotnrsearch.btnSubmit=submit
admin.lotnrsearchresults.title=Matching lot number records by prevention.
admin.lotnrsearchresults.description=Matching lot number records by prevention.
admin.lotnrsearchresults.btnSubmit=submit
admin.lotnrsearchresults.prevention=Prevention
admin.lotnrsearchresults.lotnr=Lot Number
admin.lotnrsearchresults.btnLastPage=Previous Page
admin.lotnrsearchresults.btnNextPage=Next Page
admin.lotnrsearchresults.msgClickForEditing=Please select by clicking on the lot number for editing.
admin.admin.delete_lot_nr.title=DELETE PREVENTION LOT NUMBER
admin.admin.delete_lot_nr.description=Delete Prevention Lot number
admin.lotdeleterecordhtm.btnlotDeleteRecord=Delete
admin.lotdeleterecord.msgDeletionSuccess=Successful Deletion of Log number for the selected prevention.
admin.lotdeleterecord.msgDeletionFailure=Sorry, deletion has failed.
admin.lotdeleterecord.msgNonExistentLotnr=Sorry, the lot number you are trying to delete does not exist for this prevention.
admin.admin.adddelete_lot_nr.msgMissingParams=Prevention and lot number must be entered!
admin.lotdeleterecord.description=Delete Prevention Lot number
admin.admin.integratorPush=Integrator Push Manager

admin.manageCodeStyles.sucess=Your style has been updated
admin.manageCodeStyles.noStyleError=You have not entered any style!
admin.manageCodeStyles.noStyleNameError=Please enter a name for your style.
admin.manageCodeStyles.confirmDelete=You are about to remove the selected style.  This will also reset all links to service codes with this style.\\r\\nDo you wish to continue?
admin.manageCodeStyles.ManualEnter=I know what I am doing
admin.manageCodeStyles.CurrentStyles=Current Styles
admin.manageCodeStyles.StyleText=Style Text:
admin.manageCodeStyles.NoneSelected=None Selected
admin.manageCodeStyles.Edit=Edit
admin.manageCodeStyles.Delete=Delete
admin.manageCodeStyles.StyleName=Style Name:
admin.manageCodeStyles.Instructions=To create a style, select from the options below:
admin.manageCodeStyles.Clear=Clear
admin.manageCodeStyles.FontSize=Font Size:
admin.manageCodeStyles.FontStyle=Font Style:
admin.manageCodeStyles.FontVariant=Font Variant:
admin.manageCodeStyles.FontWeight=Font Weight:
admin.manageCodeStyles.TextDecoration=Text Decoration:
admin.manageCodeStyles.TextColour=Text Colour:
admin.manageCodeStyles.BackgroundColour=Background Colour:
admin.manageCodeStyles.Save=Save
admin.manageCodeStyles.Example=The Brown Fox Jumped over 2 sunny moons
admin.manageCodeStyles.Apply=Apply

admin.provideraddrecordhtm.title=ADD A PROVIDER
admin.provideraddrecordhtm.description=Add a Provider
admin.provideraddrecordhtm.suggest=Suggest
admin.provider.formProviderNo=Provider No.
admin.provider.formLastName=Last Name
admin.provider.formFirstName=First Name
admin.provider.formType=Type (receptionist/doctor/nurse/resident/admin)
admin.provider.formType.optionReceptionist=receptionist
admin.provider.formType.optionDoctor=doctor
admin.provider.formType.optionNurse=nurse
admin.provider.formType.optionResident=resident
admin.provider.formType.optionAdmin=admin
admin.provider.formSpecialty=Specialty
admin.provider.formTeam=Team
admin.provider.formSex=Sex(F/M)
admin.provider.formDOB=DOB
admin.provideraddrecordhtm.dateFormat=yyyy-mm-dd
admin.provider.formAddress=Address
admin.provider.formHomePhone=Phone (home)
admin.provider.formWorkPhone=Phone (work)
admin.provider.formPager=Pager
admin.provider.formCell=Cell
admin.provider.formOtherPhone=Other Phone
admin.provider.formFax=Fax
admin.provider.formEmail=Email
admin.provider.formOhipNo=Provincial Billing/MSP #
admin.provider.formCPSID=CPSID #
admin.provider.formExcellerisID=ExcellerisID #
admin.provider.formLifeLabsID=LifeLabsID #
admin.provider.formCPSIDType=CPSID Type
admin.provider.formRmaNo=3rd Party Billing #
admin.provider.formBillingNo=Billing #
admin.provider.formHsoNo=Alternate Billing #
admin.provider.formStatus=Status
admin.provider.forcePasswordReset=Force Password Reset
admin.provideradmin.resourcebaseurl.btnAdd=Add a service code.
admin.provider.formSpecialtyCode=Specialty Code #
admin.provider.formBillingGroupNo=Group Billing #
admin.provider.formSlpUsername=Self Learning Username
admin.provider.formSlpPassword=Self Learning Password
admin.provider.formPractitionerNo=Practitioner Number
admin.provideraddrecordhtm.btnProviderAddRecord=Add Provider Record
AddProviderStatus.msgAddFailure=Sorry, addition has failed.
admin.provider.formClinicalConnectId=ClinicalConnect Username
admin.provider.formClinicalConnectType=ClinicalConnect Auth.Type
admin.provider.formOfficialFirstName=Official First Name
admin.provider.formOfficialSecondName=Official Second Name
admin.provider.formOfficialLastName=Official Last Name
admin.provider.formOfficialOlisIdentifierType=OLIS Identifier Type
admin.provider.formOfficialOlisIdentifierType.option.notset=Not Set
admin.provider.formOfficialOlisIdentifierType.option.mdl=Physician
admin.provider.formOfficialOlisIdentifierType.option.ddsl=Dentist
admin.provider.formOfficialOlisIdentifierType.option.npl=Nurse Practitioner
admin.provider.formOfficialOlisIdentifierType.option.ml=Midwife

admin.provideraddrecord.title=ADD A PROVIDER RECORD
admin.provideraddrecord.description=Add a provider
admin.provideraddrecord.msgAdditionSuccess=Successful Addition of a Provider Record.
admin.provideraddrecord.msgAdditionFailure=Sorry, addition has failed.
admin.provideraddrecord.msgAlreadyExists=Provider No already in use.
admin.provideraddrecord.msgGenericError=Something went wrong trying to add this provider
admin.provider.sitesAssigned=Sites Assigned
admin.provideraddrecord.msgFormalizeProviderIdFailure=Provider Number is out of range or invalid. Please change site range setting on Satellite-sites Admin or turn off validation on properties multioffice.formalize.provider.id
admin.provideraddrecord.msgFormalizeProviderIdMultiSiteFailure=Non-doctor role can not be assigned multi-sites. This validation can be turn off on properties multioffice.formalize.provider.id

global.btnBack=Back

displayMessages.title=Inbox

admin.providersearchrecordshtm.title=SEARCH PROVIDER RECORDS
admin.providersearchrecordshtm.description=Search a Provider
admin.search.formSearchCriteria=Search Criteria
admin.search.btnLastPage=Last Page
admin.search.btnNextPage=Next Page
admin.providersearch.formName=Name
admin.providersearch.formLastName=Last Name
admin.providersearch.formNo=Provider No.
admin.providersearch.formAllStatus=All
admin.providersearch.formActiveStatus=Active Only
admin.providersearch.formInactiveStatus=Inactive Only
admin.search.btnSubmit=Search
admin.providersearchrecordshtm.formReserved=Reserved
admin.providersearchrecordshtm.msgInstructions=Instructions will be provided later.

admin.providerdelete.title=DELETE A PROVIDER RECORD
admin.providerdelete.msgDeletionSuccess=Successful Deletion of a Provider Record
admin.providerdelete.msgDeletionFailure=Sorry, deletion has failed

admin.providersearchresults.title=PROVIDER LIST
admin.providersearchresults.description=Provider: the following records
admin.providersearchresults.btnSubmit=Submit
admin.providersearchresults.reserved=Reserved
admin.search.keywords=Results based on keyword(s)
admin.providersearchresults.ID=ID
admin.providersearchresults.phone=Phone
admin.providersearchresults.btnLastPage=Last Page
admin.providersearchresults.btnNextPage=Next Page
admin.providersearchresults.msgClickForEditing=Please select by clicking on the provider's id for editing.

admin.providerupdate.title=PROVIDERS
admin.providerupdate.description=Provider: the following records
admin.providerupdate.msgUpdateSuccess=Update a Provider Record Successfully !
admin.providerupdate.msgUpdateFailure=Sorry, failed to update !

admin.providerupdateprovider.title=UPDATE A PROVIDER RECORD
admin.providerupdateprovider.description=Update Provider Record
admin.providerupdateprovider.btnSubmit=Update Record
admin.providerupdateprovider.msgDeleteRecord=Delete the Record
admin.providerupdateprovider.cancel=Cancel

admin.admindisplaymygroup.title=MY GROUP
admin.admindisplaymygroup.description=My Group
admin.adminmygroup.formGroupNo=Group Name
admin.admindisplaymygroup.formProviderName=Provider's Name
admin.admindisplaymygroup.btnSubmit1=Save
admin.admindisplaymygroup.btnSubmit2=Add to Group

admin.adminnewgroup.title=NEW GROUP
admin.adminnewgroup.description=New Group
admin.adminnewgroup.btnSubmit=Save

admin.adminsavemygroup.title=ADD MYGROUP RECORDS
admin.adminsavemygroup.msgAdditionSuccess=<strong>Successful!</strong> group record has been added.
admin.adminsavemygroup.msgAdditionFailure=<strong>Sorry, </strong> group record has not been added.

admin.groupacl.title=GROUP ACCESS CONTROL
admin.groupacl.description=GROUP ACCESS CONTROL
admin.groupacl.btnSubmit=Save

admin.grouppref.title=GROUP PREFERENCES
admin.grouppref.selectBillingForm=Select Default Billing Form

admin.preferenceaddarecord.title=ADD A PREFERENCE FOR A USER
admin.preferenceaddarecord.description=Add a Preference for a User
admin.preference.formProviderNo= Provider No
admin.preference.formStartHour=Start Hour
admin.preference.formEndHour=End Hour
admin.preference.formPeriod=Period
admin.preference.inMin=in min.
admin.preference.formGroupNo=Group No
admin.preference.defaultForm=Default Billing Form

admin.preference.billing.generate_simulate=Generate/Simulate
admin.preference.billing.ohip_invoices=OHIP Invoices
admin.preference.billing.third_party_invoices=3rd Party Invoices
admin.preference.billing.invoice_report=Invoice Report
admin.preference.billing.mailbox=Mailbox
admin.preference.billing.view_moh_files=View MOH Files
admin.preference.billing.billing_reconciliation=Billing Reconciliation

admin.preferenceaddarecord.btnSubmit=Add Record
admin.preferenceaddarecord.msgSuccessful=Successful Update a Preference Record.

admin.preferenceaddpreference.title=Add a preference for a user
admin.preferenceaddpreference.description=ADD A PREFERENCE FOR A USER
admin.preferenceaddpreference.msgAdditionSuccess=Successful Addition of a Preference Record.
admin.preferenceaddpreference.msgAdditionFailure=Sorry, addition has failed.
admin.preferenceaddpreference.msgAccessFailure=Sorry, the provider no you are trying to access is in different site, access denied.

admin.preferencesearchrecordshtm.title=Search a Preference
admin.preferencesearchrecordshtm.description=SEARCH PREFERENCE RECORDS
admin.preferencesearchrecordshtm.reserved=Reserved
admin.preferencesearchrecordshtm.msgInstructions=Instructions will be provided later.

admin.preferencesearchresults.title=Preference: the following records
admin.preferencesearchresults.description=PREFEFENCE
admin.preferencesearchresults.instructions=Instructions will be provided later.
admin.preferencesearchresults.msgClickForDetail=Please select by clicking on the provider's no for details.

admin.preferenceupdatepreference.title=Update Preference Record
admin.preferenceupdatepreference.description=UPDATE A PREFERENCE RECORD
admin.preferenceupdatepreference.btnSubmit=Update Record
admin.preferenceupdatepreference.altImgDelete=Delete the Record

admin.preferenceupdate.title=Preference: the following records
admin.preferenceupdate.description=PREFERENCE
admin.preferenceupdate.msgUpdateSuccess=Update a Preference Record Successfully !
admin.preferenceupdate.msgUpdateFailure=Sorry, failed to update !

admin.preferencedelete.description=DELETE A PREFERENCE RECORD
admin.preferencedelete.msgDeletionSuccess=Successful Deletion of a Preference Record
admin.preferencedelete.msgDeletionFailure=Sorry, deletion has failed

admin.renal.managePatientLetter=Manage Patient Letter

admin.admin.appointmentTypeList=Appointment Type List
admin.admin.appointmentStatusSetting=Appointment Status Setting
admin.appt.status.mgr.title=Appointment Status Manager
admin.appt.status.mgr.label.status=Code
admin.appt.status.mgr.label.desc=Description
admin.appt.status.mgr.label.color=BG Color
admin.appt.status.mgr.label.oldcolor=Old Bg Color
admin.appt.status.mgr.label.newcolor=New Bg Color
admin.appt.status.mgr.label.enable=Status
admin.appt.status.mgr.label.active=Activation
admin.schedule.display.settings.enrollment=Display Enrollment Status (Roster) and Enrollment Physician name on schedule
admin.systemManagement.mandatoryDemographicFields.email=Make Email Mandatory
admin.systemManagement.generalSetting.displayPatientNameOnMessagePrint=Display linked demographic's name when printing message from eChart
admin.echartSettings.hideEncounterLink=Hide E-Chart Link on Search Patient Page, Master File, Document, Lab, HRM
admin.echartSettings.excludeMiscFromConsultationReports=Exclude Misc Data from Consultation Reports / Get OSCAR Data
oscar.appt.status.mgr.label.edit=Edit
oscar.appt.status.mgr.label.submit=Submit
oscar.appt.status.mgr.label.cancel=Cancel

provider.providerpreference.title=UPDATE PREFERENCES
provider.providerpreference.msgTypeNumbers=You must type in numbers in some fields.
provider.providerpreference.titlePopup=Receptmygroup
provider.providerpreference.description=PREFERENCE
provider.preference.formStartHour=Start Hour
provider.preference.formEndHour=End Hour
provider.preference.formPeriod=Period
provider.preference.min=min.
provider.providerpreference.viewedit=View/Edit Groups
provider.preference.formGroupNo=Group No
provider.preference.doctorType=doctor
provider.preference.defaultDoctor=Default Doctor
provider.preference.defaultPharmacy=Default Pharmacy
provider.preference.defaultCoverPage=Consultation Cover Page Default
provider.providerpreference.btnSubmit=Update
provider.preference.hideNoShowsCancellations=Hide No Shows/Cancellations
provider.providerpreference.qrCodeOnPrescriptions=Print Qr Codes on Prescriptions
provider.providerpreference.appointmentScreenLinkNameDisplayLength=Length of link and form names to display on appointment screen
provider.providerpreference.displaySelectedFormsInPatientSearchSection=display selected forms in patient search section
provider.providerpreference.formsToDisplayOnAppointmentScreen=Encounter Forms to display on appointment screen
provider.providerpreference.eFormsToDisplayOnAppointmentScreen=eForms to display on appointment screen
provider.providerpreference.quickLinksToDisplayOnAppointmentScreen=quick links to display on appointment screen
provider.providerpreference.showAppointmentReason=Show appointment reason for all providers
provider.providerpreference.twelveHourFormat=Show schedule in twelve hour format (am/pm)
provider.providerpreference.labelShortcutEnabled=Display label printing on appointments
provider.providerpreference.allowOnlineBooking=Allow Online Booking
provider.providerpreference.labelBillInsurance=Bill demographic's insurance company for 3rd party invoices
provider.providerpreference.ticklerDefaultAssignedProvider=Filter Ticklers assigned to you by default
provider.providerpreference.ticklerDefaultRecipient=Default Tickler Recipient
provider.providerpreference.rxInteractionWarningLevel=Minimum Rx Interaction Warning Level
provider.providerpreference.insigPortalScheduleLink=Enable Patient Portal Link on the Schedule
provider.providerpreference.pillwayHomeDelivery=Enable Pillway Home Delivery Button
provider.btnChangePassword=Change Your Password
provider.btnAddDeleteForm=Add/Delete a Form
provider.btnScheduleSetting=Schedule Setting
provider.btnManageClinicalResource=Manage Clinical Resource
provider.btnEditSignature=Edit your Signature
provider.btnManageSignatures=Manage Signature Stamp
provider.btnBillPreference=Edit Billing Preferences
provider.labelDefaultBillForm=Default Billing Form
provider.btnAddDeleteTemplate=Add/Delete a Template
provider.btnEditAddress=Set Address (RX/Consult/Prevention)
provider.btnEditPhoneNumber=Set Phone Number (RX/Consult/Prevention)
provider.btnEditFaxNumber=Set Fax Number (RX/Consult/Prevention)
provider.btnEditColour=Set Provider Colour
provider.btnSetDefaultPrinter=Set default printer for envelopes, labels, receipts
provider.btnEditStaleDate=Set Stale Date for Case Management Notes
provider.btnSetmyDrugrefID=Set myDrugref ID
provider.btnSetRxPageSize=Set Rx Script Page Size
provider.btnSetRx3=Set To Use Rx3
provider.configureSignAndSaveButtonInEchart=Configure Sign & Save Button in eChart
provider.btnSetCppSingleLine=Set To Enable CPP Single Line
provider.btnDisableAckCommentLab=Manage Comment Box When Acknowledging Labs
provider.btnLabRecallSettings=Lab Recall Settings
provider.btnEditDefaultEncounterWindowSize=Set Default Encounter Window Size
provider.btnEditDefaultQuickChartSize=Set Default Quick Chart Size
provider.btnEditSetPatientNameLength=Set Maximum Patient Name Length
provider.btnSetEDocBrowserInMasterFile=Set to Enable Document Browser in Master Record
provider.btnSetEDocBrowserInDocumentReport=Set to Enable Document Browser in Document Report (EDoc)
provider.btnEditClients=Manage API Clients
provider.btnSetDisplayDocumentAs=Set Display Document as PDF or Image
provider.btnSetDefaultInboxStartDateRange = Set Default Inbox Start Date Range
provider.btnSetDocumentDescriptionTemplate= Set Document Description Template
provider.btnSetHideNoShowsAndCancellations=Set to hide No Shows and/or Cancellations on the schedule

provider.hideOldEchartLinkInAppt.title=Hide Old Echart Link in Appointment
provider.hideOldEchartLinkInAppt.msgPrefs=Preferences
provider.hideOldEchartLinkInAppt.msgSuccess_selected=Old Echart Link Hidden in Appointment
provider.hideOldEchartLinkInAppt.msgSuccess_unselected=Old Echart Link Shown in Appointment
provider.btnHideOldEchartLinkInAppt=Hide Old Echart Link in Appointment
provider.hideOldEchartLinkInAppt.btnSubmit=Save
provider.hideOldEchartLinkInAppt.btnCancel=Cancel
provider.hideOldEchartLinkInAppt.btnClose=Close

provider.bornPrefs.title=BORN Preferences
provider.bornPrefs.msgPrefs=Preferences
provider.bornPrefs.msgSuccess_selected=BORN Preferences updated
provider.bornPrefs.msgSuccess_unselected=BORN Preferences updated
provider.bornPrefs=BORN Preferences
provider.bornPrefs.btnSubmit=Save
provider.bornPrefs.btnCancel=Cancel
provider.bornPrefs.btnClose=Close
provider.pref.bornPrompts=Disable RBR/NDDS Prompts
provider.btnEditSetAppointmentCardPrefs=Set Appointment Card Preferences
provider.btnViewBornPrefs=Set BORN Preferences (RBR/NDDS Prompts)
provider.btnSelectDefaultSearchModePrefs=Set Default Demographic Search Mode
provider.btnViewDashboardPrefs=Set Dashboard Preferences

provider.dashboardPrefs.title=Dashboard Preferences
provider.dashboardPrefs.msgPrefs=Preferences
provider.dashboardPrefs.msgSuccess_selected=Dashboard Preferences updated
provider.dashboardPrefs.msgSuccess_unselected=Dashboard Preferences updated
provider.dashboardPrefs=Dashboard Preferences
provider.dashboardPrefs.btnSubmit=Save
provider.dashboardPrefs.btnCancel=Cancel
provider.dashboardPrefs.btnClose=Close
provider.pref.dashboardShare=Send your metrics to Common Provider Dashboard
provider.setEDocBrowserInMasterFile.title=Select if you want to enable Document Browser in Master Record
provider.btnEditSetAppointmentCardPrefs=Set Appointment Card Preferences

provider.setEDocBrowserInMasterFile.title=Select if you want to enable Document Browser in Master File 
provider.setEDocBrowserInMasterFile.msgPrefs=Preferences
provider.setEDocBrowserInMasterFile.msgProfileView=Enable Document Browser in Master Record
provider.setEDocBrowserInMasterFile.msgEdit=Do you want to enable Document Browser in Master Record?
provider.setEDocBrowserInMasterFile.btnSubmit=Save
provider.setEDocBrowserInMasterFile.msgSuccess_selected=Document Browser in Master Record is enabled
provider.setEDocBrowserInMasterFile.msgSuccess_unselected=Document Browser in Master Record is disabled
provider.setEDocBrowserInMasterFile.msgEnableLink=Enable Document Browser in Master Record 

provider.setEDocBrowserInDocumentReport.title=Select if you want to enable Document Browser in Document Report (EDoc)
provider.setEDocBrowserInDocumentReport.msgPrefs=Preferences
provider.setEDocBrowserInDocumentReport.msgProfileView=Enable Document Browser in Document Report (EDoc)
provider.setEDocBrowserInDocumentReport.msgEdit=Do you want to enable Document Browser in Document Report (EDoc)
provider.setEDocBrowserInDocumentReport.btnSubmit=Save
provider.setEDocBrowserInDocumentReport.msgSuccess_selected=Document Browser in Document Report (EDoc) is enabled
provider.setEDocBrowserInDocumentReport.msgSuccess_unselected=Document Browser in Document Report (EDoc) is disabled
provider.setEDocBrowserInDocumentReport.msgEnableLink=Enable Document Browser in Document Report (EDoc)

provider.setCppSingleLine.title=Select if you want to enable CPP Single Line view
provider.setCppSingleLine.msgPrefs=Preferences
provider.setCppSingleLine.msgProfileView=Enable CPP Single Line view
provider.setCppSingleLine.msgEdit=Do you want to enable CPP Single Line view?
provider.setCppSingleLine.btnSubmit=Save
provider.setCppSingleLine.msgSuccess_selected=CPP Single Line view is enabled
provider.setCppSingleLine.msgSuccess_unselected=CPP Single Line view is disabled

provider.setAckComment.title=Select if you want to disable comment before acknowledging lab
provider.setAckComment.msgPrefs=Preferences
provider.setAckComment.msgProfileView=Disable Comment before Acknowledging Lab
provider.setAckComment.msgEdit=Do you want to disable the comment box before acknowledging labs?
provider.setAckComment.btnSubmit=Save
provider.setAckComment.msgSuccess_selected=Comment box is now disabled when acknowledging labs
provider.setAckComment.msgSuccess_unselected=Comment box is now enabled when acknowledging labs

provider.setLabRecall.title=Lab Recall Settings - Preferences
provider.setLabRecall.msgPrefs=Preferences
provider.setLabRecall.msgProfileView=Lab Recall Settings
provider.setLabRecall.msgEdit=
provider.setLabRecall.btnSubmit=Save
provider.setLabRecall.msgSuccess=Lab Recall Settings Saved!
provider.setLabRecall.msgDeleted=Lab Recall Settings Deleted!

provider.btnSetShowPatientDOB=Set to Show Patient's Date of Birth

provider.setRxRxUseRx3.title=Select if you want to use Rx3
provider.setRxRxUseRx3.msgPrefs=Preferences
provider.setRxRxUseRx3.msgProfileView=Use Rx3
provider.setRxUseRx3.msgEdit=Do you want to use Rx3?
provider.setRxUseRx3.btnSubmit=Save
provider.setRxUseRx3.msgSuccess_selected=Rx3 is selected
provider.setRxUseRx3.msgSuccess_unselected=Rx3 is NOT selected

provider.configureSignAndSaveButtonInEchart.title=Select if you want to configure Sign & Save Button In eChart
provider.configureSignAndSaveButtonInEchart.msgPrefs=Preferences
provider.configureSignAndSaveButtonInEchart.msgProfileView=Configure Sign & Save Button In eChart
provider.configureSignAndSaveButtonInEchart.msgEdit=Do you want disable the eChart from closing automatically when Sign & Save in clicked?
provider.configureSignAndSaveButtonInEchart.btnSubmit=Save
provider.configureSignAndSaveButtonInEchart.msgSuccess_selected=The eChart from closing automatically when Sign & Save is selected
provider.configureSignAndSaveButtonInEchart.msgSuccess_unselected=The eChart from closing automatically when Sign & Save is NOT selected

provider.setHarmonyID = Set Harmony ID
provider.setHarmonyID.msgEdit = Enter your desired ID for Harmony
provider.setHarmonyID.msgSuccess = Your Harmony ID is

provider.setShowPatientDOB.title=Select if you want to show patient's date of birth in prescription
provider.setShowPatientDOB.msgPrefs=Preferences
provider.setShowPatientDOB.msgProfileView=Show patient's date of birth in prescription
provider.setShowPatientDOB.msgEdit=Do you want to show patient's date of birth in prescription?
provider.setShowPatientDOB.btnSubmit=Save
provider.setShowPatientDOB.msgSuccess=Selection saved
provider.setShowPatientDOB.msgSuccess_selected=Show patient's date of birth is selected
provider.setShowPatientDOB.msgSuccess_unselected=Show patient's date of birth is NOT selected.

provider.btnSetmyOntarioMD=Set OntarioMD.ca username/password
provider.btnSetConsultationCutoffTimePeriod=Set Consultation Request Cutoff Time period
provider.btnSetConsultationTeam=Set Consultation Request Warning Team
provider.btnSetWorkLoadManagement=Set Workload Management
provider.btnSetIndivoId=Set PHR login ID
provider.btnSetConsultPasteFmt=Set Paste Format in Consultation Request
provider.btnSetEformGroup=Set Favourite eForm Group
provider.btnSetUseMyMeds=Set To Use MyMeds
provider.providerupdatepreference.description=UPDATE A PREFERENCE RECORD
provider.providerupdatepreference.msgUpdateFailure=Sorry, update has failed.

provider.setUseMyMeds.title=Select if you want to use MyMeds
provider.setUseMyMeds.msgPrefs=Preferences
provider.setUseMyMeds.msgProfileView=Use MyMeds
provider.setUseMyMeds.msgEdit=Do you want to use MyMeds?
provider.setUseMyMeds.btnSubmit=Save
provider.setUseMyMeds.msgSuccess=MyMeds Selection saved
provider.setUseMyMeds.msgSuccess_selected=MyMeds is selected
provider.setUseMyMeds.msgSuccess_unselected=MyMeds is unselected

provider.setDocumentDescriptionTemplate.title=Set Document Description Template
provider.setDocumentDescriptionTemplate.useClinicDefault=Use clinic default document description template
provider.setDocumentDescriptionTemplate.setClinicDefault=Set clinic default document description template
provider.setDocumentDescriptionTemplate.Type=Type
provider.setDocumentDescriptionTemplate.Description=Description
provider.setDocumentDescriptionTemplate.DescriptionShortcut=Description shortcut
provider.setDocumentDescriptionTemplate.Add=Add
provider.setDocumentDescriptionTemplate.Update=Update
provider.setDocumentDescriptionTemplate.Delete=Delete
provider.setDocumentDescriptionTemplate.DescriptionCannotBeEmpty= Document description or shortcut cannot be empty

provider.consultSignatureStamp.title=Set Signature Stamp (Rx/Consult)
provider.consultSignatureStamp.edit=Please upload your Signature file and press Update, recommended max size is 400px wide by 150px high

admin.securityaddarecord.title=Add a Login User
admin.securityaddarecord.description=ADD A LOGIN USER
admin.securityaddarecord.onlyInternet=(only for Internet access.)
admin.securityaddarecord.btnSubmit=Add Record

admin.securityrecord.formUserName=User Name
admin.securityrecord.formPassword=Password
admin.securityrecord.formConfirm=Confirm
admin.securityrecord.formProviderNo=Provider No.
admin.securityrecord.formExpiryDate=Expiry Date
admin.securityrecord.formDate=Date
admin.securityrecord.formRemotePIN=Pin(remote) Enable
admin.securityrecord.formLocalPIN=Pin(local) Enable
admin.securityrecord.formPIN=PIN
admin.securityrecord.msgAtLeast=at least
admin.securityrecord.msgSymbols=symbols
admin.securityrecord.msgDigits=digits
admin.securityrecord.msgIsRequired=is required.
admin.securityrecord.msgPasswordNotConfirmed=You have not confirmed your password. Please input your password again.
admin.securityrecord.msgPinNotConfirmed=You have not confirmed your pin. Please input your pin again.
admin.securityrecord.msgProviderAlreadyHasSecurityRec=We are sorry, the selected provider already has a security record. Please delete the security record first.

admin.securityaddsecurity.title=ADD A LOGIN USER
admin.securityaddsecurity.description=Add a login user record
admin.securityaddsecurity.msgAdditionSuccess=Successful Addition of a Login User Record.
admin.securityaddsecurity.msgAdditionSuccessPasswordForceReset=Successful Addition of a Login User Record. New password will be prompted for entry on login
admin.securityaddsecurity.msgAdditionFailure=Sorry, addition has failed.
admin.securityaddsecurity.msgAdditionFailureDuplicate=Sorry, addition has failed, that username is already in use.
admin.securityaddsecurity.msgLoginAlreadyExistsForProvider=Sorry, only one user name is allowed for each provider.

admin.securitysearchrecordshtm.title=Search security records
admin.securitysearchrecordshtm.description=SEARCH SECURITY RECORDS
admin.securitysearchrecordshtm.msgInstructions=Instructions will be provided later.
admin.securitysearchrecordshtm.reserved=Reserved
admin.securitysearchrecordshtm.msgCriteria=Search Criteria
admin.securitysearchrecordshtm.btnBack=Back to Admin Page.
admin.securitysearchrecordshtm.btnLogOut=Log Out

admin.securitysearchresults.title=SECURITY: THE FOLLOWING RECORDS
admin.securitysearchresults.description=Security
admin.securitysearchresults.reserved=Reserved
admin.securitysearchresults.btnLastPage=Last Page
admin.securitysearchresults.btnNextPage=Next Page
admin.securitysearchresults.msgClickForDetail=Please select by clicking on the user name for details.

admin.securityupdate.title=SECURITY: THE FOLLOWING RECORDS
admin.securityupdate.description=Security
admin.securityupdate.msgUpdateSuccess=Security Record Updated Successfully !
admin.securityupdate.msgUpdateFailure=Sorry, fail to update !!!
admin.securityupdate.msgAdditionFailureDuplicate=Sorry, update has failed, that username is already in use.

admin.securityupdatesecurity.title=OSCAR Project
admin.securityupdatesecurity.description=UPDATE A SECURITY RECORD
admin.securityupdatesecurity.msgFailed=failed
admin.securityupdatesecurity.btnSubmit=Update Record
admin.securityupdatesecurity.altImgDelete=Delete the Record

admin.securitydelete.description=Delete a security record
admin.securitydelete.msgDeletionSuccess=Successful Deletion of a Security Record
admin.securitydelete.msgDeletionFailure=Sorry, deletion has failed

password.policy.violation.msgPasswordLengthError=Password is too short, minimum length is
password.policy.violation.msgSymbols=symbols.
password.policy.violation.msgPasswordStrengthError=Password must contain at least
password.policy.violation.msgPasswordGroups=of the following: capital chars, lower chars, digits, special chars.
password.policy.violation.msgPinLengthError=PIN is too short, minimum length is
password.policy.violation.msgDigits=digits.
password.policy.violation.msgPinGroups=PIN must contain digits only.

provider.providerchangepassword.title=CHANGE PASSWORD
provider.providerchangepassword.description=Change your password
provider.providerchangepassword.msgOldPasswordError=Old Password is required.
provider.providerchangepassword.msgCurrPasswordError=Current Password is required.
provider.providerchangepassword.msgCurrPinError=Current PIN is required.
provider.providerchangepassword.msgPasswordConfirmError=The confirm password must be the same as the new password.
provider.providerchangepassword.msgPasswordAndPINBlank=Please fill in the password/PIN that you would like to update.
provider.providerchangepassword.msgPinConfirmError=The confirm PIN must be the same as the new PIN.
provider.providerchangepassword.msgInstructions=Enter your old password and then choose your new password. Click
provider.providerchangepassword.msgUpdate=Update
provider.providerchangepassword.msgClickButton=button when you're done.
provider.providerchangepassword.msgEnterOld=Enter your
provider.providerchangepassword.formOldPassword=Old Password
provider.providerchangepassword.msgChooseNew=Choose a
provider.providerchangepassword.formNewPassword=New Password
provider.providerchangepassword.msgConfirm=Confirm your
provider.providerchangepassword.msgAtLeast=at least
provider.providerchangepassword.msgSymbols=symbols
provider.providerchangepassword.btnSubmit=Update
provider.providerchangepassword.currentPIN=current PIN
provider.providerchangepassword.newPIN=new PIN
provider.providerchangepassword.confNewPIN=confirm new PIN

provider.providerchangepassword.formCurrPassword=Current Password
provider.providerchangepassword.currentPIN=current PIN
provider.providerchangepassword.newPIN=new PIN

#Messages used on error pages.
error.description=Error Page
error.msgException=Received the exception

#Messages for print label form
demographic.demographiclabelprintsetting.title=_
demographic.demographiclabelprintsetting.msgMainLabel=PATIENT'S LABELS
demographic.demographiclabelprintsetting.msgLabel=Label
demographic.demographiclabelprintsetting.msgNumeberOfLabel=Number of Label
demographic.demographiclabelprintsetting.msgLocation=Location
demographic.demographiclabelprintsetting.btnNewPatientLabel=New Patient Label
demographic.demographiclabelprintsetting.formLeft=left
demographic.demographiclabelprintsetting.formTop=top
demographic.demographiclabelprintsetting.formHeight=height
demographic.demographiclabelprintsetting.formGap=gap
demographic.demographiclabelprintsetting.msgPx=px
demographic.demographiclabelprintsetting.btnPrintPreviewPrint=Print Preview/Print
demographic.demographiclabelprintsetting.msgFailed=Failed!!!
demographic.demographiclabelprintsetting.msgHome=Home
demographic.demographiclabelprintsetting.msgDr=Dr.
demographic.demographiclabelprintsetting.msgBus=Bus

demographic.demographicprintdemographic.btnPrint=Print
demographic.demographicprintdemographic.title=_
demographic.demographiceditdemographic.btnCreatePDFLabel=PDF Label
demographic.demographiceditdemographic.btnCreatePDFAddressLabel=PDF Address Label
demographic.demographiceditdemographic.btnCreatePDFChartLabel=PDF Chart Label
demographic.demographiceditdemographic.btnCreatePublicHealthLabel=Sexual Health Clinic Label
demographic.demographiceditdemographic.btnCreatePDFEnvelope=PDF Envelope
demographic.demographiceditdemographic.btnClientLabLabel=Client Lab Label
demographic.demographiceditdemographic.msgLoading= Loading ...
demographic.demographiceditdemographic.btnCheckElig=Check Eligibility
demographic.demographiceditdemographic.msgFrom=From
demographic.demographiceditdemographic.rxInteractionWarningLevel=Minimum Rx Interaction Warning Level
demographic.demographiceditdemographic.paperChartIndicator=Paper Chart
demographic.demographiceditdemographic.paperChartIndicator.archived=Archived
demographic.demographiceditdemographic.paperChartIndicator.dateArchived=Archive Date
demographic.demographiceditdemographic.paperChartIndicator.programArchived=Program which Archived
demographic.demographiceditdemographic.paperChartIndicator.yes=Yes
demographic.demographiceditdemographic.paperChartIndicator.no=No
demographic.demographicappthistory.msgTo=To
demographic.demographicappthistory.msgStatus=Status
demographic.demographicappthistory.msgType=Type
demographic.demographiceditdemographic.msgEnrollmentHistory=Enrollment History
demographic.demographiceditdemographic.primaryEMR=Primary EMR
demographic.demographiceditdemographic.consent=Consent(s)
demographic.demographiceditdemographic.privacyConsent=Privacy Consent (verbal)
demographic.demographiceditdemographic.usConsent=U.S. Resident Consent Form
demographic.demographiceditdemographic.mergeConsent=Merge Consent
demographic.demographiceditdemographic.informedConsent=Informed Consent (verbal)
demographic.demographiceditdemographic.aboriginal=First Nation
demographic.demographiceditdemographic.formDemographicMiscId=External Id
demographic.demographiceditdemographic.msgPatientType=Patient Type
demographic.demographiceditdemographic.formPHU=PHU
demographic.demographiceditdemographic.consentToUseEmailForCare=Consent to use Email for care.
demographic.demographiceditdemographic.consentToUseEmailForEOrder=Consent to use Email for eOrder.
demographic.demographiceditdemographic.pacsLabRequisition=PACS Lab Requisition

web.record.details.saving=Saving...
web.record.details.integrator=Integrator
web.record.details.viewIntegratedCommunity=View Integrated Community
web.record.details.manageLinkedClients=Manage Linked Clients
web.record.details.compareWithIntegrator=Compare with Integrator
web.record.details.updateFromIntegrator=Update from Integrator
web.record.details.sendNoteIntegrator=Send Note to Integrator
web.record.details.appointmentHistory=Appointment History
web.record.details.history=History
web.record.details.dateOfBirth=Date of Birth
web.record.details.viewPhrRecord=View PHR Record
web.record.details.clickCardSwipe=Click for Card Swipe
web.record.details.sin=SIN #
web.record.details.spoken=Spoken
web.record.details.homePhoneExt=Home Phone Extension
web.record.details.workPhoneExt=Work Phone Extension
web.record.details.validate=Validate
web.record.details.hinVer=HIN Version
web.record.details.addInformation=Additional Information
web.record.details.archivedPaperChart=Archived Paper Chart
web.record.details.privacyConsent=Privacy Consent
web.record.details.informedConsent= Informed Consent
web.record.details.careTeam=Care Team
web.record.details.mrp=MRP
web.record.details.midwife=Midwife
web.record.details.pickReferralDoctor=Pick a referral doctor
web.record.details.addStatus=Add Status
web.record.details.rosterTerminationDate=Roster Termination Date
web.record.details.rosterTerminationReason=Roster Termination Reason
web.record.details.securityQuestion=Security Question
web.record.details.selectSecurityQuestion=Select a Security Question
web.record.details.answer=Answer
web.record.details.answerToSecurityQuestion=Answer to Security Question
web.record.details.phoneShortHand=P:
web.record.details.manage=Manage
web.record.details.proContacts=Professional Contacts
web.record.details.rxInteractionLevel=RX Interaction Level
web.record.details.hasPrimaryCarePhysician=Has Primary Care Physician
web.record.details.employmentStatus=Employment Status

global.msgSomethingWrong=OSCAR has encountered an unexpected error

schedule.scheduletemplatesetting.title=SCHEDULE SETTING
schedule.scheduletemplatesetting.msgMainLabel=SCHEDULE TEMPLATE SETTING
schedule.scheduletemplatesetting.msgStepOne=1. Select the provider's name to set up the provider's schedule.
schedule.scheduletemplatesetting.msgStepTwo=2. Check the 'Holiday Setting' link to specify the holidays.
schedule.scheduletemplatesetting.formSelectProvider=Select a provider
schedule.scheduletemplatesetting.msgNoProvider=---None---
schedule.scheduletemplatesetting.formOrDo=Or do
schedule.scheduletemplatesetting.msgHolidaySettingTip=Define Holidays
schedule.scheduletemplatesetting.btnHolidaySetting=Holiday Setting
schedule.scheduletemplatesetting.btnTemplateCodeSetting=Template Code Setting
schedule.scheduletemplatesetting.btnTemplateSetting=Template Setting
schedule.scheduletemplatesetting.msgForProvider=for
schedule.scheduletemplatesetting.msgPublic=Public
schedule.scheduletemplatesetting.btnCancel=Cancel

schedule.scheduletemplatecodesetting.title=TEMPLATE CODE SETTING
schedule.scheduletemplatecodesetting.msgCheckInput=Please check your input!!!
schedule.scheduletemplatecodesetting.formTemplateCode=Template Code
schedule.scheduletemplatecodesetting.btnEdit=Edit
schedule.scheduletemplatecodesetting.msgApptTemplateCode=Appt Template Code
schedule.scheduletemplatecodesetting.formCode=Code
schedule.scheduletemplatecodesetting.formDescription=Description
schedule.scheduletemplatecodesetting.formBookingLimit=Booking Limit
schedule.scheduletemplatecodesetting.formAvailability=Available
schedule.scheduletemplatecodesetting.formDuration=Duration
schedule.scheduletemplatecodesetting.formColor=Color
schedule.scheduletemplatecodesetting.msgColorExample=e.g. \#1b2c3d, ivory.
schedule.scheduletemplatecodesetting.btnDelete=Delete
schedule.scheduletemplatecodesetting.btnSave=Save
schedule.scheduletemplatecodesetting.msgCode=Code: only one char.
schedule.scheduletemplatecodesetting.msgDescription=Description: less than 40 chars.
schedule.scheduletemplatecodesetting.msgDuration=Duration: unit is minute.
schedule.scheduletemplatecodesetting.msgColor=Color: html code for background color.
schedule.scheduletemplatecodesetting.msgAvailable=Availability: whether doctor is available for booking on this code. 

schedule.scheduleholidaysetting.title=SCHEDULE SETTING
schedule.scheduleholidaysetting.msgCheckInput=Please check your input!!!
schedule.scheduleholidaysetting.formHolidayName=Holiday Name
schedule.scheduleholidaysetting.btnLastMonthTip=View Last Month
schedule.scheduleholidaysetting.btnLastMonth=last month
schedule.scheduleholidaysetting.btnNextMonthTip=View Next Month
schedule.scheduleholidaysetting.btnNextMonth=next month
schedule.scheduleholidaysetting.msgSunday=Sun
schedule.scheduleholidaysetting.msgMonday=Mon
schedule.scheduleholidaysetting.msgTuesday=Tue
schedule.scheduleholidaysetting.msgWednesday=Wed
schedule.scheduleholidaysetting.msgThursday=Thu
schedule.scheduleholidaysetting.msgFriday=Fri
schedule.scheduleholidaysetting.msgSaturday=Sat
schedule.scheduleholidaysetting.btnDelete=Delete
schedule.scheduleholidaysetting.btnSave=Save

schedule.scheduledatesave.title=SCHEDULE SETTING

schedule.scheduledatepopup.title=SCHEDULE SETTING
schedule.scheduledatepopup.formDate=Date
schedule.scheduledatepopup.formAvailable=Available
schedule.scheduledatepopup.formAvailableYes=Yes
schedule.scheduledatepopup.formAvailableNo=No
schedule.scheduledatepopup.formTemplate=Template
schedule.scheduledatepopup.formCreator=By
schedule.scheduledatepopup.btnSave=Save
schedule.scheduledatepopup.btnCancel=Cancel
schedule.scheduledatepopup.btnDelete=Delete

schedule.scheduledatefinal.title=SCHEDULE SETTING
schedule.scheduledatefinal.msgMainLabel=SCHEDULE SETTING
schedule.scheduledatefinal.msgStepOne=1. Click the 'Do it again' button to go back to the first schedule page.
schedule.scheduledatefinal.msgStepTwo=2. Click the 'Finish' button to exit the window.
schedule.scheduledatefinal.msgSettingFinished=You have finished one Schedule Setting successfully.
schedule.scheduledatefinal.btnDoAgain=Do it again
schedule.scheduledatefinal.btnFinish=Finish

schedule.schedulecreatedate.title=SCHEDULE SETTING
schedule.schedulecreatedate.msgMainLabel=SCHEDULE SETTING
schedule.schedulecreatedate.msgStepOne=1. Select the right month.
schedule.schedulecreatedate.msgStepTwo=2. Click the date you want to specify.
schedule.schedulecreatedate.msgStepThree=3. Type in the schedule of that day in the pop-up window.
schedule.schedulecreatedate.msgStepFour=4. Repeat 1-4 until ...
schedule.schedulecreatedate.msgStepFive=5. Click the 'Next' button.
schedule.schedulecreatedate.msgEffective=effective
schedule.schedulecreatedate.btnLastMonthTip=View Last Month
schedule.schedulecreatedate.btnLastMonth=last month
schedule.schedulecreatedate.btnNextMonthTip=View Next Month
schedule.schedulecreatedate.btnNextMonth=next month
schedule.schedulecreatedate.msgSunday=Sun
schedule.schedulecreatedate.msgMonday=Mon
schedule.schedulecreatedate.msgTuesday=Tue
schedule.schedulecreatedate.msgWednesday=Wed
schedule.schedulecreatedate.msgThursday=Thu
schedule.schedulecreatedate.msgFriday=Fri
schedule.schedulecreatedate.msgSaturday=Sat
schedule.schedulecreatedate.btnNext=Next
schedule.schedulecreatedate.btnCancel=Cancel
schedule.schedulecreatedate.msgConflict=Warning, this schedule's dates overlap another existing schedule

schedule.scheduletemplateapplying.title=SCHEDULE SETTING
schedule.scheduletemplateapplying.msgDeleteConfirmation=Are you sure you want to delete this template setting?
schedule.scheduletemplateapplying.msgIncorrectOutput=The date you input is incorrect!!!
schedule.scheduletemplateapplying.msgInputDate=Please input a date!!!
schedule.scheduletemplateapplying.msgInputCorrectDate=Please input a correct date!!!
schedule.scheduletemplateapplying.msgDateOrder=Start date must begin before end date
schedule.scheduletemplateapplying.msgScheduleConflict=New schedule date conflicts with existing schedule
schedule.scheduletemplateapplying.msgMainLabel=SCHEDULE TEMPLATE SETTING
schedule.scheduletemplateapplying.msgStepOne=1. Use the current R Schedule or select a different one from the select field.
schedule.scheduletemplateapplying.msgStepTwo=2. Type in the start date and end date for this R Schedule.
schedule.scheduletemplateapplying.msgStepThree=3. Check the day of week which is AVAILABLE.
schedule.scheduletemplateapplying.msgStepFour=4. Click the 'Next' button.
schedule.scheduletemplateapplying.msgObs=* Select R Schedule from the drop down list. Don't change the start date to update the R Sch.
schedule.scheduletemplateapplying.btnDelete=Delete
schedule.scheduletemplateapplying.msgAvaiableEvery=is available EVERY
schedule.scheduletemplateapplying.msgDayOfWeek=Day of Week
schedule.scheduletemplateapplying.msgAlternateWeekSetting=Alternating Week Setting
schedule.scheduletemplateapplying.msgSunday=Sun
schedule.scheduletemplateapplying.msgMonday=Mon
schedule.scheduletemplateapplying.msgTuesday=Tue
schedule.scheduletemplateapplying.msgWednesday=Wed
schedule.scheduletemplateapplying.msgThursday=Thu
schedule.scheduletemplateapplying.msgFriday=Fri
schedule.scheduletemplateapplying.msgSaturday=Sat
schedule.scheduletemplateapplying.btnNext=Next
schedule.scheduletemplateapplying.btnCancel=Cancel

schedule.scheduleedittemplate.title=DAY TEMPLATE SETTING
schedule.scheduleedittemplate.msgCheckInput=Please check your input!!!
schedule.scheduleedittemplate.formProvider=Provider
schedule.scheduleedittemplate.btnEdit=Edit
schedule.scheduleedittemplate.msgMainLabel=Add A Template
schedule.scheduleedittemplate.formTemplateName=Template Name
schedule.scheduleedittemplate.msgLessTwentyChars=(<20 chars)
schedule.scheduleedittemplate.formSummary=Summary
schedule.scheduleedittemplate.formTemplateCode=Template Code
schedule.scheduleedittemplate.btnDelete=Delete
schedule.scheduleedittemplate.btnSave=Save

oscarEncounter.oscarConsultationRequest.ConsultChoice.title=ConsultChoice
oscarEncounter.oscarConsultationRequest.ConsultChoice.oscarConS=oscarConNew
oscarEncounter.oscarConsultationRequest.ConsultChoice.msgTitle=Consultations
oscarEncounter.oscarConsultationRequest.ConsultChoice.btnNewCon=New Consultation
oscarEncounter.oscarConsultationRequest.ConsultChoice.btnOldCon=Old Consultations
global.btnCancel=Cancel
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.title=OSCAR Consultation Request
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.appointmentInstr=Appointment Instructions
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgCal=oscarCal
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgReqID=Requested ID
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgFormSet=this is from in the form setter
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgStatus=Status
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgCreated=Created by
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgNoth=Nothing
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgSpecCall=Pending Specialist Callback
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgPatCall=Pending Patient Callback
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgPreliminary=Preliminary
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgBookingConfirmed=Booking Confirmed
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgCompleted=Completed
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formRefDate=Referral Date
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formService=Service
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formServSelect=Select Service
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formCons=Consultant
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formSelectSpec=Select Specialist
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formUrgency=Urgency
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formInstructions=Referrer Instructions
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgUrgent=Urgent
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgNUrgent=Non-Urgent
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgReturn=Return
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgPriority=Priority
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formPhone=Phone
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formFax=Fax
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formAddr=Address
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnAppointmentDate=Appointment Date
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formAppointmentTime=Appointment Time
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgPatient=Patient
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgPreferredName=Preferred Name
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgAddress=Address
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgPhone=Tel.No.
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgWPhone=Work No.
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgCPhone=Cell No.
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgBirthDate=Birthdate
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgSex=Sex
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgHealthCard=Health Card No.
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgSendTo=Send to
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgTeams=Teams
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.msgEmail=Email
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formReason=Reason for Consultation
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formClinInf=Pertinent clinical information
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formSignificantProblems=Significant concurrent problems
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formCurrMedications=Current Medications
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formAllergies=Allergies
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formAppointmentNotes=Appointment Notes
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formLastFollowup=Last Follow Up Date
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnSubmit=Submit Consultation Request
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnSubmitAndPrint=Submit Consultation Request & Print
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnSubmitAndSendElectronicReferral=Submit Consultation Request & Send Electronically
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnUpdate=Update Consultation Request
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnUpdateAndPrint=Update Consultation Request & Print
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnUpdateAndSendElectronicReferral=Update Consultation Request & Send Electronically
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnImportSocHistory=Social History
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnImportFamHistory=Family History
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnImportMedHistory=Medical History
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnImportConcerns=Ongoing Concerns
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnImport=Import
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnImportOtherMeds=Other Meds
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnImportReminders=Reminders
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.attachDoc=Attach File to Consultation
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.curAttachDoc=Currently Attached Files
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formPatientBook = Patient Will Book
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.optChooseServ = Choose a Service
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.optChooseSpec = Choose a Specialist
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.optAllServices = All Services
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.optAllSpecs = All Specialists
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.siteName = Referring Site
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.chkPreserveFormatting = Preserve Print Formatting
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.letterheadName=Letterhead Name
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.letterheadAddress=Letterhead Address
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.letterheadPhone=Letterhead Phone
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.letterheadFax=Letterhead Fax
oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.formSignature=Signature
oscarEncounter.oscarConsultationRequest.msgNothingPrinted=There are currently no pdf documents attached to this consultation
oscarEncounter.oscarConsultationRequest.AttachDocPopup.title=Attach Consultation Documents
oscarEncounter.oscarConsultationRequest.AttachDocPopup.empty=--------------None--------------
oscarEncounter.oscarConsultationRequest.AttachDocPopup.header=Documents for
oscarEncounter.oscarConsultationRequest.AttachDocPopup.available=Available Documents
oscarEncounter.oscarConsultationRequest.AttachDocPopup.attached=Attached Documents
oscarEncounter.oscarConsultationRequest.AttachDocPopup.preview=Document Preview
oscarEncounter.oscarConsultationRequest.AttachDocPopup.submit=Done Close Window
oscarEncounter.oscarConsultationRequest.AttachDoc.Legend=Legend
oscarEncounter.oscarConsultationRequest.AttachDoc.LegendDocs=Blue - Documents
oscarEncounter.oscarConsultationRequest.AttachDoc.LegendLabs=Purple - Labs
oscarEncounter.oscarConsultationRequest.AttachDoc.LegendHRMs=Red - HRM
oscarEncounter.oscarConsultationRequest.AttachDoc.LegendEForms=Green - eForms
oscarEncounter.oscarConsultationRequest.AttachDoc.LegendForms=Orange - Forms
oscarEncounter.oscarConsultationRequest.AttachDoc.Empty=None
oscarEncounter.oscarConsultationRequest.AttachDocPopup.hrmDocuments=HRM Documents
oscarEncounter.oscarConsultationRequest.PrintReport.docs= 's Documents
oscarEncounter.oscarConsultationRequest.PrintReport.pdfPrintErr=You must select at least one pdf file to print
oscarEncounter.oscarConsultationRequest.PrintReport.ContentType=Content Type
oscarEncounter.oscarConsultationRequest.PrintReport.Observation=Observation Date

OscarEncounter.oscarConsultationRequest.CalendarPopUp.title=CALENDAR
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgVLastMonth=View Last Month
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgLastMonth=last month
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgNextMonth=next month
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgVNextMonth=View Next Month
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgSun=Sun
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgMon=Mon
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgTue=Tue
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgWed=Wed
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgThu=Thu
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgFri=Fri
oscarEncounter.oscarConsultationRequest.CalendarPopUp.msgSat=Sat

oscarEncounter.oscarConsultationRequest.consultationFormPrint.title=ConsultationFormPrint
oscarEncounter.oscarConsultationRequest.consultationFormPrint.pwb=Patient Will Book
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgFaxFooter=Fax Footer
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgPrint=Print
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgPrintAttached=Print Attached
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgConsReq=Consultation Request
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgDate=Date
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgStatus=Status
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgService=Service
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgConsultant=Consultant
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgPhone=Phone
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgWPhone=Work Phone
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgCPhone=Cell Phone
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgEmail=Email
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgFax=Fax
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgAddr=Address
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgPat=Patient
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgPreferredName=Preferred Name
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgBirth=Birthdate
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgSex=Sex
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgCard=Health Card No.
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgappDate=Appointment date
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgTime=Time
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgChart=Chart No.
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgReason=Reason for consultation
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgClinicalInfom=Pertinent Clinical Information
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgSigProb=Significant Concurrent Problems
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgCurrMed=Current Medications
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgAllergies=Allergies
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgAssociated=Associated with
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgFamilyDoc=Referring Doctor
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgReferringIsPhysician=Physician
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgAssociated2=Referring Practitioner
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgReqPhysician=Requesting Physician
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgFamilyDoc2=MRP
oscarEncounter.oscarConsultationRequest.consultationFormPrint.createdby=Created by
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgUrgent=Urgent
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgNUrgent=Non-Urgent
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgReturn=Return
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgSignature=Signature
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgOcularExamination=Ocular Examination

oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.title=View Consultation Requests
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.pwb=Patient Will Book
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgConsReq=oscarConsultationRequest
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgConsReqFor=Consultation Requests for
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgClickLink=Click on a link to view Consultation Request Form
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgStatus=Status
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgPat=Patient
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgProvider=Provider
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgService=Service
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgRefDate=Referral Date
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgNothingDone=Nothing has been done
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgSpecialistCall=Pending Specailist Callback
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgPatCall=Pending Patient Callback
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgAppMade=Appointment Made
oscarEncounter.oscarConsultationRequest.DisplayDemographicConsultationRequests.msgBookingConfirmed=Booking Confirmed

ectViewConsultationRequests.title=View Consultation Requests
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgConsReq=oscarConsultationRequest
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgConsConfig=OscarConsultationConfig
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msfConsReqForTeam=Consultation Requests for team
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgViewAll=View All
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgEditSpecialists=Edit Specialists Listing
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.formSelectTeam=Select Team
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.formViewAll=View All
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.btnConsReq=Consultation Requests for this team
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgStatus=Status
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgConsultationStatus=Consultation Status
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgPatient=Patient
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgProvider=Provider
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgService=Service
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgRefDate=Referral Date
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.formTeamNotApplicable=N/A
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgND=ND
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgSR=SR
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgPR=PR
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgBC=BC
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgDONE=DONE

oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgStart=Start
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgEnd=End
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgIncludeCompleted=Include Completed
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgSearchon=Search on Referral Date
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgApptDate=Appt. Date
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgUrgency=Urgency
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgAppointmentDate=Appointment Date
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgTeam=Team
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgConsultant=Consultant
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgFollowUpDate=Last Followup
oscarEncounter.oscarConsultationRequest.ViewConsultationRequests.msgSiteName=Site Name

oscarEncounter.eyeExam.normal=normal
oscarEncounter.eyeExam.Clear=clear
oscarEncounter.eyeExam.Nasal=Nasal
oscarEncounter.eyeExam.Inferieur=Inferior
oscarEncounter.eyeExam.Temporal=Temporal
oscarEncounter.eyeExam.Superior=Superior
oscarEncounter.eyeExam.white=white
oscarEncounter.eyeExam.deepAndQuite=deep and quiet
oscarEncounter.eyeExam.open=open
oscarEncounter.eyeExam.open.blank=

oscarEncounter.oscarConsultationReport.PrintReport.Date=Date
oscarEncounter.oscarConsultationReport.PrintReport.To=To
oscarEncounter.oscarConsultationReport.PrintReport.Address=Address
oscarEncounter.oscarConsultationReport.PrintReport.Phone=Phone
oscarEncounter.oscarConsultationReport.PrintReport.Cell=Cell No.
oscarEncounter.oscarConsultationReport.PrintReport.Fax=Fax
oscarEncounter.oscarConsultationReport.PrintReport.CC=CC
oscarEncounter.oscarConsultationReport.PrintReport.Patient=Patient
oscarEncounter.oscarConsultationReport.PrintReport.DOB=DOB
oscarEncounter.oscarConsultationReport.PrintReport.OHIP=OHIP #
oscarEncounter.oscarConsultationReport.PrintReport.Dear=Dear
oscarEncounter.oscarConsultationReport.PrintReport.ClinicalInfo=Clinical Information
oscarEncounter.oscarConsultationReport.PrintReport.Examination=Examination
oscarEncounter.oscarConsultationReport.PrintReport.DistanceVision=Distance vision
oscarEncounter.oscarConsultationReport.PrintReport.NearVision=Near vision
oscarEncounter.oscarConsultationReport.PrintReport.AutoRefraction=Auto-refraction
oscarEncounter.oscarConsultationReport.PrintReport.Applanation=Applanation
oscarEncounter.oscarConsultationReport.PrintReport.Cornea=Cornea
oscarEncounter.oscarConsultationReport.PrintReport.ConjunctivaSclera=Conjunctiva/Sclera
oscarEncounter.oscarConsultationReport.PrintReport.Angle=Angle
oscarEncounter.oscarConsultationReport.PrintReport.AnteriorChamber=Anterior chamber
oscarEncounter.oscarConsultationReport.PrintReport.Iris=Iris
oscarEncounter.oscarConsultationReport.PrintReport.Lens=Lens
oscarEncounter.oscarConsultationReport.PrintReport.PosteriorSegment=POSTERIOR SEGMENT
oscarEncounter.oscarConsultationReport.PrintReport.OpticDisc=Optic disc
oscarEncounter.oscarConsultationReport.PrintReport.CDRatio=C/D ratio
oscarEncounter.oscarConsultationReport.PrintReport.Macula=Macula
oscarEncounter.oscarConsultationReport.PrintReport.Retina=Retina
oscarEncounter.oscarConsultationReport.PrintReport.Vitreous=Vitreous
oscarEncounter.oscarConsultationReport.PrintReport.CRT=CRT
oscarEncounter.oscarConsultationReport.PrintReport.ImpressionPlan=Impression/Plan
oscarEncounter.oscarConsultationReport.PrintReport.VisionAssessment=VISION ASSESSMENT
oscarEncounter.oscarConsultationReport.PrintReport.VisionMeasurement=VISION MEASUREMENT
oscarEncounter.oscarConsultationReport.PrintReport.IntraocularPressure=INTRAOCULAR PRESSURE
oscarEncounter.oscarConsultationReport.PrintReport.AnteriorSegment=ANTERIOR SEGMENT
oscarEncounter.oscarConsultationReport.PrintReport.OtherExam=OTHER EXAM
oscarEncounter.oscarConsultationReport.PrintReport.Pupil=Pupil
oscarEncounter.oscarConsultationReport.PrintReport.IhadPleasure=I had the pleasure of seeing
oscarEncounter.oscarConsultationReport.PrintReport.Yearsold=year old
oscarEncounter.oscarConsultationReport.PrintReport.On=on
oscarEncounter.oscarConsultationReport.PrintReport.OnYourReferral=as per your referral
oscarEncounter.oscarConsultationReport.PrintReport.CurrentHistory=Current History
oscarEncounter.oscarConsultationReport.PrintReport.PastOcularHistory=Past Ocular History
oscarEncounter.oscarConsultationReport.PrintReport.MedicalHistory=Medical/Past Surgical History
oscarEncounter.oscarConsultationReport.PrintReport.ColourVision=Colour vision
oscarEncounter.oscarConsultationReport.PrintReport.AmslerGrid=Amsler grid
oscarEncounter.oscarConsultationReport.PrintReport.ThankYou=Thank you for allowing me to participate in the care of this patient.

oscarEncounter.RemoteAttachments.title=oscarComm
oscarEncounter.RemoteAttachments.msgViewAtt=oscarVA
oscarEncounter.RemoteAttachments.msgSendAtt=oscarSA
oscarEncounter.RemoteAttachments.msgEncounterTable=encounterTable
oscarEncounter.RemoteAttachments.msgSendEDoc=Send eDocs <br>for this Patient
oscarEncounter.RemoteAttachments.msgDemogAtt=Demographic Attachments
oscarEncounter.RemoteAttachments.msgSubject=Subject
oscarEncounter.RemoteAttachments.msgSentFrom=Sent From
oscarEncounter.RemoteAttachments.msgSavedBy=Saved By
oscarEncounter.RemoteAttachments.msgDate=Date


oscarEncounter.ViewAttachment.title=Template
oscarEncounter.ViewAttachment.msgEncounterTable=encounterTable
oscarEncounter.ViewAttachment.msgViewAtt=View Attachment
oscarEncounter.ViewAttachment.msgFrom=From
oscarEncounter.ViewAttachment.msgAt=at
oscarEncounter.ViewAttachment.msgSubject=Subject
oscarEncounter.ViewAttachment.msgDate=Date
oscarEncounter.ViewAttachment.msgExpandAll=Expand All
oscarEncounter.ViewAttachment.msgColapseAll=Collapse All

oscarResearch.oscarDxResearch.dxResearch.title=oscarDxResearch
oscarResearch.oscarDxResearch.dxResearch.msgDxResearch=oscarDxResearch
oscarResearch.oscarDxResearch.dxResearch.msgResearch=Research
oscarResearch.oscarDxResearch.dxResearch.msgCode=Code
oscarResearch.oscarDxResearch.dxResearch.msgDiagnosis=Diagnosis
oscarResearch.oscarDxResearch.dxResearch.msgFirstVisit=First Visit
oscarResearch.oscarDxResearch.dxResearch.msgLastVisit=Last Visit
oscarResearch.oscarDxResearch.dxResearch.msgAction=Action
oscarResearch.oscarDxResearch.dxResearch.btnResolve=Resolve
oscarResearch.oscarDxResearch.dxResearch.btnResolved=Resolved
oscarResearch.oscarDxResearch.dxResearch.btnDelete=Delete
oscarResearch.oscarDxResearch.dxResearch.btnUpdate=Update

oscarResearch.oscarDxResearch.dxResearchCodeSearch.title=Research Code Search
oscarResearch.oscarDxResearch.dxResearchCodeSearch.msgCode=Code
oscarResearch.oscarDxResearch.dxResearchCodeSearch.msgDescription=Description
oscarResearch.oscarDxResearch.dxResearchCodeSearch.msgNoMatch=No match found

oscarEncounter.encounterPrint.msgDr=Dr
oscarEncounter.encounterPrint.msgSocialHist=Social History
oscarEncounter.encounterPrint.msgFamilyHist=Family History
oscarEncounter.encounterPrint.msgMed=Medical History
oscarEncounter.encounterPrint.msgOngCon=Ongoing Concerns
oscarEncounter.encounterPrint.msgReminders=Reminders
oscarEncounter.encounterPrint.msgEncounter=Encounter

oscarEncounter.timeOut.title=TimeOut
oscarEncounter.timeOut.msgEncounter=encounterTable
oscarEncounter.timeOut.msgSaveExit=Saving and Exiting Encounter

oscarEncounter.search.demographicSearch.formAddr=Address
oscarEncounter.search.demographicSearch.formBirthDay=Day Of Birth
oscarEncounter.search.demographicSearch.formBirthMonth=Month Of Birth
oscarEncounter.search.demographicSearch.formBirthYear=Year Of Birth
oscarEncounter.search.demographicSearch.formCity=City
oscarEncounter.search.demographicSearch.formCNumber=Chart Number
oscarEncounter.search.demographicSearch.formFemale=F
oscarEncounter.search.demographicSearch.formHIN=Health Ins. #
oscarEncounter.search.demographicSearch.formMale=M
oscarEncounter.search.demographicSearch.formPhone=Phone
oscarEncounter.search.demographicSearch.msgAddr=Address
oscarEncounter.search.demographicSearch.msgCity=City
oscarEncounter.search.demographicSearch.msgDemoN=Demo #
oscarEncounter.search.demographicSearch.msgDOB=DOB
oscarEncounter.search.demographicSearch.msgEncounter=encounterTable
oscarEncounter.search.demographicSearch.msgFirstName=First Name
oscarEncounter.search.demographicSearch.msgHin=Hin
oscarEncounter.search.demographicSearch.msgLastName=Last Name
oscarEncounter.search.demographicSearch.msgNoResult=Your Search produced no results
oscarEncounter.search.demographicSearch.msgPatSearch=patient search
oscarEncounter.search.demographicSearch.msgPhone=Phone
oscarEncounter.search.demographicSearch.msgProvice=Province
oscarEncounter.search.demographicSearch.msgSex=Sex
oscarEncounter.search.demographicSearch.title=oscarConsultationRequest

oscarEncounter.error.title=Oscar Prescription Module - Error
oscarEncounter.error.msgExpired=Session Expired
oscarEncounter.error.msgSessionFailed=Either your Encounter session has failed or you do not have sufficient priviledges to view this page. <p> Please go back to Oscar and try your request again.<p>If this message appears to have been shown for no reason an error may have occurred. If this problem persists, please contact OSCAR Technical Support.

oscarEncounter.StartCookie.title=Drug Search Results
oscarEncounter.StartCookie.msgLaunch=Launch Encounter

oscarRx.MyDrugref.error.msgFailed=WARNING: Drug interaction information unavailable.
oscarRx.MyDrugref.InteractingDrugs.error.msgFailed=WARNING: Drug interaction information unavailable with current prescriptions.

oscarRx.MyDrugref.InteractingDrugs.effect.AugmentsNoClinicalEffect=Augments (no clinical effect)
oscarRx.MyDrugref.InteractingDrugs.effect.Augments=Augments
oscarRx.MyDrugref.InteractingDrugs.effect.InhibitsNoClinicalEffect=Inhibits (no clinical effect)
oscarRx.MyDrugref.InteractingDrugs.effect.Inhibits=Inhibits
oscarRx.MyDrugref.InteractingDrugs.effect.NoEffect=No Effect
oscarRx.MyDrugref.InteractingDrugs.effect.Unknown=Unknown Effect

oscarRx.MyDrugref.InteractingDrugs.evidence.Poor=(Poor Evidence)
oscarRx.MyDrugref.InteractingDrugs.evidence.Fair=(Fair Evidence)
oscarRx.MyDrugref.InteractingDrugs.evidence.Good=(Good Evidence)
oscarRx.MyDrugref.InteractingDrugs.evidence.Unknown=(Unknown Evidence)

e_form.ShowMyForm.title=ShowMyForm
e_form.ShowMyForm.msgMyForm=My eForm
e_form.ShowMyForm.btnAddEForm=Add eForm
e_form.ShowMyForm.btnBack=Back
e_form.ShowMyForm.msgFormLib=eForm Library
e_form.ShowMyForm.btnListDelForms=List Deleted eForms
e_form.ShowMyForm.btnFormName=eForm Name
e_form.ShowMyForm.btnSubj=Additional Information
e_form.ShowMyForm.btnDate=eForm Date
e_form.ShowMyForm.msgTime=eForm Time
e_form.ShowMyForm.msgAction=Action
e_form.ShowMyForm.btnDel=Delete
e_form.ShowMyForm.msgNoData=No data

e_form.MakeMyForm.title=MakeMyForm
e_form.MakeMyForm.msgNoSuchFile=NO such file in database

e_form.MyForm.title=MyForm
e_form.MyForm.msgPleaseChooseFile=Please choose a file first, then click Upload
e_form.MyForm.msgMyForm=My eForm
e_form.MyForm.msgFormLib=eForm Library
e_form.MyForm.btnFormName=eForm Name
e_form.MyForm.btnSubj=Additional Information
e_form.MyForm.btnFile=File
e_form.MyForm.btnFormDate=eForm Date
e_form.MyForm.btnFormTime=eForm Time

e_form.CallDeletedFormData.title=CallDeletedFormData
e_form.CallDeletedFormData.msgMyForms=My eForms
e_form.CallDeletedFormData.msgSelectForm=Select a eForm to show
e_form.CallDeletedFormData.msgFormsDeleted=The eForms you have deleted
e_form.CallDeletedFormData.btnCurrentFormLibrary=Go to Current eForm Library
e_form.CallDeletedFormData.btnReturn=Return
e_form.CallDeletedFormData.btnUndelete=UnDelete
e_form.CallDeletedFormData.msgNoData=no data in it

e_form.CallDeletedForm.title=Search a Provider
e_form.CallDeletedForm.msgUploadFirst=Please choose a file first, then click Upload
e_form.CallDeletedForm.msgAdminForms=FORMS ADMINISTRATOR
e_form.CallDeletedForm.msgDeletedForms=The eForms you already deleted
e_form.CallDeletedForm.btnFormLib=Current eForm Library
e_form.CallDeletedForm.btnReturn=Return to Admin
e_form.CallDeletedForm.btnUnDel=UnDelete

oscarEncounter.oscarConsultationRequest.ConfirmConsultationRequest.title=Confirm Consultation
oscarEncounter.oscarConsultationRequest.ConfirmConsultationRequest.msgConsReq=Consultation Request Form has been
oscarEncounter.oscarConsultationRequest.ConfirmConsultationRequest.msgClose5Sec=This window will close in 5 seconds, or press Close to close it now
oscarEncounter.oscarConsultationRequest.ConfirmConsultationRequest.msgUpdated=Updated
oscarEncounter.oscarConsultationRequest.ConfirmConsultationRequest.msgCreated=Created
oscarEncounter.oscarConsultationRequest.ConfirmConsultationRequest.msgCreatedUpdateESent=Created / Updated and Referral Electronically Sent.
oscarEncounter.oscarConsultationRequest.ConfirmConsultationRequest.msgCreatedUpdateESendError=The referral was created / updated but there was an error electronically sending the referral, please try again or manually process the referral.

oscarEncounter.immunization.ScheduleConfig.title=Configure Immunization Schedule
oscarEncounter.immunization.ScheduleConfig.msgImm=immunizations
oscarEncounter.immunization.ScheduleConfig.btnConfig=Config
oscarEncounter.immunization.ScheduleConfig.btnSaveConfig=Save Configuration
oscarEncounter.immunization.ScheduleConfig.createTemplate=Manage Immu. Template
oscarEncounter.immunization.ScheduleConfig.addTemplate=Add. Immu. Template

oscarEncounter.immunization.Schedule.title=Immunization Schedule
oscarEncounter.immunization.Schedule.msgRecordImm=RecordImmunization
oscarEncounter.immunization.Schedule.msgRefused=Refused
oscarEncounter.immunization.Schedule.msgImm=immunizations
oscarEncounter.immunization.Schedule.btnConf=Configure
oscarEncounter.immunization.Schedule.msgComments=Comments

oscarEncounter.immunization.ScheduleEdit.title=Record Immunization
oscarEncounter.immunization.ScheduleEdit.msgInvalidDate=Invalid date. Please fix the value and resubmit
oscarEncounter.immunization.ScheduleEdit.msgRecordImm=record immunization
oscarEncounter.immunization.ScheduleEdit.msgNotGiven=Not Given
oscarEncounter.immunization.ScheduleEdit.msgGivn=Immunization Given
oscarEncounter.immunization.ScheduleEdit.formGDate=Given Date
oscarEncounter.immunization.ScheduleEdit.msgJan=January
oscarEncounter.immunization.ScheduleEdit.msgFeb=February
oscarEncounter.immunization.ScheduleEdit.msgMar=March
oscarEncounter.immunization.ScheduleEdit.msgApr=April
oscarEncounter.immunization.ScheduleEdit.msgMay=May
oscarEncounter.immunization.ScheduleEdit.msgJun=June
oscarEncounter.immunization.ScheduleEdit.msgJul=July
oscarEncounter.immunization.ScheduleEdit.msgAug=August
oscarEncounter.immunization.ScheduleEdit.msgSep=September
oscarEncounter.immunization.ScheduleEdit.msgOct=October
oscarEncounter.immunization.ScheduleEdit.msgNov=November
oscarEncounter.immunization.ScheduleEdit.msgDec=December
oscarEncounter.immunization.ScheduleEdit.formLotNumber=Lot Number
oscarEncounter.immunization.ScheduleEdit.formProv=Provider
oscarEncounter.immunization.ScheduleEdit.formRefused=Refused
oscarEncounter.immunization.ScheduleEdit.formRefusedDate=Refused Date
oscarEncounter.immunization.ScheduleEdit.formComments=Comments
oscarEncounter.immunization.ScheduleEdit.btnSaveClose=Save and Close

oscarEncounter.immunization.config.administrativeImmunizationSets.title=Administrate Immunization Sets
oscarEncounter.immunization.config.administrativeImmunizationSets.msgImmName=Immunization Set Name
oscarEncounter.immunization.config.administrativeImmunizationSets.msgDateCreated=Date Created
oscarEncounter.immunization.config.administrativeImmunizationSets.btnAddNew=Add New
oscarEncounter.immunization.config.administrativeImmunizationSets.msgImm=immunizations
oscarEncounter.immunization.config.administrativeImmunizationSets.btnRestore=Restore
oscarEncounter.immunization.config.administrativeImmunizationSets.btnDelete=Delete
oscarEncounter.immunization.config.administrativeImmunizationSets.btnSetlist=Immu. Set List
oscarEncounter.immunization.config.administrativeImmunizationSets.btnDelList=Deleted List

oscarEncounter.immunization.config.createImmunizationSetinit.title=GotoSearch
oscarEncounter.immunization.config.createImmunizationSetinit.msgCreateNewSet=Create a new set of Immunizations
oscarEncounter.immunization.config.createImmunizationSetinit.msgFollowSteps=Please follow these steps to create an Immunization Set
oscarEncounter.immunization.config.createImmunizationSetinit.msgStep1=Step 1: Please enter what you would like the Set to be called.
oscarEncounter.immunization.config.createImmunizationSetinit.msgStep2=Step 2: Please enter how many rows you would like this set to have <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ie: How many Immunizations would you like this set to have
oscarEncounter.immunization.config.createImmunizationSetinit.msgStep3=Step 3: Please enter how many columns you would like this set to have<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ie: How many dates you would like this set to have
oscarEncounter.immunization.config.createImmunizationSetinit.formSetName=Set Name
oscarEncounter.immunization.config.createImmunizationSetinit.formNRows=Number of Rows
oscarEncounter.immunization.config.createImmunizationSetinit.formNCol=Number of Columns
oscarEncounter.immunization.config.createImmunizationSetinit.btnNext=Next Step

oscarEncounter.immunization.config.createImmunizationSetConfig.title=GotoSearch
oscarEncounter.immunization.config.createImmunizationSetConfig.msgCreateNew=Create a new set of Immunizations
oscarEncounter.immunization.config.createImmunizationSetConfig.msgStep4=Step 4: Please label each column heading with dates of the set ie 2mon,4mon,6mon,1year,4year,etc
oscarEncounter.immunization.config.createImmunizationSetConfig.msgStep5=Step 5: Please label each row heading to the left with the immunizations this set will have
oscarEncounter.immunization.config.createImmunizationSetConfig.msgStep6=Step 6: Please check off each box where an immunization needs to be given
oscarEncounter.immunization.config.createImmunizationSetConfig.msgSetName=Set Name
oscarEncounter.immunization.config.createImmunizationSetConfig.btnRender=Render Immunization Set

oscarEncounter.immunization.config.immunizationSetDisplay.title=Immunization Schedule
oscarEncounter.immunization.config.immunizationSetDisplay.msgSet=Set

oscarEncounter.class.EctSaveEncounterAction.msgSigned=Signed on
oscarEncounter.class.EctSaveEncounterAction.msgSigBy=by
oscarEncounter.class.EctSaveEncounterAction.msgVerAndSig=Verified and Signed on
oscarEncounter.class.EctSaveEncounterAction.msgVer=Verified on
oscarEncounter.class.EctSaveEncounterAction.msgSplitChart=SPLIT CHART
oscarEncounter.class.EctSaveEncounterAction.msgLocked=Locked

date.yyyyMMddHHmmss=yyyy-MM-dd HH:mm:ss
date.yyyyMMddHHmm=yyyy-MM-dd HH:mm
date.yyyy-MM-dd=yyyy-MM-dd
date.EEE=EEE
date.EEEyyyyMMdd=EEE, yyyy-MM-dd

inboxmanager.document.uploadDocuments=Upload Documents
inboxmanager.document.initialFileUpload=0 Files Uploaded
inboxmanager.document.addMultipleDocuments=Add Multiple Documents
inboxmanager.document.title=Upload Multiple Documents
inboxmanager.document.uploadDoc=Doc Upload
inboxmanager.document.changeView=Change view
inboxmanager.document.DocumentUploaded=Document Uploaded:
inboxmanager.document.ContentType=Content Type:
inboxmanager.document.NumberOfPages=Number of Pages:
inboxmanager.document.PatientMsg=Patient:
inboxmanager.document.ObservationDateMsg=Observation Date:
inboxmanager.document.DemographicMsg=Demographic:
inboxmanager.document.SendToMRPMsg=Send To MRP
inboxmanager.document.SendToMRPFailedMsg=Failed to send MRP
inboxmanager.document.FlagProviderMsg=Flag Provider:
inboxmanager.document.FlagAbnormalMsg=Flag as abnormal
inboxmanager.document.SuccessfullySavedMsg=Successfully saved
inboxmanager.document.SaveAndNext=Save & Next
inboxmanager.document.NextAppointmentMsg=Next Appointment:
inboxmanager.document.Comment=Comments:
inboxmanager.document.LinkedProvidersMsg=Linked Providers
inboxmanager.documents=Docs
inboxmanager.notAssignedDocs=Not Assigned Docs
inboxmanager.documentsInQueues=Documents In Queues
inboxmanager.document.pendingDocs=Pending Docs
inboxmanager.document.RemoveLinkedProviderMsg=(Remove)
inboxmanager.document.incomingDocs = Incoming Docs

inboxmanager.document.changeView=Change view
inboxmanager.document.listView=List
inboxmanager.document.readerView=Preview

inboxmanager.document.split=Split
inboxmanager.document.rotate180=Rotate 180&deg;
inboxmanager.document.rotate90=Rotate 90&deg;
inboxmanager.document.removeFirstPage=Delete First Page

dms.documentReport.title=DOCUMENT MANAGEMENT SYSTEM
dms.documentReport.msgDelete=Are you sure you want to delete
dms.documentReport.msgNotAllowed=You're not allowed to delete this document!
dms.documentReport.msgDocReport=DOCUMENT REPORT
dms.documentReport.btnAddHTML=Add HTML Code
dms.documentReport.btnAddDoc=Add Document
dms.documentReport.msgDocDesc=Document Description
dms.documentReport.msgDocType=Document Type
dms.documentReport.msgUpdate=Updatedate
dms.documentReport.msgCreator=Creator
dms.documentReport.msgAction=Action
dms.documentReport.btnDelete=Delete
dms.documentReport.btnUnDelete=Undelete
dms.documentReport.btnEdit=Edit
dms.documentReport.msgNoMatch=no match found
dms.documentReport.msgShareFolder=Share folder
dms.documentReport.msgActive=Active
dms.documentReport.btnDoneClose=Done - Close Window
dms.documentReport.msgViewStatus=View Status
dms.documentReport.msgAll=All
dms.documentReport.msgDeleted=Deleted
dms.documentReport.msgPublished=Published
dms.documentReport.msgDocuments=Documents
dms.documentReport.msgContent=Content
dms.documentReport.msgType=Type
dms.documentReport.msgDisplayDocumentCategories=Display Document Categories
dms.documentReport.msgDocumentCategories=Document Categories
dms.documentReport.msgDemographicDocumentCategories=Demographic Document Categories
dms.documentReport.msgProviderDocumentCategories=Provider Document Categories
dms.documentReport.msgDocumentType=Document type
dms.documentReport.msgAddNewDocumentType=Add New Document Type
dms.documentReport.msgEnterDocumentType=Enter document Type:
dms.documentReport.msgResponsible=Responsible
dms.documentReport.msgReviewer=Reviewer
dms.documentReport.msgNoDocumentsToDisplay=No documents to display
dms.documentReport.btnCombinePDF=Combine PDFs
dms.documentReport.msgDate=Date
dms.documentReport.msgView=View
dms.documentReport.msgBrowser=Browser
dms.documentReport.msgFlagAbnormal=Flag as abnormal

dms.documentBrowser.title=DOCUMENT BROWSER
dms.documentBrowser.msgView=View
dms.documentBrowser.msgViewStatus=View Status
dms.documentBrowser.msgAll=All
dms.documentBrowser.msgDeleted=Deleted
dms.documentBrowser.msgPublished=Published
dms.documentBrowser.msgOnlyPDFCanBeCombined= Only PDFs can be combined.
dms.documentBrowser.msgSortDate=Sort Date
dms.documentBrowser.msgUpdate=Update
dms.documentBrowser.msgContent=Content
dms.documentBrowser.msgObservation=Observation
dms.documentBrowser.msgFileButNotAcknowledgedOn=Filed but not Acknowledged on
dms.documentBrowser.msgNotAcknowledgeSince=Not Acknowledged since
dms.documentBrowser.msgAcknowledgedOn=Acknowledged on
dms.documentBrowser.DocumentUpdated=Document Updated
dms.documentBrowser.ContentUpdated= Content Updated
dms.documentBrowser.ObservationDate=Observation Date
dms.documentBrowser.Type=Type
dms.documentBrowser.Class=Class
dms.documentBrowser.Subclass=Subclass
dms.documentBrowser.Description=Description
dms.documentBrowser.Creator=Creator
dms.documentBrowser.Responsible=Responsible
dms.documentBrowser.Reviewer=Reviewer
dms.documentBrowser.Source=Source
dms.documentBrowser.ObservationTypeDescription=&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Observ.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;[type] Description
dms.documentBrowser.msgAddTickler=Add Tikler
dms.documentBrowser.msgDelete=Delete
dms.documentBrowser.msgUndelete=Undelete
dms.documentBrowser.msgAnnotate=Annotate
dms.documentBrowser.msgEdit=Edit
dms.documentBrowser.msgRefile=Refile

dms.incomingDocs.queue=Queue
dms.incomingDocs.fax=Fax
dms.incomingDocs.mail=Mail
dms.incomingDocs.file=File
dms.incomingDocs.refile=Refile
dms.incomingDocs.noFile=No File
dms.incomingDocs.PDF=PDF
dms.incomingDocs.image=Image
dms.incomingDocs.page=Page
dms.incomingDocs.first=First
dms.incomingDocs.next=Next
dms.incomingDocs.previous=Previous
dms.incomingDocs.last=Last
dms.incomingDocs.selectPDF=Select PDF
dms.incomingDocs.selectPage=Select Page
dms.incomingDocs.rotateThisPage=Rotate this page
dms.incomingDocs.rotateAllPages=Rotate all pages
dms.incomingDocs.extractPage=Extract page
dms.incomingDocs.deletePage=Delete page
dms.incomingDocs.deletePDF=Delete
dms.incomingDocs.type=Type
dms.incomingDocs.select=Select
dms.incomingDocs.selectType=Select Type
dms.incomingDocs.class=Class
dms.incomingDocs.selectClass=Select Class
dms.incomingDocs.docSubClass=Sub Class
dms.incomingDocs.description=Description
dms.incomingDocs.obsDate=Obs. Date
dms.incomingDocs.lastPatients=Last Patients
dms.incomingDocs.cannotExtractPage=Cannot extract pages
dms.incomingDocs.pagesInvalid=Pages invalid:
dms.incomingDocs.nothingToDelete=Nothing to delete.
dms.incomingDocs.onlyOneToDeleteUseDeletePDF=Only 1 page. Use Delete PDF instead
dms.incomingDocs.areYouSureToDeletePage=Are you sure you want to delete page #
dms.incomingDocs.areYouSureToDelete=Are you sure you want to delete 
dms.incomingDocs.nothingToExtract=Nothing to extract
dms.incomingDocs.enterPagesToExtract=Enter page numbers to extract (e.g: 1-3 or 3,5,6)
dms.incomingDocs.nothingToSave=Nothing to save
dms.incomingDocs.missingDocumentType=Cannot save: Missing document type 
dms.incomingDocs.missingDocumentDescription=Cannot save: Missing document description
dms.incomingDocs.saveWithoutFlagging=Save without flagging any provider ?
dms.incomingDocs.selectDemographicFirst=Select demographic first
dms.incomingDocs.invalidPages= Invalid Pages
dms.incomingDocs.cannotDeletePage=Cannot delete page 
dms.incomingDocs.cannotRotateAllPages=Cannot rotate all pages 
dms.incomingDocs.cannotRotatePage=Cannot rotate page 
dms.incomingDocs.cannotDelete=Cannot delete 
dms.incomingDocs.MRP=MRP
dms.incomingDocs.noMRP= There is no MRP
dms.incomingDocs.viewAs=View as
dms.incomingDocs.demographic=Demographic
dms.incomingDocs.of=of
dms.incomingDocs.flagProvider=Flag Provider
dms.incomingDocs.dataEntryMode=Data Entry Mode
dms.incomingDocs.normal=Normal
dms.incomingDocs.fast = Fast
dms.incomingDocs.selectDocumentFirst = Select a document first
dms.incomingDocs.nothingSelected= Nothing selected
dms.incomingDocs.createNewDemographic= Create New Demographic
dms.incomingDocs.errorInOpening= Error in opening: 
dms.incomingDocs.PDFCouldBeCorrupted=The PDF could be corrupted.
dms.incomingDocs.invalidDate= Invalid Date

dms.addHTMLDocument.title=DOCUMENT MANAGEMENT SYSTEM
dms.addHTMLDocument.msgAddHtml=ADD HTML DOCUMENT
dms.addHTMLDocument.msgFunction=Function
dms.addHTMLDocument.msgFunctionID=Function ID
dms.addHTMLDocument.formDocumentType=Document Type
dms.addHTMLDocument.formSelectType=Select Type
dms.addHTMLDocument.formDocumentClass=Report Class
dms.addHTMLDocument.formDocumentSubClass=Report SubClass
dms.addHTMLDocument.formSelectClass=Select Class
dms.addHTMLDocument.formSelectSubClass=Select SubClass
dms.addHTMLDocument.formSelectSubClass2=Select Part 2
dms.addHTMLDocument.formDocDescription=Document Description
dms.addHTMLDocument.formURL=URL
dms.addHTMLDocument.btnRedirect=Redirect
dms.addHTMLDocument.formHtmlSource=HTML Source
dms.addHTMLDocument.msgCreateDate=Create Date
dms.addHTMLDocument.msgCreator=Creator
dms.addHTMLDocument.btnSubmit=Submit

dms.addDocument.title=DOCUMENT MANAGEMENT SYSTEM
dms.addDocument.msgFunction=Function
dms.addDocument.msgInstructionTitle=Instruction:
dms.addDocument.msgInstructions=1. click upload file<br>2. select a file to upload<br>3. click &quot;upload&quot; button<br>4. once the file uploaded, click &quot;done&quot;<br>5. The file name will appear <br>6. Select document type<br>7. Enter document description<br>8. click submit, done.
dms.addDocument.msgID=Function ID
dms.addDocument.msgDocType=Document Type
dms.addDocument.msgDocClass=Report Class
dms.addDocument.msgDocSubClass=Report SubClass
dms.addDocument.formSelect=Select Type
dms.addDocument.formSelectClass=Select Class
dms.addDocument.formSelectSubClass=Select SubClass
dms.addDocument.formSelectSubClass2=Select Part 2
dms.addDocument.formDocDesc=Document Description
dms.addDocument.formContentAddedUpdated=Content Added/Updated
dms.addDocument.formSentDate=Sent Date
dms.addDocument.msgDate=Create Date
dms.addDocument.msgCreator=Creator
dms.addDocument.msgFileName=File Name
dms.addDocument.btnUpload=upload file
dms.addDocument.btnSubmit=SUBMIT

dms.addDocument.msgAddDocument=Add Document
dms.addDocument.msgManageUploadDocument=Manage Upload Documents
dms.addDocument.AddLink=Add Link
dms.addDocument.AddHTML=Add HTML

dms.addDocument.errorZeroSize=Empty files not accepted. Upload failed.
dms.addDocument.errorNoWrite=File could not be saved. Upload failed.

dms.documentGetFile.title=OSCAR - Document Viewer
dms.documentGetFile.msgFileNotfound=Error: File not found.

dms.docViewerHead.title=Untitled Document
dms.docViewerHead.msgDocumentViewer=DOCUMENT VIEWER

dms.documentDelete.msgDeleteDoc=DELETE A DOCUMENT
dms.documentDelete.msgSuccess=Successful Delete a Record.
dms.documentDelete.msgFailed=Sorry, update has failed.

dms.documentDelete.msgError=Error: Description, Filename, HTML content and Document Type  must be entered.

dms.dbDocument.msgError=Error: Description, Filename and Document Type  must be entered.

dms.errorpage.titlex=DOCUMENT MANAGEMENT SYSTEM
dms.errorpage.msgErrorPage=DOCUMENT ERROR PAGE

dms.documentList.title=DOCUMENT MANAGEMENT SYSTEM
dms.documentList.msgDocActivity=DOCUMENT ACTIVITY LIST
dms.documentList.msgDocDesc=Document Description
dms.documentList.msgDocType=Document Type
dms.documentList.msgCreator=Creator
dms.documentList.msgUpdate=Updatedate
dms.documentList.msgStatus=Status

dms.documentEdit.title=DOCUMENT MANAGEMENT SYSTEM
dms.documentEdit.msgAddDocument=ADD DOCUMENT
dms.documentEdit.formDocDesc=Document Description
dms.documentEdit.formDocType=Document Type
dms.documentEdit.formAddNewDocType=Add New 
dms.documentEdit.formSelType=Select Type
dms.documentEdit.msgSelectModuleName=Select module name:
dms.documentEdit.formDocumentClass=Report Class
dms.documentEdit.formDocumentSubClass=Report SubClass
dms.documentEdit.formSelectClass=Select Class
dms.documentEdit.formSelectSubClass=Select SubClass
dms.documentEdit.formSelectSubClass2=Select Part 2
dms.documentEdit.formDate=Update Date
dms.documentEdit.btnChange=Change

dms.documentUploader.destination=Destination
dms.documentUploader.folder=Folder
dms.documentUpload.alreadyExists= already exists in incoming Docs
dms.documentUpload.onlyPdf= Only .pdf file can be uploaded to incoming Docs

dms.zadddocument.title=DOCUMENT MANAGEMENT SYSTEM
dms.zadddocument.msgUpload=UPLOAD DOCUMENT
dms.zadddocument.msgAddDocument=Add Document
dms.zadddocument.btnUpload=Upload

dms.error.descriptionInvalid=Description missing
dms.error.typeMissing=Document type missing
dms.error.uploadError=Document failed to upload
dms.error.htmlMissing=Field missing
dms.error.moduleMissing=Module type missing
dms.error.unsupportedFileType=File type not supported
dms.error.statusMissing=Document Type Status missing
dms.uptdateBtnStatus=Update Status

indivo.authenticateError=Authentication Failed {0}
indivo.sendbinFileError=Error Sending File {0} {1}
indivo.configError=Provider id must be provided along with file id to send
indivo.successMsg=PHR has been successfully updated!
indivo.errorMsg=An Error Ocurred Updating Indivo

oscarEncounter.close.msgClick1=Click
oscarEncounter.close.btnHere=here
oscarEncounter.close.msgClick2= to close this window.

demographic.search.title=PATIENT SEARCH RESULTS
demographic.search.msgWrongDOB=You have a wrong DOB input!!!
demographic.search.msgWrongRosterDate=Invalid Roster Date!
demographic.search.msgWrongRosterTerminationDate=Invalid Roster Termination Date!
demographic.search.msgWrongPatientStatusDate=Invalid Patient Status Date!
demographic.search.msgLeaveBlank=You may leave it blank.
demographic.search.msgForbiddenRosterDate=You cannot enter Roster Date when Roster Status is Blank!
demographic.demographiceditdemographic.msgNoTerminationReason=You must pick a Roster Termination Reason!
demographic.demographiceditdemographic.msgBlankRoster=You must pick a Roster Status!
demographic.search.msgSearchPatient=Patient Search
demographic.search.msgSearch=Search
demographic.search.formName=Name
demographic.search.formPhone=Phone
demographic.search.formDOB=DOB(yyyymmdd)
demographic.search.formAddr=Address
demographic.search.formHIN=Health Ins. #
demographic.search.formChart=Chart No
demographic.search.btnCreateNew=Create Demographic
demographic.search.btnReturnToSchedule=Return to Schedule
demographic.search.btnCreateNewTitle=Create A New Demographic
demographic.search.btnELearning=E-Learning Search
demographic.search.btnSearch=Search
demographic.search.msgInclIntegratedResults=Incl. Integrated Results

demographic.demographicsearchresults.title=PATIENT SEARCH RESULTS
demographic.demographicsearchresults.msgWrongDOB=You have a wrong DOB format!!!
demographic.demographicsearchresults.msgSearchPatient=SEARCH FOR PATIENT RECORDS
demographic.demographicsearchresults.msgSearchKeys=Results based on keyword(s)
demographic.demographicsearchresults.msgRecentPatients=Most recent patients
demographic.demographicsearchresults.btnDemoNo=Demographic No.
demographic.demographicsearchresults.module=Module
demographic.demographicsearchresults.btnName=Name
demographic.demographicsearchresults.btnLastName=Last Name
demographic.demographicsearchresults.btnFirstName=First Name
demographic.demographicsearchresults.btnChart=Chart No.
demographic.demographicsearchresults.btnSex=Sex
demographic.demographicsearchresults.btnDOB=DOB 
demographic.demographicsearchresults.btnDOBFormat=yyyy-mm-dd
demographic.demographicsearchresults.btnDoctor=Doctor
demographic.demographicsearchresults.btnRosSta=Roster Status
demographic.demographicsearchresults.btnPatSta=Patient Status
demographic.demographicsearchresults.btnPhone=Phone
demographic.demographicsearchresults.btnLastPage=Last Page
demographic.demographicsearchresults.btnNextPage=Next Page
demographic.demographicsearchresults.msgClick=Please select by clicking on the patient's demographic id for details.
demographic.demographicsearchresults.btnDemoName = Name

demographic.zdemographicfulltitlesearch.msgSearch=Search
demographic.zdemographicfulltitlesearch.msgBy=By
demographic.zdemographicfulltitlesearch.msgInput=Input
demographic.zdemographicfulltitlesearch.formName=Name
demographic.zdemographicfulltitlesearch.formPhone=Phone
demographic.zdemographicfulltitlesearch.formCellPhone=Cell Phone
demographic.zdemographicfulltitlesearch.formDOB=DOB yyyy-mm-dd
demographic.zdemographicfulltitlesearch.formAddr=Address
demographic.zdemographicfulltitlesearch.formHIN=Health Ins. #
demographic.zdemographicfulltitlesearch.formChart=Chart No
demographic.zdemographicfulltitlesearch.formNote=Note
demographic.zdemographicfulltitlesearch.formEmail=Email
demographic.zdemographicfulltitlesearch.formDemographicNo=Demographic No
demographic.zdemographicfulltitlesearch.formFirstname=firstname and/or second firstname
demographic.zdemographicfulltitlesearch.formBandNumber=Band #
oscarEncounter.formAnnual.title=Annual Health Review

oscarEncounter.formMaleAnnual.title=Annual Health Review
oscarEncounter.formMaleAnnual.msgWannaSave=Are you sure you want to save this form?
oscarEncounter.formMaleAnnual.msgNotSave=Are you sure you wish to exit without saving your changes?
oscarEncounter.formMaleAnnual.msgSaveExit=Are you sure you wish to save and close this window?
oscarEncounter.formMaleAnnual.msgTypeANumber=You must type in a number in the field.
oscarEncounter.formMaleAnnual.btnSave=Save
oscarEncounter.formMaleAnnual.btnSaveExit=Save and Exit
oscarEncounter.formMaleAnnual.btnExit=Exit
oscarEncounter.formMaleAnnual.btnPrint=Print
oscarEncounter.formMaleAnnual.btnAnnualReview=Annual Review Planner
oscarEncounter.formMaleAnnual.msgAnnualMaleReview=ANNUAL MALE HEALTH REVIEW
oscarEncounter.formMaleAnnual.formName=Name
oscarEncounter.formMaleAnnual.formAge=Age
oscarEncounter.formMaleAnnual.formDate=Date
oscarEncounter.formMaleAnnual.msgCurrentConcerns=CURRENT CONCERNS
oscarEncounter.formMaleAnnual.msgSeeChart=See chart for continuation
oscarEncounter.formMaleAnnual.btnNo=No
oscarEncounter.formMaleAnnual.btnYes=Yes
oscarEncounter.formMaleAnnual.msgSystemsReview=SYSTEMS REVIEW
oscarEncounter.formMaleAnnual.formN=N
oscarEncounter.formMaleAnnual.formAbN=AbN
oscarEncounter.formMaleAnnual.formHeadNeck=Head & Neck
oscarEncounter.formMaleAnnual.formResp=Resp
oscarEncounter.formMaleAnnual.formCardio=Cardio
oscarEncounter.formMaleAnnual.formGI=G.I.
oscarEncounter.formMaleAnnual.formGU=G.U.
oscarEncounter.formMaleAnnual.formSkin=Skin
oscarEncounter.formMaleAnnual.formMSK=MSK
oscarEncounter.formMaleAnnual.formEndocrin=Endocrin
oscarEncounter.formMaleAnnual.formOther=OTHER
oscarEncounter.formMaleAnnual.formReview=REVIEW
oscarEncounter.formMaleAnnual.formDrugs=Drugs
oscarEncounter.formMaleAnnual.formMedSheet=Med. Sheet Updated
oscarEncounter.formMaleAnnual.formAllergies=Allergies & Drug Reactions
oscarEncounter.formMaleAnnual.formFrontSheet=Front Sheet Updated
oscarEncounter.formMaleAnnual.formFamilyHist=Family History
oscarEncounter.formMaleAnnual.msgLifestyleReview=LIFESTYLE REVIEW
oscarEncounter.formMaleAnnual.formNo=No
oscarEncounter.formMaleAnnual.formYes=Yes
oscarEncounter.formMaleAnnual.formSmoking=Smoking
oscarEncounter.formMaleAnnual.formAlcohol=Alcohol
oscarEncounter.formMaleAnnual.formIllicitDrugs=OTC/Illicit Drugs
oscarEncounter.formMaleAnnual.formExcercise=Exercise/Sports
oscarEncounter.formMaleAnnual.formNitrition=Nutrition
oscarEncounter.formMaleAnnual.formDentalHygiene=Dental Hygiene
oscarEncounter.formMaleAnnual.formRelationship=Relationship Issues
oscarEncounter.formMaleAnnual.formSexualityRisks=Sexuality Risks (STD/HIV)
oscarEncounter.formMaleAnnual.formOccupationalRisks=Occupational Risks
oscarEncounter.formMaleAnnual.formDrivingSafety=Driving Safety
oscarEncounter.formMaleAnnual.formForeignTravel=Foreign Travel (in last yr.)
oscarEncounter.formMaleAnnual.msgScreeningReview=SCREENING REVIEW
oscarEncounter.formMaleAnnual.btnRisk=Risk/Check List
oscarEncounter.formMaleAnnual.msgPhysicalExam=PHYSICAL EXAM
oscarEncounter.formMaleAnnual.msgVitals=VITALS
oscarEncounter.formMaleAnnual.formBP=B.P
oscarEncounter.formMaleAnnual.formPulse=Pulse
oscarEncounter.formMaleAnnual.msgMinute=min
oscarEncounter.formMaleAnnual.formHeight=Height
oscarEncounter.formMaleAnnual.msgHeightUnit=cm
oscarEncounter.formMaleAnnual.formWeight=Weight
oscarEncounter.formMaleAnnual.msgWeightUnit=Kg
oscarEncounter.formMaleAnnual.msgR=(R)
oscarEncounter.formMaleAnnual.msgL=(L)
oscarEncounter.formMaleAnnual.formRythm=Rhythm
oscarEncounter.formMaleAnnual.formUrineDipstick=Urine Dipstick
oscarEncounter.formMaleAnnual.formPhysicalSigns= PHYSICAL SIGNS
oscarEncounter.formMaleAnnual.formAssessment=ASSESSMENT
oscarEncounter.formMaleAnnual.formPlan=PLAN
oscarEncounter.formMaleAnnual.formSignature=Signature
oscarEncounter.formMaleAnnual.msgAnyConcerns=Any concerns with ...?

oscarEncounter.formFemaleAnnual.msgAnyConcerns=Any concerns with ...?
oscarEncounter.formFemaleAnnual.title=Annual Health Review
oscarEncounter.formFemaleAnnual.msgWannaSave=Are you sure you want to save this form?
oscarEncounter.formFemaleAnnual..msgNotSave=Are you sure you wish to exit without saving your changes?
oscarEncounter.formFemaleAnnual.msgSaveExit=Are you sure you wish to save and close this window?
oscarEncounter.formFemaleAnnual.msgTypeANumber=You must type in a number in the field.
oscarEncounter.formFemaleAnnual.btnSave=Save
oscarEncounter.formFemaleAnnual.btnSaveExit=Save and Exit
oscarEncounter.formFemaleAnnual.btnExit=Exit
oscarEncounter.formFemaleAnnual.btnPrint=Print
oscarEncounter.formFemaleAnnual.btnPrintPage=Print Page
oscarEncounter.formFemaleAnnual.btnAnnualReview=Annual Review Planner
oscarEncounter.formFemaleAnnual.msgAnnualFemaleReview=ANNUAL FEMALE HEALTH REVIEW
oscarEncounter.formFemaleAnnual.formName=Name
oscarEncounter.formFemaleAnnual.formAge=Age
oscarEncounter.formFemaleAnnual.formDate=Date
oscarEncounter.formFemaleAnnual.msgCurrentConcerns=CURRENT CONCERNS
oscarEncounter.formFemaleAnnual.msgSeeChart=See chart for continuation
oscarEncounter.formFemaleAnnual.brtNo=No
oscarEncounter.formFemaleAnnual.btnYes=Yes
oscarEncounter.formFemaleAnnual.msgSystemReview=SYSTEMS REVIEW
oscarEncounter.formFemaleAnnual.formN=N
oscarEncounter.formFemaleAnnual.formAbN=AbN
oscarEncounter.formFemaleAnnual.formHeadNeck=Head & Neck
oscarEncounter.formFemaleAnnual.fomrResp=Resp
oscarEncounter.formFemaleAnnual.formCardio=Cardio
oscarEncounter.formFemaleAnnual.formGI=G.I.
oscarEncounter.formFemaleAnnual.formGU=G.U.
oscarEncounter.formFemaleAnnual.formSkin=Skin
oscarEncounter.formFemaleAnnual.formMSK=MSK
oscarEncounter.formFemaleAnnual.formEndocrin=Endocrin
oscarEncounter.formFemaleAnnual.formOther=OTHER
oscarEncounter.formFemaleAnnual.msgGTPALRevisions=GTPAL Revisions?
oscarEncounter.formFemaleAnnual.formFrontSheeyUpdated=Front Sheet Updated
oscarEncounter.formFemaleAnnual.formLMP=LMP
oscarEncounter.formFemaleAnnual.formMenopause=Menopause
oscarEncounter.formFemaleAnnual.formMenopauseUnit=yrs.
oscarEncounter.formFemaleAnnual.formPreviousPap=Previous Pap. Smears
oscarEncounter.formFemaleAnnual.forReview=REVIEW
oscarEncounter.formFemaleAnnual.formDrugs=Drugs
oscarEncounter.formFemaleAnnual.formMedSheet=Med. Sheet Updated
oscarEncounter.formFemaleAnnual.formAllergies=Allergies & Drug Reactions
oscarEncounter.formFemaleAnnual.formFrontSheet=Front Sheet Updated
oscarEncounter.formFemaleAnnual.formFamilyHist=Family History
oscarEncounter.formFemaleAnnual.msgLifestyleReview=LIFESTYLE REVIEW
oscarEncounter.formFemaleAnnual.formNo=No
oscarEncounter.formFemaleAnnual.formYes=Yes
oscarEncounter.formFemaleAnnual.formSmoking=Smoking
oscarEncounter.formFemaleAnnual.formAlcohol=Alcohol
oscarEncounter.formFemaleAnnual.formIllicitDrugs=OTC/Illicit Drugs
oscarEncounter.formFemaleAnnual.formExercise=Exercise/Sports
oscarEncounter.formFemaleAnnual.formNutrition=Nutrition
oscarEncounter.formFemaleAnnual.formDentalHygiene=Dental Hygiene
oscarEncounter.formFemaleAnnual.formRelationship=Relationship Issues
oscarEncounter.formFemaleAnnual.formSexualityRisks=Sexuality Risks (STD/HIV)
oscarEncounter.formFemaleAnnual.formOccupationalRisks=Occupational Risks
oscarEncounter.formFemaleAnnual.formDrivingSafety=Driving Safety
oscarEncounter.formFemaleAnnual.formForeignTravel=Foreign Travel (in last yr.)
oscarEncounter.formFemaleAnnual.msgScreeningReview=SCREENING REVIEW
oscarEncounter.formFemaleAnnual.btnRisk=Risk/Check List
oscarEncounter.formFemaleAnnual.formMammogram=Mammogram
oscarEncounter.formFemaleAnnual.formBreastSelf=Breast-Self Exam
oscarEncounter.formFemaleAnnual.formPapSmear=Pap Smear
oscarEncounter.formFemaleAnnual.formImmunization=Immunization
oscarEncounter.formFemaleAnnual.formPrecontraceptive=Precontraceptive Councelling (Rubella, Folate)
oscarEncounter.formFemaleAnnual.formCardiacRiskFactors=Cardiac Risk Factors
oscarEncounter.formFemaleAnnual.formOsteoporosisRisk=Osteoporosis Risk
oscarEncounter.formFemaleAnnual.msgVitals=VITALS
oscarEncounter.formFemaleAnnual.formBP=B.P
oscarEncounter.formFemaleAnnual.formPulse=Pulse
oscarEncounter.formFemaleAnnual.msgR=(R)
oscarEncounter.formFemaleAnnual.formHeight=Height
oscarEncounter.formFemaleAnnual.HeightUnit=cm.
oscarEncounter.formFemaleAnnual.formRhythm=Rhythm
oscarEncounter.formFemaleAnnual.formUrineDipstick=Urine Dipstick
oscarEncounter.formFemaleAnnual.formWeight=Weight
oscarEncounter.formFemaleAnnual.formWeightUnit=Kg.
oscarEncounter.formFemaleAnnual.msgL=(L)
oscarEncounter.formFemaleAnnual.msgPhysicalExam=PHYSICAL SIGNS
oscarEncounter.formFemaleAnnual.formAssessment=ASSESSMENT
oscarEncounter.formFemaleAnnual.formPlan=PLAN
oscarEncounter.formFemaleAnnual.formSignature=Signature

oscarEncounter.formAnnualPrint.title=Annual Health Review
oscarEncounter.formAnnualPrint.msgAnnualHealthReview=ANNUAL HEALTH REVIEW
oscarEncounter.formAnnualPrint.msgName=Name
oscarEncounter.formAnnualPrint.msgAge=Age
oscarEncounter.formAnnualPrint.msgDate=Date
oscarEncounter.formAnnualPrint.msgCurrentConcerns=CURRENT CONCERNS
oscarEncounter.formAnnualPrint.msgSeeChart=See chart for continuation
oscarEncounter.formAnnualPrint.msgNo=No
oscarEncounter.formAnnualPrint.msgYes=Yes
oscarEncounter.formAnnualPrint.msgSystemsReview=SYSTEMS REVIEW
oscarEncounter.formAnnualPrint.msgN=N
oscarEncounter.formAnnualPrint.msgAbN=AbN
oscarEncounter.formAnnualPrint.msgHeadNeck=Head & Neck
oscarEncounter.formAnnualPrint.msgResp=Resp
oscarEncounter.formAnnualPrint.msgCardio=Cardio
oscarEncounter.formAnnualPrint.msgGI=G.I.
oscarEncounter.formAnnualPrint.msgGU=G.U.
oscarEncounter.formAnnualPrint.msgWomen=Women
oscarEncounter.formAnnualPrint.msgGTPAL=GTPAL Revisions?
oscarEncounter.formAnnualPrint.msgFrontSheet=Front Sheet Updated
oscarEncounter.formAnnualPrint.msgLMP=LMP
oscarEncounter.formAnnualPrint.msgMenopause=Menopause
oscarEncounter.formAnnualPrint.msgMenopauseUnit=yrs.
oscarEncounter.formAnnualPrint.msgPreviousPapSmears=Previous Pap. Smears
oscarEncounter.formAnnualPrint.msgSkin=Skin
oscarEncounter.formAnnualPrint.msgneck=& Neck
oscarEncounter.formAnnualPrint.msgMSK=MSK
oscarEncounter.formAnnualPrint.msgEndocrin=Endocrin
oscarEncounter.formAnnualPrint.msgOther=OTHER
oscarEncounter.formAnnualPrint.msgReview=REVIEW
oscarEncounter.formAnnualPrint.msgDrugs=Drugs
oscarEncounter.formAnnualPrint.msgMedSheet=Med. Sheet Updated
oscarEncounter.formAnnualPrint.msgFrontSheetUpdated=Front Sheet Updated
oscarEncounter.formAnnualPrint.msgLifestyleReview=LIFESTYLE REVIEW
oscarEncounter.formAnnualPrint.msgSmoking=Smoking
oscarEncounter.formAnnualPrint.msgSexualityRisks=Sexuality Risks (STD/HIV)
oscarEncounter.formAnnualPrint.msgAlcohol=Alcohol
oscarEncounter.formAnnualPrint.msgOccupationalRisks=Occupational Risks
oscarEncounter.formAnnualPrint.msgIllicitDrugs=OTC/Illicit Drugs
oscarEncounter.formAnnualPrint.msgDrivingSafety=Driving Safety
oscarEncounter.formAnnualPrint.msgExercise=Exercise/Sports
oscarEncounter.formAnnualPrint.msgForeignTravel=Foreign Travel (in last yr.)
oscarEncounter.formAnnualPrint.msgNutrition=Nutrition
oscarEncounter.formAnnualPrint.msgDentalHygiene=Dental Hygiene
oscarEncounter.formAnnualPrint.msgRelationshipIssues=Relationship Issues
oscarEncounter.formAnnualPrint.msgScreeningReview=SCREENING REVIEW
oscarEncounter.formAnnualPrint.msgMen=Men
oscarEncounter.formAnnualPrint.msgMammogram=Mammogram
oscarEncounter.formAnnualPrint.msgRectalExam=Rectal Exam
oscarEncounter.formAnnualPrint.msgBreastSelf=Breast-Self Exam
oscarEncounter.formAnnualPrint.msgCardiacRisk=Cardiac Risk Factors
oscarEncounter.formAnnualPrint.msgPapSmear=Pap Smear
oscarEncounter.formAnnualPrint.msgImmunization=Immunization
oscarEncounter.formAnnualPrint.msgPrecontraceptive=Precontraceptive Councelling (Rubella, Folate)
oscarEncounter.formAnnualPrint.msgOsteoporosis=Osteoporosis Risk
oscarEncounter.formAnnualPrint.msgPhysicalExam=PHYSICAL EXAM
oscarEncounter.formAnnualPrint.msgVitals=VITALS
oscarEncounter.formAnnualPrint.msgBP=B.P
oscarEncounter.formAnnualPrint.msgR=(R)
oscarEncounter.formAnnualPrint.msgPulse=Pulse
oscarEncounter.formAnnualPrint.msgPulseUnit=min
oscarEncounter.formAnnualPrint.msgHeight=Height
oscarEncounter.formAnnualPrint.msgHeightUnit=cm.
oscarEncounter.formAnnualPrint.msgWeight=Weight
oscarEncounter.formAnnualPrint.msgWeightUnit=Kg.
oscarEncounter.formAnnualPrint.msgL=(L)
oscarEncounter.formAnnualPrint.msgRhythm=Rhythm
oscarEncounter.formAnnualPrint.msgUrinedipstick=Urine Dipstick
oscarEncounter.formAnnualPrint.msgPhysicalSigns=PHYSICAL SIGNS
oscarEncounter.formAnnualPrint.msgIconOfBreasts=icon of breasts
oscarEncounter.formAnnualPrint.msgSomeBlankSpace=some blank space
oscarEncounter.formAnnualPrint.msgIconOfAbdomen=icon of abdomen
oscarEncounter.formAnnualPrint.msgAssessment=ASSESSMENT
oscarEncounter.formAnnualPrint.msgPlan=PLAN
oscarEncounter.formAnnualPrint.msgSignature=Signature
oscarEncounter.formAnnualPrint.btnCancel=Cancel
oscarEncounter.formAnnualPrint.btnPrint=Print

oscarEncounter.formFemaleAnnualPrint.title=Annual Female Health Review
oscarEncounter.formFemaleAnnualPrint.msgAnnualFemaleHealthReview=ANNUAL FEMALE HEALTH REVIEW
oscarEncounter.formFemaleAnnualPrint.msgName=Name
oscarEncounter.formFemaleAnnualPrint.msgAge=Age
oscarEncounter.formFemaleAnnualPrint.msgDate=Date
oscarEncounter.formFemaleAnnualPrint.msgCurrentConcerns=CURRENT CONCERNS
oscarEncounter.formFemaleAnnualPrint.msgSeeChart=See chart for continuation
oscarEncounter.formFemaleAnnualPrint.msgNo=No
oscarEncounter.formFemaleAnnualPrint.msgyes=Yes
oscarEncounter.formFemaleAnnualPrint.msgSystemReview=SYSTEMS REVIEW
oscarEncounter.formFemaleAnnualPrint.msgN=N
oscarEncounter.formFemaleAnnualPrint.msgAbN=AbN
oscarEncounter.formFemaleAnnualPrint.msgHeadNeck=Head & Neck
oscarEncounter.formFemaleAnnualPrint.msgResp=Resp
oscarEncounter.formFemaleAnnualPrint.msgCardio=Cardio
oscarEncounter.formFemaleAnnualPrint.msgGI=G.I.
oscarEncounter.formFemaleAnnualPrint.msgGU=G.U.
oscarEncounter.formFemaleAnnualPrint.msgSkin=Skin
oscarEncounter.formFemaleAnnualPrint.MSK=MSK
oscarEncounter.formFemaleAnnualPrint.msgEndocrin=Endocrin
oscarEncounter.formFemaleAnnualPrint.msgOther=OTHER
oscarEncounter.formFemaleAnnualPrint.msgGTPAL=GTPAL Revisions?
oscarEncounter.formFemaleAnnualPrint.msgFrontSheet=Front Sheet Updated
oscarEncounter.formFemaleAnnualPrint.msgLMP=LMP
oscarEncounter.formFemaleAnnualPrint.msgMenopause=Menopause
oscarEncounter.formFemaleAnnualPrint.msgMenopauseUnit=yrs.
oscarEncounter.formFemaleAnnualPrint.msgPreviousPaoSmears=Previous Pap. Smears
oscarEncounter.formFemaleAnnualPrint.msgReview=REVIEW
oscarEncounter.formFemaleAnnualPrint.msgDrugs=Drugs
oscarEncounter.formFemaleAnnualPrint.msgMedSheet=Med. Sheet Updated
oscarEncounter.formFemaleAnnualPrint.msgAllergies=Allergies & Drug Reactions
oscarEncounter.formFemaleAnnualPrint.msgFamilyHist=Family History
oscarEncounter.formFemaleAnnualPrint.msgLifestyle=LIFESTYLE REVIEW
oscarEncounter.formFemaleAnnualPrint.msgSmoking=Smoking
oscarEncounter.formFemaleAnnualPrint.msgAlcohol=Alcohol
oscarEncounter.formFemaleAnnualPrint.msgIllicitDrugs=OTC/Illicit Drugs
oscarEncounter.formFemaleAnnualPrint.msgExercise=Exercise/Sports
oscarEncounter.formFemaleAnnualPrint.msgNutrition=Nutrition
oscarEncounter.formFemaleAnnualPrint.msgDentalHygiene=Dental Hygiene
oscarEncounter.formFemaleAnnualPrint.msgRelationshipIssues=Relationship Issues
oscarEncounter.formFemaleAnnualPrint.msgSexualityRisks=Sexuality Risks (STD/HIV)
oscarEncounter.formFemaleAnnualPrint.msgOccupationalRisks=Occupational Risks
oscarEncounter.formFemaleAnnualPrint.msgDrivingSafety=Driving Safety
oscarEncounter.formFemaleAnnualPrint.msgForeignTravel=Foreign Travel (in last yr.)
oscarEncounter.formFemaleAnnualPrint.msgScreeningReview=SCREENING REVIEW
oscarEncounter.formFemaleAnnualPrint.msgMammogram=Mammogram
oscarEncounter.formFemaleAnnualPrint.msgBreastSelfTest=Breast-Self Exam
oscarEncounter.formFemaleAnnualPrint.msgPapSmear=Pap Smear
oscarEncounter.formFemaleAnnualPrint.msgImmunization=Immunization
oscarEncounter.formFemaleAnnualPrint.msgprecontraceptive=Precontraceptive Councelling (Rubella, Folate)
oscarEncounter.formFemaleAnnualPrint.msgCardiacRisk=Cardiac Risk Factors
oscarEncounter.formFemaleAnnualPrint.msgOsteoporosis=Osteoporosis Risk
oscarEncounter.formFemaleAnnualPrint.msgPhysicalExam=PHYSICAL EXAM
oscarEncounter.formFemaleAnnualPrint.msgVitals=VITALS
oscarEncounter.formFemaleAnnualPrint.msgBP=B.P
oscarEncounter.formFemaleAnnualPrint.msgR=(R)
oscarEncounter.formFemaleAnnualPrint.msgPulse=Pulse
oscarEncounter.formFemaleAnnualPrint.msgPulseUnit=/min
oscarEncounter.formFemaleAnnualPrint.msgHeight=Height
oscarEncounter.formFemaleAnnualPrint.msgHeightUnit=cm.
oscarEncounter.formFemaleAnnualPrint.msgWeight=Weight
oscarEncounter.formFemaleAnnualPrint.msgWeightUnit=Kg.
oscarEncounter.formFemaleAnnualPrint.msgL=(L)
oscarEncounter.formFemaleAnnualPrint.msgRhythm=Rhythm
oscarEncounter.formFemaleAnnualPrint.msgUrineDipstick=Urine Dipstick
oscarEncounter.formFemaleAnnualPrint.msgPhysicalSigns=PHYSICAL SIGNS
oscarEncounter.formFemaleAnnualPrint.msgAssessment=ASSESSMENT
oscarEncounter.formFemaleAnnualPrint.msgPlan=PLAN
oscarEncounter.formFemaleAnnualPrint.msgSignature=Signature

oscarEncounter.formAlpha.title=Antenatal Psychosocial Health Assessment (ALPHA)
oscarEncounter.formAlpha.msgWannaSave=Are you sure you want to save this form?
oscarEncounter.formAlpha.msgWannaSaveClose=Are you sure you wish to save and close this window?
oscarEncounter.formAlpha.msgWannaExit=Are you sure you wish to exit without saving your changes?
oscarEncounter.formAlpha.btnSave=Save
oscarEncounter.formAlpha.btnSaveExit=Save and Exit
oscarEncounter.formAlpha.btnExit=Exit
oscarEncounter.formAlpha.btnPrint=Print
oscarEncounter.formAlpha.msgAlpha=ANTENATAL PSYCHOSOCIAL HEALTH ASSESSMENT (ALPHA)
oscarEncounter.formAlpha.msgFamilyFactors=FAMILY FACTORS
oscarEncounter.formAlpha.formSocialSupport=Social support (CA, WA, PD)
oscarEncounter.formAlpha.msgFamilyFeel=How does your partner/family feel about your pregnancy?
oscarEncounter.formAlpha.msgFormWhoHelp=Who will be helping you when you go home with your baby?
oscarEncounter.formAlpha.formRecentStressfulEvents=Recent stressful life events (CA, WA, PD, PI)
oscarEncounter.formAlpha.msgLifeChanges=What life changes have you experienced this year?
oscarEncounter.formAlpha.msgPlanningChanges=What changes are you planning during this pregnancy?
oscarEncounter.formAlpha.formCoupleRelationship=Couple's relationship (CD, PD, WA, CA)
oscarEncounter.formAlpha.msgRelationship=How would you describe your relationship with your partner?
oscarEncounter.formAlpha.msgRelationshipAfterBirth=What do you think your relationship will be like after the birth?
oscarEncounter.formAlpha.msgMaternalFactors=MATERNAL FACTORS
oscarEncounter.formAlpha.formPrenatal=Prenatal care (late onset) (WA)
oscarEncounter.formAlpha.msgPrenatalVisit=First prenatal visit in third trimester? (check records)
oscarEncounter.formAlpha.formPrenatalEducation=Prenatal education (refusal or quit) (CA)
oscarEncounter.formAlpha.msgPrenatalPlans=What are your plans for prenatal classes?
oscarEncounter.formAlpha.formFeelingsTowardpregnancy=Feelings toward pregnancy after 20 weeks (CA, WA)
oscarEncounter.formAlpha.msgFellPregnant=How did you feel when you just found out you were pregnant?
oscarEncounter.formAlpha.msgFellAboutpregnant=How do you feel about it now?
oscarEncounter.formAlpha.formRelationshipWithParents=Relationship with parents in childhood (CA)
oscarEncounter.formAlpha.msgRelationshipWithParents=How did you get along with your parents?
oscarEncounter.formAlpha.msgLovedByParents=Did you feel loved by your parents?
oscarEncounter.formAlpha.formSelfEsteem=Self esteem (CA, WA)
oscarEncounter.formAlpha.msgSelfEsteemConcerns=What concerns do you have about becoming/being a mother?
oscarEncounter.formAlpha.formEmotionalProblems=History of psychiatric/emotional problems (CA, WA, PD)
oscarEncounter.formAlpha.msgEmotioanlProblems=Have you ever had emotional problems?
oscarEncounter.formAlpha.msgPsychiatrist=Have you ever seen a psychiatrist or therapist?
oscarEncounter.formAlpha.formDepression=Depression in this pregnancy (PD)
oscarEncounter.formAlpha.msgMood=How has your mood been during this pregnancy?
oscarEncounter.formAlpha.msgSubstanceUse=SUBSTANCE USE
oscarEncounter.formAlpha.formAlcohol=Alcohol/drug abuse (WA, CA)
oscarEncounter.formAlpha.msgDrinksPerWeek=How many drinks of alcohol do you have per week?
oscarEncounter.formAlpha.msgTimesDrinkMore=Are there times when you drink more than that?
oscarEncounter.formAlpha.msgDrugsUse=Do you or your partner use recreational drugs?
oscarEncounter.formAlpha.msgPartnerAlcoholAndDrugs=Do you or your partner have a problem with alcohol or drugs?
oscarEncounter.formAlpha.msgCAGE=Consider CAGE (<b>C</b>ut down, <b>A</b>nnoyed, <b>G</b>uilty, <b>E</b>ye opener)
oscarEncounter.formAlpha.msgFamilyViolence=FAMILY VIOLENCE
oscarEncounter.formAlpha.msgAbuse=Woman or partner experienced or witnessed abuse<br>(physical, emotional, sexual) (CA, WA)
oscarEncounter.formAlpha.msgFatherViolence=Did your father ever scare or hurt your mother?
oscarEncounter.formAlpha.msgParentsViolence=Did your parents ever scare or hurt you?
oscarEncounter.formAlpha.msgAbusedAsAChild=Were you ever sexually abused as a child?
oscarEncounter.formAlpha.formWomanAbuse=Current or past woman abuse (WA, CA, PD)
oscarEncounter.formAlpha.msgSolveArguments=How do you and your partner solve arguments?
oscarEncounter.formAlpha.msgFeelFrightened=Do you ever feel frightened by what your partner says or does?
oscarEncounter.formAlpha.msgBeenHit=Have you ever been hit/pushed/slapped by a partner?
oscarEncounter.formAlpha.msgHumiliated=Has your partner ever humiliated you or psychologically abused you in other ways?
oscarEncounter.formAlpha.msgForcedSex=Have you ever been forced to have sex against your will?
oscarEncounter.formAlpha.formPreviousChildAbuse=Previous child abuse by woman or partner (CA)
oscarEncounter.formAlpha.msgDistantChild=Do you/your partner have children not living with you? If so, why?
oscarEncounter.formAlpha.msgChildProtectionAgency=Have you ever had involvement with a child protection agency<br>(ie Children's Aid Society)?
oscarEncounter.formAlpha.formChildDiscipline=Child discipline (CA)
oscarEncounter.formAlpha.msgDiscilinedMother=How were you disciplined as a child?
oscarEncounter.formAlpha.msgHowWillDiscipline=How do you think you will discipline your child?
oscarEncounter.formAlpha.msgMisbehave=How do you deal with your kids at home when they misbehave?
oscarEncounter.formAlpha.msgFollowUp=FOLLOW-UP PLAN
oscarEncounter.formAlpha.formCounselling=Supportive counselling by provider
oscarEncounter.formAlpha.formHomecare=Homecare
oscarEncounter.formAlpha.formAssaultedWomen=Assaulted women's helpline / shelter / counselling
oscarEncounter.formAlpha.form=Additional prenatal appointments
oscarEncounter.formAlpha.formParentingClasses=Parenting classes / parents' support group
oscarEncounter.formAlpha.formLegalAdvise=Legal advice
oscarEncounter.formAlpha.formPospartumAppointments=Additional postpartum appointments
oscarEncounter.formAlpha.formAddictionTreatment=Addiction treatment programs
oscarEncounter.formAlpha.formChildrenAid=Children's Aid Society
oscarEncounter.formAlpha.formBabyVisits=Additional well baby visits
oscarEncounter.formAlpha.formSmokingCessation=Smoking cessation resources
oscarEncounter.formAlpha.formOther=Other
oscarEncounter.formAlpha.formPublicHealth=Public Health referral
oscarEncounter.formAlpha.formSocialWorker=Social Worker
oscarEncounter.formAlpha.formPsychologist=Psychologist / Psychiatrist
oscarEncounter.formAlpha.formNutrucionist=Nutritionist
oscarEncounter.formAlpha.formFamilyTherapist=Psychotherapist / marital / family therapist
oscarEncounter.formAlpha.formMothersGroup=Community resources / mothers' group
oscarEncounter.formAlpha.formComments=COMMENTS

oscarEncounter.formRourke2009.formBirhtRemarks= Birth remarks:
oscarEncounter.formRourke2009.formPremature=Premature
oscarEncounter.formRourke2009.formHighRisk=High Risk
oscarEncounter.formRourke2009.formNoConcerns=No Concerns
oscarEncounter.formRourke2009.formAPGAR=APGAR
oscarEncounter.formRourke2009.form1min=1 min.
oscarEncounter.formRourke2009.form5min=5 min.
oscarEncounter.formRourke2009.formNotSet=Not Set
oscarEncounter.formRourke2009.formFamHistory=Family history
oscarEncounter.formRourke2009.form2ndHandSmoke=2nd hand smoke exposure
oscarEncounter.formRourke2009.formSubstanceabuse=Substance abuse in utero
oscarEncounter.formRourke2009.formAlcohol=Alcohol
oscarEncounter.formRourke2009.formDrugs=Drugs
oscarEncounter.formRourke2009.formFSA=Forward Sorting Area
oscarEncounter.formRourke2009.formRiksFactors=Risk Factors
oscarEncounter.formRourke2009.formNo=No
oscarEncounter.formRourke2009.formNotDiscussed=Not Discussed
oscarEncounter.formRourke2009.formNotDone=Not Done
oscarEncounter.formRourke2009.formHips = Hips
oscarEncounter.formRourke2009_1.btnGrowthmsg= Correct percentiles<br/>until 24-36 months<br/>if < 37 weeks gestation
oscarEncounter.formRourke2009_1.msgBreastFeedingDescr=Vitamin D 400 IU/day*
oscarEncounter.formRourke2009_1.msgFormulaFeeding=<i>Formula Feeding</i> (iron-fortified)<br/>[450-750 mL(15-25 oz) /day*]
oscarEncounter.formRourke2009_3.msgFormulaFeeding=<i>Formula Feeding - iron-fortified</i><br>[720-960 mLs(24-32 oz) /day*]
oscarEncounter.formRourke2009_1.formSleepPos=Sleep position/bed sharing/room sharing
oscarEncounter.formRourke2009_1.formHomeVisit=High risk infants/assess home visit need
oscarEncounter.formRourke2009.msgDevelopment=Development
oscarEncounter.formRourke2009.msgDevelopmentDesc=(Inquiry &amp; observation of milestones)<br> Tasks are set after the time of normal milestone acquisition.<br> Absence of any item suggests consideration for further assessment of development
oscarEncounter.formRourke2009_1.msgDevelopmentDesc=(Inquiry &amp; observation of milestones)<br> Tasks are set after the time of normal milestone acquisition.<br/> <b>Absence of any item suggests the need for further assessment of development</b><br/>NB-Correct for age if < 37 weeks gestation
oscarEncounter.formRourke2020.msgDevelopmentDesc=(Inquiry &amp; observation of milestones)<br> Tasks are set after the time of typical milestone acquisition.<br/> <b>Absence of any item suggests the need for further assessment of development</b><br/>NB-Correct for age if < 37 weeks gestation
oscarEncounter.formRourke2009.msgPhysicalExaminationLegend=assessed no concerns<br/>X assessed concerns
oscarEncounter.formRourke2009.msgProblemsLegend=given<br>X not given
oscarEncounter.formRourke2009.msgProblemsLegendp2=Done No Concerns<br>X Done Concerns
oscarEncounter.formRourke2009_1.formCough=No OTC cough/cold medn
oscarEncounter.formRourke2009_1.formCalmes=Calms when comforted
oscarEncounter.formRourke2009_1.formSkin=Skin (jaundice)
oscarEncounter.formRourke2009_2.msgFormulaFeeding2m=Formula Feeding (iron-fortified)<br>[600-900 mL(20-30 oz) /day*]
oscarEncounter.formRourke2009_2.msgFormulaFeeding4m=Formula Feeding (iron-fortified)<br>[750-1080 mL(25-36 oz) /day*]
oscarEncounter.formRourke2009_2.msgBreastFeedingDescr6m=Breastfeeding*\u2013 initial introduction of solids<br>Vitamin D 400 IU/day*
oscarEncounter.formRourke2009_2.msgFormulaFeedingLong6m=Formula Feeding \u2013 iron-fortified<br>[750-1080 mL(25-36 oz) /day*]
oscarEncounter.formRourke2009_2.msgLiquids=Avoid sweetened liquids
oscarEncounter.formRourke2009_2.formHotWater=Hot water <49oC/bath safety
oscarEncounter.formRourke2009_2.formHomeVisit=High risk infants/assess home visit need
oscarEncounter.formRourke2009_2.formReading=Encourage reading
oscarEncounter.formRourke2009_2.formChildCare=Child care**/return to work
oscarEncounter.formRourke2009_2.formAltMed=OTC/complementary/alternative medicine
oscarEncounter.formRourke2009_2.formSounds=Coos-throaty, gurgling sounds
oscarEncounter.formRourke2009_2.formHeadUp=Lifts head up while lying on tummy
oscarEncounter.formRourke2009_2.formCuddled=Can be comforted & calmed by touching/rocking
oscarEncounter.formRourke2009_2.form2sucks= Sequences 2 or more sucks before swallowing/breathing
oscarEncounter.formRourke2009_2.formMovingObj= Follows a moving toy or person with eye
oscarEncounter.formRourke2009_2.formResponds=Responds to people with excitement (leg movement/panting/vocalizing)
oscarEncounter.formRourke2009_2.formHeadSteady=Holds head steady when supported at the chest or waist in a sitting position
oscarEncounter.formRourke2009_2.formGrasp=Holds an object briefly when placed in hand
oscarEncounter.formRourke2009_2.formLaughs=Laughs/smiles responsively
oscarEncounter.formRourke2009.formNoparentConcerns=No parent/caregiver concerns
oscarEncounter.formRourke2009_2.formmakesSound=Makes sounds while you talk to him/her
oscarEncounter.formRourke2009_2.formVocalizes=Vocalizes pleasure and displeasure
oscarEncounter.formRourke2009_2.formRolls=Rolls from back to side
oscarEncounter.formRourke2009_2.formSits=Sits with support (e.g. pillows)
oscarEncounter.formRourke2009_2.formreachesGrasps= Reaches/grasps objects
oscarEncounter.formRourke2020_2.formreachesGrasps= Reaches/grasps objects with both hands equally
oscarEncounter.formRourke2009_2.formholdsObj=Holds an object briefly when placed in hand
oscarEncounter.formRourke2009_2.formCoos=Coos - throaty, gurgling sounds
oscarEncounter.formRourke2009_3.formEncourageCup=Encourage standard cup instead of bottle<br>[500-750 mLs(16-24 oz) /day*]
oscarEncounter.formRourke2009_3.formCarSeat=Car seat (infant)
oscarEncounter.formRourke2009_3.formHomeVisit= High risk children/assess home visit need
oscarEncounter.formRourke2009_3.formactiveLife=Active healthy living/screen time
oscarEncounter.formRourke2009_3.formhiddenToy=Looks for an object seen hidden
oscarEncounter.formRourke2009_3.formSounds=Babbles a series of different sounds (eg. baba, duhduh)
oscarEncounter.formRourke2009_3.formMakeSounds=Makes sounds/gestures to get attention or help
oscarEncounter.formRourke2009_3.formStands= Stands with support when helped into standing position
oscarEncounter.formRourke2009_3.formThumb&Index=Opposes thumb and fingers when grasps objects
oscarEncounter.formRourke2009_3.formplayGames=Plays social games with you (eg. nose touching, peek-a-boo)
oscarEncounter.formRourke2009_3.formAttention=Cries or shouts for attention
oscarEncounter.formRourke2009_3.formSimpleReq=Understands simple requests, eg. Where is the ball?
oscarEncounter.formRourke2009_3.formConsonant=Makes at least 1 consonant/vowel combination
oscarEncounter.formRourke2009_3.formShowDistress=Shows distress when separated from parent/caregiver
oscarEncounter.formRourke2009_3.formfollowGaze=Follows your gaze to jointly reference an object
oscarEncounter.formRourke2009_3.formwalksSideways=Walks sideways holding onto furniture
oscarEncounter.formRourke2009_3.formSays5words=Says 5 or more words (words do not have to be clear)
oscarEncounter.formRourke2009_3.formshowsFearStrangers=Shows fear of strange people/places
oscarEncounter.formRourke2009_3.formsays3words=Says 3 or more words (do not have to be clear)
oscarEncounter.formRourke2009_3.formResponds2people=Responds differently to different people
oscarEncounter.formRourke2009_3.formCrawls=Crawls up a few stairs/steps
oscarEncounter.formRourke2009_4.formRemarks=Pregnancy/Birth remarks/Apgar
oscarEncounter.formRourke2009_4.formNoBottle= No bottles<br>[500-750 mLs(16-24 oz) /day*]
oscarEncounter.formRourke2009_4.One2percent=1% to 2% milk
oscarEncounter.formRourke2009_4.formFoodGuide=Canada\u2019s Food Guide*<br>[~ 500 mLs(16 oz) /day*]
oscarEncounter.formRourke2009_4.formDiscipline=Discipline/Parenting skills programs
oscarEncounter.formRourke2009_4.formWeanPacifier=Wean from pacifier
oscarEncounter.formRourke2009_4.formDentalCleaning=Dental care/Dentist
oscarEncounter.formRourke2009_4.formCheckSerum=Serum lead if at risk
oscarEncounter.formRourke2009_4.formnoPacifier=No pacifiers
oscarEncounter.formRourke2009_4.formOtherChildren=Interested in other children
oscarEncounter.formRourke2009_4.formGetAttn=Tries to get your attention to show you something
oscarEncounter.formRourke2009_4.formRecsName=Turns/responds when name is called
oscarEncounter.formRourke2009_4.formPoints2want=Points to what he/she wants
oscarEncounter.formRourke2009_4.formLooks4toy=Looks for toy when asked or pointed in direction
oscarEncounter.formRourke2009_4.formInitSpeech=Imitates speech sounds and gestures
oscarEncounter.formRourke2009_4.formSays20words=Says 20 or more words (words do not have to be clear)
oscarEncounter.formRourke2009_4.form4consonants=Produces 4 consonants, e.g. B D G H N W
oscarEncounter.formRourke2009_4.formWalksAlone=Walks alone
oscarEncounter.formRourke2009_4.formPoints=Points to several different body parts
oscarEncounter.formRourke2009_4.form2wordSentence=Combines 2 or more words
oscarEncounter.formRourke2009_4.formone2stepDirections=Understands 1 and 2 step directions
oscarEncounter.formRourke2009_4.formwalksbackward=Walks backward 2 steps without support
oscarEncounter.formRourke2009_4.formpretendsPlay=Uses toys for pretend play (eg. give doll a drink)
oscarEncounter.formRourke2009_4.form3Directions= Understands 3-part directions
oscarEncounter.formRourke2009_4.formAsksQuestions=Asks and answers lots of questions (eg.&quot; What are you doing?&quot;)
oscarEncounter.formRourke2009_4.formupDownStairs=Walks up/down stairs alternating feet
oscarEncounter.formRourke2009_4.formundoesZippers=Undoes buttons and zippers
oscarEncounter.formRourke2009_4.form2Directions=Understands 2 and 3 step directions (eg. \u201cPick up your hat and shoes and put them in the closet.\u201d)
oscarEncounter.formRourke2009_4.form5ormoreWords= Uses sentences with 5 or more words
oscarEncounter.formRourke2009_4.formwalksUpStairs=Walks up stairs using handrail
oscarEncounter.formRourke2009_4.formplayMakeBelieve= Plays make-believe games with actions and words (eg. pretending to cook a meal, fix a car)
oscarEncounter.formRourke2009_4.formCountsOutloud=Counts out loud or on fingers to answer &quot;How many are there?&quot;
oscarEncounter.formRourke2009_4.formSpeaksClearly= Speaks clearly in adult-like sentences most of the time
oscarEncounter.formRourke2009_4.formHops1Foot=Hops on 1 foot several times
oscarEncounter.formRourke2009_4.formdressesUndresses=Dresses and undresses with little help
oscarEncounter.formRourke2009_4.formobeysAdult=Cooperates with adult requests most of the time
oscarEncounter.formRourke2009_4.formretellsStory=Retells the sequence of a story
oscarEncounter.formRourke2009_4.formSeparates=Separates easily from parent/caregiver
oscarEncounter.formRourke2009_4.formFontanellesClosed=Fontanelles closed
oscarEncounter.formRourke2009_4.formEyes=Eyes (red reflex)
oscarEncounter.formRourke2009_4.formCorneal=Corneal light reflex/Cover-uncover test & inquiry
oscarEncounter.formRourke2009_4.msgNippissing=Enhanced inquiry after Nipissing Developmental Screen (NDDS) ** List NDDS items not yet attained:
oscarEncounter.formRourke2020_4.msgNippissing=Enhanced Inquiry after Looksee Checklist by NDDSÂ©**List Looksee Checklist by NDDSÂ© items not yet attained:
oscarEncounter.formRourke2009.footer=<i>Grades of evidence\:</i> (A) <b>Bold type -- Good evidence</b> (B) <i>Italic -- Fair evidence</i> CC) Plain -- Consensus with no definitive evidence<br><span style\="background-color\:\#C11B17; font-style\:italic;">Where the evidence level is\:</span>&nbsp;&nbsp;A AND the data item is marked \u201cYes \u2013Concerns\u201d, THEN the background text should be turned Red.<br><span style\="background-color\:\#FDD017; font-style\:italic;">Where the evidence level is\:</span>&nbsp;&nbsp; A AND the data item is marked \u201cNo\u201d or \u201cNot Discussed\u201d the background text should be turned Amber.<br><span style\="background-color\:\#FDD017; font-style\:italic;">Where the evidence level is\:</span>&nbsp\:&nbsp<b>not</b> A, all items where the data item is marked \u201cYes \u2013Concerns\u201d, the background text should be turned Amber.

oscarEncounter.formRourke2009.formCopyRight=&copy; Leslie Rourke, James Rourke and Denis Leduc, 2006.  Adapted, used and reproduced by OSCAR McMaster with the permission of the authors.
oscarEncounter.formRourke2006.frmError=Measurements must be numeric and observation dates for each measurement are required
oscarEncounter.formRourke2006_1.visitDate=Date of Visit
oscarEncounter.formRourke2006.btnPrintAll=Print All
oscarEncounter.formRourke2006_1.btnGrowthmsg=Correct percentiles<br/> if < 36 weeks gestation
oscarEncounter.formRourke2006_1.btnBreastFeeding=Breastfeeding (exclusive)*
oscarEncounter.formRourke2006_1.msgBreastFeedingDescr=Vitamin D 10 &#181;g = 400 IU/day*
oscarEncounter.formRourke2006_1.msgFormulaFeeding=<i>Formula Feeding</i> (iron-fortified)<br/>[150 mL = 5 oz/kg/day]
oscarEncounter.formRourke2006_1.msgFormulaFeedingShort=<i>Formula Feeding</i> (iron-fortified)
oscarEncounter.formRourke2006.msgEducationalLegend=discussed and no concerns<br/>X if concerns
oscarEncounter.formRourke2006_1.formSleepPos=Sleep position/bed sharing/co-sleeping*
oscarEncounter.formRourke2006_1.formFireArm=Firearm safety/removal
oscarEncounter.formRourke2006_1.formSmokeSafety=Carbon monoxide/<i>Smoke detectors</i>
oscarEncounter.formRourke2006_1.formHotWater=Hot water < 49&deg;C
oscarEncounter.formRourke2006_1.formBathSafety=Bath safety
oscarEncounter.formRourke2020.formBurns=Burns
oscarEncounter.formRourke2006_1.formSafeToys=Choking/safe toys
oscarEncounter.formRourke2006_1.formInjuryPrev=Injury Prevention
oscarEncounter.formRourke2006_1.formBehaviour=Behaviour and family issues
oscarEncounter.formRourke2006_1.formsleepCry=Sleeping/crying
oscarEncounter.formRourke2006_1.formSoothability=Soothability/responsiveness
oscarEncounter.formRourke2006_1.formHomeVisit=Assess home visit need
oscarEncounter.formRourke2006_1.formBonding=Parenting/bonding
oscarEncounter.formRourke2006_1.formParentFatigue=Parental fatigue/postpartum depression
oscarEncounter.formRourke2006_1.formFamConflict=Family conflict/stress
oscarEncounter.formRourke2006_1.formSiblings=Siblings
oscarEncounter.formRourke2006_1.formOtherIssues=Other Issues
oscarEncounter.formRourke2006_1.formAltMed=Inquiry on complimentary/alternative medicine
oscarEncounter.formRourke2006_1.formPacifierUse=Counsel on pacifier use
oscarEncounter.formRourke2006_1.formFever=Fever advice / thermometers
oscarEncounter.formRourke2006_1.formTempCtrl=Temperature control and overdressing
oscarEncounter.formRourke2006_1.formSunExposure=Sun exposure/sunscreens/insect repellent
oscarEncounter.formRourke2006_1.msgDevelopmentDesc=(Inquiry &amp; observation of milestones)<br> Tasks are set after the time of normal milestone acquisition.<br/> <b>Absence of any item suggests the need for further assessment of development</b><br/>NB-Correct for age if < 36 weeks gestation
oscarEncounter.formRourke2006_1.msgDevelopmentLegend=if attained<br/>X if not attained
oscarEncounter.formRourke2006_1.formEarDrums=Ears (TMs) Hearing inquiry/screening
oscarEncounter.formRourke2006_1.formHips=Hips
oscarEncounter.formRourke2006_1.formMuscleTone=Muscle tone
oscarEncounter.formRourke2020_2.formHeadLag=No head lag
oscarEncounter.formRourke2006_1.formCornealReflex=Corneal light reflex
oscarEncounter.formRourke2006_1.formHearingInquiry=Hearing inquiry/screening
oscarEncounter.formRourke2006_1.msgImmunizationColTitle=Record on Guide V: Immunization Record
oscarEncounter.formRourke2006_1.msgImmunizationHepatitis=If HBsAg-positive parent or sibling:
oscarEncounter.formRourke2006_1.msgImmunizationHepatitisVaccine=Hepatitis B vaccine
oscarEncounter.formRourke2006.footer=<i>Grades of evidence:</i> (A) <b>Bold type -- Good evidence</b> (B) <i>Italic -- Fair evidence</i> CC) Plain -- Consensus with no definitive evidence
oscarEncounter.formRourke2006.footnote1=(*) see Infant/Child Health Maintenance: Selected Guidelines on reverse of Guide 1
oscarEncounter.formRourke2006.footnote2=(**) see Healthy Child Development Selected Guidelines on reverse of Guide IV
oscarEncounter.formRourke.title=formRourke
oscarEncounter.formRourke2006.Pg1=Pg1
oscarEncounter.formRourke2006.Pg2=Pg2
oscarEncounter.formRourke2006.Pg3=Pg3
oscarEncounter.formRourke2006.Pg4=Pg4
oscarEncounter.formRourke2006_2.msgRourkeBabyRecord=Rourke Baby Record: EVIDENCE BASED INFANT/CHILD HEALTH MAINTENANCE GUIDE II
oscarEncounter.formRourke2006_2.msg2mos=2 months
oscarEncounter.formRourke2006_2.msg4mos=4 months
oscarEncounter.formRourke2006_2.msg6mos=6 months
oscarEncounter.formRourke2006_2.msgFormulaFeeding=<i>Formula Feeding</i> (iron-fortified)
oscarEncounter.formRourke2006_2.msgFormulaFeedingLong=<i>Formula Feeding (iron-fortified) follow-up</i>
oscarEncounter.formRourke2006_2.msgBottle=No bottles in bed
oscarEncounter.formRourke2006_2.msgLiquids=No sweetened liquids, encourage water
oscarEncounter.formRourke2006_2.msgIronFoods=Iron containing foods<br/>(cereals, meat, egg yolk, tofu)
oscarEncounter.formRourke2006_2.msgVegFruits=Fruits and vegetables to follow
oscarEncounter.formRourke2006_2.msgEggWhites=No egg white, nuts, or honey
oscarEncounter.formRourke2006_2.msgChoking=Choking/safe food
oscarEncounter.formRourke2006_2.formPoisons=Poisons*:PCC#*
oscarEncounter.formRourke2006_2.formElectric=Electric plugs/cords
oscarEncounter.formRourke2006_2.formFalls=Falls (stairs, walkers, change table)
oscarEncounter.formRourke2006_2.formChildCare=Child care/return to work
oscarEncounter.formRourke2006_2.formTeething=Teething/<b>Dental cleaning/Flouride</b>
oscarEncounter.formRourke2020_2.formTeething=Teething*/<b>Dental cleaning/Flouride</b>
oscarEncounter.formRourke2006_2.formTempCtrl=Temperature control and overdressing
oscarEncounter.formRourke2006_2.formPesticides=Pesticide exposure
oscarEncounter.formRourke2006_2.formSleepPos=Sleep position/bed sharing/co-sleeping/crib safety
oscarEncounter.formRourke2006_2.formAltMed=Complimentary/alternative medicine
oscarEncounter.formRourke2006_2.formPacifierUse=Pacifier use
oscarEncounter.formRourke2006_2.formsleepCry=Sleeping/crying/<b>Night waking</b>
oscarEncounter.formRourke2006_2.formEyesMove=Follows movement with eyes
oscarEncounter.formRourke2006_2.formSounds=Has a variety of sounds and cries
oscarEncounter.formRourke2006_2.formHeadUp=Holds head up when held at adult's shoulder
oscarEncounter.formRourke2006_2.formSmiles=Smiles responsively
oscarEncounter.formRourke2006_2.formCuddled=Enjoys being touched and cuddled
oscarEncounter.formRourke2006_2.formTurnsHead=Turns head toward sounds
oscarEncounter.formRourke2006_2.formLaughs=Laughs/squeals at parent
oscarEncounter.formRourke2006_2.formHeadSteady=Head steady
oscarEncounter.formRourke2006_2.formGrasp=Grasps/reaches
oscarEncounter.formRourke2006_2.formMovingObj=Follows a moving object
oscarEncounter.formRourke2006_2.formLooks=Looks in the direction of a new sound
oscarEncounter.formRourke2006_2.formBabbles=Babbles
oscarEncounter.formRourke2006_2.formRolls=Rolls from back to stomach or stomach to back
oscarEncounter.formRourke2006_2.formSits=Sits with support
oscarEncounter.formRourke2006_2.formHandToMouth=Brings hands or toys to mouth
oscarEncounter.formRourke2006_2.formHeart=Heart
oscarEncounter.formRourke2006_2.formHips=Hips
oscarEncounter.formRourke2006_2.formCornealReflex=Corneal light reflex/Cover-uncover test and inquiry
oscarEncounter.formRourke2020.formAnemiaScreening=Anemia screening (If at risk)
oscarEncounter.formRourke2006_2.formTB=Inquire about risk factors for TB
oscarEncounter.formRourke2006_2.formRiskFactors=Past Problems/Risk factors:
oscarEncounter.formRourke2006_2.formFamHistory=Family history:
oscarEncounter.formRourke2006_2.formWt6m=Wt. (x2 BW)
oscarEncounter.formRourke2006_3.msgRourkeBabyRecord=Rourke Baby Record: EVIDENCE BASED INFANT/CHILD HEALTH MAINTENANCE GUIDE III
oscarEncounter.formRourke2006_3.msg9mos=9 months (optional)
oscarEncounter.formRourke2006_3.msg12mos=12-13 months
oscarEncounter.formRourke2006_3.msg15mos=15 months (optional)
oscarEncounter.formRourke2006_3.formHdCirc=Head circ. <small>(cm)</small>
oscarEncounter.formRourke2006_3.formWt12m=Wt. <small>(x3 BW)</small>
oscarEncounter.formRourke2006_3.formHdCirc12m=Head circ. <br/><small>(av. 47cm)</small>
oscarEncounter.formRourke2006_3.msgFormulaFeeding=Formula Feeding - iron-fortified follow-up
oscarEncounter.formRourke2006_3.msgCereal=Cereal, meat/alternatives, fruits, vegetables
oscarEncounter.formRourke2006_3.msgIntroCowMilk=1rst introduction cow's milk products
oscarEncounter.formRourke2006_3.formEncourageCup=Encourage cup instead of bottle
oscarEncounter.formRourke2006_3.formCarSeat=Car seat (infant/child)
oscarEncounter.formRourke2006_3.formChildProof=Childproofing, including:
oscarEncounter.formRourke2006_3.formFalls=Falls/stairs/walkers
oscarEncounter.formRourke2006_3.formParenting=Parenting
oscarEncounter.formRourke2006_3.formParentFatigue=Parental fatigue/depression
oscarEncounter.formRourke2006_3.formTeething=Teething/<b>Dental cleaning / Flouride / Dentist</b>
oscarEncounter.formRourke2006_3.formactiveLife=Active healthy living/media use
oscarEncounter.formRourke2006_3.formEncourageReading=Encourage reading
oscarEncounter.formRourke2006_3.formFootwear=Footwear
oscarEncounter.formRourke2006_3.formEnvHealth=Environmental health including:
oscarEncounter.formRourke2006_3.formCheckSerum=Check serum lead if at risk
oscarEncounter.formRourke2006_3.formhiddenToy=Looks for hidden toy
oscarEncounter.formRourke2006_3.formSounds=Babbles different sounds
oscarEncounter.formRourke2006_3.formMakeSounds=Makes sounds to get attention
oscarEncounter.formRourke2006_3.formSits=Sits without support
oscarEncounter.formRourke2006_3.formStands=Stands with support
oscarEncounter.formRourke2006_3.formThumb&Index=Opposes thumb and index finger
oscarEncounter.formRourke2006_3.formPickedUp=Reaches to be picked up and held
oscarEncounter.formRourke2006_3.formResponds=Responds to own name
oscarEncounter.formRourke2006_3.formSimpleReq=Understands simple requests, e.g. find your shoes
oscarEncounter.formRourke2006_3.formChatters=Chatters using 3 different sounds
oscarEncounter.formRourke2006_3.formCrawls=Crawls or 'bum' shuffles
oscarEncounter.formRourke2006_3.formPulltoStand=Pulls to stand/walks holding on
oscarEncounter.formRourke2006_3.formShowEmotions=Shows many emotions
oscarEncounter.formRourke2006_3.formSays2words=Attempts to say 2 or more words (words do not have to be clear)
oscarEncounter.formRourke2006_3.formReaches=Tries to get something by making sounds, while reaching or pointing
oscarEncounter.formRourke2006_3.formFingerFoods=Picks up and eats finger foods
oscarEncounter.formRourke2006_3.formCrawlsStairs=Crawls up stairs/steps
oscarEncounter.formRourke2006_3.formSquats=Tries to squat to pick up toys from the floor
oscarEncounter.formRourke2006_3.formTieShoes=Removes socks and tries to untie shoes
oscarEncounter.formRourke2006_3.formStacks2Blocks=Stacks 2 blocks
oscarEncounter.formRourke2006_3.formHowToReact=Looks at you to see how to react (when falls or with strangers)
oscarEncounter.formRourke2006_3.formCornealReflex=Cormeal light reflex/Cover-uncover test and inquiry
oscarEncounter.formRourke2006_3.formTonsilSize=Tonsil size/Teeth
oscarEncounter.formRourke2006_3.formAntiHB=Anti-HBs and HbsAG
oscarEncounter.formRourke2006_3.formAntiHBcond=(If HbsAg positive mother)
oscarEncounter.formRourke2006_3.formHemoglobin=Hemoglobin (If at risk)
oscarEncounter.formRourke2006_4.msgRourkeBabyRecord=Rourke Baby Record: EVIDENCE BASED INFANT/CHILD HEALTH MAINTENANCE GUIDE IV
oscarEncounter.formRourke2006_4.msg18mos=18 months
oscarEncounter.formRourke2006_4.msg2yrs=2-3 years
oscarEncounter.formRourke2006_4.msg4yrs=4-5 years
oscarEncounter.formRourke2006_4.formHdCirc24m=Head circ.<br/>-if prior abnormal
oscarEncounter.formRourke2006_4.formHomoMilk=Homogenized Milk
oscarEncounter.formRourke2006_4.formNoBottle=No bottles
oscarEncounter.formRourke2006_4.Homo2percent=Homogenized or 2% milk
oscarEncounter.formRourke2006_4.formLowerFatDiet=Gradual transition to lower fat diet
oscarEncounter.formRourke2006_4.formFoodGuide=Canada's Food Guide
oscarEncounter.formRourke2006_4.form2percentMilk=2% milk
oscarEncounter.formRourke2006_4.formCarSeatChild=Car seat (child)
oscarEncounter.formRourke2006_4.formBathSafety=Bath safety
oscarEncounter.formRourke2006_4.formCarSeatChildBooster=Car seat (child/booster)
oscarEncounter.formRourke2006_4.formBikeHelmet=Bike Helmets
oscarEncounter.formRourke2006_4.formMatches=Matches
oscarEncounter.formRourke2006_4.formWaterSafety=Water Safety
oscarEncounter.formRourke2006_4.formBehaviour=Behaviour
oscarEncounter.formRourke2006_4.formParentChild=Parent/child interaction
oscarEncounter.formRourke2006_4.formDiscipline=Discipline/Limit setting
oscarEncounter.formRourke2006_4.formHighRisk=High-risk children
oscarEncounter.formRourke2006_4.formFamily=Family
oscarEncounter.formRourke2006_4.formParentFatigue=Parental fatigue/stress/depression
oscarEncounter.formRourke2006_4.formDentalCleaning=Dental cleaning/fluoride/Dentist
oscarEncounter.formRourke2006_4.formToiletLearning=Toilet learning
oscarEncounter.formRourke2006_4.formSocializing=Socializing opportunities
oscarEncounter.formRourke2006_4.formOther=Other
oscarEncounter.formRourke2006_4.formDayCare=Asses day care/preschool needs/school readiness
oscarEncounter.formRourke2006_4.formSocPeerPlay=Socializing/peer play opportunities
oscarEncounter.formRourke2006_4.formSocialEmotion=Social/Emotional
oscarEncounter.formRourke2006_4.formManageable=Child's behaviour is usually manageable
oscarEncounter.formRourke2006_4.formSoothability=Usually easy to soothe
oscarEncounter.formRourke2006_4.formComfort=Comes for comfort when distressed
oscarEncounter.formRourke2006_4.formCommSkills=Communication Skills
oscarEncounter.formRourke2006_4.formPoints=Points to 3 different body parts
oscarEncounter.formRourke2006_4.formGetAttn=Tries to get your attention to see something of interest
oscarEncounter.formRourke2006_4.formPretendPlay=Pretend play with toys and figures (e.g. feeds stuffed animal)
oscarEncounter.formRourke2006_4.formRecsName=Turns when name is called
oscarEncounter.formRourke2006_4.formInitSpeech=Initiates speech sounds regularly
oscarEncounter.formRourke2006_4.form3consonants=Produces 3 consonants, e.g. P M B W H N
oscarEncounter.formRourke2006_4.formMotorSkills=Motor Skills
oscarEncounter.formRourke2006_4.formWalksBack=Walks backward 2 steps without support
oscarEncounter.formRourke2006_4.formFeedsSelf=Feeds self with spoon with little spilling
oscarEncounter.formRourke2006_4.formAdaptiv=Adaptive Skills
oscarEncounter.formRourke2006_4.formRemovesHat=Removes hat/socks without help
oscarEncounter.formRourke2006_4.form2yrs=2 years
oscarEncounter.formRourke2006_4.formNewWords=At least 1 new word/week
oscarEncounter.formRourke2006_4.form2wordSentence=2-word sentences
oscarEncounter.formRourke2006_4.formTriestoRun=Tries to run
oscarEncounter.formRourke2006_4.formSmallContainer=Puts objects into small container
oscarEncounter.formRourke2006_4.formcopiesActions=Copies adult's actions
oscarEncounter.formRourke2006_4.formNewSkills=Continues to develop new skills
oscarEncounter.formRourke2006_4.form4yrs=4 years
oscarEncounter.formRourke2006_4.form3Directions=Understands related 3-part directions
oscarEncounter.formRourke2006_4.formAsksQuestions=Asks lots of questions
oscarEncounter.formRourke2006_4.formStands1Foot=Stands on 1 foot for 1-3 seconds
oscarEncounter.formRourke2006_4.formDraws=Draws a person with at least 3 body parts
oscarEncounter.formRourke2006_4.formToiletTrained=Toilet trained during the day
oscarEncounter.formRourke2006_4.formTries2comfort=Tries to comfort someone who is upset
oscarEncounter.formRourke2006_4.form3yrs=3 years
oscarEncounter.formRourke2006_4.form2Directions=Understands 2 step directions
oscarEncounter.formRourke2006_4.formTwistsLids=Twists lids off jars or turns knobs
oscarEncounter.formRourke2006_4.formTurnsPages=Turns pages one at a time
oscarEncounter.formRourke2006_4.formSharesSometimes=Shares some of the time
oscarEncounter.formRourke2006_4.formListensMusik=Listens to music or stories for 5-10 minutes with adults
oscarEncounter.formRourke2006_4.form5yrs=5 years
oscarEncounter.formRourke2006_4.formCounts10=Counts to 10 and knows common colours and shapes
oscarEncounter.formRourke2006_4.formSpeaksClearly=Speaks clearly in sentences
oscarEncounter.formRourke2006_4.formThrowsCatches=Throws and catches a ball
oscarEncounter.formRourke2006_4.formHops1Foot=Hops on 1 foot
oscarEncounter.formRourke2006_4.formSharesWillingly=Shares willingly
oscarEncounter.formRourke2006_4.formWorksAlone=Works alone at an activity for 20-30 minutes
oscarEncounter.formRourke2006_4.formSeparates=Separates easily from parents
oscarEncounter.formRourke2006_4.formHearingInquiry=Hearing inquiry
oscarEncounter.formRourke2006_4.formBloodPressure=Blood pressure
oscarEncounter.formRourke2006_4.formRedReflex=Eyes (red reflex)/Visual acuity

oscarEncounter.formRourke2017.msgParentConcerns=PARENT/CAREGIVER CONCERNS
oscarEncounter.formRourke2017.formBehaviourAndFamily=Behaviour & Family
oscarEncounter.formRourke2017.formCrying=Crying
oscarEncounter.formRourke2017.formHealthySleep=Healthy sleep habits
oscarEncounter.formRourke2017.formNightWaking=Night waking
oscarEncounter.formRourke2017.formHighRiskOrHomeVisit=High risk infants/Assess home visit need
oscarEncounter.formRourke2017.formMakingEndsMeetInquiry=Inquire re difficulty making ends meet or feeding your family
oscarEncounter.formRourke2017.formEnvironmentalHealth=Environmental Health
oscarEncounter.formRourke2017.formTummyTime=Supervised tummy time while awake
oscarEncounter.formRourke2017.formNoparentConcerns=No parent/caregiver concerns
oscarEncounter.formRourke2017.formDrySkin=<i>Skin (jaundice**, bruising**)</i>
oscarEncounter.formRourke2017.formTongueMobility=Tongue mobility
oscarEncounter.formRourke2017.formNeckTorticollis=Neck/Torticollis
oscarEncounter.formRourke2017.formFemoralPulses=Abdomen/Femoral pulses
oscarEncounter.formRourke2017.formHips=Hips (Barlow/Ortolani)
oscarEncounter.formRourke2017.formGenitalia=Testicles/Genitalia
oscarEncounter.formRourke2017.formPatencyOfAnus=Patency of anus
oscarEncounter.formRourke2017.formHearingInquiry=Hearing inquiry/screening
oscarEncounter.formRourke2017.formHeartAbdomen=Heart/Abdomen
oscarEncounter.formRourke2017.msgProblemsAndPlans=PROBLEMS AND PLANS
oscarEncounter.formRourke2017.msgCurrentAndNewReferrals=CURRENT & NEW REFERRALS
oscarEncounter.formRourke2017.msgPlansAndReferralsDescription=E.g. medical specialist, dietitian, speech, audiology, PT, OT, eyes, dental, social-determinants resources
oscarEncounter.formRourke2017.msgInvestigationsScreeningAndImmunization=INVESTIGATIONS/SCREENING** AND IMMUNIZATION***
oscarEncounter.formRourke2017.msgInvestigationsScreeningAndImmunizationDesc=Discuss immunization pain reduction strategies***
oscarEncounter.formRourke2017.formImmunizationNewbornScreening=Newborn screening as per province
oscarEncounter.formRourke2017.formImmunizationHemoglobinopathyScreen=Hemoglobinopathy screen (if at risk)**
oscarEncounter.formRourke2017.formImmunizationHemoglobin=Hemoglobin (If at risk)
oscarEncounter.formRourke2017.formImmunizationNewbornHearingScreening=Universal newborn hearing screening (UNHS)**
oscarEncounter.formRourke2017.formImmunizationIfHepatitis=If HBsAg-positive parent/sibling
oscarEncounter.formRourke2017.formImmunizationHepatitisVaccine1=Hep B vaccine #1
oscarEncounter.formRourke2017.formImmunizationHepatitisVaccine2=Hep B vaccine #2
oscarEncounter.formRourke2017.formImmunizationHepatitisVaccine3=Hep B vaccine #3
oscarEncounter.formRourke2017.formFutureIntroductionSolids=Discuss future introduction of solids
oscarEncounter.formRourke2017.formIronContainingFoods=Iron containing foods* (iron fortified infant cereals, meat, tofu, legumes, poultry, fish, whole eggs)
oscarEncounter.formRourke2017.formFruitsVegetablesAndMilk=Fruits, vegetables and milk products (yogurt, cheese) to follow
oscarEncounter.formRourke2017.formNoHoney=No Honey
oscarEncounter.formRourke2017.formSweetenedLiquids=Avoid juices/sweetened liquids
oscarEncounter.formRourke2017.formBehaviourCrying=Crying
oscarEncounter.formRourke2017.formElectricPlugs=Electric plugs/cords
oscarEncounter.formRourke2017.formChildCare=Child care**/Return to work
oscarEncounter.formRourke2017.formHealthyActiveLiving=Family healthy active living/Sedentary behaviour/Screen time
oscarEncounter.formRourke2017.formNoOtcColdMed=No OTC cough/Cold medicine
oscarEncounter.formRourke2017.formAnteriorFontanelle=Anterior fontanelle
oscarEncounter.formRourke2017.formAnteriorFontanelleClosed=Anterior fontanelle closed
oscarEncounter.formRourke2017.formHipsLimitedHipAdbn=Hips (limited hip abd'n)
oscarEncounter.formRourke2017.formBruising=Bruising
oscarEncounter.formRourke2017.formCoverUncoverTest=Cover-uncover test &amp; inquiry
oscarEncounter.formRourke2017.formTeeth=Teeth
oscarEncounter.formRourke2017.formBreastfeedingVitaminD=Breastfeeding*/Vitamin D 400 IU/day*
oscarEncounter.formRourke2017.formIronFruitsAndVegetables=Iron containing foods*, fruits, vegetables
oscarEncounter.formRourke2017.formCowsMilkProducts=Cow's milk products (e.g., yogurt, cheese, homogenized milk)
oscarEncounter.formRourke2017.formEncourageChangeToCup=Encourage change from bottle to cup
oscarEncounter.formRourke2017.formPromoteOpenCup=Promote open cup instead of bottle
oscarEncounter.formRourke2017.formNoBottle=No bottles
oscarEncounter.formRourke2017.formEatsVarietyOfTexture=Eats a variety of texture
oscarEncounter.formRourke2017.formEatsFamilyFoodTextures=Eats family foods with a variety of textures
oscarEncounter.formRourke2017.formIndependentSelfFeeding=Independent/self-feeding
oscarEncounter.formRourke2017.formHomogenizedMilk=Homogenized milk [500-750 mLs(16-24 oz) /day*]
oscarEncounter.formRourke2017.formInquireVegetarianDiets=Inquire re: vegetarian diets
oscarEncounter.formRourke2017.formParenting=Parenting
oscarEncounter.formRourke2017.formParentFatigueDepression=Parental fatigue/depression
oscarEncounter.formRourke2017.formParentFatigueStressDepression=Parental fatigue/Stress/depression
oscarEncounter.formRourke2017.formDental=Teething/<b>Dental cleaning/Fluoride/Dentist</b>
oscarEncounter.formRourke2017.formAltMed=Complementary/Alternative medicine
oscarEncounter.formRourke2017.formFootwear=Footwear
oscarEncounter.formRourke2017.formThumbAndFingersGrasp=Opposes thumb and fingers when grasps objects and finger foods
oscarEncounter.formRourke2017.formCrawls=Crawls or 'bum' shuffles
oscarEncounter.formRourke2017.formHasPincerGrasp=Has pincer grasp to pick up and eat finger foods
oscarEncounter.formRourke2017.formTonsilSize=Tonsil size/Sleep-disordered breathing
oscarEncounter.formRourke2017.form9Or12MonthsHbvAntibodies=If HBsAg positive mother check HBV antibodies and HBsAg3 (at 9 or 12 months)
oscarEncounter.formRourke2017.formHemoglobin=Hemoglobin (If at risk)
oscarEncounter.formRourke2017.formBloodLead=Blood lead (If at risk)
oscarEncounter.formRourke2017.formLowerFatDiet=Gradual transition to lower fat diet
oscarEncounter.formRourke2017.formMilk=Skim, 1% or 2% milk [~ 500 mLs(16 oz) /day*]
oscarEncounter.formRourke2017.formCanadasFoodGuide=Canada's Food Guide
oscarEncounter.formRourke2017.formCarBoosterSeat=Motorized vehicles/Car seat (child/booster)
oscarEncounter.formRourke2017.formFallsAndFurniture=Falls (stairs, change table, unstable furniture/TV)
oscarEncounter.formRourke2017.formFallsFurnitureAndTrampolines=Falls (stairs, change table, unstable furniture/TV, trampolines)
oscarEncounter.formRourke2017.formWeanFromPacifier=Wean from pacifier
oscarEncounter.formRourke2017.formNoPacifiers=No pacifiers
oscarEncounter.formRourke2017.formWaterSafety=Water Safety
oscarEncounter.formRourke2017.formFirearmSafety=Firearm safety
oscarEncounter.formRourke2017.forMatches=Matches
oscarEncounter.formRourke2017.forBikeHelmets=Bike helmets
oscarEncounter.formRourke2017.forChildCareAndSchoolReadiness=Assess child care/Preschool needs/school readiness
oscarEncounter.formRourke2017.formDentalCleaningFluoride=Dental cleaning/Fluoride/Dentist
oscarEncounter.formRourke2017.formSays15Words=Says 15 or more words (words do not have to be clear)
oscarEncounter.formRourke2017.formListensMusic=Listens to music or stories for 5-10 minutes with adults
oscarEncounter.formRourke2017.formBloodPressureIfAtRisk=Blood pressure if at risk
oscarEncounter.formRourke2017.EyesVisualAcuity=Eyes (red reflex)/Visual acuity
oscarEncounter.formRourke2017.formRedReflex=Eyes (red reflex)
oscarEncounter.formRourke2017.formVitaminD200=Vitamin D 400 IU/day

oscarEncounter.formRourke2020.msgParentConcerns=PARENT/CAREGIVER CONCERNS
oscarEncounter.formRourke2020.formBehaviourAndFamily=Behaviour & Family
oscarEncounter.formRourke2020.formCrying=Crying
oscarEncounter.formRourke2020.formHealthySleep=Healthy sleep habits
oscarEncounter.formRourke2020.formNightWaking=Night waking
oscarEncounter.formRourke2020.formHighRiskOrHomeVisit=High risk infants/Assess home visit need
oscarEncounter.formRourke2020.formMakingEndsMeetInquiry=Inquire re: difficulty making ends meet or food insecurity
oscarEncounter.formRourke2020.formEnvironmentalHealth=Environmental Health
oscarEncounter.formRourke2020.formTummyTime=Supervised tummy time while awake
oscarEncounter.formRourke2020.formNoparentConcerns=No parent/caregiver concerns
oscarEncounter.formRourke2020.formDrySkin=<i>Skin (jaundice**, bruising**)</i>
oscarEncounter.formRourke2020.formTongueMobility=Tongue mobility if breastfeeding problems
oscarEncounter.formRourke2020.formNeckTorticollis=Neck/Torticollis
oscarEncounter.formRourke2020.formHeartLungsAbdomen=Heart/Lungs/Abdomen
oscarEncounter.formRourke2020.formFemoralPulses=Abdomen/Femoral pulses
oscarEncounter.formRourke2020.formHips=Hips (Barlow/Ortolani)
oscarEncounter.formRourke2020.formGenitalia=Testicles/Genitalia
oscarEncounter.formRourke2020.formSpine=Spine (dimple/sinus)
oscarEncounter.formRourke2020.formPatencyOfAnus=Patency of anus
oscarEncounter.formRourke2020.formHearingInquiry=Hearing inquiry/screening
oscarEncounter.formRourke2020.formHeartAbdomen=Heart/Lungs/Abdomen
oscarEncounter.formRourke2020.msgProblemsAndPlans=PROBLEMS AND PLANS
oscarEncounter.formRourke2020.msgCurrentAndNewReferrals=CURRENT & NEW REFERRALS
oscarEncounter.formRourke2020.msgPlansAndReferralsDescription=E.g. medical specialist, dietitian, speech, audiology, PT, OT, eyes, dental, social-determinants resources
oscarEncounter.formRourke2020.msgInvestigationsScreeningAndImmunization=INVESTIGATIONS/SCREENING** AND IMMUNIZATION***
oscarEncounter.formRourke2020.msgInvestigationsScreeningAndImmunizationDesc= Discuss immunization benefits and pain reduction strategies***
oscarEncounter.formRourke2020.formImmunizationNewbornScreening=Newborn screening as per province
oscarEncounter.formRourke2020.formImmunizationHemoglobinopathyScreen=Hemoglobinopathy screen (if at risk)**
oscarEncounter.formRourke2020.formImmunizationHemoglobin=Hemoglobin (If at risk)
oscarEncounter.formRourke2020.formImmunizationNewbornHearingScreening=Universal newborn hearing screening (UNHS)**
oscarEncounter.formRourke2020.formImmunizationIfHepatitis=If HBsAg-positive parent/sibling
oscarEncounter.formRourke2020.formImmunizationHepatitisVaccine1=Hep B vaccine #1
oscarEncounter.formRourke2020.formImmunizationHepatitisVaccine2=Hep B vaccine #2
oscarEncounter.formRourke2020.formImmunizationHepatitisVaccine3=Hep B vaccine #3
oscarEncounter.formRourke2020.formFutureIntroductionSolids=Discuss future introduction of solids, with emphasis on iron containing and allergenic foods
oscarEncounter.formRourke2020.formIronContainingFoods=Iron containing foods* (iron fortified infant cereals, meat, tofu, legumes, poultry, fish, whole eggs)
oscarEncounter.formRourke2020.formAllergenicFoods=Allergenic foods(especially eggs and peanut products)
oscarEncounter.formRourke2020.formFruitsVegetablesAndMilk=Fruits, vegetables and milk products (yogurt, cheese) to follow
oscarEncounter.formRourke2020.formAvoidJuiceAndBeverages=Avoid juice and food/beverages high in sugar or salt
oscarEncounter.formRourke2020.formNoHoney=No Honey
oscarEncounter.formRourke2020.formSweetenedLiquids=Avoid juices/sweetened liquids
oscarEncounter.formRourke2020.formBehaviourCrying=Crying
oscarEncounter.formRourke2020.formElectricPlugs=Electric plugs/cords
oscarEncounter.formRourke2020.formChildCare=Child care**/Return to work
oscarEncounter.formRourke2020.formHealthyActiveLiving=Family healthy active living/Sedentary behaviour/Screen time
oscarEncounter.formRourke2020.formNoOtcColdMed=No OTC cough/Cold medicine
oscarEncounter.formRourke2020.formAnteriorFontanelle=Anterior fontanelle
oscarEncounter.formRourke2020.formTeethRiskAssesment=Teeth/Caries risk assessment
oscarEncounter.formRourke2020.formTeethRisk=Teeth/Caries risk
oscarEncounter.formRourke2020.formAnteriorFontanelleClosed=Anterior fontanelle closed
oscarEncounter.formRourke2020.formHipsLimitedHipAdbn=Hips (limited hip abd'n)
oscarEncounter.formRourke2020.formBruising=Bruising
oscarEncounter.formRourke2020.formCoverUncoverTest=Cover-uncover test &amp; inquiry
oscarEncounter.formRourke2020.formBreastfeedingVitaminD=Breastfeeding*/Vitamin D 400 IU/day*
oscarEncounter.formRourke2020.formAvoidJuiceAndBeverages=Avoid juice and food/beverages high in sugar or salt
oscarEncounter.formRourke2020.formIronFruitsAndVegetables=Iron containing foods*, Allergenic foods*, fruits, vegetables
oscarEncounter.formRourke2020.formCowsMilkProducts=Cow's milk products (e.g., yogurt, cheese, homogenized milk)
oscarEncounter.formRourke2020.formEncourageChangeToCup=Encourage change from bottle to cup
oscarEncounter.formRourke2020.formPromoteOpenCup=Promote open cup instead of bottle
oscarEncounter.formRourke2020.formNoBottlesInBed=No bottles in bed 
oscarEncounter.formRourke2020.formNoBottle=No bottles
oscarEncounter.formRourke2020.formEatsVarietyOfTexture=Eats a variety of texture
oscarEncounter.formRourke2020.formEatsFamilyFoodTextures=Eats family foods with a variety of textures
oscarEncounter.formRourke2020.formIndependentSelfFeeding=Independent/self-feeding
oscarEncounter.formRourke2020.formHomogenizedMilk=Homogenized milk [500-750 mLs(16-24 oz) /day*]
oscarEncounter.formRourke2020.formInquireVegetarianDiets=Inquire re: vegetarian diets
oscarEncounter.formRourke2020.formParenting=Parenting
oscarEncounter.formRourke2020.formParentFatigueDepression=Parental fatigue/depression
oscarEncounter.formRourke2020.formParentFatigueStressDepression=Parental fatigue/Stress/depression
oscarEncounter.formRourke2020.formDental=Teething*/<b>Dental cleaning/Fluoride/Dentist</b>
oscarEncounter.formRourke2020.formAltMed=Complementary/Alternative medicine
oscarEncounter.formRourke2020.formFootwear=Footwear
oscarEncounter.formRourke2020.formThumbAndFingersGrasp=Opposes thumb and fingers when grasps objects and finger foods
oscarEncounter.formRourke2020.formCrawls=Crawls or 'bum' shuffles
oscarEncounter.formRourke2020.formHasPincerGrasp=Has pincer grasp to pick up and eat finger foods
oscarEncounter.formRourke2020.formTonsilSize=Tonsil size/Sleep-disordered breathing
oscarEncounter.formRourke2020.form9Or12MonthsHbvAntibodies=If HBsAg positive mother check HBV antibodies and HBsAg3 (at 9 or 12 months)
oscarEncounter.formRourke2020.formHemoglobin=Hemoglobin (If at risk)
oscarEncounter.formRourke2020.formBloodLead=Blood lead (If at risk)
oscarEncounter.formRourke2020.formLowerFatDiet=Gradual transition to lower fat diet
oscarEncounter.formRourke2020.formMilk=Skim, 1% or 2% milk [~ 500 mLs(16 oz) /day*]
oscarEncounter.formRourke2020.formCanadasFoodGuide=Canada's Food Guide
oscarEncounter.formRourke2020.formCarBoosterSeat=Motorized vehicles safety/Car seat (child/booster)
oscarEncounter.formRourke2020.formFallsAndFurniture=Falls (stairs, change table, unstable furniture/TV)
oscarEncounter.formRourke2020.formFallsFurnitureAndTrampolines=Falls (stairs, change table, unstable furniture/TV, trampolines)
oscarEncounter.formRourke2020.formWeanFromPacifier=Wean from pacifier
oscarEncounter.formRourke2020.formNoPacifiers=No pacifiers
oscarEncounter.formRourke2020.formWaterSafety=Water Safety
oscarEncounter.formRourke2020.formFirearmSafety=Firearm safety
oscarEncounter.formRourke2020.forMatches=Matches
oscarEncounter.formRourke2020.forBikeHelmets=Bike helmets
oscarEncounter.formRourke2020.forChildCareAndSchoolReadiness=Assess child care/Preschool needs/school readiness
oscarEncounter.formRourke2020.formDentalCleaningFluoride=Dental cleaning/Fluoride/Dentist
oscarEncounter.formRourke2020.formSays15Words=Says 15 or more words (words do not have to be clear)
oscarEncounter.formRourke2020.formListensMusic=Listens to music or stories for 5-10 minutes with adults
oscarEncounter.formRourke2020.formBloodPressureIfAtRisk=Blood pressure if at risk (3+yrs)
oscarEncounter.formRourke2020.EyesVisualAcuity=Eyes (red reflex)/Visual acuity
oscarEncounter.formRourke2020.formRedReflex=Eyes (red reflex)
oscarEncounter.formRourke2020.formVitaminD200=Vitamin D 400 IU/day
oscarEncounter.formRourke2020.formIntectPalate=Intact palate (inspection/palpation)
oscarEncounter.formRourke2020.formNoPersistentClosed=No persistent closed/fisted hands
oscarEncounter.formRourke2020.formSafeSleep=Safe sleep (9 mo: position, avoid bed sharing, crib safety)
oscarEncounter.formRourke2020.formUseBothHand=Uses both hands equally
oscarEncounter.formRourke2020.formVaccineMsg=VACCINE
oscarEncounter.formRourke2020.formMsgNaciRec=NACI RECOMMENDATIONS
oscarEncounter.formRourke2020.formMsgDateGiven=DATE GIVEN
oscarEncounter.formRourke2020.formMsgInjection=INJECTION SITE
oscarEncounter.formRourke2020.formMsgLotNum=LOT NUMBER
oscarEncounter.formRourke2020.formMsgExpire=EXPIRY DATE
oscarEncounter.formRourke2020.formMsgInitial=INITIALS
oscarEncounter.formRourke2020.formMsgComments=Comments
oscarEncounter.formRourke2020.formMsgDose1=dose #1
oscarEncounter.formRourke2020.formMsg1Dose=1 dose
oscarEncounter.formRourke2020.formMsgDose2=dose #2
oscarEncounter.formRourke2020.formMsgDose3=dose #3
oscarEncounter.formRourke2020.formMsgDose4=dose #4
oscarEncounter.formRourke2020.formMsg1Dose=1 dose
oscarEncounter.formRourke2020.formMsgRotavirus=Rotavirus <br> 2 or 3 doses <br> # doses varies with manufacturer
oscarEncounter.formRourke2020.formMsgDtaIpvHib=DTaP/IPV/ <br> Hib <br> 4 doses <br> (2, 4, 6, 18 months)
oscarEncounter.formRourke2020.formMsgPneu=Pneu-C-13 <br> 3 or 4 doses <br> (2, 4, ?6, 12-15 months)
oscarEncounter.formRourke2020.formMsgMenConjugate= Men-Conjugate <br> MCV-C: 1 dose at 12 months <br>  MCV-C or MCV-4: 1 dose at 12 years or during adolescence
oscarEncounter.formRourke2020.formMsgMenConjugateIfRisk=If at increased risk:  <br> - MCV-C: 3 doses at 2, 4 & 12 months  <br> - MCV-4: at 2 years or older <br> - 4CMenB: at 2 months or older
oscarEncounter.formRourke2020.formMsgMcv2Dose= MCV-C: 2 doses at 2 and 4 months <br> only if at increased risk
oscarEncounter.formRourke2020.formMsgMcv1Dose= MCV-C: 1 dose at 12 months
oscarEncounter.formRourke2020.formMsgMcv4= MCV-C or MCV-4: 1 dose at <br> 12 years or during adolescence
oscarEncounter.formRourke2020.formMsgweeks=(6 weeks-14 weeks + 6 days)
oscarEncounter.formRourke2020.formMsg2Months=(2 Months)
oscarEncounter.formRourke2020.formMsg4Months= (4 Months)
oscarEncounter.formRourke2020.formMsg6Months= (6 Months)
oscarEncounter.formRourke2020.formMsg18Months= (18 Months)
oscarEncounter.formRourke2020.formMsg4To6Years= (4 - 6 years)
oscarEncounter.formRourke2020.formMsg14To16Years= (14 - 16 years)
oscarEncounter.formRourke2020.formMsg12Months= (12-15 Months)
oscarEncounter.formRourke2020.formMsg18MonthsOr4Year= (18 months OR 4 years )
oscarEncounter.formRourke2020.formMsgMMRorMMRV= MMR or MMRV <br> 2 doses (12 months, 18 months OR 4 years)
oscarEncounter.formRourke2020.formMsgHepatitis= Hepatitis B <br> 3 doses in infancy OR <br> 2 - 3 doses preteen/teen <br> Can be combined with Hep A vaccine
oscarEncounter.formRourke2020.formMsgVaricella= Varicella <br> 2 doses <br> (12 months\u201312 years \u2013 MMRV or univalent) <br> OR <br> 2 doses (>13 years\u2013univalent) 
oscarEncounter.formRourke2020.formMsgInfluenza= Influenza <br> 1 dose annually <br> (6 - 59 months and high risk > 5 years) <br> First yr only for < 9 years - <br> give 2 doses 1 month apart
oscarEncounter.formRourke2020.formMsgDTaPIpv=dTap/Ipv
oscarEncounter.formRourke2020.formMsgdTap=dTap
oscarEncounter.formRourke2020.formMsgHPV= HPV <br> Starting at 9 years of age, as per provincial/ <br> territorial guidelines
oscarEncounter.formRourke2020.formMsgOther= Other

oscarEncounter.formOnar.msgNoEDB=Final EDB not set. Printing without graph

oscarEncounter.formRourke1.title=Rourke Baby Record
oscarEncounter.formRourke1.msgSavePrintPreview=Do you wish to save this form and view the print preview?
oscarEncounter.formRourke1.msgSave=Are you sure you want to save this form?
oscarEncounter.formRourke1.msgExit=Are you sure you wish to exit without saving your changes?
oscarEncounter.formRourke1.msgSaveExit=Are you sure you wish to save and close this window?
oscarEncounter.formRourke1.msgTypeNumbers=You must type in a number in the field.
oscarEncounter.formRourke1.btnSave=Save
oscarEncounter.formRourke1.btnSaveExit=Save and Exit
oscarEncounter.formRourke1.btnExit=Exit
oscarEncounter.formRourke1.btnPrint=Print
oscarEncounter.formRourke1.btnGraphLenghtWeight=Graph Length and Weight
oscarEncounter.formRourke1.btnGraphHead=Graph Head Circumference
oscarEncounter.formRourke1.msgPage1=Page 1
oscarEncounter.formRourke1.btnPage2=Page 2
oscarEncounter.formRourke1.btnPage3=Page 3
oscarEncounter.formRourke1.msgRourkeBabyRecord=Rourke Baby Record: EVIDENCE BASED INFANT/CHILD HEALTH MAINTENANCE GUIDE I
oscarEncounter.formRourke1.formBirhtRemarks=Birth remarks
oscarEncounter.formRourke1.formRiksFactors=Risk Factors/Family History
oscarEncounter.formRourke1.msgName=Name
oscarEncounter.formRourke1.msgBirthDate=Birth Date
oscarEncounter.formRourke1.msgMale=Male
oscarEncounter.formRourke1.msgFemale=Female
oscarEncounter.formRourke1.msgLenght=Length
oscarEncounter.formRourke1.msgLenghtUnit=cm
oscarEncounter.formRourke1.msgHeadCirc=Head Circ
oscarEncounter.formRourke1.msgHeadCircUnit=cm
oscarEncounter.formRourke1.msgBirthWt=Birth Wt
oscarEncounter.formRourke1.msgBirthWtUnit=kg
oscarEncounter.formRourke1.msgDischargeWt=Discharge Wt
oscarEncounter.formRourke1.msgDischargeWtUnit=kg
oscarEncounter.formRourke1.msgStartOfPregnancy=Date of start of pregnancy
oscarEncounter.formRourke1.btnAge=AGE
oscarEncounter.formRourke1.msgWithin=within
oscarEncounter.formRourke1.btn1Week=1 week
oscarEncounter.formRourke1.btn2Weeks=2 weeks
oscarEncounter.formRourke1.msgOptional=(optional)
oscarEncounter.formRourke1.btn1month=1 month
oscarEncounter.formRourke1.btn2Months=2 months
oscarEncounter.formRourke1.msgDate=DATE
oscarEncounter.formRourke1.formDate=(yyyy/mm/dd)
oscarEncounter.formRourke1.btnGrowth=GROWTH
oscarEncounter.formRourke2020_1.btnGrowth=GROWTH* use WHO growth charts.
oscarEncounter.formRourke1.formHt=Ht. <small>(cm)</small>
oscarEncounter.formRourke1.formWt=Wt. <small>(kg)</small>
oscarEncounter.formRourke1.formHdCirc=Hd. Circ <small>(cm)<br>av. 35cm</small>
oscarEncounter.formRourke1.formBmi=BMI
oscarEncounter.formRourke1.formParentalConcerns=PARENTAL CONCERNS
oscarEncounter.formRourke1.msgNutrition=NUTRITION
oscarEncounter.formRourke1.btnBreastFeeding=Breast feeding
oscarEncounter.formRourke1.msgBreastFeedingDescr=*<br> &nbsp;&nbsp;Vit.D 10ug=400IU/day*
oscarEncounter.formRourke1.msgFormulaFeeding=<i>Formula Feeding</i> (Fe fortified) <br>[150ml = 5oz/kg/day]
oscarEncounter.formRourke1.formStoolPatern=Stool pattern &amp; urine output
oscarEncounter.formRourke1.formSupplementationSolids=Supplementation: Solids
oscarEncounter.formRourke1.formSupplementationWater=Supplementation: Water
oscarEncounter.formRourke1.formSupplementationOtherFluids=Supplementation: Other Fluids
oscarEncounter.formRourke1.msgEducational=EDUCATION &amp; ADVICE
oscarEncounter.formRourke1.msgEducationalSubtitle=Repeat discussion of items is based on perceived risk or need
oscarEncounter.formRourke1.msgSafety=Safety
oscarEncounter.formRourke1.msgBehaviour=Behaviour
oscarEncounter.formRourke1.msgFamily=Family
oscarEncounter.formRourke1.msgOther=Other
oscarEncounter.formRourke1.formMotorizedvehicles=Motorized vehicles
oscarEncounter.formRourke2020_1.formMotorizedvehicles=Motorized vehicle safety
oscarEncounter.formRourke2020_1.formCarSeat=Car seat
oscarEncounter.formRourke1.formCarSeatInfant=Car seat (infant)
oscarEncounter.formRourke1.formCribSafety=Crib safety
oscarEncounter.formRourke1.formSleeping=Sleeping/crying
oscarEncounter.formRourke1.formSoothability=Soothability/ responsiveness
oscarEncounter.formRourke1.formParenting=Parenting/bonding
oscarEncounter.formRourke1.formFatigue=Fatigue/depression
oscarEncounter.formRourke1.formFamilyConflict=Family conflict/stress
oscarEncounter.formRourke1.formSiblings=Siblings
oscarEncounter.formRourke1.btnHomeVisit=Assess home visit need
oscarEncounter.formRourke1.btnSleepPosition=Sleep position
oscarEncounter.formRourke1.formTemperatureControl=<i>Temperature control &amp; overdressing</i>
oscarEncounter.formRourke1.formSecondHandSmoke=Second hand smoke
oscarEncounter.formRourke2020_1.formSecondHandSmoke=Second hand smoke/E-cigs/Cannabis
oscarEncounter.formRourke1.formSleepwear=Non-inflam. sleepwear
oscarEncounter.formRourke1.btnHotWater=Hot water &lt; 54&deg;C
oscarEncounter.formRourke1.btnSafeToys=Choking/safe toys
oscarEncounter.formRourke1.formParentChildinteraction=Parent/child interaction
oscarEncounter.formRourke1.formAssessSupports=Assess supports
oscarEncounter.formRourke1.btnFalls=Falls
oscarEncounter.formRourke1.formDepression=Depression/family stress
oscarEncounter.formRourke1.formFeverControl=Fever control
oscarEncounter.formRourke1.msgDevelopment=DEVELOPMENT
oscarEncounter.formRourke1.msgDevelopmentDesc=(Inquiry &amp; observation of milestones)<br> Tasks are set after the time of normal milestone acquisition.<br> Absence of any item suggests the need for further assessment of development
oscarEncounter.formRourke1.formFocusesGaze=Focuses gaze
oscarEncounter.formRourke1.formSuddenNoise=Startles to loud or sudden noise
oscarEncounter.formRourke1.formSucksWell=Sucks well on nipple
oscarEncounter.formRourke1.formNoparentConcerns=No parent concerns
oscarEncounter.formRourke1.formFollowsMovement=Follows movement with eyes
oscarEncounter.formRourke1.formVarietyOfSounds=Has a variety of sounds &amp; cries
oscarEncounter.formRourke1.formHoldHeadsUp=Holds head up when held at adult&#146;s shoulder
oscarEncounter.formRourke1.EnjoysBeingTouched=Enjoys being touched &amp; cuddled
oscarEncounter.formRourke1.msgPhysicalExamination=PHYSICAL EXAMINATION
oscarEncounter.formRourke1.msgPhysicalExaminationDesc=Evidence based screening for specific conditions is highlighted, but an appropriate age-specific focused physical examination is recommended at each visit
oscarEncounter.formRourke1.formDrySkin=<i>Skin (jaundice, dry)</i>
oscarEncounter.formRourke1.formFontanelles=Fontanelles
oscarEncounter.formRourke1.formRedReflex=<i>Eyes (red reflex)</i>
oscarEncounter.formRourke1.formEarDrums=<i>Ears (drums)</i>
oscarEncounter.formRourke1.formHeart=Heart/Lungs
oscarEncounter.formRourke1.formUmbilicus=Umbilicus
oscarEncounter.formRourke1.formFemoralPulses=Femoral pulses
oscarEncounter.formRourke1.formHips=<b>Hips</b>
oscarEncounter.formRourke1.formTescicles=Testicles
oscarEncounter.formRourke1.formMaleUrinaryStream=Male urinary stream/foreskin care
oscarEncounter.formRourke1.btnCoverTest=Cover/uncover test &amp; inquiry
oscarEncounter.formRourke1.formHearingInquirity=Hearing inquiry
oscarEncounter.formRourke1.formHeart1=Heart
oscarEncounter.formRourke1.msgProblemsAndPlans=PROBLEMS &amp; PLANS
oscarEncounter.formRourke1.formThyroid=<b> PKU, Thyroid</b>
oscarEncounter.formRourke1.formHemoglobinopathy=Hemoglobinopathy Screen
oscarEncounter.formRourke1.msgImmunization=IMMUNIZATION
oscarEncounter.formRourke1.msgImmunizationDesc=Guidelines may vary by province
oscarEncounter.formRourke1.formHBsAg=If HBsAg-positive parent or sibling
oscarEncounter.formRourke1.btnHep=Hep. B vaccine
oscarEncounter.formRourke1.msgGiveInformation=Give information
oscarEncounter.formRourke1.formImmunization=Immunization
oscarEncounter.formRourke1.formAcetaminophen=Acetaminophen
oscarEncounter.formRourke1.formHIB=<b>HIB</b>
oscarEncounter.formRourke1.formPolio=<b> aPDT polio </b>
oscarEncounter.formRourke1.formSignature=Signature

oscarEncounter.formRourke2.title=Rourke Baby Record
oscarEncounter.formRourke2.msgSavePrintPreview=Do you wish to save this form and view the print preview?
oscarEncounter.formRourke2.msgSave=Are you sure you want to save this form?
oscarEncounter.formRourke2.msgExit=Are you sure you wish to exit without saving your changes?
oscarEncounter.formRourke2.msgSaveExit=Are you sure you wish to save and close this window?
oscarEncounter.formRourke2.msgTypeNumbers=You must type in a number in the field.
oscarEncounter.formRourke2.btnSave=Save
oscarEncounter.formRourke2.btnSaveExit=Save and Exit
oscarEncounter.formRourke2.btnExit=Exit
oscarEncounter.formRourke2.btnPrint=Print
oscarEncounter.formRourke2.btnGraphLenght=Graph Length and Weight
oscarEncounter.formRourke2.btnGraphHead=Graph Head Circumference
oscarEncounter.formRourke2.btnpage1=Page 1
oscarEncounter.formRourke2.msgPage2=Page 2
oscarEncounter.formRourke2.btnPage3=Page 3
oscarEncounter.formRourke2.msgTitle=Rourke Baby Record: EVIDENCE BASED INFANT/CHILD HEALTH MAINTENANCE GUIDE II
oscarEncounter.formRourke2.formBirthRemarks=Birth remarks
oscarEncounter.formRourke2.formRiskFactors=Risk Factors/Family History
oscarEncounter.formRourke2.msgName=Name
oscarEncounter.formRourke2.msgBirthDate=Birth Date
oscarEncounter.formRourke2.formLenght=Length
oscarEncounter.formRourke2.formHeadCirc=Head Circ
oscarEncounter.formRourke2.formBirthWt=Birth Wt:
oscarEncounter.formRourke2.msgBirthWtUnit=kg
oscarEncounter.formRourke2.formDischargeWt=Discharge Wt
oscarEncounter.formRourke2.msgDischargeWtUnit=kg
oscarEncounter.formRourke2.form4Months=4 months
oscarEncounter.formRourke2.form6Months=6 months
oscarEncounter.formRourke2.form9Months=9 months
oscarEncounter.formRourke2.msgOptional=(optional)
oscarEncounter.formRourke2.form12Months=12-13 months
oscarEncounter.formRourke2.msgDate=DATE
oscarEncounter.formRourke2.formHt=Ht. <small>(cm)</small>
oscarEncounter.formRourke2.formWt=Wt. <small>(kg)</small>
oscarEncounter.formRourke2.formHdCirc=Hd. Circ <small>(cm)</small>
oscarEncounter.formRourke2.formWt2=Wt. <small>(kg)<br>(x2 BW)</small>
oscarEncounter.formRourke2.formWt3=Wt. <small>(kg)<br>(x3 BW)</small>
oscarEncounter.formRourke2.HdCirc47=Hd. Circ <small>(cm)<br>(av. 47cm)</small>
oscarEncounter.formRourke2.msgParentalConcerns=PARENTAL CONCERNS
oscarEncounter.formRourke2.msgnutrition=NUTRITION
oscarEncounter.formRourke2.formFormulaFeeding=<i>Formula Feeding</i> (Fe fortified)
oscarEncounter.formRourke2.formIronFortified=<i>Iron fortified cereal</i>
oscarEncounter.formRourke2.btnBreastFeeding=Breast feeding
oscarEncounter.formRourke2.msgBreastFeedingUnit=*<br> &nbsp;&nbsp;Vit.D 10ug=400IU/day*
oscarEncounter.formRourke2.formFormulaFeedingIronFortified=<i>Formula Feeding<br>Iron fortified follow-up formula</i>
oscarEncounter.formRourke2.formNoBottles=No bottles in bed
oscarEncounter.formRourke2.formVeg=Veg/fruits
oscarEncounter.formRourke2.formNoEgg=No egg white, nuts, or honey
oscarEncounter.formRourke2.formChokingSafeFood=Choking/safe food
oscarEncounter.formRourke2.formMeat=Meat & alternatives*
oscarEncounter.formRourke2.formMilk=Milk Products*
oscarEncounter.formRourke2.formHomogenizedMilk=Homogenized milk
oscarEncounter.formRourke2.formEncourageCup=Encourage cup vs bottle
oscarEncounter.formRourke2.formAppetiteReduced=Appetite reduced
oscarEncounter.formRourke2.msgEducationAdvice=EDUCATION &amp; ADVICE
oscarEncounter.formRourke2.msgSafety=Safety
oscarEncounter.formRourke2.msgBehaviour=Behaviour
oscarEncounter.formRourke2.msgFamily=Family
oscarEncounter.formRourke2.msgOther=Other
oscarEncounter.formRourke2.formCarSeat=Car seat (toddler)
oscarEncounter.formRourke2.formWalker=<i>Stairs/walker</i>
oscarEncounter.formRourke2.formBathSafety=Bath safety*; safe toys
oscarEncounter.formRourke2.formParentChildInteraction=Parent/child interaction
oscarEncounter.formRourke2.formChildCare=Child care/return to work
oscarEncounter.formRourke2.formFamilyConflict=Family conflict/stress
oscarEncounter.formRourke2.formSiblings=Siblings
oscarEncounter.formRourke2.btnPoisons=Poisons*; PCC#
oscarEncounter.formRourke2.formElectricPlugs=<i>Electric plugs</i>
oscarEncounter.formRourke2.formNightWaking=Night waking/crying
oscarEncounter.formRourke2.formChildProofing=Childproofing
oscarEncounter.formRourke2.formSeparation=Separation anxiety
oscarEncounter.formRourke2.formAssessDay=Assess day care need
oscarEncounter.formRourke2.formAssessHomeVisit=Assess home visit need
oscarEncounter.formRourke2.formSecondHandSmoke=Second hand smoke
oscarEncounter.formRourke2.formSmokeDetectors=Smoke detectors
oscarEncounter.formRourke2.formHotWater=<i>Hot Water &lt; 54&deg;C</i>
oscarEncounter.formRourke2.formTeething=Teething/
oscarEncounter.formRourke2.btnDentalCare=Dental care
oscarEncounter.formRourke2.msgDevelopment=DEVELOPMENT
oscarEncounter.formRourke2.msgDecelopmentDesc=(Inquiry &amp; observation of milestones)<br>Tasks are set after the time of normal milestone acquisition.<br>Absence of any item suggests the need for further assessment of development<br>
oscarEncounter.formRourke2.formTurnsHead=Turns head toward sounds
oscarEncounter.formRourke2.formLaughs=Laughs/squeals at parent
oscarEncounter.formRourke2.formHeadSteady=Head steady
oscarEncounter.formRourke2.formGrasps=Grasps/reaches
oscarEncounter.formRourke2.formNoParentConcern=No parent concern
oscarEncounter.formRourke2.formFollowsMovingObjects=Follows a moving object
oscarEncounter.formRourke2.formRespondsName=Responds to own name
oscarEncounter.formRourke2.formBabbles=Babbles
oscarEncounter.formRourke2.formRollsFromBack=Rolls from back to stomach or stomach to back
oscarEncounter.formRourke2.formSitsWithSupport=Sits with support
oscarEncounter.formRourke2.formBringHandsToMouth=Brings hands/toys to mouth
oscarEncounter.formRourke2.formLooksForHiddenToy=Looks for hidden toy
oscarEncounter.formRourke2.formDifferentSounds=Babbles different sounds & to get attention
oscarEncounter.formRourke2.formSitsWithoutSupport=Sits without support
oscarEncounter.formRourke2.formStandsWithSupport=Stands with support
oscarEncounter.formRourke2.formOpposesThumbAndIndex=Opposes thumb & index finger
oscarEncounter.formRourke2.formReachestobePicked=Reaches to be picked up & held
oscarEncounter.formRourke2.formUnderstandsSimpleRequests=Understands simple requests, e.g. find your shoes
oscarEncounter.formRourke2.formChatters=Chatters using 3 different sounds
oscarEncounter.formRourke2.formCrawls=Crawls or 'bum' shuffles
oscarEncounter.formRourke2.formPullsToStand=Pulls to stand/walks holding on
oscarEncounter.formRourke2.formShowsManyEmotions=Shows many emotions
oscarEncounter.formRourke2.msgPhysicalExamination=PHYSICAL EXAMINATION
oscarEncounter.formRourke2.msgPhysicalExaminationDesc=Evidence based screening for specific conditions is highlighted, but an appropriate age-specific focused physical examination is recommended at each visit
oscarEncounter.formRourke2.formRedReflex=<i>Eyes (red reflex)</i>
oscarEncounter.formRourke2.formCover=Cover/uncover test & inquiry
oscarEncounter.formRourke2.formHearing=<b>Hearing inquiry</b>
oscarEncounter.formRourke2.formBabbling=Babbling
oscarEncounter.formRourke2.formHips=<b>Hips</b>
oscarEncounter.formRourke2.formFontanelles=Fontanelles
oscarEncounter.formRourke2.msgProblems=PROBLEMS &amp; PLANS
oscarEncounter.formRourke2.formTBexposure=Inquire about possible TB exposure
oscarEncounter.formRourke2.btnAntiHB=Anti-HBs & HbsAG
oscarEncounter.formRourke2.formAntiHB=*</b><br> &nbsp;&nbsp;(If HbsAg pos mother)
oscarEncounter.formRourke2.formHgb=Hgb. (If at risk)*
oscarEncounter.formRourke2.formSerumLead=<i>Serum lead (If at risk)*</i>
oscarEncounter.formRourke2.msgImmunization=IMMUNIZATION
oscarEncounter.formRourke2.msgImmunizarionDesc=Guidelines may vary by province
oscarEncounter.formRourke2.formHIB=<b>HIB</b>
oscarEncounter.formRourke2.formAPDT=<b>aPDT polio</b>
oscarEncounter.formRourke2.formHep=Hep.B vaccine
oscarEncounter.formRourke2.formTB=TB skin text?
oscarEncounter.formRourke2.formMMR=<b>MMR</b>
oscarEncounter.formRourke2.formVaricella=Varicella vaccine
oscarEncounter.formRourke2.formSignature=Signature

oscarEncounter.formRourke3.title=Rourke Baby Record
oscarEncounter.formRourke3.msgSaveAndPrintPreview=Do you wish to save this form and view the print preview?
oscarEncounter.formRourke3.msgSave=Are you sure you want to save this form?
oscarEncounter.formRourke3.msgExit=Are you sure you wish to exit without saving your changes?
oscarEncounter.formRourke3.msgSaveExit=Are you sure you wish to save and close this window?
oscarEncounter.formRourke3.msgTypeANumber=You must type in a number in the field.
oscarEncounter.formRourke3.btnSave=Save
oscarEncounter.formRourke3.btnSaveExit=Save and Exit
oscarEncounter.formRourke3.btnExit=Exit
oscarEncounter.formRourke3.btnPrint=Print
oscarEncounter.formRourke3.btnGraphLenght=Graph Length and Weight
oscarEncounter.formRourke3.btnGraphHead=Graph Head Circumference
oscarEncounter.formRourke3.btnPage1=Page 1
oscarEncounter.formRourke3.btnPage2=Page 2
oscarEncounter.formRourke3.msgPage3=Page 3
oscarEncounter.formRourke3.msgRourkeBabyRecord=Rourke Baby Record: EVIDENCE BASED INFANT/CHILD HEALTH MAINTENANCE GUIDE III
oscarEncounter.formRourke3.msgBirthRemarks=Birth remarks
oscarEncounter.formRourke3.msgName=Name
oscarEncounter.formRourke3.msgRiskFactors=Risk Factors/Family History
oscarEncounter.formRourke3.msgBirthDate=Birth Date
oscarEncounter.formRourke3.msgMale=Male
oscarEncounter.formRourke3.msgFemale=Female
oscarEncounter.formRourke3.formLenght=Length
oscarEncounter.formRourke3.msgLenghtUnit=cm
oscarEncounter.formRourke3.formHeadCirc=Head Circ
oscarEncounter.formRourke3.msgHeadCircUnit=cm
oscarEncounter.formRourke3.formBirthWt=Birth Wt
oscarEncounter.formRourke3.msgBirthUnit=kg
oscarEncounter.formRourke3.formDischargeWt=Discharge Wt
oscarEncounter.formRourke3.msgDischargeWtUnit=kg
oscarEncounter.formRourke3.msgAge=AGE
oscarEncounter.formRourke3.msg18Months=18 months
oscarEncounter.formRourke3.msg2-3years=2-3 years
oscarEncounter.formRourke3.msg4-5years=4-5 years
oscarEncounter.formRourke3.msgDate=DATE
oscarEncounter.formRourke3.msgGrowth=GROWTH
oscarEncounter.formRourke3.formHt=Ht. <small>(cm)</small>
oscarEncounter.formRourke3.formWt=Wt. <small>(kg)</small>
oscarEncounter.formRourke3.formHdCirc=Hd. Circ <small>(cm)</small>
oscarEncounter.formRourke3.msgParentalConcerns=PARENTAL CONCERNS
oscarEncounter.formRourke3.msgNutrition=NUTRITION
oscarEncounter.formRourke3.formNoBottles=No bottles
oscarEncounter.formRourke3.formHomogenized=Homogenized or 2% milk
oscarEncounter.formRourke3.formFoodGuide=Canada's Food Guide
oscarEncounter.formRourke3.form2-100milk=2% milk
oscarEncounter.formRourke3.msgEducationalAdvice=EDUCATION &amp; ADVICE
oscarEncounter.formRourke3.msgSafety=Safety
oscarEncounter.formRourke3.msgBehaviour=Behaviour
oscarEncounter.formRourke3.msfFamily=Family
oscarEncounter.formRourke3.msgOther=Other
oscarEncounter.formRourke3.btnbathSafety=Bath safety
oscarEncounter.formRourke3.btnChokngSafeToys=Choking/safe toys
oscarEncounter.formRourke3.formTemperment=Temperment
oscarEncounter.formRourke3.formLimitSetting=Limit setting
oscarEncounter.formRourke3.formSocializingOpp=Socializing opportunities
oscarEncounter.formRourke3.formDentalCare=Dental Care
oscarEncounter.formRourke3.formToiletTraining=Toilet training
oscarEncounter.formRourke3.formBikeHelmets=Bike Helmets
oscarEncounter.formRourke3.formMatches=Matches
oscarEncounter.formRourke3.formCarbonMonoxide=Carbon monoxide
oscarEncounter.formRourke3.formSmokeDetectors=Smoke detectors
oscarEncounter.formRourke3.formParentChildInteraction=Parent/child interaction
oscarEncounter.formRourke3.formAssessDayCare=Assess day care & preschool needs
oscarEncounter.formRourke3.formDentalCareCheckUp=Dental Care/check up
oscarEncounter.formRourke3.formWaterSafety=Water Safety
oscarEncounter.formRourke3.formSchoolReadiness=School readiness
oscarEncounter.formRourke3.msgDevelopment=DEVELOPMENT
oscarEncounter.formRourke3.msgDevelopmentDesc=(Inquiry &amp; observation of milestones)<br> Tasks are set after the time of normal milestone acquisition.<br> Absence of any item suggests the need for further assessment of development<br>
oscarEncounter.formRourke3.formPoints=Points to pictures (eg. show me the ...) and to 3 different body parts
oscarEncounter.formRourke3.form5Words=At least 5 words
oscarEncounter.formRourke3.formFingerFood=Picks up and eats finger food
oscarEncounter.formRourke3.formWalkAlone=Walks alone
oscarEncounter.formRourke3.formStack3Blocks=Stacks at least 3 blocks
oscarEncounter.formRourke3.formShowAffection=Shows affection
oscarEncounter.formRourke3.formPointShow=Points to show parent something
oscarEncounter.formRourke3.formLooksWhenTalk=Looks at you when talking/playing together
oscarEncounter.formRourke3.formNoParentsConcerns=No parent concerns
oscarEncounter.formRourke3.msg2Years=2 years
oscarEncounter.formRourke3.formNewWordWeek=At least 1 new word/week
oscarEncounter.formRourke3.form2WordSentences=2-word sentences
oscarEncounter.formRourke3.formTriesToRun=Tries to run
oscarEncounter.formRourke3.formPutObjectsContainer=Puts objects into small container
oscarEncounter.formRourke3.formCopies=Copies adult's actions
oscarEncounter.formRourke3.formDevelopNewSkills=Continues to develop new skills
oscarEncounter.formRourke3.msg3Years=3 years
oscarEncounter.formRourke3.formUnderstands2StepDirection=Understands 2 step direction
oscarEncounter.formRourke3.formTurnsKnobs=Twists lids off jars or turns knobs
oscarEncounter.formRourke3.formTurnsOnePage=Turns pages one at a time
oscarEncounter.formRourke3.formShareSomeTime=Share some of the time
oscarEncounter.formRourke3.formListenMusic=Listens to music or stories for 5-10 minutes with adults
oscarEncounter.formRourke3.msg4Years=4 years
oscarEncounter.formRourke3.formUnderstandsRelated3PartDirection=Understands related 3 part direction
oscarEncounter.formRourke3.formAsksQuestions=Asks a lot of questions
oscarEncounter.formRourke3.formStandsOn1Foot=Stands on 1 foot for 1-3 seconds
oscarEncounter.formRourke3.formDraw3PartsPerson=Draws a person with at least 3 body parts
oscarEncounter.formRourke3.formToiletTrained=Toilet trained during the day
oscarEncounter.formRourke3.msg5Years=5 years
oscarEncounter.formRourke3.formCounts10=Counts to 10 and knows common colours & shapes
oscarEncounter.formRourke3.formSpeaksClearly=Speaks clearly in sentences
oscarEncounter.formRourke3.formPlayWithBall=Throws & catches a ball
oscarEncounter.formRourke3.formHops1Foot=Hops on 1 foot
oscarEncounter.formRourke3.formSharesWillingly=Shares willingly
oscarEncounter.formRourke3.formWorksAlone20Minutes=Works alone at an activity for 20-30 minutes
oscarEncounter.formRourke3.msgPhysicalExamination=PHYSICAL EXAMINATION
oscarEncounter.formRourke3.msgPhysicalExaminationDecs=Evidence based screening for specific conditions is highlighted, but an appropriate age-specific focused physical examination is recommended at each visit
oscarEncounter.formRourke3.formRedEyes=<i>Eyes (red reflex)</i>
oscarEncounter.formRourke3.btnCoverTest=Cover/uncover test & inquiry
oscarEncounter.formRourke3.msgHearing=Hearing inquiry
oscarEncounter.formRourke3.formVisualAcuity=<i>Visual acuity</i>
oscarEncounter.formRourke3.formBloodPressure=<i>Blood pressure</i>
oscarEncounter.formRourke3.msgProblems=PROBLEMS &amp; PLANS
oscarEncounter.formRourke3.msgSerumLead=Serum lead (If at risk)
oscarEncounter.formRourke3.msgImmunization=IMMUNIZATION
oscarEncounter.formRourke3.msgImmunizationDesc=Guidelines may vary by province
oscarEncounter.formRourke3.formHIB=HIB
oscarEncounter.formRourke3.formPolio=aPDT polio
oscarEncounter.formRourke3.formMMR=MMR
oscarEncounter.formRourke3.formSignature=Signature

oscarEncounter.formIntakeHx.title=Intake History
oscarEncounter.formIntakeHx.notValidated=NOT VALIDATED
oscarEncounter.formIntakeHx.save=Save
oscarEncounter.formIntakeHx.saveAndExit=Save & Exit
oscarEncounter.formIntakeHx.print=Print PDF
oscarEncounter.formIntakeHx.exit=Exit
oscarEncounter.formIntakeHx.sectionDemographicTitle=Demographic Information
oscarEncounter.formIntakeHx.firstName=First Name
oscarEncounter.formIntakeHx.lastName=Last Name
oscarEncounter.formIntakeHx.sectionEmergencyContact=Emergency Contact
oscarEncounter.formIntakeHx.chartNo=Chart No.
oscarEncounter.formIntakeHx.dob=Date of Birth
oscarEncounter.formIntakeHx.sex=Sex
oscarEncounter.formIntakeHx.emergencyName=Name
oscarEncounter.formIntakeHx.emergencyPhone=Telephone
oscarEncounter.formIntakeHx.emergencyAddress=Address
oscarEncounter.formIntakeHx.sectionHomePhysician=Home Physician
oscarEncounter.formIntakeHx.physicianName=Name
oscarEncounter.formIntakeHx.physicianTelephone=Telephone
oscarEncounter.formIntakeHx.physicianAddress=Address
oscarEncounter.formIntakeHx.sectionAcademicInfo=Academic Information
oscarEncounter.formIntakeHx.notSpecified=Not Specified
oscarEncounter.formIntakeHx.academicFullTime=Full-time
oscarEncounter.formIntakeHx.academicPartTime=Part-time
oscarEncounter.formIntakeHx.academicFaculty=Faculty
oscarEncounter.formIntakeHx.academicYear=Academic Year
oscarEncounter.formIntakeHx.academicEnrollment=Enrollment Status

oscarEncounter.formIntakeHx.sectionAllergiesTitle=Allergies
oscarEncounter.formIntakeHx.allergicToDrugsYN=Are you allergic to any drugs
oscarEncounter.formIntakeHx.allergicDrugNames=Please list the names of ALL drugs to which you are allergic and provide details on reactions or symptoms for each
oscarEncounter.formIntakeHx.drug1=Drug 1
oscarEncounter.formIntakeHx.drug2=Drug 2
oscarEncounter.formIntakeHx.drug3=Drug 3
oscarEncounter.formIntakeHx.drug4=Drug 4
oscarEncounter.formIntakeHx.drug5=Drug 5
oscarEncounter.formIntakeHx.drug6=Drug 6
oscarEncounter.formIntakeHx.drug7=Drug 7
oscarEncounter.formIntakeHx.drug8=Drug 8
oscarEncounter.formIntakeHx.reactionSymptoms=Reaction(s)/Symptoms(s)
oscarEncounter.formIntakeHx.allergyShotYN=Are you presently receiving allergy shots
oscarEncounter.formIntakeHx.allergyNonDrugYN=Do you have any NON-DRUG allergies
oscarEncounter.formIntakeHx.allergicNonDrug=Please indicate (check) if you are allergic to any of the following
oscarEncounter.formIntakeHx.allergicNonDrugBeeSting=Bee or wasp sting
oscarEncounter.formIntakeHx.allergicNonDrugOtherPollen=Other pollen(s)
oscarEncounter.formIntakeHx.allergicNonDrugRagweedPollen=Ragweed pollen
oscarEncounter.formIntakeHx.allergicNonDrugGrasses=Grasses
oscarEncounter.formIntakeHx.allergicNonDrugAnimalFur=Animal Fur
oscarEncounter.formIntakeHx.allergicNonDrugFood=Food(s)
oscarEncounter.formIntakeHx.allergicNonDrugDust=Dust allergy
oscarEncounter.formIntakeHx.allergicNonDrugOtherAgent=Any other agent(s)

oscarEncounter.formIntakeHx.sectionRxTitle=Prescriptions
oscarEncounter.formIntakeHx.takeRxDrugsYN=Do you take ANY prescription drugs (INCLUDING BIRTH CONTROL PILLS or ACNE MEDICATION) at this time
oscarEncounter.formIntakeHx.medsTaken=Please indicate the names of any medications you are currently taking

oscarEncounter.formIntakeHx.sectionMedHistoryTitle=Medical History
oscarEncounter.formIntakeHx.hospitalization=Have you had any serious past illness(es) that have required hospitalization OTHER THAN OPERATIONS
oscarEncounter.formIntakeHx.pastIllness=Please list all serious past illness(es) you have had and indicate your age at the time of each illness
oscarEncounter.formIntakeHx.age=Age
oscarEncounter.formIntakeHx.pastIllness1=Illness 1
oscarEncounter.formIntakeHx.pastIllness2=Illness 2
oscarEncounter.formIntakeHx.pastIllness3=Illness 3
oscarEncounter.formIntakeHx.pastIllness4=Illness 4
oscarEncounter.formIntakeHx.pastIllness5=Illness 5
oscarEncounter.formIntakeHx.pastIllness6=Illness 6
oscarEncounter.formIntakeHx.operation=Have you had any operations
oscarEncounter.formIntakeHx.operationAndAge=Please list all operations you have had and indicate your age at the time of each operation
oscarEncounter.formIntakeHx.operation1=Operation 1
oscarEncounter.formIntakeHx.operation2=Operation 2
oscarEncounter.formIntakeHx.operation3=Operation 3
oscarEncounter.formIntakeHx.operation4=Operation 4
oscarEncounter.formIntakeHx.operation5=Operation 5
oscarEncounter.formIntakeHx.operation6=Operation 6

oscarEncounter.formIntakeHx.medicalConditionsTitle=Medical Conditions
oscarEncounter.formIntakeHx.hadConditions=Please indicate if you have, or have had the following conditions and provide details if Yes
oscarEncounter.formIntakeHx.brokenBones=Broken bones
oscarEncounter.formIntakeHx.migraine=Migraine
oscarEncounter.formIntakeHx.neuroDisorder=Neurological disorder
oscarEncounter.formIntakeHx.asthma=Asthma
oscarEncounter.formIntakeHx.pneumonia=Pneumonia
oscarEncounter.formIntakeHx.lungDisease=Lung disease
oscarEncounter.formIntakeHx.heartDisease=Heart disease
oscarEncounter.formIntakeHx.ulcer=Ulcer
oscarEncounter.formIntakeHx.bowelDisease=Bowel Disease
oscarEncounter.formIntakeHx.hepatitis=Hepatitis
oscarEncounter.formIntakeHx.hivPositive=Positive HIV test
oscarEncounter.formIntakeHx.thyroidProblem=Thyroid problem
oscarEncounter.formIntakeHx.bloodDisorder=Blood disorder
oscarEncounter.formIntakeHx.diabetes=Diabetes
oscarEncounter.formIntakeHx.bloodTransfusion=Blood transfusion
oscarEncounter.formIntakeHx.cancer=Cancer or leukemia
oscarEncounter.formIntakeHx.sexualDisease=Sexual Disease
oscarEncounter.formIntakeHx.describe=Describe
oscarEncounter.formIntakeHx.urinary=Urinary infection
oscarEncounter.formIntakeHx.emotional=Emotional disorder
oscarEncounter.formIntakeHx.arthritis=Arthritis
oscarEncounter.formIntakeHx.eatingDisorder=Eating Disorder
oscarEncounter.formIntakeHx.osteoporosis=Osteoporosis
oscarEncounter.formIntakeHx.skinProblems=Skin Problems
oscarEncounter.formIntakeHx.highBP=High Blood Pressure
oscarEncounter.formIntakeHx.learningDisability=Learning disability
oscarEncounter.formIntakeHx.schizophrenia=Schizophrenia
oscarEncounter.formIntakeHx.alcoholDependency=Alcohol Dependency
oscarEncounter.formIntakeHx.multipleSclerosis=Multiple Sclerosis
oscarEncounter.formIntakeHx.stroke=Stroke
oscarEncounter.formIntakeHx.highCholesterol=High cholesterol
oscarEncounter.formIntakeHx.depression=Depression
oscarEncounter.formIntakeHx.drugDependancy=Drug dependancy
oscarEncounter.formIntakeHx.otherDisease=Other disease

oscarEncounter.formIntakeHx.immunizationsTitle=Immunizations
oscarEncounter.formIntakeHx.whenImmunized=Please indicate yes or no if you have been immunized and the year of your last booster (if known) for each of the following
oscarEncounter.formIntakeHx.year=Year
oscarEncounter.formIntakeHx.hepatitisBSerum=Hepatitis B (Serum Hepatitis)
oscarEncounter.formIntakeHx.tetanusLockjaw=Tetanus (Lockjaw)
oscarEncounter.formIntakeHx.polio=Polio
oscarEncounter.formIntakeHx.mmr=MMR (Mumps, Measles, Rubella)
oscarEncounter.formIntakeHx.lastTBTest=Year of last TB skin test
oscarEncounter.formIntakeHx.rubella=Rubella
oscarEncounter.formIntakeHx.varicella=Varicella (Chickenpox)
oscarEncounter.formIntakeHx.meningitis=Meningitis
oscarEncounter.formIntakeHx.pneumococcus=Pneumococcus
oscarEncounter.formIntakeHx.hadDisease=Please indicate if you have HAD the disease and the year by selecting yes or no, and the year from the drop down boxes
oscarEncounter.formIntakeHx.immunizationCardOnPerson=Do you have your immunization history card with you

oscarEncounter.formIntakeHx.socialHistoryTitle=Social History
oscarEncounter.formIntakeHx.wearSeatbelt=How often do you wear a seatbelt
oscarEncounter.formIntakeHx.smoker=Are you a smoker at present
oscarEncounter.formIntakeHx.smokingAmount=How much do you smoke (individual cigarettes per day)
oscarEncounter.formIntakeHx.pastSmoking=Did you smoke in the past
oscarEncounter.formIntakeHx.streetDrugs=Do you use street drugs of any kind
oscarEncounter.formIntakeHx.drinkAlcohol=Do you drink alcohol
oscarEncounter.formIntakeHx.numberOfDrinks=On average, how many drinks do you have per occasion
oscarEncounter.formIntakeHx.drinksPerWeek=On average, how many drinks do you have per week
oscarEncounter.formIntakeHx.hoursOfExercise=On average, how many hours do you exercise per week

oscarEncounter.formIntakeHx.familyHistoryTitle=Family History
oscarEncounter.formIntakeHx.familyHistory.familyConditions=Please indicate if and which members of your biological family (siblings, mother, father, aunts, uncles, and grandparents) have the following conditions
oscarEncounter.formIntakeHx.familyHistory.migraine=Migraine
oscarEncounter.formIntakeHx.familyHistory.neuroDisorder=Neurological Disorder
oscarEncounter.formIntakeHx.familyHistory.asthma=Asthma
oscarEncounter.formIntakeHx.familyHistory.pheumonia=Pheumonia
oscarEncounter.formIntakeHx.familyHistory.lungDisease=Lung Disease
oscarEncounter.formIntakeHx.familyHistory.heartDisease=Heart Disease
oscarEncounter.formIntakeHx.familyHistory.ulcer=Ulcer
oscarEncounter.formIntakeHx.familyHistory.bowelDisease=Bowel Disease
oscarEncounter.formIntakeHx.familyHistory.hepatitis=Hepatitis
oscarEncounter.formIntakeHx.familyHistory.thyroid=Thyroid Problem
oscarEncounter.formIntakeHx.familyHistory.bloodDisorder=Blood Disorder
oscarEncounter.formIntakeHx.familyHistory.diabetes=Diabetes
oscarEncounter.formIntakeHx.familyHistory.bloodTransfusion=Blood Transfusion
oscarEncounter.formIntakeHx.familyHistory.cancer=Cancer or Leukemia
oscarEncounter.formIntakeHx.familyHistory.uri=Urinary infection
oscarEncounter.formIntakeHx.familyHistory.emotional=Emotional Disorder
oscarEncounter.formIntakeHx.familyHistory.arthritis=Arthritis
oscarEncounter.formIntakeHx.familyHistory.osteoporosis=Osteoporosis
oscarEncounter.formIntakeHx.familyHistory.skinProblems=Skin Problems
oscarEncounter.formIntakeHx.familyHistory.highBP=High Blood Pressure
oscarEncounter.formIntakeHx.familyHistory.learningDisability=Learning Disability
oscarEncounter.formIntakeHx.familyHistory.schizophrenia=Schizophrenia
oscarEncounter.formIntakeHx.familyHistory.alcoholDependency=Alcohol Dependency
oscarEncounter.formIntakeHx.familyHistory.multipleSclerosis=Multiple Sclerosis
oscarEncounter.formIntakeHx.familyHistory.stroke=Stroke
oscarEncounter.formIntakeHx.familyHistory.cholesterol=Cholesterol
oscarEncounter.formIntakeHx.familyHistory.depression=Depression
oscarEncounter.formIntakeHx.familyHistory.drugDependency=Drug Dependency

oscarEncounter.formIntakeHx.currentIssuesTitle=Current Issues
oscarEncounter.formIntakeHx.currentIssues.bodySystemProblems=For each of the following body systems please indicate any problems you may be having in your own words: 
oscarEncounter.formIntakeHx.currentIssues.general=General
oscarEncounter.formIntakeHx.currentIssues.nervousSystem=Nervous System
oscarEncounter.formIntakeHx.currentIssues.headEarsNoseThroat=Head, Eyes, Ears, Nose, Throat
oscarEncounter.formIntakeHx.currentIssues.neck=Neck
oscarEncounter.formIntakeHx.currentIssues.chest=Chest
oscarEncounter.formIntakeHx.currentIssues.heart=Heart
oscarEncounter.formIntakeHx.currentIssues.gastro=Gastrointestinal
oscarEncounter.formIntakeHx.currentIssues.genitals=Genitals and Urinary systems
oscarEncounter.formIntakeHx.currentIssues.psychiatric=Psychiatric

oscarEncounter.formIntakeHx.womensHealthTitle=Womens' Health
oscarEncounter.formIntakeHx.femalePt=If you are a female patient, please complete the following questions
oscarEncounter.formIntakeHx.firstPeriod=How old were you when you had your first period
oscarEncounter.formIntakeHx.periodFrequency=Do you get your period every month
oscarEncounter.formIntakeHx.periodLength=How long does your period last (days)
oscarEncounter.formIntakeHx.periodCramps=Do you experience severe cramps with your periods
oscarEncounter.formIntakeHx.midcycleBleeding=Do you experience unusual or mid-cycle bleeding
oscarEncounter.formIntakeHx.pid=Have you ever had pelvic inflammatory disease
oscarEncounter.formIntakeHx.ovaryCyst=Have you ever had an ovarian cyst (cyst on an ovary)
oscarEncounter.formIntakeHx.breastCancer=Have you ever had breast cancer
oscarEncounter.formIntakeHx.breastMass=Have you ever had a breast lump or breast mass
oscarEncounter.formIntakeHx.pregnant=Have you ever been pregnant
oscarEncounter.formIntakeHx.abortion=Have you ever had a therapeutic abortion
oscarEncounter.formIntakeHx.abortionAge=At what age did you have an abortion
oscarEncounter.formIntakeHx.papTest=Have you EVER had a Pap test
oscarEncounter.formIntakeHx.abnormalPap=Have you ever had an abnormal Pap test result
oscarEncounter.formIntakeHx.lastPap=What was the year of your last Pap test
oscarEncounter.formIntakeHx.birthControl=Have you ever used a birth control pill (oral contraceptive)
oscarEncounter.formIntakeHx.whichBC=Please indicate the birth control pills you have used
oscarEncounter.formIntakeHx.bc1=Birth Control 1.
oscarEncounter.formIntakeHx.bc2=Birth Control 2.
oscarEncounter.formIntakeHx.bc3=Birth Control 3.
oscarEncounter.formIntakeHx.bc4=Birth Control 4.
oscarEncounter.formIntakeHx.whichBCNow=What birth control pill are you using now
oscarEncounter.formIntakeHx.bcProblems=Are you having problems with your present birth control pill
oscarEncounter.formIntakeHx.bse=Do you practice monthly breast self-examination (BSE)

oscarEncounter.formIntakeHx.sexualHealthTitle=Sexual Health
oscarEncounter.formIntakeHx.sexualHistory=Please indicate your sexual history
oscarEncounter.formIntakeHx.intercourse=Have you ever had sexual intercourse
oscarEncounter.formIntakeHx.maleSex=Have you ever had sex with a male
oscarEncounter.formIntakeHx.femaleSex=Have you ever had sex with a female
oscarEncounter.formIntakeHx.firstIntercourse=At what age did you first have sexual intercourse
oscarEncounter.formIntakeHx.numberOfPartners=How many partners did you have intercourse with in the last year
oscarEncounter.formIntakeHx.condomFrequency=How often do you use condoms
oscarEncounter.formIntakeHx.stds=Please indicate if you have had the following sexually transmitted diseases
oscarEncounter.formIntakeHx.hadSTD=Have you ever had a sexually transmitted disease (STD)
oscarEncounter.formIntakeHx.hpv=Venereal warts/HPV
oscarEncounter.formIntakeHx.chlamydia=Chlamydia
oscarEncounter.formIntakeHx.gonorrhea=Gonorrhea
oscarEncounter.formIntakeHx.genitalHerpes=Genital Herpes
oscarEncounter.formIntakeHx.syphilis=Syphilis

demographic.patient.context.born=Born
demographic.patient.context.sex=Sex
demographic.patient.context.hin=HIN

demographic.demographicaddarecord.title=ADD A DEMOGRAPHIC RECORD
demographic.demographicaddarecord.msgDuplicatedRecord=You may have Demographic the record already!!! Please search it first, then delete the duplicated one.
demographic.demographicaddarecord.msgSuccessful=Successful Addition of a Demographic Record.
demographic.demographicaddarecord.msgFailed=Sorry, addition has failed.
demographic.demographicaddarecord.goToRecord=Go to record
demographic.demographicaddarecord.goToCaisiRecord=Go to Client Summary


demographic.footer.btnBack=Back to Demographic Search Page
demographic.footer.btnClose=Close this Window

#Next of kin info
demographic.demographicaddrecordhtm.nok=Next of Kin
demographic.demographicaddrecordhtm.nok_fname=First Name
demographic.demographicaddrecordhtm.nok_lname=Last Name
demographic.demographicaddrecordhtm.nok_email=Email
demographic.demographicaddrecordhtm.nok_phone=Cell
demographic.demographicaddrecordhtm.nok_postal=Postal Code
demographic.demographicaddrecordhtm.nok_city=City
demographic.demographicaddrecordhtm.nok_address=Address
demographic.demographicaddrecordhtm.nok_province=Province

## Message resources for Colcamex Flowsheet.
form.colcamexFlowSheets.formColcamexFlowSheet.problems=Problems
form.colcamexFlowSheets.formColcamexFlowSheet.title=Flowsheet by Colcamex
form.colcamexFlowSheets.formColcamexFlowSheet.nk=None Known

form.colcamexFlowSheets.formColcamexFlowSheet.validation.header=<h3><font color="red">Validation Errors</font></h3><ul>
form.colcamexFlowSheets.formColcamexFlowSheet.validation.footer=</ul>
form.colcamexFlowSheets.formColcamexFlowSheet.validation.prefix=<li>
form.colcamexFlowSheets.formColcamexFlowSheet.validation.suffix=</li>

form.colcamexFlowSheets.formColcamexFlowSheet.errors.integer={0} must be an integer.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.required={0} is required.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.minlength={0} can not be less than {1} characters.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.maxlength={0} can not be greater than {1} characters.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.invalid=Value {0} is invalid for {1}.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.minvalue={0} value can not be less than {1} .
form.colcamexFlowSheets.formColcamexFlowSheet.errors.maxvalue={0} value can not be greater than {1} .

form.colcamexFlowSheets.formColcamexFlowSheet.errors.byte={0} must be a byte.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.short={0} must be a short.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.nointeger={0} cannot be an integer.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.long={0} must be a long.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.float={0} must be a float.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.double={0} must be a double.

form.colcamexFlowSheets.formColcamexFlowSheet.errors.date={0} invalid date format (yyyy-mm-dd).
form.colcamexFlowSheets.formColcamexFlowSheet.errors.range={0} is not in the range {1} through {2}.
form.colcamexFlowSheets.formColcamexFlowSheet.errors.email={0} is an invalid e-mail address.

form.colcamexFlowSheets.formColcamexFlowSheet.errors.errorpage.title=Colcamex Flowsheet Error

demographic.demographicaddrecordhtm.title=ADD A DEMOGRAPHIC RECORD
demographic.demographicaddrecordhtm.msgMissingFields=You must type in the following fields: Last Name, First Name, DOB, and Sex.
demographic.demographicaddrecordhtm.msgMainLabel=ADD A DEMOGRAPHIC RECORD
demographic.demographicaddrecordhtm.msgSearch=Search
demographic.demographicaddrecordhtm.formName=Name
demographic.demographicaddrecordhtm.formPhone=Phone
demographic.demographicaddrecordhtm.formDOB=DOB
demographic.demographicaddrecordhtm.formAddress=Address
demographic.demographicaddrecordhtm.formHIN=Health Ins. #
demographic.demographicaddrecordhtm.formLastName=Last Name
demographic.demographicaddrecordhtm.formFirstName=First Name
demographic.demographicaddrecordhtm.formPrefName=Preferred Name
demographic.demographicaddrecordhtm.formFormerName=Former Name
demographic.demographicaddrecordhtm.formCity=City
demographic.demographicaddrecordhtm.formprovince=Province
demographic.demographicaddrecordhtm.formPostal=Postal
demographic.demographicaddrecordhtm.formPhoneCell=Cell Phone
demographic.demographiceditdemographic.aboriginal=Aboriginal
demographic.demographicaddrecordhtm.formPhoneHome=Phone (H)
demographic.demographicaddrecordhtm.formPhoneWork=Phone (W)
demographic.demographicaddrecordhtm.formPhoneComment=Phone Comment
demographic.demographicaddrecordhtm.formEMail=Email
demographic.demographicaddrecordhtm.formNewsLetter=Newsletter
demographic.demographicaddrecordhtm.formPHRUserName=PHR UserName
demographic.demographicaddrecordhtm.formSex=Sex
demographic.demographicaddrecordhtm.formF=F
demographic.demographicaddrecordhtm.formM=M
demographic.demographicaddrecordhtm.formVer=Ver
demographic.demographicaddrecordhtm.formEFFDate=*EFF Date
demographic.demographicaddrecordhtm.formHCType=HC Type
demographic.demographicaddrecordhtm.formNurse=Nurse
demographic.demographicaddrecordhtm.formDoctor=Physician/MRP
demographic.demographicaddrecordhtm.formResident=Resident
demographic.demographicaddrecordhtm.formReferalDoctor=Referral Doctor
demographic.demographicaddrecordhtm.formReferalDoctorN=Referral Doctor #
demographic.demographicaddrecordhtm.formFamilyDoctor=Family Doctor
demographic.demographicaddrecordhtm.formFamilyDoctorN=Family Doctor #
demographic.demographicaddrecordhtm.formPCNRosterStatus=Enrollment Status
demographic.demographicaddrecordhtm.formPCNDateJoined=Date Enrolled
demographic.demographicaddrecordhtm.formPatientStatus=Patient Status
demographic.demographicaddrecordhtm.formChartNo=Chart No.
demographic.demographicaddrecordhtm.formDateJoined=*Date Joined
demographic.demographicaddrecordhtm.formEndDate=*End Date
demographic.demographicaddrecordhtm.formAlert=Alert
demographic.demographicaddrecordhtm.allergyAlert=Allergy Alert
demographic.demographicaddrecordhtm.formNotes=Notes
demographic.demographicaddrecordhtm.btnAddRecord=Add Record
demographic.demographicaddrecordhtm.btnSwipeCard=Swipe Card
demographic.demographicaddrecordhtm.btnCancel=Cancel
demographic.demographicaddrecordhtm.formDateFormat=Date format: yyyy-mm-dd
demographic.demographicaddrecordhtm.formRG=RG
demographic.demographicaddrecordhtm.formCPF=CPF
demographic.demographicaddrecordhtm.formChartAddress=Chart Address
demographic.demographicaddrecordhtm.formMarriageCertificate=Marriage Certificate
demographic.demographicaddrecordhtm.formBirthCertificate=Birth Certificate
demographic.demographicaddrecordhtm.formMaritalState=Marital State
demographic.demographicaddrecordhtm.formMaritalState.optSingle=Single
demographic.demographicaddrecordhtm.formMaritalState.optMarried=Married
demographic.demographicaddrecordhtm.formMaritalState.optSeparated=Separated
demographic.demographicaddrecordhtm.formMaritalState.optDivorced=Divorced
demographic.demographicaddrecordhtm.formMaritalState.optWidower=Widow/Widower
demographic.demographicaddrecordhtm.formPartnerName=Partner's Name
demographic.demographicaddrecordhtm.formFatherName=Father's Name
demographic.demographicaddrecordhtm.formMotherName=Mother's Name
demographic.demographicaddrecordhtm.formDistrict=District/Area
demographic.demographicaddrecordhtm.formAddressNo=Address Number
demographic.demographicaddrecordhtm.formComplementaryAddress=Complementary Address
demographic.demographicaddrecordhtm.formNewsLetter.optUnknown=Unknown
demographic.demographicaddrecordhtm.formNewsLetter.optNo=No
demographic.demographicaddrecordhtm.formNewsLetter.optPaper=Paper
demographic.demographicaddrecordhtm.formNewsLetter.optElectronic=Electronic
demographic.demographicaddrecordhtm.cytolNum=Cytology #
demographic.demographicaddrecordhtm.AddNewRosterStatus= Add New
demographic.demographicaddrecordhtm.AddNewPatient=Add New
demographic.demographicaddrecordhtm.AC-Active=AC - Active
demographic.demographicaddrecordhtm.IN-InActive=IN - Inactive
demographic.demographicaddrecordhtm.DE-Deceased=DE - Deceased
demographic.demographicaddrecordhtm.MO-Moved=MO - Moved
demographic.demographicaddrecordhtm.FI-Fired=FI - Fired
demographic.demographicaddrecordhtm.EN-Enrolled=EN - Enrolled
demographic.demographicaddrecordhtm.NE-NotEnrolled=NE - Not Enrolled
demographic.demographicaddrecordhtm.TE-terminated=TE - terminated
demographic.demographicaddrecordhtm.FS-feeforservice=FS - fee for service
demographic.demographicaddrecordhtm.UHIP=UHIP
demographic.demographicaddrecordhtm.Ext=Ext
demographic.demographicaddrecordhtm.Search=Search #
demographic.demographicaddrecordhtm.msgDr=DR
demographic.demographiceditdemographic.aboriginal=Aboriginal
demographic.demographicaddrecordhtm.btnReturnToSchedule=Return to Schedule
demographic.demographicaddrecordhtm.formPHU=PHU

# Title info
demographic.demographicaddrecordhtm.msgSr = SR
demographic.demographicaddrecordhtm.msgSgt = SGT
demographic.demographicaddrecordhtm.msgSen = SEN
demographic.demographicaddrecordhtm.msgRtHon = RT_HON
demographic.demographicaddrecordhtm.msgRev = REV
demographic.demographicaddrecordhtm.msgReeve = REEVE
demographic.demographicaddrecordhtm.msgProf = PROF
demographic.demographicaddrecordhtm.msgMssr = MSSR
demographic.demographicaddrecordhtm.msgMr = MR
demographic.demographicaddrecordhtm.msgMrs = MRS
demographic.demographicaddrecordhtm.msgMiss = MISS
demographic.demographicaddrecordhtm.msgMs = MS
demographic.demographicaddrecordhtm.msgDr = DR
demographic.demographicaddrecordhtm.msgNotSet = -Not Set-

oscarMessenger.DisplayMessages.title=DisplayMessages
oscarMessenger.DisplayMessages.msgMessenger=Messenger
oscarMessenger.DisplayMessages.msgInbox=Inbox
oscarMessenger.DisplayMessages.msgSentTitle=Sent Items
oscarMessenger.DisplayMessages.msgArchived=Archived Items
oscarMessenger.DisplayMessages.btnCompose=Compose Message
oscarMessenger.DisplayMessages.btnRefresh=Refresh Inbox
oscarMessenger.DisplayMessages.btnSent=Sent Messages
oscarMessenger.DisplayMessages.btnDeletedMessage=Deleted Message
oscarMessenger.DisplayMessages.btnExit=Exit Messenger
oscarMessenger.DisplayMessages.msgStatus=Status
oscarMessenger.DisplayMessages.msgFrom=From
oscarMessenger.DisplayMessages.msgTo=To
oscarMessenger.DisplayMessages.msgSubject=Subject
oscarMessenger.DisplayMessages.msgDate=Date
oscarMessenger.DisplayMessages.msgLinked=Patient
oscarMessenger.DisplayMessages.formArchive=Archive
oscarMessenger.DisplayMessages.formUnarchive=Unarchive
oscarMessenger.DisplayMessages.msgNewerMessage=Newer Messages
oscarMessenger.DisplayMessages.msgOlderMessage=Older Messages
oscarMessenger.DisplayMessages.msgNextMessage=Next Messages
oscarMessenger.DisplayMessages.msgAllMessage=All Messages
#edit 2006-0811-01 by wreby
oscarMessenger.DisplayMessages.btnSearch=Search
oscarMessenger.DisplayMessages.btnClearSearch=Clear
#end edit 2006-0811-01 by wreby

oscarMessenger.CreateMessage.title=createMessage
oscarMessenger.CreateMessage.msgEmptyMessage=You have forgot to enter a message
oscarMessenger.CreateMessage.msgNoProvider=No providers have been selected to send this message too
oscarMessenger.CreateMessage.msgMessenger=Messenger
oscarMessenger.CreateMessage.msgCreate=Create a Message
oscarMessenger.CreateMessage.btnDisplay=Display Messages
oscarMessenger.CreateMessage.btnClear=Clear New Message
oscarMessenger.CreateMessage.btnExit=Exit Messenger
oscarMessenger.CreateMessage.msgRecipients=Recipients
oscarMessenger.CreateMessage.msgMessage=Message
oscarMessenger.CreateMessage.btnSendMessage=Send Message
oscarMessenger.CreateMessage.chkUrgent=Urgent
oscarMessenger.CreateMessage.btnSendnArchiveMessage=Send &amp; Archive
oscarMessenger.CreateMessage.msgRemoteLocations=RemoteLocations
oscarMessenger.CreateMessage.formSubject=Subject
oscarMessenger.CreateMessage.AttachFile=Attach File
oscarMessenger.CreateMessage.SaveAttachmentToDocs=Save a copy of attachment to echart documents
oscarMessenger.CreateMessage.msgAttachments=Message has attachments!<br>Only doctors will recieve your attachments!
oscarMessenger.CreateMessage.msgLinkThisMessage=Link this message to ...
oscarMessenger.CreateMessage.msgSearchDemographic=Search Demographic
oscarMessenger.CreateMessage.msgClearSelectedDemographic=Clear Selected Demographic
oscarMessenger.CreateMessage.msgAttachDemographic=Attach Demographic
oscarMessenger.CreateMessage.msgSelectedDemographic=Selected Demographic
#oscarMessenger.CreateMessage.msgLinkThisMessage=Link this message to ...
oscarMessenger.CreateMessage.btnSendMessageCpyToeChart=Send Message / Copy Message to eChart
oscarMessenger.CreateMessage.btnOpenEchart=Open EChart
oscarMessenger.CreateMessage.btnPasteToEchart=Paste to EChart
oscarMessenger.CreateMessage.btnOpenInOscarMsg=Open in OscarMSG

myoscar.msg.From=From
myoscar.msg.Reply=Reply
myoscar.msg.SubjectPrefix=PHR msg

oscarMessenger.SentMessage.title=SentMessage
oscarMessenger.SentMessage.msgMessenger=Messenger
oscarMessenger.SentMessage.msgMessageSent=Your Message has Been Sent
oscarMessenger.SentMessage.btnCompose=Compose Message
oscarMessenger.SentMessage.btnExit=Exit Messenger
oscarMessenger.SentMessage.msgMessageSentTo=your message has been sent to
oscarMessenger.SentMessagebtnBack=Back to Inbox

oscarMessenger.ViewMessage.title=ViewMessage
oscarMessenger.ViewMessage.msgMessenger=Messenger
oscarMessenger.ViewMessage.msgInbox=Inbox
oscarMessenger.ViewMessage.btnCompose=Compose Message
oscarMessenger.ViewMessage.btnInbox=Back To Inbox
oscarMessenger.ViewMessage.btnSent=Back to Sent
oscarMessenger.ViewMessage.btnExit=Exit Messenger
oscarMessenger.ViewMessage.msgFrom=From
oscarMessenger.ViewMessage.msgTo=To
oscarMessenger.ViewMessage.msgSubject=Subject
oscarMessenger.ViewMessage.msgDate=Date
oscarMessenger.ViewMessage.msgAttachments=Attachments
oscarMessenger.ViewMessage.btnAttach=Click here to view attachment
oscarMessenger.ViewMessage.btnReply=Reply
oscarMessenger.ViewMessage.btnReplyAll=Reply All
oscarMessenger.ViewMessage.btnForward=Forward
oscarMessenger.ViewMessage.btnDelete=Delete
oscarMessenger.ViewMessage.btnPrint=Print

admin.preferencesearchrecordhtm.title=Search a Preference
admin.preferencesearchrecordhtm.msgSearch=SEARCH PREFERENCE RECORDS
admin.preferencesearchrecordhtm.btnSearch=Search
admin.preferencesearchrecordhtm.msgReserved=Reserved
admin.preferencesearchrecordhtm.msgInstructionsLater=Instructions will be provided later.
admin.preferencesearchrecordhtm.btnBack=Back to Admin Page.
admin.preferencesearchrecordhtm.btnLogOut=Log Out


billing.manageBillingLocation.title=oscarBilling :: manage billing form  ::
billing.manageBillingLocation.msgServiceTypeExists=Service Type ID exists, please verify!
billing.manageBillingLocation.msgDeleteBillingConfirm=You are about to delete the billing form, are you sure?
billing.manageBillingLocation.msgBilling=Billing
billing.manageBillingLocation.msgCodeDescription=Code &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Description
billing.manageBillingLocation.msgClinicLocation=Clinic Location
billing.manageBillingLocation.msgDescription=Description
billing.manageBillingLocation.btnAdd=Add

billing.manageBillingform.title=Billing :: manage billing form  ::
billing.manageBillingform.msgIDExists=Service Type ID exists, please verify!
billing.manageBillingform.msgDeleteBillingConfirm=You are about to delete the billing form, are you sure?
billing.manageBillingform.msgBilling=Billing
billing.manageBillingform.formServiceCode=service code
billing.manageBillingform.formDxCode=Dx Code
billing.manageBillingform.formSelectForm=Select form
billing.manageBillingform.formAddDelete=Add/Edit/Delete Form
billing.manageBillingform.formManagePremium=Manage Premium Form
billing.manageBillingform.btnManage=Manage
billing.manageBillingform.btnManage.msgRequiredField=Service Type ID is required.

demographic.zfooterbackclose.btnBack=Back
demographic.zfooterbackclose.btnClose=Close the Window

billing.manageBillingform_add.msgAdd=Adding New Form
billing.manageBillingform_add.formServiceID=Service Type ID
billing.manageBillingform_add.formServiceName=Service Type Name
billing.manageBillingform_add.formGroup1Name=Group1 Name
billing.manageBillingform_add.formGroup2Name=Group 2 Name
billing.manageBillingform_add.formGroup3Name=Group 3 Name
billing.manageBillingform_add.formDefaultBillType=Default Billing Type
billing.manageBillingform_add.msgExistType=Manage Existing Type
billing.manageBillingform_add.msgDeleteType=exclude Existing Service
billing.manageBillingform_add.btnAdd=Add Form

billing.manageBillingform_premium.msgAdd=Add Premium
billing.manageBillingform_premium.btnAdd=Add Code
billing.manageBillingform_premium.msgDescription=Service Description
billing.manageBillingform_premium.btnDelete=Delete Code

billing.manageBillingform_service.btnUpdate=Update

billing.billingCorrection.title=Billing Correction
billing.billingCorrection.msgCorrection=<font size="3">Billing - Correction</font>
billing.billingCorrection.formInvoiceNo=Invoice No
billing.billingCorrection.msgLastUpdate=Last update
billing.billingCorrection.msgDoNotHavePermission=You do not have permission to view this invoice
billing.billingCorrection.msgSearchValidInvoiceNumber=Please search a valid invoice number
billing.billingCorrection.msgInvoiceNumberNotExist=Invoice number does not exist
billing.billingCorrection.msgPatientName=Patient Name
billing.billingCorrection.formHealth=Health#
billing.billingCorrection.msgSex=Sex
billing.billingCorrection.formDOB=D.O.B.
billing.billingCorrection.msgAddress=Address
billing.billingCorrection.msgCity=City
billing.billingCorrection.msgProvince=Province
billing.billingCorrection.msgDoctor=Referral Doctor
billing.billingCorrection.msgDoctorNo=Referral Doctor #
billing.billingCorrection.msgAditInfo=Additional Information
billing.billingCorrection.msg3rdPartyPaymentInformation=3rd Party Payment Information
billing.billingCorrection.formSpecialty=Specialty
billing.billingCorrection.formNone=None
billing.billingCorrection.formFlu=FLU
billing.billingCorrection.formHCType=HC-Type
billing.billingCorrection.formManualReview=Manual Review
billing.billingCorrection.msgRoster=Roster Status
billing.billingCorrection.formBillingType=Billing Status
billing.billingCorrection.formSelectBillType=--- Select Bill Status ---
billing.billingCorrection.formBillTypeH=H | Capitated
billing.billingCorrection.formBillTypeO=O | Bill OHIP
billing.billingCorrection.formBillTypeP=P | Bill Patient
billing.billingCorrection.formBillTypeN=N | Do Not Bill
billing.billingCorrection.formBillTypeW=W | Bill Worker's Compensation Board
billing.billingCorrection.formBillTypeB=B | Submitted OHIP
billing.billingCorrection.formBillTypeS=S | Settled/Paid by OHIP
billing.billingCorrection.formBillTypeX=X | Bad Debt
billing.billingCorrection.formBillTypeD=D | Deleted Bill
billing.billingCorrection.formBillTypeI=I | Bonus Codes
billing.billingCorrection.btnBillingDate=Billing Date
billing.billingCorrection.formVisit=Visit
billing.billingCorrection.msgSelectLocation=--- Select Visit Location ---
billing.billingCorrection.formBillingPhysician=Billing Physician#
billing.billingCorrection.msgSelectProvider=--- Select Provider ---
billing.billingCorrection.formVisitType=Visit Type
billing.billingCorrection.msgSelectVisitType=--- Select Visit Type ---
billing.billingCorrection.formClinicVisit=00 | Clinic Visit
billing.billingCorrection.formOutpatientVisit=01 | Outpatient Visit
billing.billingCorrection.formHospitalVisit=02 | Hospital Visit
billing.billingCorrection.formER=03 | ER
billing.billingCorrection.formNursingHome=04 | Nursing Home
billing.billingCorrection.formHomeVisit=05 | Home Visit
billing.billingCorrection.btnAdmissionDate=Admission Date
billing.billingCorrection.formServiceCode=Service Code
billing.billingCorrection.formDescription=Description
billing.billingCorrection.formUnit=Unit
billing.billingCorrection.formFee=$ Fee
billing.billingCorrection.formDiagnosticCode=Diagnostic Code
billing.billingCorrection.btnDXSearch=DX Search
billing.billingCorrection.btnSubmit=Submit
billing.billingCorrection.msgPayer=Bill To
billing.billingCorrection.msgNotes=Billing Notes
billing.billingCorrection.3rdPartyPaymentDate=Pay Date
billing.billingCorrection.3rdPartyPayment=Payment
billing.billingCorrection.3rdPartyPayMethod=Pay Method
billing.billingCorrection.3rdPartyRefund=Refund
billing.billingCorrection.3rdPartyPaymentOfficer=Payment Officer
billing.billingCorrection.3rdPartyTotal=Total
billing.billingCorrection.3rdPartyTotalPaid=Paid
billing.billingCorrection.3rdPartyTotalRefunded=Refunded
billing.billingCorrection.3rdPartyOutstandingBalance=Outstanding Balance
billing.billingCorrection.addPayment=Add Payment
billing.billingCorrection.typePayment=Payment
billing.billingCorrection.typeRefund=Refund
billing.billingCorrection.paymentCash=Cash
billing.billingCorrection.paymentCheque=Cheque
billing.billingCorrection.paymentVisa=Visa
billing.billingCorrection.paymentMasterCard=Mastercard
billing.billingCorrection.paymentAmex=Amex
billing.billingCorrection.paymentDebit=Debit
billing.billingCorrection.paymentAlternate=Alternate
billing.billingCorrection.paymentElectronic=Electronic
billing.billingCorrection.3rdPartyTotalOwing=Invoiced
billing.billingCorrection.3rdPartyPayAmt=Pay Amount
billing.billingCorrection.useDemoContactYesNo=Use BillTo on Reprint
billing.billingCorrection.dueDate=Due Date
billing.hospitalBilling.formDxCode=Dx Code
billing.hospitalBilling.formOscarBilling = Billing
billing.hospitalBilling.formDx = Dx
billing.hospitalBilling.optAbove = Only Above
billing.hospitalBilling.optAll = All
billing.hospitalBilling.btnReferral = Refer. Doctor #
billing.hospitalBilling.frmBillPhysician = Billing Physician
billing.hospitalBilling.frmAssgnPhysician = Assig. Physician
billing.hospitalBilling.frmBillHistory = Billing History
billing.hospitalBilling.frmLastFive = (last 5 records)
billing.hospitalBilling.frmSerial = Serial No.
billing.hospitalBilling.frmApptAdmDate = Appt/Adm Date
billing.hospitalBilling.frmCreateDate = Create Date
billing.hospitalBilling.msgDates = You can select multiple dates from the calendar to bill. The default unit value is 1.
billing.hospitalBilling.btnNext = Next

billing.billingCalendarPopup.title=CALENDAR
billing.billingCalendarPopup.btnLast=last month
billing.billingCalendarPopup.btnNext=next month
billing.billingCalendarPopup.msgSun=Sun
billing.billingCalendarPopup.msgMon=Mon
billing.billingCalendarPopup.msgTue=Tue
billing.billingCalendarPopup.msgWed=Wed
billing.billingCalendarPopup.msgThu=Thu
billing.billingCalendarPopup.msgFri=Fri
billing.billingCalendarPopup.msgSat=Sat
billing.billingCalendarPopup.btnExit=Exit

billing.billingDigSearch.title=Diagnostic Code Search
billing.billingDigSearch.msgDiagnostic=Diagnostic Code Search
billing.billingDigSearch.msgMaxSelections=(Maximum 3 selections)
billing.billingDigSearch.msgRefine=Refine Search
billing.billingDigSearch.msgCodeRange=Code Range
billing.billingDigSearch.msgOR=OR
billing.billingDigSearch.msgDescription=Description
billing.billingDigSearch.btnSearch=Search
billing.billingDigSearch.formCode=Code
billing.billingDigSearch.formDescription=Description
billing.billingDigSearch.btnUpdate=Update
billing.billingDigSearch.msgNoMatch=No match found

billing.billingCorrection.msgBillingCorrection=<font size="3">Billing - Correction</font>
billing.billingCorrection.msgCorrectionReview=Correction Review
billing.billingCorrection.msgPatientInformation=Patient Information
billing.billingCorrection.msgName=Patient Name
billing.billingCorrection.msgHealthNo=Health#
billing.billingCorrection.msgDOB=D.O.B.
billing.billingCorrection.msgPostalCode=Postal Code
billing.billingCorrection.msgReferal=Referral
billing.billingCorrection.msgReferealNo=Referral #
billing.billingCorrection.msgAdditionalInf=Additional Information
billing.billingCorrection.msgHCType=HC-Type
billing.billingCorrection.msgManualReview=Manual Review
billing.billingCorrection.msgReferralDoctor=Referral Doctor
billing.billingCorrection.msgRosterStatus=Roster Status
billing.billingCorrection.msgBillingInf=Billing Information
billing.billingCorrection.msgBillingType=Billing Type
billing.billingCorrection.msgBillingDate=Billing Date
billing.billingCorrection.msgVisitLocation=Visit Location
billing.billingCorrection.msgBillingPhysicianNo=Billing Physician#
billing.billingCorrection.msgVisitType=Visit Type
billing.billingCorrection.msgVisitDate=Visit Date
billing.billingCorrection.msgServiceCode=Service Code
billing.billingCorrection.msgDescription=Description
billing.billingCorrection.msgQuantity=Quantity
billing.billingCorrection.msgFee=$ Fee
billing.billingCorrection.msgDiagCode=Diagnostic Code
billing.billingCorrection.msgTotal=Total
billing.billingCorrection.btnCancel=Cancel

billing.billingCorrectionSubmit.title=Billing Correction
billing.billingCorrectionSubmit.msgSuccessfull=Billing Correction Successfully Done
billing.billingCorrectionSubmit.btnCorrectAnother=Correct Another One
billing.billingCorrectionSubmit.btnClose=Successful - Close this window

oscar.billing.on.paymentReceived.endDate=End Date
oscar.billing.on.paymentReceived.startDate=Start Date
oscar.billing.on.paymentReceived.freezePeriod=Payment Freeze Period
oscar.billing.on.paymentReceived.billingNo=Billing No.
oscar.billing.paymentReceived.title=Billing
oscar.billing.on.paymentReceived.providerName=Provider:
oscar.billing.on.paymentReceived.allproviders=All Providers
oscar.billing.on.paymentReceived.generateReport=Generate Report
oscar.billing.on.paymentReceived.raBillingReport=Ministry of Health Payments
oscar.billing.on.paymentReceived.3rdPartyBillingReport=3rd Party Payments
oscar.billing.on.paymentReceived.premiumPaymentReport=Premium Payments
oscar.billing.on.paymentReceived.claimNo=CLAIM No.
oscar.billing.on.paymentReceived.serviceDate=SERVICE DATE
oscar.billing.on.paymentReceived.demographicName=DEMOGRAPHIC NAME
oscar.billing.on.paymentReceived.serviceCode=SERVICE CODE
oscar.billing.on.paymentReceived.dxCode=DX
oscar.billing.on.paymentReceived.serviceCount=SERVICE COUNT
oscar.billing.on.paymentReceived.claimed=CLAIMED
oscar.billing.on.paymentReceived.paid=PAID
oscar.billing.on.paymentReceived.adjustments=ADJ
oscar.billing.on.paymentReceived.payprogram=PAY PROGRAM
oscar.billing.on.paymentReceived.invoiceNumber=INVOICE No.
oscar.billing.on.paymentReceived.errorCodes=ERROR CODE
oscar.billing.on.paymentReceived.messages=WARNING(s)
oscar.billing.on.paymentReceived.itemCount=Item Count
oscar.billing.on.paymentReceived.cumulativeTotal=Total
oscar.billing.on.paymentReceived.totalPaid=Total Paid
oscar.billing.paymentReceived.errorOnDate=Date not valid
oscar.billing.paymentReceived.errorEndDateGreater=End date is greater than start date
oscar.billing.on.paymentReceived.billed=BILLED
oscar.billing.on.paymentReceived.refund=REFUNDED
oscar.billing.on.paymentReceived.paymentDate=PAYMENT DATE
oscar.billing.on.paymentReceived.balanceOutstanding=OUTSTANDING BALANCE
oscar.billing.on.paymentReceived.currentFee=FEE
oscar.billing.on.paymentReceived.invoiceStatus=STATUS
oscar.billing.on.paymentReceived.providerName=PROVIDER NAME
oscar.billing.on.paymentReceived.payDate=PAY DATE
oscar.billing.on.genRADesc.applyPremium=STATUS
oscar.billing.on.genRADesc.premiumTitle=PREMIUM PAYMENTS
oscar.billing.on.genRADesc.submitPremium=Apply Premium(s)
oscar.billing.on.genRADesc.ohipNo=OHIP No.
oscar.billing.on.genRADesc.providerName=PROVIDER NAME
oscar.billing.on.genRADesc.totalMonthlyPayment=TOTAL MONTHLY PAYMENT
oscar.billing.on.genRADesc.paymentDate=PAYMENT DATE

oscar.billing.ca.on.billingON.review.msgServiceCodeAlreadyBilled=Service code already billed for this demographic during the last year.
oscar.billing.ca.on.billingON.review.invoiceNo=Invoice No.

billing.billingStatus.action=ACTION
billing.billingStatus.print=Print
billing.billingStatus.email=Email
billing.billing3rdInv.email=Email
billing.billing3rdInv.printPDF=Print PDF
billing.billing3rdInv.msgPrinted=Printed
billing.billing3rdInv.msgEmailed=Emailed
billing.billing3rdInv.chartNo=Chart No.

quickBillingBC.blankServiceDate=Blank Service Date

admin.demographicaddrecordhtm.title=ADD PATIENT INFO
admin.demographicaddrecordhtm.msgTitle=ADD A DEMOGRAPHIC RECORD
admin.demographicaddrecordhtm.formLastName=Last Name
admin.demographicaddrecordhtm.formFirstName=First Name
admin.demographicaddrecordhtm.formAddr=Address
admin.demographicaddrecordhtm.formCity=City
admin.demographicaddrecordhtm.formProvince=Province
admin.demographicaddrecordhtm.formPostal=Postal
admin.demographicaddrecordhtm.formPhoneH=Phone(H)
admin.demographicaddrecordhtm.formPhoneW=Phone(O)
admin.demographicaddrecordhtm.formDOB=DOB<font size="-2">(yyyy-mm-dd)</font>
admin.demographicaddrecordhtm.formAge=Age
admin.demographicaddrecordhtm.formHIN=Health Ins. #
admin.demographicaddrecordhtm.formVer=Ver
admin.demographicaddrecordhtm.formRoster=Roster Status
admin.demographicaddrecordhtm.formPatientStatus=Patient Status
admin.demographicaddrecordhtm.formDatejoined=Date Joined
admin.demographicaddrecordhtm.formHCType=HC Type
admin.demographicaddrecordhtm.formStaff=Staff
admin.demographicaddrecordhtm.formSex=Sex
admin.demographicaddrecordhtm.formF=F
admin.demographicaddrecordhtm.formM=M
admin.demographicaddrecordhtm.formEndDate=End Date
admin.demographicaddrecordhtm.formEFF=EFF Date
admin.demographicaddrecordhtm.formPCNInd=PCN Indicator
admin.demographicaddrecordhtm.formChartNo=Chart No.
admin.demographicaddrecordhtm.formHCRenewDate=HC Renew Date
admin.demographicaddrecordhtm.formFamilyDoc=Family Doctor
admin.demographicaddrecordhtm.btnAdd=Add Record
admin.demographicaddrecordhtm.btnBack=Back to Admin Page.
admin.demographicaddrecordhtm.btnLogOut=Log Out

demographic.demographicaddrecordhtm.msgDemoLanguage=Language
demographic.demographicaddrecordhtm.msgSpoken=Spoken
demographic.demographiceaddrecordhtm.msgEnglish=English
demographic.demographiceaddrecordhtm.msgFrench=French
demographic.demographicaddrecordhtm.msgDemoTitle=Title
demographic.demographicaddrecordhtm.formOther=Other
demographic.demographiceditdemographic.formOther=Other
demographic.demographicaddrecordhtm.msgCountryOfOrigin = Country Of Origin
demographic.demographicaddarecordhtm.msgWaitList=Add patient to waiting list
demographic.demographicaddarecordhtm.msgWaitListNote=Waiting List Note
demographic.demographicaddarecordhtm.msgDateOfReq=Date of request
demographic.demographicaddarecordhtm.optCreateWaitList=--Please Create Waiting List Name first--
demographic.demographicaddrecordhtm.msgSIN = SIN
demographic.demographiceditdemographic.msgSIN = SIN

provider.btnSetDefaultSex=Set Default Sex
provider.setDefaultSex.title=Set Default Sex
provider.setDefaultSex.msgPrefs=Preferences
provider.setDefaultSex.msgDefaultSex=Set Default Sex
provider.setDefaultSex.msgEdit=Please select which sex will be chosen at default when creating a new demographic.
provider.setDefaultSex.msgSuccess=The default sex has been set.
provider.setDefaultSex.msgMale=Male
provider.setDefaultSex.msgFemale=Female
provider.setDefaultSex.btnSubmit=Save
provider.setDefaultSex.msgError=An error occured while attempting to update database. Please contact IT Support.
provider.setDefaultRefPractitioner=Set Default Referring Practitioner for Consultation Requests
provider.setDefaultRefPractitioner.title=Set Default Referring Practitioner
provider.setDefaultRefPractitioner.msgPrefs=Preferences
provider.setDefaultRefPractitioner.msgDefaultRefPrac=Set Default Referring Practitioner
provider.setDefaultRefPractitioner.msgEdit=Please select which referring practitioner will be chosen at default when creating a new consultation request.
provider.setDefaultRefPractitioner.msgSuccess=The default referring practitioner has been set.
provider.setDefaultRefPractitioner.btnSubmit=Save
provider.setDefaultRefPractitioner.msgError=An error occurred while attempting to update database. Please contact IT Support.
provider.setConsultDefaultLetterhead=Set Default Consultation Request Letterhead
provider.setConsultDefaultLetterhead.title=Set Default Consultation Request Letterhead
provider.setConsultDefaultLetterhead.msgPrefs=Preferences
provider.setConsultDefaultLetterhead.msgDefaultLetterhead=Set Default Consultation Request Letterhead
provider.setConsultDefaultLetterhead.msgEdit=Please select which letterhead will be used by default when creating a new consultation request.
provider.setConsultDefaultLetterhead.msgSuccess=The default consultation letterhead has been set.
provider.setConsultDefaultLetterhead.btnSubmit=Save
provider.setConsultDefaultLetterhead.msgError=An error occurred while attempting to update database. Please contact IT Support.
provider.setConsultDefaultLetterheadMultisite.title=Set Default Letterhead Address/Fax/Phone for Multisite Consultations
provider.setConsultDefaultLetterheadMultisite.msgPrefs=Preferences
provider.setConsultDefaultLetterheadMultisite.msgDefaultLetterhead=Set Default Letterhead Address/Fax/Phone for Multisite Consultations
provider.setConsultDefaultLetterheadMultisite.msgEdit=Please select whether new consultation requests in Multisite default to the Address/Fax/Phone set to the Site/Location or the Provider Preferences
provider.setConsultDefaultLetterheadMultisite.msgSuccess=The default consultation letterhead address/fax/phone has been set.
provider.setConsultDefaultLetterheadMultisite.btnSubmit=Save
provider.btnSetHCType=Set Default HC Type
provider.setHCType.title=Set Default HC Type
provider.setHCType.msgPrefs=Preferences
provider.setHCType.msgHCType=Set Default HC Type
provider.setHCType.msgEdit=Please select which HC Type will be chosen at default when creating a new demographic.
provider.setHCType.msgSuccess=The default HC Type has now been set.
provider.setHCType.btnSubmit=Save
provider.setHCType.msgError=An error occured while attempting to update database. Please contact IT Support.
provider.btnsetDefaultSearchMode=Set Default Search Mode
provider.setDefaultSearchMode.title=Set Default Search Mode
provider.setDefaultSearchMode.msgPrefs=Preferences
provider.setDefaultSearchMode.msgDefaultSex=Set Default Demographic Search Mode
provider.setDefaultSearchMode.msgEdit=Please select which search mode will be chosen at default when searching demographics.
provider.setDefaultSearchMode.msgSuccess=The default search mode has been set.
provider.setDefaultSearchMode.btnSubmit=Save
provider.setDefaultSearchMode.msgError=An error occured while attempting to update database. Please contact IT Support.

provider.setDefaultOhipProvider.title=Set Default Physician on Generate OHIP File
provider.setDefaultOhipProvider.msgPrefs=Preferences
provider.setDefaultOhipProvider.msgDefaultText=Set Default Physician on Generate OHIP File
provider.setDefaultOhipProvider.msgEdit=Please select what billable provider will be selected by default on generate OHIP file
provider.setDefaultOhipProvider.msgSuccess=The default provider has been set.
provider.setDefaultOhipProvider.btnSubmit=Save
provider.setDefaultOhipProvider.msgError=An error occured while attempting to update database. Please contact IT Support.

provider.setDefaultRxPrintFullPage.title=Set Default Rx Page Print Mode
provider.setDefaultRxPrintFullPage.msgPrefs=Preferences
provider.setDefaultRxPrintFullPage.msgDefaultText=Set Default Rx Page Print Mode
provider.setDefaultRxPrintFullPage.msgEdit=Please select whether the checkbox on rx print
provider.setDefaultRxPrintFullPage.msgSuccess=The default rx print mode has been set.
provider.setDefaultRxPrintFullPage.btnSubmit=Save
provider.setDefaultRxPrintFullPage.msgError=An error occured while attempting to update database. Please contact IT Support.

provider.setDisplayCustomRosterStatus.title=Set Default Rx Page Print Mode
provider.setDisplayCustomRosterStatus.msgPrefs=Preferences
provider.setDisplayCustomRosterStatus.msgDefaultText=Set whether to display custom roster statuses on the schedule
provider.setDisplayCustomRosterStatus.msgEdit=Display custom roster statuses on the schedule:
provider.setDisplayCustomRosterStatus.msgSuccess=Prefence has been saved
provider.setDisplayCustomRosterStatus.btnSubmit=Save
provider.setDisplayCustomRosterStatus.msgError=An error occured while attempting to update database. Please contact IT Support.

provider.setDefaultPsBillingCode.msgEdit=Set Default Progress sheet Billing Code
provider.setDefaultPsBillingCode.msgSuccess=The Default Progress sheet Billing Code has been set.

admin.demographicaddrecord.title=ADD A DEMOGRAPHIC RECORD
admin.demographicaddrecord.msgDuplicated=You may have the CPP record already!!! Please search it first, then delete the duplicated one.
admin.demographicaddrecord.msgSuccessful=Successful Addition of a Demographic Record.
admin.demographicaddrecord.msgFailed=Sorry, addition has failed.

admin.resourcebaseurl.title=Help\Resources Link Management
admin.resourcebaseurl.formBaseUrl=Website:
admin.resourcebaseurl.formBaseUrlExample=https://oscarsupport.zendesk.com/hc/en-us
admin.resourcebaseurl.btnSave= Save
admin.resourcebaseurl.btnExit= Exit
admin.resourcebaseurl.btnAdd=Add Service Code

admin.providertemplate.title=ADD/DELETE A TEMPLATE
admin.providertemplate.msgTitle=ADD/EDIT A TEMPLATE
admin.providertemplate.formEdit=Edit Template
admin.providertemplate.btnEdit= Edit
admin.providertemplate.btnSave= Save
admin.providertemplate.btnExit= Exit
admin.providertemplate.btnDelete=Delete

admin.eChartAlert.title=eChart Admin Alert
admin.eChartAlert.enableDemographiceChart=Enable Demographic eChart Popup
admin.eChartAlert.enableDemographiceChartAbout=Sets whether a specific demographics chart alerts will appear as a popup on the echart
admin.eChartAlert.systemWideAlert=System Wide eChart Alert
admin.eChartAlert.placeHolder=Enter alert text here...
admin.eChartAlert.btnSave=Save
admin.eChartAlert.note1=Note: The chart alert will show for all users as a pop up when they enter the eChart.
admin.eChartAlert.note2=Users will have the option to click "Okay" and the popup will display again the next time they enter the same chart, or "Dismiss"  to no longer see the popup when they enter that chart. The choice of the user will not affect other users seeing the alert.

admin.allergyAlert.enableAllergyAlertPopup=Enable Allergy Alert Popup

admin.careConnect.title=CareConnect Rapid Access Configuration
admin.careConnect.careConnectStatus=CareConnect Rapid Access Button Status
admin.careConnect.careConnectAbout=Sets whether OSCAR will attempt to connect to CareConnect over PPN/Internet or some custom method
admin.careConnect.customURLAbout=OSCAR is currently configured to use a custom URL. If you wish to make any changes, please contact OSCAR Pro Support.
admin.careConnect.careConnectOrgs=CareConnect Orgs
admin.careConnect.careConnectOrgsAbout=Note: this field is used as a routing control which has not been implemented at this time.

tickler.ticklerMain.title=Tickler
tickler.ticklerMain.msgFolderName=Please enter a name for your folder.
tickler.ticklerMain.msgTickler=<font size="3">Tickler</font>
tickler.ticklerMain.formDateRange=Service Date-Range
tickler.ticklerMain.btnBegin=Begin
tickler.ticklerMain.btnEnd=End
tickler.ticklerMain.btnViewAll=View All
tickler.ticklerMain.formMoveTo=Move To
tickler.ticklerMain.formActive=Active
tickler.ticklerMain.formCompleted=Completed
tickler.ticklerMain.formDeleted=Deleted
tickler.ticklerMain.formSelectProvider=Select provider
tickler.ticklerMain.formAllProviders=All Providers
tickler.ticklerMain.btnCreateReport=Create Report
tickler.ticklerMain.msgDemographicName=Demographic Name
tickler.ticklerMain.msgDoctorName=Doctor Name
tickler.ticklerMain.msgDate=Service Date
tickler.ticklerMain.msgCreationDate=Creation Date
tickler.ticklerMain.msgProgram=Program
tickler.ticklerMain.msgStatus=Status
tickler.ticklerMain.msgMessage=Message
tickler.ticklerMain.msgNoMessages=No Tickler Messages
tickler.ticklerMain.btnCheckAll=Check All
tickler.ticklerMain.btnClearAll=Clear All
tickler.ticklerMain.btnAddTickler=Add Tickler
tickler.ticklerMain.btnEraseCompletely=Erase Completely
tickler.ticklerMain.btnComplete=Complete
tickler.ticklerMain.btnDelete=Delete
tickler.ticklerMain.msgAssignedTo=Assigned To
tickler.ticklerMain.msgSaveView=Save View
tickler.ticklerMain.editTickler=Edit
tickler.ticklerMain.msgCreator=Creator
tickler.ticklerMain.sortUp=Up
tickler.ticklerMain.sortDown=Down

tickler.ticklerEdit.title=Tickler
tickler.ticklerEdit.demographicName=Demographic Name
tickler.ticklerEdit.status=Status
tickler.ticklerEdit.chartNo=Chart No.
tickler.ticklerEdit.assignedTo=Assigned To
tickler.ticklerEdit.age=Age
tickler.ticklerEdit.serviceDate=Service Date
tickler.ticklerEdit.messages=Messages
tickler.ticklerEdit.addedBy=Added By
tickler.ticklerEdit.creationDate=Creation Date
tickler.ticklerEdit.updateDate=Update Date
tickler.ticklerEdit.priority=Priority
tickler.ticklerEdit.primaryPhone=Phone No. (Primary)
tickler.ticklerEdit.secondaryPhone=Phone No. (Secondary)
tickler.ticklerEdit.email=Email
tickler.ticklerEdit.update=Update
tickler.ticklerEdit.pasteMessage=Paste Message
tickler.ticklerEdit.newMessage=New Message
tickler.ticklerEdit.cancel=Cancel
tickler.ticklerEdit.suggestedText=Suggested Text
tickler.ticklerEdit.messageText=Message Text
tickler.ticklerEdit.add6month=6-month
tickler.ticklerEdit.add1year=1-year
tickler.ticklerEdit.calendarLookup=Calendar Lookup
tickler.ticklerEdit.emailFailed.error=Tickler email could not be sent. Check if email address is valid.
tickler.ticklerEdit.arg.error=There is an error with the tickler arguments.
tickler.ticklerEdit.emailDemographic=Email Demographic
tickler.ticklerEdit.emailedDemographic=Emailed Demographic

tickler.ticklerTextSuggest.activeText=Active
tickler.ticklerTextSuggest.inactiveText=Inactive
tickler.ticklerTextSuggest.textSuggestTitle=Text Suggestions
tickler.ticklerTextSuggest.enterText=New Text
tickler.ticklerTextSuggest.addText=Add Text
tickler.ticklerTextSuggest.save=Save
tickler.ticklerTextSuggest.cancel=Cancel

tickler.ticklerAdd.title=Tickler
tickler.ticklerAdd.msgInvalidDemographic=Invalid demographic information, please verify!
tickler.ticklerAdd.msgMissingDate=Missing service date, please verify!
tickler.ticklerAdd.msgTickler=<font size="3">Tickler</font>
tickler.ticklerAdd.msgNoProgramSelected=Please select a program to assign to the tickler.
tickler.ticklerAdd.formDemoName=Demographic Name
tickler.ticklerAdd.btnSearch=Search
tickler.ticklerAdd.formChartNo=Chart No
tickler.ticklerAdd.formServiceDate=Service Date
tickler.ticklerAdd.btnCalendarLookup=Calendar Lookup
tickler.ticklerAdd.btnToggleQuickpickDates=Show/Hide Date Quickpick Options
tickler.ticklerAdd.btnShowQuickpick=Show Quickpick
tickler.ticklerAdd.btnHideQuickpick=Hide Quickpick
tickler.ticklerAdd.btn6Month=6-month
tickler.ticklerAdd.btn1Year=1-year
tickler.ticklerAdd.formReminder=Reminder Message
tickler.ticklerAdd.btnCancel=Cancel and EXIT
tickler.ticklerAdd.btnSubmit=Submit and EXIT

tickler.ticklerDemoMain.title=Tickler
tickler.ticklerDemoMain.msgFolderName=Please enter a name for your folder.
tickler.ticklerDemoMain.msgTitle=<font size="3">Tickler</font>
tickler.ticklerDemoMain.formMoveTo=Move To
tickler.ticklerDemoMain.formActive=Active
tickler.ticklerDemoMain.formCompleted=Completed
tickler.ticklerDemoMain.formDeleted=Deleted
tickler.ticklerDemoMain.btnBegin=Begin
tickler.ticklerDemoMain.btnEnd=End
tickler.ticklerDemoMain.btnCreateReport=Create Report
tickler.ticklerDemoMain.msgDemoName=Demographic Name
tickler.ticklerDemoMain.msgDoctorName=Doctor Name
tickler.ticklerDemoMain.msgDate=Date
tickler.ticklerDemoMain.msgStatus=Status
tickler.ticklerDemoMain.msgMessage=Message
tickler.ticklerDemoMain.msgNoMessages=No Tickler Messages
tickler.ticklerDemoMain.btnCheckAll=Check All
tickler.ticklerDemoMain.btnClearAll=Clear All
tickler.ticklerDemoMain.btnAddTickler=Add Tickler
tickler.ticklerDemoMain.btnErase=Erase Completely
tickler.ticklerDemoMain.btnComplete=Complete
tickler.ticklerDemoMain.btnDelete=Delete

provider.appointmentprovideradminmonth.title=Provider Appointment Access
provider.appointmentprovideradminmonth.msgDateFormat=yyyy mm dd-date
provider.appointmentprovideradminmonth.msgDateDays=[+/-n]d - n days
provider.appointmentprovideradminmonth.msgDateWeeks=[+/-n]w - n weeks
provider.appointmentprovideradminmonth.msgDateMonths=[+/-n]m - n months
provider.appointmentprovideradminmonth.msgDaySchedule=View your daily schedule
provider.appointmentprovideradminmonth.btnDay=Day
provider.appointmentprovideradminmonth.msgWeekSchedule=View your weekly schedule
provider.appointmentprovideradminmonth.btnWeek=Week
provider.appointmentprovideradminmonth.msgMonthTemplate=View your monthly template
provider.appointmentprovideradminmonth.btnMonth=Month
provider.appointmentprovideradminmonth.msgResources=View Resources
provider.appointmentprovideradminmonth.btnResource=Resource
provider.appointmentprovideradminmonth.msgSearchPatients=Search for patient records
provider.appointmentprovideradminmonth.btnSearch=Search
provider.appointmentprovideradminmonth.msgGenReport=Generate a report
provider.appointmentprovideradminmonth.btnReport=Report
provider.appointmentprovideradminmonth.msgGenBilling=Generate a billing report
provider.appointmentprovideradminmonth.btnBilling=Billing
provider.appointmentprovideradminmonth.msgLabRep=View lab reports
provider.appointmentprovideradminmonth.btnLab=Lab
provider.appointmentprovideradminmonth.msgSettings=Edit your personal setting
provider.appointmentprovideradminmonth.btnPref=Pref
provider.appointmentprovideradminmonth.btnGo=GO
provider.appointmentprovideradminmonth.formAllProviders=All Providers
provider.appointmentprovideradminmonth.formGRP=GRP
provider.appointmentprovideradminmonth.btnlogOut=Log Out
provider.appointmentprovideradminmonth.msgSun=Sun
provider.appointmentprovideradminmonth.msgMon=Mon
provider.appointmentprovideradminmonth.msgTue=Tue
provider.appointmentprovideradminmonth.msgWed=Wed
provider.appointmentprovideradminmonth.msgThu=Thu
provider.appointmentprovideradminmonth.msgFri=Fri
provider.appointmentprovideradminmonth.msgSat=Sat
provider.appointmentprovideradminmonth.msgWeek=Week
provider.appointmentprovideradminmonth.btnLastMonth=Last Month
provider.appointmentprovideradminmonth.btnNextMonth=Next Month
provider.appointmentprovideradminmonth.msgJanuary=January
provider.appointmentprovideradminmonth.msgFebruary=February
provider.appointmentprovideradminmonth.msgMarch=March
provider.appointmentprovideradminmonth.msgApril=April
provider.appointmentprovideradminmonth.msgMay=May
provider.appointmentprovideradminmonth.msgJune=June
provider.appointmentprovideradminmonth.msgJuly=July
provider.appointmentprovideradminmonth.msgAugust=August
provider.appointmentprovideradminmonth.msgSeptember=September
provider.appointmentprovideradminmonth.msgOctober=October
provider.appointmentprovideradminmonth.msgNovember=November
provider.appointmentprovideradminmonth.msgDecember=December

schedule.scheduleflipview.title=Flip View
schedule.scheduleflipview.msgLastMonth=Last Month
schedule.scheduleflipview.msgNextmonth=Next Month
schedule.scheduleflipview.msgbookinglimit=# of bookings permitted
schedule.scheduleflipview.msgbookings=current # of bookings
schedule.scheduleflipview.btnNextMonth=next month
schedule.scheduleflipview.btnLastMonth=last month

eform.uploadhtml.title=Upload
eform.uploadhtml.msgFileMissing=Please input a file first, then click 'Upload' button.
eform.uploadhtml.msgUploadEForm=Upload eForm
eform.uploadhtml.btnBack=Back to Admin Page
eform.uploadhtml.formName=eForm name
eform.uploadhtml.formSubject=Additional Information
eform.uploadhtml.formFileName=Filename
eform.uploadhtml.showLatestFormOnly=Show Latest Form Only
eform.uploadhtml.patientIndependent=Patient Independent
eform.uploadhtml.enableAttachments=Enable Attachments
eform.uploadhtml.btnUpload=Upload
eform.uploadhtml.btnDeleted=List Deleted eForms
eform.uploadhtml.btnExport=Export
eform.uploadhtml.btnFormName=eForm Name
eform.uploadhtml.btnSubject=Additional Information
eform.uploadhtml.btnFile=File
eform.uploadhtml.btnDate=Modified Date
eform.uploadhtml.btnTime=Modified Time
eform.uploadhtml.btnRoleType=Role Type
eform.uploadhtml.msgAction=Action
eform.uploadhtml.btnDelete=Delete
eform.uploadhtml.msgLibrary=eForm Library
eform.uploadhtml.confirmDelete=Are you sure you want to delete this eForm?
eform.uploadhtml.editform=Edit
eform.uploadhtml.zipFile=Zip File
eform.uploadhtml.msgCurrentResources=Current Resources in OSCAR
eform.edithtml.msgEditEform=Edit eForm
eform.edithtml.msgBackToForms=Back to eForm List
eform.edithtml.msgSave=Save
eform.edithtml.msgLastModified=Last Saved
eform.edithtml.msgEditHtml=Edit HTML
eform.edithtml.cancelChanges=Restore Last Saved
eform.edithtml.msgPreviewLast=Preview Last Saved
eform.edithtml.msgChangesSaved=Changes Saved
eform.edithtml.frmSubmit=Submit
eform.edithtml.frmUploadFile=Upload File
eform.edithtml.frmUpload=Upload
eform.edithtml.createnew=Create eForm in Editor

eform.download.msgDownloadEform=Download All eForms
eform.download.msgName=eForm Name
eform.download.msgCreator=Author
eform.download.msgCategory=Category
eform.download.msgCreated=Created On
eform.download.btnLoadEform=Load eForm
eform.download.msgK2AResources=K2A Resources
eform.download.msgK2ABrowse=Browse K2A
eform.download.msgRefresh=Refresh

eform.independent.btnBack=Back to admin page
eform.independent.btnCurrent=Current eforms
eform.independent.btnDeleted=Deleted eforms

eform.uploadimages.msgChooseFile=Please choose a file first, then click Upload
eform.uploadimages.msgTitle=UPLOAD IMAGE
eform.uploadimages.btnBack=Back to Admin Page
eform.uploadimages.msgFileName=File name
eform.uploadimages.btnUpload=Upload
eform.uploadimages.processing=Processing...
eform.uploadimages.msgImageLibrary=Image Library
eform.uploadimages.msgimgName=Image Name
eform.uploadimages.msgImgAction=Action
eform.uploadimages.btnDelete=Delete
eform.uploadimages.imgDelete=Are you sure you want to permanently delete this image?
eform.uploadimages.imageMissing=<li>Image path is missing</li>
eform.uploadimages.imageAlreadyExists=<li>Image name "{0}" is already present</li>


dms.complete.title=DOCUMENT MANAGEMENT SYSTEM
dms.complete.msgTitle=UPLOAD DOCUMENT
dms.complete.msgCompleted=completed
dms.complete.btnDone=Done

admin.updatedemographicprovider.title=UPDATE PATIENT PROVIDERS
admin.updatedemographicprovider.msgTitle=UPDATE PATIENT PROVIDER
admin.updatedemographicprovider.msgRecords=record(s) have been updated.
admin.updatedemographicprovider.msgResident=Resident
admin.updatedemographicprovider.formReplace=Replace
admin.updatedemographicprovider.formWith=with
admin.updatedemographicprovider.formCondition=if a patient's last name starts with
admin.updatedemographicprovider.formTo=to
admin.updatedemographicprovider.btnGo= Go
admin.updatedemographicprovider.msgNurse=Nurse
admin.updatedemographicprovider.msgAltProvider1=Alt. Provider 1
admin.updatedemographicprovider.msgAltProvider2=Alt. Provider 2
admin.updatedemographicprovider.msgAltProvider3=Alt. Provider 3
admin.updatedemographicprovider.msgNoProvider=_________ (No Provider)

oscarReport.LabReqReport.msgLabReqReport=Lab Requestion Report
oscarReport.LabReqReport.msgMonth=Month
oscarReport.LabReqReport.msgAllProviders=ALL Providers
oscarReport.LabReqReport.msgPatientName=Patient Name
oscarReport.LabReqReport.msgLabReq=Laboratory Requisition
oscarReport.LabReqReport.msgReqDate=Requisition Date
oscarReport.LabReqReport.msgScannedLabDocs=Scanned Lab Documents
oscarReport.LabReqReport.msgLabDocuments=Lab Documents
oscarReport.LabReqReport.msgDocumentDesc=Document Description
oscarReport.LabReqReport.msgDate=Date
oscarReport.LabReqReport.msgMonths=Months
oscarReport.LabReqReport.msgReport=Report
oscarReport.LabReqReport.msgUpdateReport=Update Report

oscarReport.oscarReportscpbDemo.msgGA = GA Today
oscarReport.oscarReportscpbDemo.msgNoHeader = No.
oscarReport.oscarReportscpbDemo.btnBack = Back To Report
oscarReport.oscarReportscpbDemo.msgApproxEDD = Approximate EDD between
oscarReport.oscarReportscpbDemo.msgEither = Either (G>0)
oscarReport.oscarReportscpbDemo.msgMultiparous = Multiparous (T>0 or P>0)
oscarReport.oscarReportscpbDemo.msgPrimiparous = Primiparous (T=0 and P=0)
oscarReport.oscarReportscpbDemo.msgFNameBracket = (First Name)
oscarReport.oscarReportscpbDemo.msgLNameBracket = (Last Name)
oscarReport.oscarReportscpbDemo.msgIndyClientSearch = Individual Client Search
oscarReport.oscarReportscpbDemo.msgEDDbetween = EDD between
oscarReport.oscarReportscpbDemo.msgAnd = and
oscarReport.oscarReportscpbDemo.msgDOB = Date of Birth between
oscarReport.oscarReportscpbDemo.msgDeliveredClient = Delivered Clients
oscarReport.oscarReportscpbDemo.msgInActiveClient = Inactive Clients
oscarReport.oscarReportscpbDemo.msgActiveClient = Active Clients
oscarReport.oscarReportscpbDemo.msgLimit = Limitation
oscarReport.oscarReportscpbDemo.msgPartner = Partner's Name
oscarReport.oscarReportscpbDemo.msgFamPhys = Family Physician
oscarReport.oscarReportscpbDemo.msgEDD = EDD
oscarReport.oscarReportscpbDemo.msgPrefLang = Preferred Language
oscarReport.oscarReportscpbDemo.msgEmail = Email
oscarReport.oscarReportscpbDemo.msgWPhone = Phone (W)
oscarReport.oscarReportscpbDemo.msgHPhone = Phone (H)
oscarReport.oscarReportscpbDemo.msgPostalCode = Postal Code
oscarReport.oscarReportscpbDemo.msgCity = City
oscarReport.oscarReportscpbDemo.msgAddress = Address
oscarReport.oscarReportscpbDemo.msgProvince = Province
oscarReport.oscarReportscpbDemo.msgPHN = PHN
oscarReport.oscarReportscpbDemo.msgHINType=HC Type
oscarReport.oscarReportscpbDemo.msgHIN = Health Ins.
oscarReport.oscarReportscpbDemo.msgDateJoined = Date Joined
oscarReport.oscarReportscpbDemo.msgFirstName = First Name
oscarReport.oscarReportscpbDemo.msgLastName = Last Name
oscarReport.oscarReportscpbDemo.msgColHeader = Data Field
oscarReport.oscarReportscpbDemo.btnCSV = Report in CSV
oscarReport.oscarReportscpbDemo.btnHTML = Report in HTML
oscarReport.oscarReportscpbDemo.msgHeader = Client Database Report
oscarReport.oscarReportscpbDemo.title = Report List

oscarReport.oscarReportByTemplate.msgK2ABrowse=Browse K2A
oscarReport.oscarReportByTemplate.msgRefresh=Refresh
oscarReport.oscarReportByTemplate.msgK2ATemplate = K2A Report By Templates
oscarReport.oscarReportByTemplate.msgDownloadFromK2A = Download From K2A
oscarReport.oscarReportByTemplate.msgName = Template Name
oscarReport.oscarReportByTemplate.msgAuthor = Author
oscarReport.oscarReportByTemplate.msgCreated = Created On
oscarReport.oscarReportByTemplate.msgUpdated = Updated At
oscarReport.oscarReportByTemplate.msgDownload = Download

oscarReport.oscarReportDemoSetEdit.msgDemographic = demographic
oscarReport.oscarReportDemoSetEdit.msgSetEdit = Set Edit
oscarReport.oscarReportDemoSetEdit.msgPatientSet = Patient Set
oscarReport.oscarReportDemoSetEdit.msgOptionSet = --Select Set--
oscarReport.oscarReportDemoSetEdit.btnDisplaySet = Display Set
oscarReport.oscarReportDemoSetEdit.btnSetIneligible = Set Ineligible
oscarReport.oscarReportDemoSetEdit.msgIneligible = Check patients to set ineligible and click "Set Ineligible"
oscarReport.oscarReportDemoSetEdit.btnDelete = Delete
oscarReport.oscarReportDemoSetEdit.msgDelete = Check patients to Delete
oscarReport.oscarReportDemoSetEdit.msgDemo = Demo #
oscarReport.oscarReportDemoSetEdit.msgName = Name
oscarReport.oscarReportDemoSetEdit.msgDOB = DOB
oscarReport.oscarReportDemoSetEdit.msgAge = Age
oscarReport.oscarReportDemoSetEdit.msgRoster = Roster Status
oscarReport.oscarReportDemoSetEdit.msgDoctor = Doctor
oscarReport.oscarReportDemoSetEdit.msgEligibility = Eligibility
oscarReport.oscarReportDemoSetEdit.msgStatusEligibile = Eligible
oscarReport.oscarReportDemoSetEdit.msgStatusIneligibile = Ineligible
oscarReport.oscarReportDemoReport.frmNewsletter = Newsletter

oscarReport.oscarReportAgeSex.title=Billing Report
oscarReport.oscarReportAgeSex.msgOscarReport=<font size="3">Report</font>
oscarReport.oscarReportAgeSex.msgAgeSexRep=Age Sex Report
oscarReport.oscarReportAgeSex.formRostered=Rostered
oscarReport.oscarReportAgeSex.formNotRostered=Not Rostered
oscarReport.oscarReportAgeSex.formTotal=Total
oscarReport.oscarReportAgeSex.formSelectProvider=Select provider
oscarReport.oscarReportAgeSex.btnCreate=Create Report
oscarReport.oscarReportAgeSex.msgServiceDate=Service Date-Range
oscarReport.oscarReportAgeSex.btnBegin=Begin Date
oscarReport.oscarReportAgeSex.btnEnd=End Date
oscarReport.oscarReportAgeSex.msgDate=Date
oscarReport.oscarReportAgeSex.msgUnit=Unit
oscarReport.oscarReportAgeSex.msgPhysician=Physician

oscarReport.oscarReportAgeSex_noroster.msgAge=Age
oscarReport.oscarReportAgeSex_noroster.msgFemale=Female
oscarReport.oscarReportAgeSex_noroster.msgMale=Male
oscarReport.oscarReportAgeSex_noroster.msgTotal=Total
oscarReport.oscarReportAgeSex_noroster.msgGroup=Group

oscarReport.manageProvider.title=Manage Provider
oscarReport.manageProvider.msgManageProvider=Manage Provider List for
oscarReport.manageProvider.msgTeam=Team
oscarReport.manageProvider.msgProviderName=Provider Name
oscarReport.manageProvider.msgCheck=Check
oscarReport.manageProvider.btnSubmit=Save

oscarReport.oscarReportCatchment.title=Visit Report
oscarReport.oscarReportCatchment.msgDemographic=Demographic
oscarReport.oscarReportCatchment.msgSex=Sex
oscarReport.oscarReportCatchment.msgDOB=DoB
oscarReport.oscarReportCatchment.msgCity=City
oscarReport.oscarReportCatchment.msgProvince=Province
oscarReport.oscarReportCatchment.msgPostal=Postal
oscarReport.oscarReportCatchment.msgStatus=Status
oscarReport.oscarReportCatchment.msgLastPage=Last Page
oscarReport.oscarReportCatchment.msgNextPage=Next Page

oscarReport.oscarReportFluBilling.title=Flu Report
oscarReport.oscarReportFluBilling.msgReport=Report
oscarReport.oscarReportFluBilling.msgFluReport=Flu Report
oscarReport.oscarReportFluBilling.msgAllProviders=ALL Providers
oscarReport.oscarReportFluBilling.btnUpdate=update report
oscarReport.oscarReportFluBilling.msgTitle=Flu Report
oscarReport.oscarReportFluBilling.msgName=Name
oscarReport.oscarReportFluBilling.msgDOB=DOB
oscarReport.oscarReportFluBilling.msgAge=Age
oscarReport.oscarReportFluBilling.msgRoster=Roster Status
oscarReport.oscarReportFluBilling.msgPatientStatus=Patient Status
oscarReport.oscarReportFluBilling.msgPhone=Phone
oscarReport.oscarReportFluBilling.msgBillingDate=Billing Date
oscarReport.oscarReportFluBilling.msgNoMatch=No Match Found

oscar.appt.ApptStatusData.msgTodo=To Do
oscar.appt.ApptStatusData.magDaySheetPrinted=Daysheet Printed
oscar.appt.ApptStatusData.msgHere=Here
oscar.appt.ApptStatusData.msgPicked=Picked
oscar.appt.ApptStatusData.msgNoShow=No Show
oscar.appt.ApptStatusData.msgCanceled=Cancelled
oscar.appt.ApptStatusData.msgBilled=Billed
oscar.appt.ApptStatusData.msgSignedTodo=Todo/Signed
oscar.appt.ApptStatusData.msgSignedDaysheet=Daysheet Printed/Signed
oscar.appt.ApptStatusData.msgSignedHere=Here/Signed
oscar.appt.ApptStatusData.msgSignedPicked=Picked/Signed
oscar.appt.ApptStatusData.msgSignedNoShow=NoShow/Signed
oscar.appt.ApptStatusData.msgSignedCanceled=Cancelled/Signed
oscar.appt.ApptStatusData.msgSignedBilled=Billed/Signed
oscar.appt.ApptStatusData.msgVerifiedTodo=Todo/Verified
oscar.appt.ApptStatusData.msgVerifiedDaySheet=Daysheet Printed/Verified
oscar.appt.ApptStatusData.msgVerifiedHere=Here/Verified
oscar.appt.ApptStatusData.msgVerifiedPicked=Picked/Verified
oscar.appt.ApptStatusData.msgVerifiedNoShow=NoShow/Verified
oscar.appt.ApptStatusData.msgVerifiedCanceled=Cancelled/Verified
oscar.appt.ApptStatusData.msgVerifiedBilled=Billed/Verified
oscar.appt.ApptStatusData.msgEmpty=Empty Room
oscar.appt.ApptStatusData.msgSignedEmpty=EmptyRoom/Signed
oscar.appt.ApptStatusData.msgVerifiedEmpty=EmptyRoom/Verified

provider.providerchangemygroup.title=My Group
provider.providerchangemygroup.msgTitle=CHANGE GROUP NO
provider.providerchangemygroup.msgChangeGroup=Change Your Group NO.
provider.providerchangemygroup.btnChange=Change
provider.providerchangemygroup.btnCancel=Cancel
provider.providerchangemygroup.msgGroup=Group
provider.providerchangemygroup.msgName=Name

recepcionist.recepcionistchangemygroup.title=My Group
recepcionist.recepcionistchangemygroup.msgTitle=CHANGE GROUP NO
recepcionist.recepcionistchangemygroup.formChange=Change Your Group NO.
recepcionist.recepcionistchangemygroup.btnChange=Change
recepcionist.recepcionistchangemygroup.btnCancel=Cancel
recepcionist.recepcionistchangemygroup.msgGroup=Group
recepcionist.recepcionistchangemygroup.msgName=Name

provider.providernewgroup.title=New Group
provider.providernewgroup.msgTitle=NEW GROUP
provider.providernewgroup.msgGroupNo=Group No.
provider.providernewgroup.msgMaxChars=(Max. 10 chars.)
provider.providernewgroup.btnExit= Exit
provider.providernewgroup.btnSave= Save

provider.providerdisplaymygroup.title=My Group
provider.providerdisplaymygroup.msgTitle=MY GROUP
provider.providerdisplaymygroup.msgGroupNo=Group No.
provider.providerdisplaymygroup.msgProvider=Provider's Name
provider.providerdisplaymygroup.btnDelete=Delete
provider.providerdisplaymygroup.btnNew=New Group/Add a Member
provider.providerdisplaymygroup.btnClose= Exit

provider.providersavemygroup.msgTitle=ADD MYGROUP RECORDS
provider.providersavemygroup.msgSuccessful=Successful Addition of a Group Record.
provider.providersavemygroup.msgFailed=Sorry, addition has failed.
provider.providersavemygroup.btnClose=Close this window

provider.providerSignature.title=Provider Signature
provider.providerSignature.msgPrefs=prefs
provider.providerSignature.msgTitle=Provider Signature
provider.providerSignature.msgCurrentSignature=Your current Signature is
provider.providerSignature.btnClickHere=Click here
provider.providerSignature.msgChangeIt=to change it
provider.providerSignature.msgSigNotSet=You do not have a Signature set
provider.providerSignature.msgCreate=to create one

provider.editSignature.title=Provider Signature
provider.editSignature.msgPrefs=prefs
provider.editSignature.msgProviderSignature=Provider Signature
provider.editSignature.msgEdit=Please edit your Signature and press Update
provider.editSignature.msgNew=Please type in your signature and press submit
provider.editSignature.btnUpdate=Update
provider.editSignature.btnSubmit=Submit
provider.editSignature.btnRemove=Remove Signature

provider.editRxPhone.title=Provider Phone Number
provider.editRxPhone.msgProviderPhoneNumber=Provider Phone Number
provider.editRxPhone.msgEdit=Please enter your phone number in the following format ************ and press submit
provider.editRxPhone.msgSuccess=The provider phone number has been updated to

provider.editRxAddress.title=Provider Address
provider.editRxAddress.msgProviderAddress=Provider Address
provider.editRxAddress.msgEdit=Please enter your address and press submit
provider.editRxAddress.msgSuccess=The provider address has been updated to


provider.editRxFax.title=Provider Fax Number
provider.editRxFax.msgPrefs=Preferences
provider.editRxFax.msgProviderFaxNumber=Provider Fax Number
provider.editRxFax.msgEdit=Please enter your fax number in the following format ************ and press submit
provider.editRxFax.msgSuccess=The provider fax number has been updated to
provider.editRxFax.btnSubmit=Submit
provider.editRxFax.msgError=An error occurred while attempting to update database.  Please contact IT Support.
provider.editRxFax.msgPhoneFormat=Format for phone number is ###-###-####

provider.setColour.title=Set Provider Colour
provider.setColour.msgPrefs=Preferences
provider.setColour.msgProviderColour=Provider Colour
provider.setColour.msgEdit=Click on palette and choose your colour.  Then press 'Accept'.
provider.setColour.msgSuccess=Your colour has been set.
provider.setColour.btnSubmit=Accept
provider.setColour.msgError=An error occurred while attempting to update database.  Please contact IT Support.
provider.setColour.msgSatus=Your selected colour is

provider.setNoteStaleDate.title=Set Case Management Note Stale Date
provider.setNoteStaleDate.msgPrefs=Preferences
provider.setNoteStaleDate.msgProviderStaleDate=Case Management Note Stale Date
provider.setNoteStaleDate.msgEdit=Please set how many months in the past before a Case Management Note is fully visible<br>e.g. Set to 6 will display fully all notes within the last 6 months
provider.setNoteStaleDate.btnSubmit=Save
provider.setNoteStaleDate.btnReset=Clear
provider.setNoteStaleDate.msgSuccess=Your stale date has been saved

provider.setmyDrugrefId.title=Set myDrugref ID
provider.setmyDrugrefId.msgPrefs=Preferences
provider.setmyDrugrefId.msgProvider=myDrugref ID
provider.setmyDrugrefId.msgEdit=Enter your desired login for myDrugref
provider.setmyDrugrefId.btnSubmit=Save
provider.setmyDrugrefId.msgSuccess=myDrugref Id saved

provider.setRxPageSize.title=Set Rx Script Page Size
provider.setRxPageSize.msgPrefs=Preferences
provider.setRxPageSize.msgPageSize=Rx Script Page Size
provider.setRxPageSize.msgEdit=Select your desired page size
provider.setRxPageSize.btnSubmit=Save
provider.setRxPageSize.msgSuccess=Rx Script Page Size saved

provider.setRxProfileView.title=Set Rx Profile View
provider.setRxProfileView.msgPrefs=Preferences
provider.setRxProfileView.msgProfileView=Rx Profile View
provider.setRxProfileView.msgEdit=Select your desired display
provider.setRxProfileView.btnSubmit=Save
provider.setRxProfileView.msgSuccess=Rx Profile View saved

provider.setOntarioMD.title=Set OntarioMD.ca username/password
provider.setOntarioMD.msgPrefs=Preferences
provider.setOntarioMD.msgProvider=OntarioMD.ca username/password
provider.setOntarioMD.msgEdit=Enter your username/password for OntarioMD.ca
provider.setOntarioMD.btnSubmit=Save
provider.setOntarioMD.msgSuccess=OntarioMD.ca username/password saved

provider.setConsultationCutOffDate.title=Set Consultation Cutoff Time Warning
provider.setConsultationCutOffDate.msgPrefs=Preferences
provider.setConsultationCutOffDate.msgProvider=Consultation Cutoff Time Warning
provider.setConsultationCutOffDate.msgEdit=Enter your desired Consultation Cutoff Time Warning
provider.setConsultationCutOffDate.btnSubmit=Save
provider.setConsultationCutOffDate.msgSuccess=Consultation Cutoff Time Warning


provider.setConsultationTeamWarning.title=Set Consultation Team Warning
provider.setConsultationTeamWarning.msgPrefs=Preferences
provider.setConsultationTeamWarning.msgProvider=Consultation Team Warning
provider.setConsultationTeamWarning.msgEdit=Enter your desired Consultation Team Warning
provider.setConsultationTeamWarning.btnSubmit=Save
provider.setConsultationTeamWarning.msgSuccess=Consultation Team Warning


provider.setWorkLoadManagement.title=Set WorkLoad Management Type
provider.setWorkLoadManagement.msgPrefs=Preferences
provider.setWorkLoadManagement.msgProvider=WorkLoad Management Type
provider.setWorkLoadManagement.msgEdit=Enter your desired WorkLoad Management Type
provider.setWorkLoadManagement.btnSubmit=Save
provider.setWorkLoadManagement.msgSuccess=WorkLoad Management Type

provider.setConsulReqtPasteFmt.title=Set How CPP Pastes in Consultation Request
provider.setConsulReqtPasteFmt.msgPrefs=Preferences
provider.setConsulReqtPasteFmt.msgProvider=CPP Paste Format
provider.setConsulReqtPasteFmt.msgEdit=Select how CPP pastes in a Consultation Request
provider.setConsulReqtPasteFmt.btnSubmit=Save
provider.setConsulReqtPasteFmt.msgSuccess=CPP paste property has been saved

provider.setFavEfrmGrp.title=Set Favourite eForm Group
provider.setFavEfrmGrp.msgPrefs=Preferences
provider.setFavEfrmGrp.msgProvider=Default eForm Group
provider.setFavEfrmGrp.msgEdit=Select your favourite eForm Group
provider.setFavEfrmGrp.btnSubmit=Save
provider.setFavEfrmGrp.msgSuccess=Favourite Eform Group saved

provider.setHideNoShowsAndCancellations.title=Set Appointments To Hide
provider.setHideNoShowsAndCancellations.msgPrefs=Preferences
provider.setHideNoShowsAndCancellations.msgHideTypes=Appointment Types to Hide 
provider.setHideNoShowsAndCancellations.msgEdit=Select the appointment type you wish to hide on the schedule (No Shows, Cancellations, or Both)
provider.setHideNoShowsAndCancellations.btnSubmit=Save
provider.setHideNoShowsAndCancellations.msgSuccess=Appointment types to hide saved

provider.login.title.confidentiality=Acceptable Use Agreement
provider.login.btn.agree=I agree
provider.login.btn.refuse=Refuse

provider.setPHRLogin.title=Set PHR Login Id
provider.setPHRLogin.msgEdit=Enter your desired login for PHR
provider.setPHRLogin.msgMyOscarId=PHR Login Id
provider.setPHRLogin.btnSubmit=Submit
provider.setPHRLogin.msgSuccess=Your login id for PHR is
provider.setPHRLogin.msgNotUnique=Your requested login id is already being used.  Please choose another.

provider.encounterWindowSize.title=Set Encounter Window Size
provider.encounterWindowSize.msgPrefs=Preferences
provider.encounterWindowSize.msgProvider=Encounter Window Size
provider.encounterWindowSize.msgEdit=Enter your custom values for default encounter window size.
provider.encounterWindowSize.btnSubmit=Save
provider.encounterWindowSize.msgSuccess=Encounter Window Size saved

provider.encounterPrintOptions.title=Set Encounter Print/Fax Options
provider.encounterPrintOptions.msgPrefs=Preferences
provider.encounterPrintOptions.msgProvider=Encounter Print/Fax Options
provider.encounterPrintOptions.msgEdit=Set your default options for printing/faxing encounter notes.
provider.encounterPrintOptions.btnSubmit=Save
provider.encounterPrintOptions.msgSuccess=Encounter Print/Fax Options saved

provider.quickChartSize.title=Set Quick Chart Size
provider.quickChartSize.msgPrefs=Preferences
provider.quickChartSize.msgProvider=Quick Chart Size
provider.quickChartSize.msgEdit=Enter your custom values for quick chart size.
provider.quickChartSize.btnSubmit=Save
provider.quickChartSize.msgSuccess=Quick Chart Size saved

provider.patientNameLength.title=Set Maximum Patient Name Length to display on appointment screen
provider.patientNameLength.msgPrefs=Preferences
provider.patientNameLength.msgProvider=Maximum Patient Name Length
provider.patientNameLength.msgEdit=Enter the maximum patient name length to display
provider.patientNameLength.btnSubmit=Save
provider.patientNameLength.msgSuccess=Maximum Patient Name Length saved

provider.displayDocumentAs.title=Set Display Document as PDF or Image
provider.displayDocumentAs.msgPrefs=Preferences
provider.displayDocumentAs.msgProvider=Display Document As
provider.displayDocumentAs.msgEdit=Display Document As
provider.displayDocumentAs.btnSubmit=Save
provider.displayDocumentAs.msgSuccess=Display document as PDF or Image saved

provider.cobalt.title=Set new OSCAR User Interface (UI)
provider.cobalt.msgPrefs=Preferences
provider.cobalt.msgProvider=
provider.cobalt.msgEdit=
provider.cobalt.btnSubmit=Save
provider.cobalt.msgSuccess_selected=new OSCAR UI setting enabled
provider.cobalt.msgSuccess_unselected=new OSCAR UI setting disabled
provider.btnSetCobalt=Enable new OSCAR User Interface (UI)
provider.cobalt.msgProfileView=Preferences
provider.appointmentCardPrefs.title=Set Appointment Card Preferences
provider.appointmentCardPrefs.msgPrefs=Preferences
provider.appointmentCardPrefs.msgProvider=Appointment Card Preferences
provider.appointmentCardPrefs.msgEdit=Enter your custom values for appointment card preferences.
provider.appointmentCardPrefs.btnSubmit=Save
provider.appointmentCardPrefs.msgSuccess=Appointment card preferences saved


provider.clinicalConnectPrefs.title=Set myDrugref ID
provider.clinicalConnectPrefs.msgPrefs=ClinicalConnect Preferences
provider.clinicalConnectPrefs.btnSubmit=Save
provider.clinicalConnectPrefs.btnCancel=Cancel
provider.clinicalConnectPrefs.btnClose=Close
provider.clinicalConnectPrefs.msgSuccess=ClinicalConnect preferences saved

receptionist.receptionistdisplaymygroup.title=My Group
receptionist.receptionistdisplaymygroup.msgTitle=MY GROUP
receptionist.receptionistdisplaymygroup.msgGroupNo=Group No.
receptionist.receptionistdisplaymygroup.msgProviderName=Provider's Name
receptionist.receptionistdisplaymygroup.msgOrder=Order
receptionist.receptionistdisplaymygroup.btnUpdate=Update
receptionist.receptionistdisplaymygroup.btnDelete=Delete
receptionist.receptionistdisplaymygroup.btnNew=New Group/Add a Member
receptionist.receptionistdisplaymygroup.btnExit= Exit

receptionist.receptionistnewgroup.title=New Group
receptionist.receptionistnewgroup.msgAlert=No Group No.!
receptionist.receptionistnewgroup.msgTitle=NEW GROUP
receptionist.receptionistnewgroup.msgGroupNo=Group No.
receptionist.receptionistnewgroup.msgMaxChar=(Max. 10 chars.)
receptionist.receptionistnewgroup.btnExit= Exit
receptionist.receptionistnewgroup.btnSave= Save

provider.setDefaultPrinter.title=Set Provider Default Printer
provider.setDefaultPrinter.appointmentReceipt=Appointment Receipt
provider.setDefaultPrinter.PDFEnvelope=PDF Envelope
provider.setDefaultPrinter.PDFLabel=PDF Label
provider.setDefaultPrinter.PDFAddressLabel=PDF Address Label
provider.setDefaultPrinter.PDFChartLabel=PDF Chart Label
provider.setDefaultPrinter.ClientLabLabel=Client Lab Label
provider.setDefaultPrinter.silentPrint=Silent Print
provider.setDefaultPrinter.setDefaultPrinterFor=Set default printer for
provider.setDefaultPrinter.msgPrefs=Preferences
provider.setDefaultPrinter.msgdefaulPrinter=default Printer
provider.setDefaultPrinter.btnSave=Save
provider.setDefaultPrinter.msgSuccess=Default printer saved.
provider.setDefaultPrinter.requirement=Requires a PDF Reader with javascript support such as Acrobat Reader
provider.setDefaultPrinter.requirementSilentPrint= For silent print, you will need to add the Oscar Server hostname or IP  as a Privileged Location in Adobe Reader.  Preferences --> Security (Enhanced) --> add Host

report.appointmentReceipt.Name=Name
report.appointmentReceipt.Date=Date
report.appointmentReceipt.DOB= DOB
report.appointmentReceipt.Time=Time
report.appointmentReceipt.With=With
report.appointmentReceipt.Printed=Printed
report.appointmentReceipt.DefaultPrinter=Default printer
report.appointmentReceipt.SilentlyPrintToDefaultPrinter=Silently print to default printer
report.appointmentReceipt.title=Print Appointment Receipt

report.printLabel.DefaultPrinter=Default printer
report.printLabel.SilentlyPrintToDefaultPrinter=Silently print to default printer
report.printLabel.title=Print Label

report.reportindex.btnOSISReport = OSIS Report
report.reportindex.btnCDSOneTimeConsultReport = One Time Consult CDS Report
report.reportindex.btnInjectionReport=Injection Report
report.reportindex.btnClinicalReport=Clinical Report
report.reportindex.btnSCBPDemoRept = SCBP Demographic Report
report.reportindex.btnFormReport=Form Report
report.reportindex.btnWaiting=Waiting List
report.reportindex.btnReport18n=Ontario Prevention Report
report.reportindex.btnDemoSetEdit=Demographic Set Edit
report.reportindex.msgStart = Start Date
report.reportindex.title=REPORT SETTING
report.reportindex.msgGoConfirm=Are you sure you want to see only new appts? (The new appts status would be changed to 'old'.)
report.reportindex.msgTitle=REPORT LIST
report.reportindex.btnEDBList=EDB List
report.reportindex.btnEDDList=EDD List
report.reportindex.btnCreateReport=CREATE REPORT
report.reportindex.formFrom=From
report.reportindex.formTo=To
report.reportindex.btnActivePList=Active Patient List
report.reportindex.formDaySheet=Day Sheet
report.reportindex.formAllProviders=All Providers
report.reportindex.btnAllAppt=All appointments
report.reportindex.btnDaySheetTable = Tabular-style Daysheet
report.reportindex.chkRostered = Non Rostered Only
report.reportindex.msgNewApptsOld=New appts will be old after this view, !
report.reportindex.btnPrintDaySheet=Print Day Sheet for only new appointments
report.reportindex.formNoShow=No Show List & Letters
report.reportindex.formBadAppt=Bad Appt Sheet
report.reportindex.btnPatientChartList=Patient Chart List
report.reportindex.btnOldPatient=Old Patient List
report.reportindex.btnOldPatientAge=age >
report.reportindex.btnNoShowAppointmentList=No Show Appointment List
report.reportindex.btnConsultationReport=Consultation Report
report.reportindex.btnLaboratoryRequisition=Laboratory Requisition Report
report.reportindex.btnDemographicReportTool=Demographic Report Tool
report.reportindex.btnDemographicStudyList=Demographic Study List
report.reportindex.btnCancel=Cancel

report.reportnewdblist.title=REPORT EDB
report.reportnewdblist.msgEDBList=EDB LIST
report.reportnewdblist.msgEDDList=EDD LIST
report.reportnewdblist.msgEDB=EDB
report.reportnewdblist.msgEDD=EDD
report.reportnewdblist.msgName=Patient's Name
report.reportnewdblist.msgAge=Age
report.reportnewdblist.msgDOB=DOB
report.reportnewdblist.msgGravida=Grav
report.reportnewdblist.msgTerm=Term
report.reportnewdblist.msgPhone=Phone
report.reportnewdblist.msProvider=Provider
report.reportnewdblist.msGP=GP
report.reportnewdblist.msLanguage=Language
report.reportnewdblist.msPHN=PHN
report.reportnewdblist.msgLastPage=Last Page
report.reportnewdblist.msgNextPage=Next Page

report.reportactivepatientlist.title=REPORT ACTIVE PATIENT
report.reportactivepatientlist.msgTitle=ACTIVE PATIENT LIST
report.reportactivepatientlist.msgLastName=Last Name
report.reportactivepatientlist.msgFirstName=First Name
report.reportactivepatientlist.msgChart=Chart No
report.reportactivepatientlist.msgAge=Age
report.reportactivepatientlist.msgSex=Sex
report.reportactivepatientlist.msgHIN=HIN
report.reportactivepatientlist.msgVer=Ver
report.reportactivepatientlist.msgMCDoc=MC Doc
report.reportactivepatientlist.msgDateJoined=Date Joined
report.reportactivepatientlist.msgPhone=Phone
report.reportactivepatientlist.msgLastPage=Last Page
report.reportactivepatientlist.msgNextPage=Next Page

report.reportapptsheet.title=BAD APPT SHEET
report.reportapptsheet.msgApptDate=Appt Date
report.reportapptsheet.msgStartTime=Start Time
report.reportapptsheet.msgEndTime=End Time
report.reportapptsheet.msgName=Patient's Name
report.reportapptsheet.msgComments=Comments

report.reportpatientchartlist.title=PATIENT CHART LIST
report.reportpatientchartlist.msgTitle=PATIENT CHART LIST
report.reportpatientchartlist.msgLastName=Last Name
report.reportpatientchartlist.msgFirstName=First Name
report.reportpatientchartlist.msgChart=Chart No

report.reportpatientchartlistspecial.title=PATIENT CHART LIST
report.reportpatientchartlistspecial.msgTitle=PATIENT CHART LIST
report.reportpatientchartlistspecial.btnLastName=Last Name
report.reportpatientchartlistspecial.btnFisrtName=First Name
report.reportpatientchartlistspecial.btnSex=Sex
report.reportpatientchartlistspecial.btnChart=Chart No
report.reportpatientchartlistspecial.btnApptDate=Appt. Date
report.reportpatientchartlistspecial.btnAddress=Address
report.reportpatientchartlistspecial.btnCity=City,Province
report.reportpatientchartlistspecial.btnPostal=Postal
report.reportpatientchartlistspecial.btnDOB=DOB
report.reportpatientchartlistspecial.btnStatus=Status

report.reportnoshowapptlist.title=PATIENT NO SHOW LIST
report.reportnoshowapptlist.msgTitle=PATIENT NO SHOW LIST
report.reportnoshowapptlist.msgNoShow=No Show

report.tabulardaysheetreport.title=DaySheet
report.tabulardaysheetreport.msgTitle=Day Sheet

report.tabulardaysheetreport.msgTime=Time
report.tabulardaysheetreport.msgChart=Chart #
report.tabulardaysheetreport.msgName=Name
report.tabulardaysheetreport.msgDoB=DoB
report.tabulardaysheetreport.msgPHN=PHN
report.tabulardaysheetreport.msgFee1=Fee1
report.tabulardaysheetreport.msgDiag1=Diag1
report.tabulardaysheetreport.msgDiag2=Diag2
report.tabulardaysheetreport.msgDiag3=Diag3
report.tabulardaysheetreport.msgDescription=Description

report.tabulardaysheetreport.btnExit=Exit
report.tabulardaysheetreport.btnPrint=Print


report.ClinicalReports.title=Clinical Reports
report.ClinicalReports.msgClear=Clear
report.ClinicalReports.msgCSV=CSV
report.ClinicalReports.msgDel=Del
report.ClinicalReports.msgNumerator=Numerator
report.ClinicalReports.msgValue=Value
report.ClinicalReports.msgDenominator=Denominator
report.ClinicalReports.Fieldstoinclude=Fields To Include
report.ClinicalReports.msgLastName=Last Name
report.ClinicalReports.msgFirstName=First Name
report.ClinicalReports.msgSex=Sex
report.ClinicalReports.msgPhone=Phone
report.ClinicalReports.msgAddress=Address
report.ClinicalReports.msgNoMeasurements=No Measurements
report.ClinicalReports.msgResults=Results
report.ClinicalReports.msgPercentage=Percentage
report.ClinicalReports.msgExporttoCSV=Export to CSV
report.ClinicalReports.msgExporttoXLS=Export to XLS
report.ClinicalReports.btnEvaluate=Evaluate



oscarReport.ConsultationReport.title=Consultation Report
oscarReport.ConsultationReport.msgReport=Report
oscarReport.ConsultationReport.msgTitle=Consultation Report
oscarReport.ConsultationReport.formAllProviders=ALL Providers
oscarReport.ConsultationReport.btnUpdateReport=update report
oscarReport.ConsultationReport.msgConsDoc=Consultation Documents
oscarReport.ConsultationReport.msgDocDesc=Document Description
oscarReport.ConsultationReport.msgDate=Date
oscarReport.ConsultationReport.formMonth=Month
oscarReport.ConsultationReport.formMonths=Months

report.demographicstudyreport.title=REPORT STUDY
report.demographicstudyreport.msgTitle=Demographic Study List
report.demographicstudyreport.msgStudy=Study
report.demographicstudyreport.msgProvider=Provider

demographic.demographiceditdemographic.btnSearch = Search
demographic.demographiceditdemographic.msgDateOfReq = Date of request
demographic.demographiceditdemographic.msgWaitListNote = Waiting List Note
demographic.demographiceditdemographic.optCreateWaitList = --Please Create Waiting List Name first--
demographic.demographiceditdemographic.optSelectWaitList = --Select Waiting List--
demographic.demographiceditdemographic.optFired = FI - Fired
demographic.demographiceditdemographic.optMoved = MO - Moved
demographic.demographiceditdemographic.optDeceased = DE - Deceased
demographic.demographiceditdemographic.optInActive = IN - Inactive
demographic.demographiceditdemographic.optActive = AC - Active
demographic.demographiceditdemographic.btnAddNew = Add New
demographic.demographiceditdemographic.btnDelete = Delete
demographic.demographiceditdemographic.btnAddCustom = Add Custom
demographic.demographiceditdemographic.btnEdit = Edit
demographic.demographiceditdemographic.optFeeService = FS - fee for service
demographic.demographiceditdemographic.optUhip = UHIP
demographic.demographiceditdemographic.optBillInsurance = BI - Bill Insurance
demographic.demographiceditdemographic.optTerminated = TE - terminated
demographic.demographiceditdemographic.optNotEnrolled = NE - Not Enrolled
demographic.demographiceditdemographic.optEnrolled = EN - Enrolled
demographic.demographiceditdemographic.optOther = Other
demographic.demographiceditdemographic.msgRegisterPHR = Register for PHR
demographic.demographiceditdemographic.msgExt = Ext
demographic.demographiceditdemographic.msgSpoken = Spoken
demographic.demographiceditdemographic.msgFrench = French
demographic.demographiceditdemographic.msgEnglish = English
demographic.demographiceditdemographic.msgSr = SR
demographic.demographiceditdemographic.msgDr = DR
demographic.demographiceditdemographic.msgSgt = SGT
demographic.demographiceditdemographic.msgSen = SEN
demographic.demographiceditdemographic.msgRtHon = RT_HON
demographic.demographiceditdemographic.msgRev = REV
demographic.demographiceditdemographic.msgReeve = REEVE
demographic.demographiceditdemographic.msgProf = PROF
demographic.demographiceditdemographic.msgMssr = MSSR
demographic.demographiceditdemographic.msgMr = MR
demographic.demographiceditdemographic.msgMrs = MRS
demographic.demographiceditdemographic.msgMiss = MISS
demographic.demographiceditdemographic.msgMs = MS
demographic.demographiceditdemographic.msgDr = DR
demographic.demographiceditdemographic.msgNotSet = -Not Set-
demographic.demographiceditdemographic.msgPatientClinicStatus = Patient Clinic Status
demographic.demographiceditdemographic.msgInternalProviders = Internal Providers
demographic.demographiceditdemographic.msgExternalProviders = External Providers
demographic.demographiceditdemographic.msgHealthIns = Health Insurance
demographic.demographiceditdemographic.msgContactInfo = Contact Information
demographic.demographiceditdemographic.msgClinicStatus = Clinic Status
demographic.demographiceditdemographic.msgAddRelation = Add Relation
demographic.demographiceditdemographic.msgOtherContacts = Other Contacts
demographic.demographiceditdemographic.msgManageContacts = Manage Contacts  
demographic.demographiceditdemographic.msgSpokenLang = Spoken Language
demographic.demographiceditdemographic.msgCountryOfOrigin = Country Of Origin
demographic.demographiceditdemographic.msgDemoLanguage = Language
demographic.demographiceditdemographic.msgDemoAge = Age
demographic.demographiceditdemographic.msgDemoTitle = Title
demographic.demographiceditdemographic.msgDemographic = Demographic
demographic.demographiceditdemographic.msgRecord = Record
demographic.demographiceditdemographic.msgSendMsgPHR = Send Message to PHR
demographic.demographiceditdemographic.MyOscarDataSync = Automated Data Synchronisation
demographic.demographiceditdemographic.msgAddPatientSet = Add Patient Set
demographic.demographiceditdemographic.msgSendMsg = Send a Message
demographic.demographiceditdemographic.msgCustom = Custom
demographic.demographiceditdemographic.msgEncType = Encounter Type
demographic.demographiceditdemographic.msgINRBill = Bill INR
demographic.demographiceditdemographic.msgINRBilling = INR Billing
demographic.demographiceditdemographic.msgAddINR = Add INR
demographic.demographiceditdemographic.msgAddINRBilling = Add INR Billing
demographic.demographiceditdemographic.msgAddBatchBilling = Add Batch Billing
demographic.demographiceditdemographic.msgHospitalBilling = Hospital Billing
demographic.demographiceditdemographic.msgFluBilling = Flu Billing
demographic.demographiceditdemographic.msgAddFluBill = Add Flu Billing
demographic.demographiceditdemographic.msgBillPatient = bill a patient
demographic.demographiceditdemographic.msgCreateInvoice = Create Invoice
demographic.demographiceditdemographic.msgInvoiceList = Invoice List
demographic.demographiceditdemographic.msgBillHistory = Billing History
demographic.demographiceditdemographic.msgWaitList = Waiting List
demographic.demographiceditdemographic.msgAppt = Appointment
demographic.demographiceditdemographic.msgNextAppt = Next Appointment
demographic.demographiceditdemographic.msgInvalidEntry = Invalid Entry
demographic.demographiceditdemographic.msgPromptStatus = Please enter the new status
demographic.demographiceditdemographic.msgPromptPronoun = Please enter the new pronoun
demographic.demographiceditdemographic.msgPromptGender = Please enter the new gender
demographic.demographiceditdemographic.msgPromptFirstNationCom = Please enter the new Band Name
demographic.demographiceditdemographic.msgWrongReferral = The referral doctor's no. is wrong. Please correct it!
demographic.demographiceditdemographic.msgWrongFamily = The family doctor's no. is wrong. Please correct it!
demographic.demographiceditdemographic.msgWrongHIN = You must type in the right HIN.
demographic.demographiceditdemographic.msgWrongDate = Date entered should be correct and in the past
demographic.demographiceditdemographic.registerPatientPortalAccount = Register Patient Portal Account
demographic.demographiceditdemographic.unRegisterPatientPortalAccount = Un-register Patient Portal Account
demographic.demographiceditdemographic.msgNameRequired = You must type in the following fields: Last Name, First Name.
demographic.demographiceditdemographic.msgEdit = Edit
demographic.demographiceditdemographic.msgExport = Export this Demographic
demographic.demographiceditdemographic.registerPatientPortalAccount = Register Patient Portal Account
demographic.demographiceditdemographic.unRegisterPatientPortalAccount = Un-register Patient Portal Account
demographic.demographiceditdemographic.accessDenied = You have no rights to access the data!
demographic.demographiceditdemographic.title=PATIENT DETAIL INFO
demographic.demographiceditdemographic.alias=Alias
demographic.demographiceditdemographic.msgWrongDOB=You have a wrong DOB format input!!!
demographic.demographiceditdemographic.msgPatientDetailRecord=Master Record
demographic.demographiceditdemographic.msgDocuments=Documents
demographic.demographiceditdemographic.msgDocumentBrowser=Document Browser
demographic.demographiceditdemographic.btnEForm=Current eForms
demographic.demographiceditdemographic.btnApptHist=Appointment History
demographic.demographiceditdemographic.btnBillingHist=Billing History
demographic.demographiceditdemographic.btnEmailInvite=Email Invite
demographic.demographiceditdemographic.btnEmailInviteSent=Invite Sent
demographic.demographiceditdemographic.btnEmailInviteError=Error
demographic.demographiceditdemographic.formLastName=Last Name
demographic.demographiceditdemographic.formFirstName=First Name
demographic.demographiceditdemographic.formPrefName=Preferred Name
demographic.demographiceditdemographic.formFormerName=Former Name
demographic.demographiceditdemographic.formAddr=Address
demographic.demographiceditdemographic.formCity=City
demographic.demographiceditdemographic.formProcvince=Province
demographic.demographiceditdemographic.formPostal=Postal
demographic.demographiceditdemographic.formPhoneH=Phone(H)
demographic.demographiceditdemographic.formPhoneW=Phone(W)
demographic.demographiceditdemographic.formPhoneC=Cell Phone
demographic.demographiceditdemographic.formEmail=Email
demographic.demographiceditdemographic.formGender = Gender Identity
demographic.demographiceditdemographic.formPronoun = Pronoun
demographic.demographiceditdemographic.emailOnConsult=Show email on Consults
demographic.demographiceditdemographic.formNewsLetter=Newsletter
demographic.demographiceditdemographic.formPHRUserName=PHR UserName
demographic.demographiceditdemographic.formDOB=DOB
demographic.demographiceditdemographic.formDOBDetais=<font size="-2">(yyyymmdd)</font>
demographic.demographiceditdemographic.formSex=Sex
demographic.demographiceditdemographic.formHin=Health Ins. #
demographic.demographiceditdemographic.formVer=Ver.
demographic.demographiceditdemographic.formEFFDate=Effective Date
demographic.demographiceditdemographic.formHCRenewDate=Renew Date
demographic.demographiceditdemographic.formHCType=Health Card Type
demographic.demographiceditdemographic.formNurse=Nurse
demographic.demographiceditdemographic.formDoctor=Physician/MRP
demographic.demographiceditdemographic.formResident=Resident
demographic.demographiceditdemographic.formAltProvider1=Alt. Provider 1
demographic.demographiceditdemographic.formAltProvider2=Alt. Provider 2
demographic.demographiceditdemographic.formAltProvider3=Alt. Provider 3
demographic.demographiceditdemographic.formRefDoc=Referral Doctor
demographic.demographiceditdemographic.formRefDocNo=Referral Doctor #
demographic.demographiceditdemographic.formFamDoc=Family Doctor
demographic.demographiceditdemographic.formFamDocNo=Family Doctor #
demographic.demographiceditdemographic.formPCN=PCN
demographic.demographiceditdemographic.formRosterStatus=Enrollment Status
demographic.demographiceditdemographic.formEnrollmentDoctor=Enrollment Physician
demographic.demographiceditdemographic.DateJoined=Date Enrolled
demographic.demographiceditdemographic.InsuranceNumber=Insurance No
demographic.demographiceditdemographic.RosterTerminationDate=Termination Date
demographic.demographiceditdemographic.RosterTerminationReason=Termination Reason
demographic.demographiceditdemographic.BillInsurance=Insurance Company
demographic.demographiceditdemographic.AllowAppointmentReminders=Allow Appointment Reminders
demographic.demographiceditdemographic.PhonePreference=Preferred Phone Number
demographic.demographiceditdemographic.PatientStatusDate=Patient Status Date
demographic.demographiceditdemographic.formChartNo=Chart Number
demographic.demographiceditdemographic.formDateJoined1=Date Joined
demographic.demographiceditdemographic.formEndDate=End Date
demographic.demographiceditdemographic.formAlert=Alert
demographic.demographiceditdemographic.formNotes=Notes
demographic.demographiceditdemographic.allergyAlert=Allergy Alert
demographic.demographiceditdemographic.btnUpdate=Update Record
demographic.demographiceditdemographic.updateCBIReminder=Whenever any CBI data changes it is uploaded within 24 hours. Signing the CBI form sends it to the pending upload list.
demographic.demographiceditdemographic.btnSwipeCard=Swipe Card
demographic.demographiceditdemographic.btnPrintLabel=Print Label
demographic.demographiceditdemographic.btnAddEForm=Add eForm
demographic.demographiceditdemographic.btnAddDocument=Add Document
demographic.demographiceditdemographic.btnEChart=E-Chart
demographic.demographiceditdemographic.btnMasterFile=Master Record
demographic.demographiceditdemographic.btnConsultation=Consultations
demographic.demographiceditdemographic.cytolNum=Cytology #
demographic.demographiceditdemographic.confirmAccount=Confirm account ownership to allow data sharing

demographic.onAccount.onAccountBilling=On Account Billing
demographic.onAccount.onAccountBilling.balance=Deposit Balance
demographic.onAccount.onAccountBilling.paymentowing=Deposit Payments Owing
demographic.onAccount.onAccountBilling.refund=Pending Refunds
demographic.onAccount.onAccountBilling.balancebills=Bill Amounts Owing
demographic.onAccount.onAccountBilling.addDeposit=Add Deposit
demographic.onAccount.onAccountBilling.addCharge=Add Charge
demographic.onAccount.onAccountBilling.createBill=Create Bill
demographic.onAccount.onAccountBilling.createRefund=Create Refund
demographic.onAccount.onAccountBilling.printHistory=Print History
demographic.onAccount.onAccountBilling.filterCriteria=Filter Criteria
demographic.onAccount.onAccountBilling.applyFilter=Apply Filter
demographic.onAccount.onAccountBilling.resetFilter=Reset Filter
demographic.onAccount.onAccountBilling.type=Type
demographic.onAccount.onAccountBilling.type.all=All
demographic.onAccount.onAccountBilling.type.deposit=Deposit
demographic.onAccount.onAccountBilling.type.bill=Bill
demographic.onAccount.onAccountBilling.type.refund=Refund
demographic.onAccount.onAccountBilling.status=Status
demographic.onAccount.onAccountBilling.status.all=All
demographic.onAccount.onAccountBilling.status.refundPending=Refund Pending
demographic.onAccount.onAccountBilling.status.balanceOwing=Balance Owing
demographic.onAccount.onAccountBilling.status.void=Void
demographic.onAccount.onAccountBilling.date=Date
demographic.onAccount.onAccountBilling.date.all=All
demographic.onAccount.onAccountBilling.date.from=From
demographic.onAccount.onAccountBilling.date.to=To
demographic.onAccount.onAccountBilling.date.or=OR
demographic.onAccount.onAccountBilling.date.days.30=30
demographic.onAccount.onAccountBilling.date.days.60=60
demographic.onAccount.onAccountBilling.date.days.90=90
demographic.onAccount.onAccountBilling.open=Open

demographic.onAccount.bill.save=Save
demographic.onAccount.bill.saveClose=Save and Close
demographic.onAccount.bill.savePrintPreview=Save and Print Preview
demographic.onAccount.bill.cancel=Cancel
demographic.onAccount.bill.void=Void
demographic.onAccount.bill.date=Date
demographic.onAccount.bill.tel=Tel
demographic.onAccount.bill.fax=Fax
demographic.onAccount.bill.patientInfo=Patient Information
demographic.onAccount.bill.remitTo=Remit To
demographic.onAccount.bill.dx=Dx
demographic.onAccount.bill.primaryPhysician=Primary Physician
demographic.onAccount.bill.billingPhysician=Billing Physician
demographic.onAccount.bill.referringPhysician=Referring Physician
demographic.onAccount.bill.serviceCode=Service Code
demographic.onAccount.bill.amount=Amount
demographic.onAccount.bill.delete=Delete
demographic.onAccount.bill.loadServices=Load

demographic.onAccount.refund.addRefund=Add
demographic.onAccount.refund.refundDate=Date
demographic.onAccount.refund.refundDetails=Details
demographic.onAccount.refund.refundAmount=Amount

demographic.onAccount.deposit.addCashPayment=Add Cash Payment
demographic.onAccount.deposit.addCreditCardPayment=Add Credit Card Payment
demographic.onAccount.deposit.addChequePayment=Add Cheque Payment
demographic.onAccount.deposit.paymentDate = Date
demographic.onAccount.deposit.paymentAmount = Amount
demographic.onAccount.deposit.addPayment = Add
demographic.onAccount.deposit.chequeAmount = Amount
demographic.onAccount.deposit.creditCardType = Credit Card Type
demographic.onAccount.deposit.creditCardDigits = Last Four Digits
demographic.onAccount.deposit.addPaymentDetails = Add Payment Details
demographic.onAccount.deposit.deleteService = Delete Service
demographic.onAccount.deposit.deletePayment = Delete Payment

eform.showmyform.msgManageEFrm = Manage eForms
eform.showmyform.msgEditGroups = Edit Groups
eform.showmyform.msgShowAll = Show All
eform.showmyform.msgViewGroup = View Group
eform.showmyform.msgViewFrm = View eForm
eform.showmyform.msgManageEForms = Manage eForms
eform.showmyform.title=ShowMyForm
eform.showmyform.msgMyForm=My eForm
eform.showmyform.btnAddEForm=Add eForm
eform.showmyform.msgFormLybrary=eForm Library
eform.showmyform.btnDeleted=Deleted eForms
eform.showmyform.btnFormName=eForm Name
eform.showmyform.btnSubject=Additional Information
eform.showmyform.btnFormProvider=Submit By
eform.showmyform.formDate=Modified Date
eform.showmyform.formTime=Modified Time
eform.showmyform.msgAction=Action
eform.showmyform.msgNoData=No data!

eform.myform.title=eForm Library
eform.myform.msgChooseFile=Please choose a file first, then click Upload
eform.myform.msgEForm=eForm
eform.myform.msgFormLib=eForm Library
eform.myform.btnFile=File

eform.calldeletedformdata.backToCurrent=Back to current eForms
eform.calldeletedformdata.title=Deleted eForms List
eform.calldeletedformdata.confirmRestore=Are you sure you want to restore this eForm?
eform.calldeletedformdata.msgMyform=My eForm
eform.calldeletedformdata.btnGoToForm=Current eForms
eform.calldeletedformdata.btnRestore=Restore
eform.calldeletedform.title=Deleted eForm
eform.calldeletedform.msgtitle=Deleted eForms

eform.uploadServlet.errIO=Error getting output stream.
eform.uploadServlet.errDesc=Error description
eform.uploadServlet.errBig=Sorry, file is too large to upload.
eform.uploadServlet.msgOk=File upload successfully.<br> Please wait for 1 seconds .
eform.uploadServlet.errdoPost=Error in doPost
eform.uploadServlet.errUnexpec=An unexpected error has occurred.

demographic.demographicappthistory.title=APPOINTMENT HISTORY
demographic.demographicappthistory.msgTitle=APPOINTMENT HISTORY
demographic.demographicappthistory.msgHistory=History
demographic.demographicappthistory.msgResults=Results for Demographic
demographic.demographicappthistory.msgApptDate=APPT DATE
demographic.demographicappthistory.msgFrom=FROM
demographic.demographicappthistory.msgReason=REASON
demographic.demographicappthistory.msgProvider=PROVIDER
demographic.demographicappthistory.msgComments=COMMENTS
demographic.demographicappthistory.msgNotes=NOTES
demographic.demographicappthistory.btnLastPage=Last Page
demographic.demographicappthistory.btnPrevPage=Prev Page
demographic.demographicappthistory.btnNextPage=Next Page
demographic.demographicappthistory.msgShowDeleted=Show Deleted Appointments

### Paginacao
list.page.length=5
pager.header.title=Resultado:
pager.max.page.index=10
pager.next.desc=Pr\u00f3ximo
pager.prev.desc=Anterior

oscarEncounter.calculators.GeneralCalculators.title=General Conversions
oscarEncounter.calculators.GeneralCalculators.msgCalculators=calculators
oscarEncounter.calculators.GeneralCalculators.msgTitle=General Conversions
oscarEncounter.calculators.GeneralCalculators.msgDistance=Distance
oscarEncounter.calculators.GeneralCalculators.msgWeight=Weight
oscarEncounter.calculators.GeneralCalculators.msgVolume=Volume
oscarEncounter.calculators.GeneralCalculators.msgTemperatures=Temperatures
oscarEncounter.calculators.GeneralCalculators.msgDistanceConversion=Distance Unit Conversion Calculator
oscarEncounter.calculators.GeneralCalculators.msgMeters=Meters
oscarEncounter.calculators.GeneralCalculators.msgInches=Inches
oscarEncounter.calculators.GeneralCalculators.msgFeet=Feet
oscarEncounter.calculators.GeneralCalculators.msgYards=Yards
oscarEncounter.calculators.GeneralCalculators.msgMiles=Miles
oscarEncounter.calculators.GeneralCalculators.msgNauticalMiles=Nautical<BR> Miles
oscarEncounter.calculators.GeneralCalculators.msgWightConversion=Weight Unit Conversion Calculator
oscarEncounter.calculators.GeneralCalculators.msgKilograms=Kilograms
oscarEncounter.calculators.GeneralCalculators.msgOunces=Ounces
oscarEncounter.calculators.GeneralCalculators.msgPounds=Pounds
oscarEncounter.calculators.GeneralCalculators.msgTroyPounds=Troy<BR> Pounds
oscarEncounter.calculators.GeneralCalculators.msgStones=Stones
oscarEncounter.calculators.GeneralCalculators.msgLongTons=Long<BR> Tons
oscarEncounter.calculators.GeneralCalculators.msgShortTons=Short<BR> Tons
oscarEncounter.calculators.GeneralCalculators.msgVolumeConversion=Volume Unit Conversion Calculator
oscarEncounter.calculators.GeneralCalculators.msgLitres=Litres
oscarEncounter.calculators.GeneralCalculators.msgFluid=Fluid<BR> Ounces
oscarEncounter.calculators.GeneralCalculators.msgQuarts=Quarts
oscarEncounter.calculators.GeneralCalculators.msgGallons=Gallons
oscarEncounter.calculators.GeneralCalculators.msgImperialGallons=Imperial<BR> Gallons
oscarEncounter.calculators.GeneralCalculators.msgTemperaturesConversion=Temperatures
oscarEncounter.calculators.GeneralCalculators.msgInstructions1=1. Click mouse in a box.
oscarEncounter.calculators.GeneralCalculators.msgInstructions2=2. Type in temperature.
oscarEncounter.calculators.GeneralCalculators.msgInstructions3=3. Click on the other box to calculate.
oscarEncounter.calculators.GeneralCalculators.msgFahrenheit=Fahrenheit...
oscarEncounter.calculators.GeneralCalculators.msgCelsius=Celsius........
oscarEncounter.calculators.GeneralCalculators.btnCalibrate=Calibrate
oscarEncounter.calculators.GeneralCalculators.btnCalculate=Calculate

oscarEncounter.calculators.SimpleCalculator.module=calculators
oscarEncounter.calculators.SimpleCalculator.title=Simple Calculator
oscarEncounter.calculators.SimpleCalculator.msgTitle=Simple Calculator
oscarEncounter.calculators.SimpleCalculator.MsgUsage=Use * to multiply

oscarEncounter.calculators.OsteoporoticFracture.title=Average 10-Year probability (%) of an osteoporotic fracture
oscarEncounter.calculators.OsteoporoticFracture.msgAverage=Average 10-Year Probability of
oscarEncounter.calculators.OsteoporoticFracture.msgOsteoporoticFracture=Osteoporotic fracture
oscarEncounter.calculators.OsteoporoticFracture.msg10YearProb=10 Year Probability
oscarEncounter.calculators.OsteoporoticFracture.msgOverall=Overall average Probability
oscarEncounter.calculators.OsteoporoticFracture.msgCalculators=calculators
oscarEncounter.calculators.OsteoporoticFracture.msgFemale=Female
oscarEncounter.calculators.OsteoporoticFracture.msgMale=Male
oscarEncounter.calculators.OsteoporoticFracture.msgAge=Age
oscarEncounter.calculators.OsteoporoticFracture.msgTScore=T-Score
oscarEncounter.calculators.OsteoporoticFracture.btnCalculate=Calculate
oscarEncounter.calculators.OsteoporoticFracture.btnTotalVal=totalVal
oscarEncounter.calculators.OsteoporoticFracture.msgCompare=Compare To Average 10 Year Risk
oscarEncounter.calculators.OsteoporoticFracture.msgProbability=Probability %
oscarEncounter.calculators.OsteoporoticFracture.msgAvg=Avg

oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.title=Coronary Artery Disease Risk Prediction
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgCoronaryRiskPrediction=Coronary Artery Disease Risk Prediction
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgTotalPointCount=Total Point Count
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msg5YearProbability=5 Year Probability
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msg10YearProbability=10 Year Probability
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgAvg10YearProbability=Average 10 Year Probability
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgFemale=Female
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgMale=Male
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgAge=Age
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgHDLCholesterol=HDL-Cholesterol
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgTotalCholesterol=Total-Cholesterol
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgSystolic=Systolic BP
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgSmoker=Smoker
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgDiabetic=Diabetic
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgECG-LVH=ECG-LVH
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgCompare10Year=Compare To Average 10 Year Risk
oscarEncounter.calculators.CoronaryArteryDiseaseRiskPrediction.msgProbability=Probability %

oscarEncounter.formlist.title=Form List
oscarEncounter.formlist.msgFormList=FORM HISTORY
oscarEncounter.formlist.formName=Form Name
oscarEncounter.formlist.formCreated=Created Time
oscarEncounter.formlist.formEditedTime=Edited Time
oscarEncounter.formlist.formActivion=Action
oscarEncounter.formlist.formLastpage=Last Page
oscarEncounter.formlist.formNextPage=Next Page
oscarEncounter.formlist.myFormTitle=My Form
oscarEncounter.formlist.deleteForm=Delete
oscarEncounter.formlist.restoreForm=Restore
oscarEncounter.formlist.btnGoToForm=Current Form
oscarEncounter.formlist.btnDeleted=Deleted Form
oscarEncounter.msgFind = find

admin.admin.learning=OSCAR Learning
admin.admin.misc=Misc
admin.admin.uploadEntryTxt=Upload Login Exam
admin.admin.ErrorUploadEntryTxt=An error occurred while uploading your file.  It was not saved.
admin.admin.SuccessUploadEntryTxt=Your file was successfully saved.
admin.admin.learning.manageCourses=Manage Courses
admin.admin.learning.importPatient=Import Patient Data
admin.admin.learning.importStudent=Import Student Data

admin.admin.updates=Updates and Migrations
admin.admin.updates.migrate_onarenhanced=Migrate ONAREnhanced

admin.admin.oscarPropertiesMenu=Oscar Properties
admin.admin.oscarProperties.billingSettingsMenu=Billing Settings
admin.admin.oscarProperties.documentSettingsMenu=Document Settings
admin.admin.oscarProperties.eChartSettingsMenu=EChart Settings
admin.admin.oscarProperties.faxSettingsMenu=Fax Settings
admin.admin.oscarProperties.generalSettingsMenu=General Settings
admin.admin.oscarProperties.labSettingsMenu=Lab Settings
admin.admin.oscarProperties.rxSettingsMenu=Rx Settings
admin.admin.swaggerDocumentation=Swagger Documentation

admin.admin.swaggerDocumentation.billingAPIMenu=Billing REST API
admin.admin.swaggerDocumentation.fhirAPIMenu=OSCAR FHIR API

admin.admin.oscarProperties.billingSettings.billingSettings=Billing Settings
admin.admin.oscarProperties.billingSettings.settingsSectionTitle=Manage Oscar Properties Billing Settings
admin.admin.oscarProperties.billingSettings.enableMOHFileManagement=Enable MOH File Management
admin.admin.oscarProperties.billingSettings.enableNewONBilling=Enable New ON Billing
admin.admin.oscarProperties.documentSettings.documentSettings=Document Settings
admin.admin.oscarProperties.documentSettings.settingsSectionTitle=Manage Oscar Properties Document Settings
admin.admin.oscarProperties.documentSettings.enableExceptRxDocumentFilter=Enable the 'except Rx document' filter
admin.admin.oscarProperties.documentSettings.displayFlagAsAbnormalCheckbox=Display 'flag as abnormal' checkbox on document view in inbox
admin.admin.oscarProperties.documentSettings.enableUpdateOnceDocumentHasBeenAssignedToPatient=Enable update (split, rotate, remove pages or replace content with a new file) once the document has been assigned to a patient
admin.admin.oscarProperties.documentSettings.enableMovingDeletedPageOrPdfIntoDeletedFolder=Enable moving the deleted page or deleted pdf into the 'deleted' folder (if disabled - document will be deleted)
admin.admin.oscarProperties.eChartSettings.eChartSettings=EChart Settings
admin.admin.oscarProperties.eChartSettings.settingsSectionTitle=Manage Oscar Properties EChart Settings
admin.admin.oscarProperties.eChartSettings.includeCellPhoneInChartPrintHeader=Include cellphone in the header of a chart print
admin.admin.oscarProperties.eChartSettings.includeHinInChartPrintHeader=Include HIN in the header of a chart print
admin.admin.oscarProperties.eChartSettings.includeHomePhoneInChartPrintHeader=Include home phone in the header of a chart print
admin.admin.oscarProperties.eChartSettings.includeWorkPhoneInChartPrintHeader=Include work phone in the header of a chart print
admin.admin.oscarProperties.eChartSettings.includeMrpInChartPrintHeader=Include MRP in the header of a chart print
admin.admin.oscarProperties.eChartSettings.useProviderCurrentProgramInChartPrint=Use the provider's current program instead of clinic contact info
admin.admin.oscarProperties.eChartSettings.showEChartRelations=Show eChart relations
admin.admin.oscarProperties.eChartSettings.showEChartDemographicNumber=Show demographic number on top of eChart
admin.admin.oscarProperties.eChartSettings.showEChartInProgramDomain=Show eChart in program domain
admin.admin.oscarProperties.faxSettings.faxSettings=Fax Settings
admin.admin.oscarProperties.faxSettings.settingsSectionTitle=Manage Oscar Properties Fax Settings
admin.admin.oscarProperties.faxSettings.enableFax=Enable Fax
admin.admin.oscarProperties.faxSettings.enableRxFax=Enable Rx Fax
admin.admin.oscarProperties.faxSettings.enableConsultationFax=Enable Consultation Fax
admin.admin.oscarProperties.faxSettings.enableEFormFax=Enable EForm Fax
admin.admin.oscarProperties.generalSettings.generalSettings=General Settings
admin.admin.oscarProperties.generalSettings.settingsSectionTitle=Manage Oscar Properties General Settings
admin.admin.oscarProperties.generalSettings.enableAppointmentReminders=Enable Appointment Reminders
admin.admin.oscarProperties.generalSettings.showAdminHamiltonPublicHealthItems=Show Hamilton public health items in admin menu
admin.admin.oscarProperties.generalSettings.caseloadPageLoadAllProvidersClients=Load all providers clients while entering the caseload page
admin.admin.oscarProperties.generalSettings.ckdScreeningDisabled=CKD screening disabled
admin.admin.oscarProperties.generalSettings.enableClientsNamesDropdown=Enable clients names dropdown list on the tickler list page
admin.admin.oscarProperties.generalSettings.displayReferralSource=Display referral source in the masterfile
admin.admin.oscarProperties.labSettings.requireChartNoOnLabRequisitionForm=Require chart no. on the lab requisition form
admin.admin.oscarProperties.labSettings.requireAcknowledgedInboxDocuments=Require confirmation dialog for acknowledged inbox documents prior to being filed
admin.admin.oscarProperties.labSettings.incomingHl7DocumentsMatchingFilter=Enable incoming HL7 Documents to be matched on 'sex + DOB + HIN', ignoring the name of the patient (if disabled, will match on 'sex + DOB + HIN + Last Name + First Name')
admin.admin.oscarProperties.generalSettings.enableNewUserPinControl=Enable new user pin control:
admin.admin.oscarProperties.generalSettings.numberOfFlowsheetValues=Number of values displayed on a flowsheet:
admin.admin.oscarProperties.generalSettings.numberOfFlowsheetValuesMustBeInteger=Number of values displayed on a flowsheet must be a whole number
admin.admin.oscarProperties.generalSettings.numberOfFlowsheetValuesMustBeGreaterThanOrEqual=Number of values displayed on a flowsheet must be greater than or equal to 0
admin.admin.oscarProperties.generalSettings.showCommentsOnPreventionPrint=Show prevention item comments on Prevention Print PDF:
admin.admin.oscarProperties.generalSettings.enablePrintPdfReferringPractitioner=Enable Print PDF referring practitioner:
admin.admin.oscarProperties.generalSettings.enableInformedConsentCheck=Enable informed consent check:
admin.admin.oscarProperties.generalSettings.showSchedulePageMenuToSearchSpecialists=Show a menu in the main bar on schedule page to search professional specialists:
admin.admin.oscarProperties.generalSettings.requireReferringMd=Require referring MD (enable codes that require a referral MD):
admin.admin.oscarProperties.generalSettings.enableResidentReviewWorkflow=Enable resident review workflow:
admin.admin.oscarProperties.generalSettings.enableSaveAsXml=Enable "Save as XML":
admin.admin.oscarProperties.generalSettings.adminUserBillingControl=Admin User Billing Control
admin.admin.oscarProperties.generalSettings.allowOnlineBooking=Allow Online Booking
admin.admin.oscarProperties.generalSettings.newFlowsheetEnabled=New Flowsheet Enabled
admin.admin.oscarProperties.generalSettings.useNewInbox=Use New Inbox
admin.admin.oscarProperties.generalSettings.dailyAppointmentsPageRefreshTimeout=Refresh timeout in seconds for daily appointments page(0 for disabled)
admin.admin.oscarProperties.generalSettings.dailyAppointmentsPageRefreshTimeoutValueNotAnInteger=Daily appointments page refresh timeout: selected value is not a whole number
admin.admin.oscarProperties.generalSettings.dailyAppointmentsPageRefreshTimeoutValueCanNotBeNegative=Daily appointments page refresh timeout: Value cannot be negative number
admin.admin.oscarProperties.generalSettings.ticklerWarningDaysNumber=Number of days until tickler text turns red(-1 to disable/ignore):
admin.admin.oscarProperties.generalSettings.enableTicklerEdit=Enable ability to add additional messages to the same tickler and update tickler status:
admin.admin.oscarProperties.generalSettings.enableTicklerEmail=Enable tickler email:
admin.admin.oscarProperties.generalSettings.ticklerWarnDaysValueMustBeInteger=Number of days until tickler text turns red must be a whole number (in days)
admin.admin.oscarProperties.generalSettings.ticklerWarnDaysValueGreaterThanOrEqual=Number of days until tickler text turns red must be greater than or equal to -1
admin.admin.oscarProperties.generalSettings.showSexualHealthLabel=Show sexual health label:
admin.admin.oscarProperties.generalSettings.showSinglePageChartLink=Show link to single page chart in classic appointment screen:
admin.admin.oscarProperties.generalSettings.skipPostalCodeValidation=Skip postal code validation:
admin.admin.oscarProperties.generalSettings.enableSiteSelectionFeature=Enable site selection feature(for CAISI users - on login, force users to explicitly select a program):
admin.admin.oscarProperties.generalSettings.assignDefaultIssueToWaitlistWhenNoNotes=Assign a default issue to waitlist project when no notes:
admin.admin.oscarProperties.generalSettings.ectAutosaveTimer=ECT autosave timer in seconds(0 to disable):
admin.admin.oscarProperties.generalSettings.ectAutosaveTimerMustBeInteger=ECT autosave timer must be a whole number(in seconds)
admin.admin.oscarProperties.generalSettings.ectAutosaveTimerGreaterThanOrEqual=ECT autosave timer must be greater or equal to 0
admin.admin.oscarProperties.generalSettings.ectSaveFeedbackTimer=ECT save feedback timer (in seconds; must be enabled and greater than 0):
admin.admin.oscarProperties.generalSettings.ectSaveFeedbackTimerMustBeInteger=ECT save feedback timer must be a whole number (in seconds)
admin.admin.oscarProperties.generalSettings.ectSaveFeedbackTimerGreaterThan=ECT save feedback timer must be greater than 0
admin.admin.oscarProperties.generalSettings.enableRenalDosingDs=Enable Renal Dosing DS
admin.admin.oscarProperties.generalSettings.enablePrevention=Enable Prevention
admin.admin.oscarProperties.generalSettings.enableImmunizationInPrevention=Enable Immunization In Prevention
admin.admin.oscarProperties.generalSettings.enableConsultationAppointmentInstructionsLookup=Enable the use of the customizable Appointment Instructions look up list in the consultation module:
admin.admin.oscarProperties.generalSettings.enableConsultationToAutoIncludeAllergies=Enable all active patient allergies to auto-populate into a consultation request when opened:
admin.admin.oscarProperties.generalSettings.enableConsultationToAutoIncludeMedications=Enable all active patient medications to auto-populate into a consultation request when opened:
admin.admin.oscarProperties.generalSettings.enableConsultationDynamicLabelling=Enable consultation dynamic labelling(requires also consultation fax to be enabled):
admin.admin.oscarProperties.generalSettings.enableConsultationLockReferralDate=Allow changes to the consultation referral date(otherwise it will be locked to the date the referral was created):
admin.admin.oscarProperties.generalSettings.enableConsultationPatientWillBookCheckbox=Enable the Patient will book checkbox in the consultation request form:
admin.admin.oscarProperties.generalSettings.enableConsultationSignatures=Enable signatures in the consultation module:
admin.admin.oscarProperties.generalSettings.enableAlertsOnScheduleScreen=Enable alerts on schedule screen:
admin.admin.oscarProperties.generalSettings.enableNotesOnScheduleScreen=Enable notes on schedule screen:
admin.admin.oscarProperties.generalSettings.enableDefaultScheduleViewall=Enable the default viewall of the main appointment screen(if enabled will show the schedules for all the providers, including blank ones, otherwise will show only the scheduled physicians for that day):
admin.admin.oscarProperties.generalSettings.enableDemographicPatientClinicStatus=Enable demographic patient clinic status:
admin.admin.oscarProperties.generalSettings.enableDemographicPatientHealthCareTeam=Enable demographic patient health care team:
admin.admin.oscarProperties.generalSettings.enableDemographicPatientRostering=Enable demographic patient rostering:
admin.admin.oscarProperties.generalSettings.enableDemographicWaitingList=Enable demographic waiting list:
admin.admin.oscarProperties.generalSettings.enableUrgentMessages=Enable urgent messages:
admin.admin.oscarProperties.generalSettings.enableExternalNameOnDemographic=Enable external name on demographic (option to display external names for providers on different dropdowns):
admin.admin.oscarProperties.generalSettings.enableExternalNameOnSchedule=Enable external name on schedule (option to display external names for providers on different dropdowns):
admin.admin.oscarProperties.generalSettings.enableProviderScheduleNote=Enable provider schedule note:
admin.admin.oscarProperties.generalSettings.enableReceptionistAltView=Enable alternate view for receptionist ('Yes' to view appointment timeslots as the size of the template period; 'No' to view the timeslots as the size of the receptionist's preference):
admin.admin.oscarProperties.generalSettings.enableReferralMenu=Enable menu in the main bar on schedule page to search professional specialists:
admin.admin.oscarProperties.billingSettings.enableDefaultBCAltBilling=Enable the default BC alt. billing
admin.admin.oscarProperties.billingSettings.autoGeneratedBilling=Auto generated billing
admin.admin.oscarProperties.billingSettings.createNewNoteWithEveryNewInvoice=Create new note with every new invoice
admin.admin.oscarProperties.billingSettings.enableAutoPopulatingPaymentFieldOnBillingReview=Enable auto-populating the payment field on billing review
admin.admin.oscarProperties.billingSettings.hideNameOnTheInvoiceReportsPrint=Hide name on the invoice reports print
admin.admin.oscarProperties.billingSettings.removePatientDetailsIn3rdPartyInvoice=Remove the patient details in 3rd party invoice
admin.admin.oscarProperties.billingSettings.rmaBillingEnabled=RMA billing enabled
admin.admin.oscarProperties.billingSettings.enableCheckAllForUpdatingBillingPrice=Enable 'Check all' for 'Updating billing price'
admin.admin.oscarProperties.billingSettings.enableConfirmActionOnDeleteBillFromBillingHistory=Enable confirm ("Are you sure?") action if user tries to delete a bill from Billing History
admin.admin.oscarProperties.billingSettings.autofillBillingDateAndLocation=Autofill Most Recent Admission Date and Location. (Autofill the Admission Date and Visit Location with the same information from the last bill)
admin.admin.oscarProperties.billingSettings.billingRefBoxDefault=Billing Ref Box Default. (Automatically include the referral doctor in the referral doctor field on bills)
admin.admin.oscarProperties.billingSettings.visitTypeON=Visit Type (ON). (Sets the Visit Type on bills. The choices are: Clinic Visit, Outpatient Visit, Hospital Visit, ER, Nursing Home, and Home Visit)
admin.admin.oscarProperties.billingSettings.visitLocationBC=Visit Location (BC). (Sets the Teleplan default Clarification Code for new invoices)
admin.admin.oscarProperties.billingSettings.dataCenterId=Data Center ID (BC). (Sets the data center ID for the instance)
admin.admin.oscarProperties.billingSettings.inPatient=In Patient. (Autofill the admission date on the billing page)
admin.admin.oscarProperties.billingSettings.useFirstLocationAndAdmission=Use first location and admission. (Invoice automatically populates the admission date / location on the new invoice if it was ever added previously)
admin.admin.oscarProperties.billingSettings.clinicNo=Clinic No. (The clinic facility number)
admin.admin.oscarProperties.generalSettings.enableEFormInAppointment=Enable eForm in appointment (for IBD clinic):
admin.admin.oscarProperties.generalSettings.enableMeditechId=Enable Meditech ID (for IBD clinic):
admin.admin.oscarProperties.generalSettings.enableAppointmentMcNumber=Enable appointment MC number (for IBD clinic):
admin.admin.oscarProperties.generalSettings.enableSignatureInEForm=Enable signature in EForm:
admin.admin.oscarProperties.generalSettings.allowMultipleSameDayGroupAppts=Allow multiple appointments per demographic per day per group (schedule group; prevents rejected claims for OHIP Fee for Service where multiple appointments of the same type are booked on the same day for the same demographic that causes the claim to be rejected):
admin.admin.oscarProperties.generalSettings.appointmentLockingTimeout=The amount of time in seconds an appointment timeslot will be reserved to a provider as viewing and/or locked (0 to disable):
admin.admin.oscarProperties.generalSettings.appointmentLockingTimeoutMustBeInteger=The amount of time in seconds an appointment timeslot will be reserved to a provider as viewing and/or locked must be a whole number
admin.admin.oscarProperties.generalSettings.appointmentLockingTimeoutValueGreaterOrEqual=The amount of time in seconds an appointment timeslot will be reserved to a provider as viewing and/or locked must be a greater or equal to 0
admin.admin.oscarProperties.generalSettings.enableAppointmentIntakeForm=Enable appointment intake form:
admin.admin.oscarProperties.generalSettings.displayAppointmentDaySheetButton=Display appointment daysheet button:
admin.admin.oscarProperties.generalSettings.encounterLayoutRefreshTimeout=Encounter layout refresh timeout (in seconds; -1 to disable):
admin.admin.oscarProperties.generalSettings.encounterLayoutRefreshTimeoutMustBeInteger=Encounter layout refresh timeout must be a whole number
admin.admin.oscarProperties.generalSettings.encounterLayoutRefreshTimeoutGreaterOrEqual=Encounter layout refresh timeout must be a greater or equal to -1
admin.admin.oscarProperties.generalSettings.showAppointmentReason=Show appointment reason:
admin.admin.oscarProperties.generalSettings.hideConReportLinkInTopNavigationBar=Hide ConReport link in top navigation bar:
admin.admin.oscarProperties.generalSettings.hideEConsultLinkInTopNavigationBar=Hide eConsult link in top navigation bar:
admin.admin.oscarProperties.generalSettings.enableMeasurementsCreateNewNote=Enable if new measurements create their own notes on a demographics encounter screen:
admin.admin.oscarProperties.generalSettings.monerisUploadEnabled=Enable Moneris upload:
admin.admin.oscarProperties.generalSettings.enableManageContactsAndHealthCareTeamLinked=Enable professional contacts added in either Manage Contacts or Health Care team to show up in both modules (if disabled - Manage Contacts and Health Care Team act independently):
admin.admin.oscarProperties.generalSettings.enableNewLabelPrint=Enable new label print:
admin.admin.oscarProperties.generalSettings.enableLinkFeature=Enable link feature:
admin.admin.oscarProperties.labSettings.labSettings=Lab Settings
admin.admin.oscarProperties.labSettings.settingsSectionTitle=Manage Oscar Properties Lab Settings
admin.admin.oscarProperties.labSettings.enablePathNetLabs=Enable PathNet Labs
admin.admin.oscarProperties.labSettings.enableHl7TextLabs=Enable HL7Text Labs
admin.admin.oscarProperties.labSettings.enableMdsLabs=Enable MDS Labs
admin.admin.oscarProperties.labSettings.enableCmlLabs=Enable CML Labs
admin.admin.oscarProperties.labSettings.enableEpsilonLabs=Enable Epsilon Labs

admin.admin.oscarProperties.rxSettings.rxSettings=Rx Settings
admin.admin.oscarProperties.rxSettings.settingsSectionTitle=Manage Oscar Properties Rx Settings
admin.admin.oscarProperties.rxSettings.enableRxSignature=Enable Rx Signature
admin.admin.oscarProperties.rxSettings.showRxBandNumber=Show Rx band number
admin.admin.oscarProperties.rxSettings.displayChartNumberOnPrescription=Display chart number on prescription
admin.admin.oscarProperties.rxSettings.displayPatientHINOnRx=Display the patient HIN on Rx
admin.admin.oscarProperties.rxSettings.hideDispensingUnitsWhenPrescribingMeds=Hide the dispensing units when prescribing medications
admin.admin.oscarProperties.rxSettings.hideDrugOfChoiceButtonFromRx3Interface=Hide the drug of choice button from rx3 interface
admin.admin.oscarProperties.rxSettings.enableDrugsInternalDispensing=Enable drugs internal dispensing(when enabled, you can set drugs to internally dispensed, and access the dispensing screen backed by a simple inventory/product manager)
admin.admin.oscarProperties.rxSettings.addWatermarkInRxPrescription=Add watermark in Rx prescription
admin.admin.oscarProperties.rxSettings.enableRxAllergyChecking=Enable Rx allergy checking
admin.admin.oscarProperties.rxSettings.enableCheckingFromDrugrefUsingRegionalIdentifier=Enable interaction checking from drugref, using regional identifier
admin.admin.oscarProperties.rxSettings.enableSignaturePadElectronicSigningForRx3=Enable signature pad electronic signing for Rx3
admin.admin.oscarProperties.rxSettings.rxOrderByDateNotPosition=Rx order by date, not position
admin.admin.oscarProperties.rxSettings.rxFooterText=Rx footer text

admin.admin.messages=Messages
admin.admin.paymentReceived=Payment Received Report
admin.admin.btnEnterPremiumPayments=Enter Premium Payments

admin.admin.btnInventory=Product Dispensing

admin.admin.btnFixNotes=Fix notes with invalid role
admin.admin.btnAuditLogPurge=Purge Audit Log

billing.batchbilling.title=Batch Billing
billing.batchbilling.header=Add Demographic to Batch Billing
billing.batchbilling.demographic=Demographic Id
billing.batchbilling.demographicName=Demographic Name
billing.batchbilling.demographicDOB=Demographic DOB
billing.batchbilling.demographicHIN=Demographic HIN
billing.batchbilling.billingProvider=Billing Provider
billing.batchbilling.serviceCode=Service Code
billing.batchbilling.DxCode=Diagnostic Code
billing.batchbilling.CreateDate=Create Date
billing.batchbilling.Creator=Creator
billing.batchbilling.submit=Save
billing.batchbilling.cancel=Cancel
billing.batchbilling.noServicecodeErr=You have not specified a service code
billing.batchbilling.noDxCodeErr=You have not specified a dx code
billing.batchbilling.noProviderErr=You have not selected a billing provider
billing.batchbilling.msgProvider=Select Provider
billing.batchbilling.msgAllProvider=All Providers
billing.batchbilling.msgClinicLocation=Clinic Location
billing.batchbilling.msgSelection=Selection
billing.batchbilling.msgDemographic=Demographic
billing.batchbilling.msgProviderTitle=Provider
billing.batchbilling.msgService=Service
billing.batchbilling.msgServiceUnit=Unit
billing.batchbilling.admissionDate=Admission Date
billing.batchbilling.visitLocation=Visit Location
billing.batchbilling.msgAmount=Amount
billing.batchbilling.msgDiagnostic=Diagnostic
billing.batchbilling.msgLastBillDate=Last Bill Date
billing.batchbilling.msgAllServiceCode=All Service Codes
billing.batchbilling.msgNoMatch=No Matches Found
billing.batchbilling.btnSubmit=Generate Batch Invoices
billing.batchbilling.badDate=The billing date is in the wrong format
billing.batchbilling.serviceDate=Service Date
billing.batchbilling.btnRemove=Remove
billing.batchbilling.msgConfirmDelete=Do you wish to remove your selected demographics from batch billing?
billing.batchbilling.msgConfirmSaved=Your demographic has been added(updated) in batch billing.
billing.bc.title=BC Billing
billing.patient=Patient
billing.patient.status=Patient Status
billing.patient.roster=Roster Status
billing.patient.age=Age
billing.provider.assignedProvider=Assigned Physician
billing.provider.apptProvider=Appointment Physician
billing.provider.billProvider=Billing Physician
billing.provider.referral=Referral
billing.billingtype=Billing Type
billing.billingform=Billing Form
billing.visittype=Visit Type
billing.visitlocation=Site Number
billing.servicedate=Service Date
billing.admissiondate=Admission Date
billing.servicedate.starttime=Start (HHMM 24hr):
billing.servicedate.endtime=End (HHMM 24hr):
billing.service.code=Service Code
billing.service.service=Service
billing.service.procedure=Procedure
billing.service.premium=Premium
billing.service.desc=Description
billing.service.facilityNum=Facility Number
billing.service.facilitySubNum=Facility Sub Number
billing.service.fee=Fee
billing.service.unit=Unit
billing.service.total=Total
billing.service.otherservice=Other service/procedure/premium codes
billing.referral.doctor=Referral Doctor
billing.referral.type=Referral Type
billing.diagnostic.code=Diagnostic Code
billing.diagnostic.desc=Description
billing.diagnostic.dx1=DX 1
billing.diagnostic.dx2=DX 2
billing.diagnostic.dx3=DX 3

admin.provider.formType.optionMidwife=midwife

oscarEncounter.Index.measurements=Measurements
oscarEncounter.Index.measurements.general=General
oscarEncounter.Index.measurements.addMeasurementType=Add Measurement Type
oscarEncounter.Index.measurements.editMesaurementType=Edit Measurement Type
oscarEncounter.Index.btnCustomize=Customize
oscarEncounter.Index.measurements.addMeasuringInstruction=Add Measuring Instruction
oscarEncounter.Index.measurements.addMeasurementGroup=Add Measurement Group
oscarEncounter.Index.oldMeasurements=Old Measurements
oscarEncounter.Index.measurements.viewMeasurementType=View All Measurement Types

global.measurements.general=General Measurements

oscarEncounter.oscarMeasurements.Measurements.headingTypeDesc=Type Description
oscarEncounter.oscarMeasurements.Measurements.headingMeasuringInstrc=Measuring Instruction
oscarEncounter.oscarMeasurements.Measurements.headingType=Type
oscarEncounter.oscarMeasurements.Measurements.headingObservationDate=Observation Date (yyyy-mm-dd hh:mm)
oscarEncounter.oscarMeasurements.Measurements.headingValue=Value
oscarEncounter.oscarMeasurements.Measurements.headingComments=Comments
oscarEncounter.oscarMeasurements.Measurements.headingDisplayName=Display Name
oscarEncounter.oscarMeasurements.Measurements.headingValidation=Validation

oscarEncounter.oscarMeasurements.MeasurementsAction.mustBe=must be
oscarEncounter.oscarMeasurements.MeasurementsAction.between=between
oscarEncounter.oscarMeasurements.MeasurementsAction.inThisFormat=in this format:
oscarEncounter.oscarMeasurements.MeasurementsAction.numericValue=a numeric value
oscarEncounter.oscarMeasurements.MeasurementsAction.and=and
oscarEncounter.oscarMeasurements.MeasurementsAction.bloodPressure=The systolic value must be higher than diastolic
oscarEncounter.oscarMeasurements.MeasurementsAction.addBtn=Add
oscarEncounter.oscarMeasurements.MeasurementsAction.editBtn=Submit
oscarEncounter.oscarMeasurements.MeasurementsAction.continueBtn=Continue
oscarEncounter.oscarMeasurements.Measurements.msgParentChanged=The Patient Encounter screen no longer refers to this patient.  Information will be added to

oscarEncounter.oscarMeasurements.AddMeasurementType.duplicateType=The specified type already exists
oscarEncounter.oscarMeasurements.SelectMeasurementGroup.selectGroup=Please select a group to edit
oscarEncounter.oscarMeasurements.MeasurementsAction.deleteBtn=Delete
oscarEncounter.oscarMeasurements.MeasurementGroup.add2Group=Please Select types and press Add button to add types into the group
oscarEncounter.oscarMeasurements.MeasurementGroup.deleteTypes=Select types and press Delete button to delete types from the group
oscarEncounter.oscarMeasurements.MeasurementGroup.allTypes=All Types
oscarEncounter.Index.measurements.editMeasurementGroup=Edit Measurement Group
oscarEncounter.Index.SelectGroup=Select Group

oscarEncounter.oscarMeasurements.displayHistory.headingType=Type
oscarEncounter.oscarMeasurements.displayHistory.headingProvider=Provider
oscarEncounter.oscarMeasurements.displayHistory.headingData=Data
oscarEncounter.oscarMeasurements.displayHistory.headingMeasuringInstruction=Measuring Instruction
oscarEncounter.oscarMeasurements.displayHistory.headingComments=Comments
oscarEncounter.oscarMeasurements.displayHistory.headingObservationDate=Observation Date
oscarEncounter.oscarMeasurements.displayHistory.headingDateEntered=Date Entered
oscarEncounter.oscarMeasurements.displayHistory.headingDelete=Delete
oscarEncounter.oscarMeasurements.displayHistory.SyncStatus=Sync Status

global.measurements.addMeasurementGroup=Add Measurement Group

report.reportindex.chronicDiseaseManagement=Chronic Disease Management

oscarReport.CDMReport.msgTitle=CDM Report
oscarReport.CDMReport.msgReport=Report
oscarReport.CDMReport.msgSelectCDMGroup=Please select the CDM group
oscarReport.CDMReport.btnContinue=Continue
oscarReport.CDMReport.msgTest=Test
oscarReport.CDMReport.msgTestDescription=Test Description
oscarReport.CDMReport.msgMeasuringInstruction=Measuring Instruction
oscarReport.CDMReport.msgFrom=From
oscarReport.CDMReport.msgTo=To
oscarReport.CDMReport.msgGuideline=Guideline
oscarReport.CDMReport.msgPercentageOfPatientWhoMetGuideline=Percentage of patient who met guideline
oscarReport.CDMReport.msgStartDate=Start Date
oscarReport.CDMReport.msgEndDate=End Date
oscarReport.CDMReport.msgNumberOfPatientsSeen=Number of Patient Seen
oscarReport.CDMReport.btnGenerateReport=Generate Report
oscarReport.CDMReport.msgAll=All
oscarReport.CDMReport.msgAboveBelow=Above/Below
oscarReport.CDMReport.msgPercentageOfPatientInAbnormalRange=Percentage of Patient in Abnormal Range
oscarReport.CDMReport.msgUpperBound=Upper Bound
oscarReport.CDMReport.msgLowerBound=Lower Bound
oscarReport.CDMReport.msgFrequencyOfRelevantTestsBeingPerformed=Frequency of Relevant Test Being Performed
oscarReport.CDMReport.msgMoreThan=More than (times)
oscarReport.CDMReport.msgLessThan=Less Than (times)
oscarReport.CDMReport.msgExactly=Exactly (times)
oscarReport.CDMReport.msgTimes=Times
oscarReport.CDMReport.msgMedicationsPrescribed=Medications Prescribed

oscarEncounter.Measurements.msgAddMeasurementGroup=Add Measurement Group

oscarEncounter.Measurements.msgAddMeasurementType=Add Measurement Type

oscarEncounter.Measurements.msgEditMeasurementType=Edit Measurement Type
oscarEncounter.Measurements.msgAddMeasurementInstruction=Add Measurement Instruction

oscarEncounter.Measurements.msgDefineNewMeasurementGroup=Define New Measurement Group

oscarEncounter.Measurements.msgDisplayHistory=Display History

oscarEncounter.Measurements.msgDisplayMeasurementTypes=Display Measurement Types

oscarEncounter.Measurements.msgEditMeasurementGroup=Edit Measurement Group

oscarEncounter.Measurements.msgCustomization=Customization

oscarEncounter.Measurements.msgProcessAddMeasurementGroupAction=Process Add Measurement Group Action

oscarEncounter.Measurements.msgProcessAddMeasurementTypeAction=Process Add Measurement Type Action

oscarEncounter.Measurements.msgProcessAddMeasuringInstructionAction=Process Add Measuring Instruction Action

oscarEncounter.Measurements.msgProcessDisplayHistoryAction=Process Display History Action

oscarEncounter.Measurements.msgProcessDisplayMeasurementTypesAction=Process Display Measurement Types Action

oscarEncounter.Measurements.msgProcessEditMeasurementGroupAction=Process Edit Measurement Group Action

oscarEncounter.Measurements.msgProcessGroupListAction=Process Group List Action

oscarEncounter.Measurements.msgProcessMeasurementsAction=Process Measurements Action

oscarEncounter.Measurements.msgProcessMeasurementsSubmission=Process Measurements Submission

oscarEncounter.Measurements.msgSelectMeasurementGroup=Select Measurement Group

oscarEncounter.Measurements.msgGroup=Group

oscarEncounter.Measurements.msgType=Type

oscarEncounter.Measurements.msgMeasuringInstruction=Measuring Instruction

oscarEncounter.Measurements.msgMeasurements=Measurements

oscarEncounter.oscarMeasurements.addMeasurementGroup.createNewMeasurementGroupName=Please enter a new measurement group name

oscarEncounter.oscarMeasurements.MeasurementAction.headingDelete=Delete

error.oscarEncounter.Measurements.type.maxlength4=<li>The name of Type cannot exceed 4 character</li>
errors.unableToFindWithId=<li>Unable to find {0} with id '{1}'</li>
errors.duplicateName=An entry with the given name '{0}' already exists.
errors.unableToComplete=<li>Unable to complete the requested operation - please contact your system administrator.</li>
error.oscarEncounter.Measurements.duplicateTypeName=<li>The entered type already exists</li>

error.oscarEncounter.Measurements.typeDesc.maxlength255=<li>The type description cannot exceed 255 characters</li>

error.oscarEncounter.Measurements.typeDisplay.maxlength100=<li>The type display name cannot exceed 100 characters</li>

error.oscarEncounter.Measurements.measuringInstrc.maxlength255=<li>The measuring instruction cannot exceed 255 characters</li>

errors.required=<li>{0} is required</li>

errors.maxlength=<li>{0} cannot be more than {1} characters</li>

errors.oscarEncounter.Measurements.cannotFindType=<li>cannot find "{0}" type with "{1}" measuring instruction</li>

errors.billing.ca.on.database=<li>Error accessing database: {0}</li>

oscarEncounter.oscarMeasurements.AddMeasurementType.successful=Measurement type has been added successfully{0}
oscarEncounter.oscarMeasurements.EditMeasurementType.successful=Measurement type has been edited successfully{0}

demographic.demographicaddrecordhtm.formMidwife=Midwife

demographic.demographiceditdemographic.formMidwife=Midwife

schedule.scheduletemplatecodesetting.msgBookingLimit=Booking Limit: # of simultaneous appts in slot

admin.updatedemographicprovider.msgMidwife=Midwife

oscarReport.CDMReport.msgProcessSelectCDMReportAction=Process Select CDM Report Action

oscarEncounter.Measurements.msgGroupName=Group Name
errors.range=<li>{0} is not in the range {1} through {2}</li>

errors.invalid=<li>{0} is invalid</li>

errors.bloodPressure=<li>Blood Pressure must be in ###/### format</li>

admin.admin.oscarMeasurements=Measurements

errors.invalidDate=<li>The date of {0} is invalid</li>

errors.invalidComments=<li>The comments of {0} include invalid character</li>

oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgFaxFooterMessage=This information is direct in confidence solely to the person named above and may not otherwise be distributed, copied or disclosed. Therefore, this information should be considered strictly confidential.  If you have received this telecopy in error, please notify us immediately by telephone. Thank you for your assistance.

oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgPleaseReplyPart1=Please reply to

oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgPleaseReplyPart2=by fax or by phone with appointment

oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgPleaseReplyPatient=Please reply to patient

oscarReport.CDMReport.msgPatientSeen=There are {0} patients seen from {1} to {2}

oscarReport.CDMReport.msgNbOfPatientsInAbnormalRange=From {0} to {1}: {2} {3} -> From {4} to {5}: {6} % of patients

oscarReport.CDMReport.msgNbOfPatientsIs=From {0} to {1}: {2} {3} -> {4} {5}% of patients

oscarReport.CDMReport.msgNbOfPatientsMetGuideline=From {0} to {1}: {2} {3} -> {4}% of patients is {5} {6}

oscarReport.CDMReport.msgPassAllSeletectedTest=From {0} to {1}: {2} patients done all the selected tests and {3}% pass all the tests

oscarReport.CDMReport.msgFrequencyOfRelevantTestsExact=From {0} to {1}: {2} {3} -> {4} of patients has done the test for exactly {5} times

oscarReport.CDMReport.msgFrequencyOfRelevantTestsMoreThan=From {0} to {1}: {2} {3} -> {4} of patients has done the test for more than {5} times

oscarReport.CDMReport.msgFrequencyOfRelevantTestsLessThan=From {0} to {1}: {2} {3} -> {4} of patients has done the test for less than {5} times

oscarEncounter.Index.tooltipClose=Close

oscarEncounter.Index.tooltipSmall=Small

oscarEncounter.Index.tooltipNormal=Normal

oscarEncounter.Index.tooltipLarge=Large

oscarEncounter.Index.tooltipFull=Full

oscarEncounter.Index.tooltipReset=Reset

oscarReport.CDMReport.msgFrequency=Frequency

oscarReport.CDMReport.msgPercentage=Percentage

oscarEncoutner.oscarMeasurements.msgTheLastValue=The last value

oscarEncoutner.oscarMeasurements.msgValueWasEnteredBy=value was entered by

oscarEncoutner.oscarMeasurements.msgOn=on

error.oscarEncounter.addNewMeasurementGroup.duplicateGroupName=<li>The Group Name {0} already exists</li>

oscarEncounter.oscarMeasurements.addMeasurementGroup.selectStyleSheet=Please select the style sheet you would like to use in the new group

oscarEncounter.oscarMeasurements.MeasurementsAction.modifyMeasurementTypeBtn=Modify Measurement Types

oscarEncounter.oscarMeasurements.SelectMeasurementGroup.msgCurrentStyleSheet=The current style sheet of

oscarEncounter.oscarMeasurements.MeasurementsAction.modifyMeasurementStyleBtn=Modify Measurement Style

oscarEncounter.oscarMeasurements.MeasurementsAction.okBtn=OK

oscarEncounter.oscarMeasurements.SelectMeasurementGroup.msgChangeTo=Change To

oscarEncounter.oscarMeasurements.Measurements.headingStyleSheetName=Style Sheet Name

oscarEncounter.Measurements.msgDisplayMeasurementStyleSheets=Display Measurement Style Sheets

error.oscarEncounter.Measurements.cannotDeleteStyleSheet=The style sheet {0} cannot be deleted since some groups are using it

oscarEncounter.Measurements.msgStyleSheets=Style Sheets

oscarEncounter.Index.measurements.viewMeasurementStyleSheet=View All Style Sheet

errors.fileNotAdded=File not Added

oscarEncounter.oscarMeasurement.msgAddedStyleSheet=Style Sheet {0} added successfully!

oscarEncounter.Index.measurements.addMeasurementStyleSheet=Add Measurement Style Sheet


admin.securityupdatesecurity.btnDelete=Delete Record

admin.preferenceupdatepreference.btnDelete=Delete Record

admin.providerupdateprovider.btnDelete=Delete Record
oscarEncounter.oscarMeasurements.AddMeasuringInstruction.successful=Measuring Instruction has been added successfully{0}
provider.appointmentProviderAdminDay.rosterMsg=Roster Status\:

oscarEncounter.oscarMeasurements.createNewMeasurementStyleSheet=Please enter the new style sheet file name

tickler.ticklerMain.taskAssignedTo=Task Assigned to

tickler.ticklerMain.programAssignedTo=Program Assigned to

tickler.ticklerMain.Priority=Priority

tickler.ticklerMain.priority.high=High

tickler.ticklerMain.priority.normal=Normal

tickler.ticklerMain.priority.low=Low

oscarEncounter.oscarMeasurements.displayHistory.plot=Plot

oscarEncounter.Index.m=M

oscarEncounter.Index.tooltipMedium=Medium

global.medlineplus=Medline Plus

global.BNF=BNF.org

oscarReport.RptByExample.MsgQueryByExamples=Query By Examples

oscarReport.RptByExample.MsgShowTextVersion=Show Text Version

oscarReport.RptByExample.MsgHideTextVersion=HideTextVersion

oscarReport.RptByExample.MsgEnterAQuery=Enter a Query

oscarReport.RptByExample.MsgSelectFromMyFavorites=Select from my favorites

oscarReport.RptByExample.MsgOr=OR

oscarReport.RptByExample.MsgEditMyFavorite=Edit My Favorite

oscarReport.RptByExample.MsgDate=Date

oscarReport.RptByExample.MsgProvider=Provider

oscarReport.RptByExample.MsgQuery=Query

oscarReport.RptByExample.MsgAllQueriesExecutedFrom=All queries executed from

oscarReport.RptByExample.MsgTo=to

oscarReport.RptByExample.MsgAllQueriesExecuted=All queries executed

oscarReport.RptByExample.MsgAddToFavorite=Add to Favorite

oscarReport.RptByExample.MsgMyFavorites=My Favorites

oscarReport.RptByExample.MsgName=Name

oscarReport.RptByExample.MsgEdit=Edit

oscarReport.RptByExample.MsgDelete=Delete

oscarReport.RptByExample.MsgCancel=Cancel

oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnUpdateAndFax=Update & Fax

oscarEncounter.oscarConsultationRequest.ConsultationFormRequest.btnSubmitAndFax=Submit And Fax

errors.minlength=<li>{0} cannot be more than {1} characteres</li>

demographic.search.Inactive=Inactive

demographic.search.All=All
demographic.search.OutOfDomain=Domain

demographic.zdemographicfulltitlesearch.tooltips.searchInactive=Search inactive patients

demographic.zdemographicfulltitlesearch.tooltips.searchActive=Search active patients

demographic.zdemographicfulltitlesearch.tooltips.searchAll=Search all patients
demographic.zdemographicfulltitlesearch.tooltips.searchOutOfDomain=Search Outside of Domain

oscarEncounter.echartHistory.title=ECHART HISTORY
oscarEncounter.echartHistory.buttonPrint=Print
oscarEncounter.echartHistory.buttonExit=Exit
oscarEncounter.echartHistory.apptDate=Appt Date
oscarEncounter.echartHistory.reason=Reason

demographic.demographicsearch2apptresults.findPatient=Find Patient
demographic.demographicsearch2apptresults.title=Patient Search Results
demographic.demographicsearch2apptresults.patientsRecord=Patient Record
demographic.demographicsearch2apptresults.msgKeywords=Results based on keyword(s):
demographic.demographicsearch2apptresults.msgWrongDOB=You have a wrong DOB input!!!
demographic.demographicsearch2apptresults.btnSearch=Search
demographic.demographicsearch2apptresults.optName=Name
demographic.demographicsearch2apptresults.optAddress=Address
demographic.demographicsearch2apptresults.optPhone=Phone
demographic.demographicsearch2apptresults.optDOB=DOB(yyyymmdd)
demographic.demographicsearch2apptresults.optHIN=HIN
demographic.demographicsearch2apptresults.demographicId=Demographic No.
demographic.demographicsearch2apptresults.lastName=Last Name
demographic.demographicsearch2apptresults.firstName=First Name
demographic.demographicsearch2apptresults.age=Age
demographic.demographicsearch2apptresults.rosterStatus=Roster Status
demographic.demographicsearch2apptresults.sex=Sex
demographic.demographicsearch2apptresults.DOB=DOB
demographic.demographicsearch2apptresults.doctor=Doctor
demographic.demographicsearch2apptresults.btnNextPage=Next Page
demographic.demographicsearch2apptresults.btnPrevPage=Last Page
demographic.demographicsearch2apptresults.optChart=Chart #
demographic.demographiceditdemographic.formPatientType=Patient Type
demographic.demographiceditdemographic.formPatientId=External ID
demographic.demographiceditdemographic.formDemographicGroups=Demographic Group

demographic.demographiceditdemographic.prescribeIt.optOut=Opt out of PrescribeIT
admin.admin.oscarEncounter=Encounter

admin.admin.btnSelectForm=Select Forms

oscarEncounter.form.msgAllAvailableForms=All Available Forms
oscarEncounter.form.uhipLbl=UHIP#:WO

oscarEncounter.form.msgSelectedForms=Selected Forms
oscarEncounter.form.labreq.patientChartNo=Chart No.
oscarEncounter.form.labreq.clientreference=Client Reference No.

share.CalendarPopUp.msgLastYear=Last Year
share.CalendarPopUp.msgNextYear=Next Year
share.CalendarPopUp.msgViewNextMonth=View Next Month
share.CalendarPopUp.msgViewLastMonth=View Last Month
share.CalendarPopUp.msgLastMonth=last month
share.CalendarPopUp.msgNextMonth=next month
share.CalendarPopUp.msgSun=Sun
share.CalendarPopUp.msgMon=Mon
share.CalendarPopUp.msgTue=Tue
share.CalendarPopUp.msgWed=Wed
share.CalendarPopUp.msgThu=Thu
share.CalendarPopUp.msgFri=Fri
share.CalendarPopUp.msgSat=Sat
share.CalendarPopUp.msgJan=Jan
share.CalendarPopUp.msgFeb=Feb
share.CalendarPopUp.msgMar=Mar
share.CalendarPopUp.msgApr=Apr
share.CalendarPopUp.msgMay=May
share.CalendarPopUp.msgJun=Jun
share.CalendarPopUp.msgJul=Jul
share.CalendarPopUp.msgAug=Aug
share.CalendarPopUp.msgSep=Sep
share.CalendarPopUp.msgOct=Oct
share.CalendarPopUp.msgNov=Nov
share.CalendarPopUp.msgDec=Dec

admin.adminNewGroup.msgGroupIsRequired=Please, put the name's group!

demographic.demographiceditdemographic.formPatientStatus=Patient Status

dms.documentReport.msgProvider=Provider
dms.documentReport.msgDemographic=Demographic
dms.documentReport.msgReceptionist=Receptionist
dms.documentReport.msgProviderFolder=Provider's folder
dms.documentReport.msgDemographicFolder=Demographic's folder
dms.documentReport.msgReceptionistFolder=Receptionist's folder

admin.securitysearchrecordshtm.btnSearch=Search

schedule.scheduletemplateapplying.msgDate=Date
schedule.scheduletemplateapplying.msgFrom=from
schedule.scheduletemplateapplying.msgTo=to
schedule.scheduletemplateapplying.msgDateFormat=(yyyy-mm-dd)

errors.incorrectFileFormat=The File format is incorrect! Report cannot be generated.

oscarEncounter.oscarConsultationRequest.config.btnEnableRequestResponse=Enable Request/Response
oscarEncounter.oscarConsultationRequest.config.btnAddSpecialist=Add Specialist
oscarEncounter.oscarConsultationRequest.config.btnAddService=Add Service
oscarEncounter.oscarConsultationRequest.config.btnEditSpecialists=Edit Specialists
oscarEncounter.oscarConsultationRequest.config.btnShowAllServices=Show All Services
oscarEncounter.oscarConsultationRequest.config.btnDeleteServices=Delete Services
oscarEncounter.oscarConsultationRequest.config.btnAddInstitution=Add Institution
oscarEncounter.oscarConsultationRequest.config.btnEditInstitutions=Edit Institutions
oscarEncounter.oscarConsultationRequest.config.btnAddDepartment=Add Department
oscarEncounter.oscarConsultationRequest.config.btnEditDepartments=Edit Departments
oscarEncounter.oscarConsultationRequest.config.btnShowAllInstitutions=Show All Institutions
oscarEncounter.oscarConsultationRequest.config.btnConsultRequestSettings=General Consultation Request Settings

oscarEncounter.oscarConsultationRequest.config.AddInstitution.addOperation=Add Institution
oscarEncounter.oscarConsultationRequest.config.AddInstitution.updateOperation=Update Institution
oscarEncounter.oscarConsultationRequest.config.AddInstitution.msgInstitutionAdded=Institution {0} has been saved.
oscarEncounter.oscarConsultationRequest.config.EditInstitutions.msgClickOn=Click on the institution you would like to edit

oscarEncounter.oscarConsultationRequest.config.AddDepartment.addOperation=Add Department
oscarEncounter.oscarConsultationRequest.config.AddDepartment.updateOperation=Update Department
oscarEncounter.oscarConsultationRequest.config.AddDepartment.msgDepartmentAdded=Department {0} has been saved.
oscarEncounter.oscarConsultationRequest.config.EditDepartments.msgClickOn=Click on the department you would like to edit

oscarEncounter.oscarConsultationRequest.config.EnableRequestResponse.title=Enable Consultation Request/Response
oscarEncounter.oscarConsultationRequest.config.EnableRequestResponse.enableRequest=Enable Consultation Request
oscarEncounter.oscarConsultationRequest.config.EnableRequestResponse.enableResponse=Enable Consultation Response
oscarEncounter.oscarConsultationRequest.config.EnableRequestResponse.msgUpdated=Setting updated
oscarEncounter.oscarConsultationRequest.config.EnableRequestResponse.btnUpdate=Update
oscarEncounter.oscarConsultationRequest.config.ConsultRequestSettings.title=General Consultation Request Settings
oscarEncounter.oscarConsultationRequest.config.ConsultRequestSettings.defaultAppointmentInstructions=Set Default Appointment Instructions
oscarEncounter.oscarConsultationRequest.config.ConsultRequestSettings.measurementsFilterByAppointment=Filter Ocular Examination measurements by appointment
oscarEncounter.oscarConsultationRequest.config.ConsultRequestSettings.referringPractitioner=Desired Provider Types to appear under Referring Practitioner selection:
oscarEncounter.oscarConsultationRequest.config.ConsultRequestSettings.displayCreatedByProviderOnThePdfVersion=Display &quot;Created By&quot; provider on the PDF version of the consultation
oscarEncounter.oscarConsultationRequest.config.ConsultRequestSettings.msgUpdated=Setting updated
oscarEncounter.oscarConsultationRequest.config.ConsultRequestSettings.btnUpdate=Update

oscarEncounter.oscarConsultationRequest.config.AddSpecialist.addOperation=Add Specialist
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.updateOperation=Update Specialist
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.title=Add Specialist
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.2ndTitle=Add Specialist
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.firstName=First Name
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.lastName=Last Name
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.professionalLetters=Professional Letters
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.address=Address
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.phone=Phone
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.fax=Fax
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.website=Website
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.email=Email
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.specialistType=Specialist Type
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.referralNo=Referral No.
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.province=Province
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.eDataUrl=eData URL
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.eDataOscarKey=eData Oscar Key
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.eDataServiceKey=eData Service Key
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.eDataServiceName=eData Service Name
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.btnAddSpecialist=Add Specialist
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.msgSpecialistAdded=Specialist {0} has been saved.
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.referralNoInUse=This referral number is in use.  Please choose another.
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.referralNoInvalid=This referral number is invalid.  {0}
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.institution=Institution
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.department=Department
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.privatePhoneNumber=Private phone
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.cellPhoneNumber=Cell phone
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.pagerNumber=Pager
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.salutation=Salutation
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.hideFromView=Hide From Search
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.eform=Referral EForm

oscarEncounter.oscarConsultationRequest.config.AddService.title=Add Service
oscarEncounter.oscarConsultationRequest.config.AddService.2ndTitle=Add Service
oscarEncounter.oscarConsultationRequest.config.AddService.service=Service
oscarEncounter.oscarConsultationRequest.config.AddService.btnAddService=Add Service
oscarEncounter.oscarConsultationRequest.config.AddService.msgServiceAdded=Service {0} has been added.
oscarEncounter.oscarConsultationRequest.config.AddService.serviceNameEmpty=Please enter a Service name

oscarEncounter.oscarConsultationRequest.config.AddSpecialist.msgDr=Dr.
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.msgMr=Mr.
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.msgMrs=Mrs.
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.msgMiss=Miss
oscarEncounter.oscarConsultationRequest.config.AddSpecialist.msgMs=Ms.
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.title=Adjust Service Providers
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.2ndTitle=Specialists
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.msgClickOn=Click on the specialists you would like to edit
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.msgCheckOff=Please check off all the specialists you would like to delete.
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.btnDeleteSpecialist=Delete Specialist
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.specialist=Specialist
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.address=Address
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.phone=Phone
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.fax=Fax
oscarEncounter.oscarConsultationRequest.config.EditSpecialists.Annotation=Annotation
oscarEncounter.oscarConsultationRequest.config.EditSpecialist.eform=Referral EForm

oscarEncounter.oscarConsultationRequest.config.ShowAllServices.title=Select Service
oscarEncounter.oscarConsultationRequest.config.ShowAllServices.2ndTitle=Select Service
oscarEncounter.oscarConsultationRequest.config.ShowAllServices.services=Services

oscarEncounter.oscarConsultationRequest.config.DeleteServices.title=Adjust Service Providers
oscarEncounter.oscarConsultationRequest.config.DeleteServices.2ndTitle=Services
oscarEncounter.oscarConsultationRequest.config.DeleteServices.msgCheckOff=Please check off all the services you would like to delete.
oscarEncounter.oscarConsultationRequest.config.DeleteServices.btnDeleteService=Delete Service
oscarEncounter.oscarConsultationRequest.config.DeleteServices.service=Service

oscarEncounter.oscarConsultationRequest.config.DisplayService.title=Adjust Service Providers
oscarEncounter.oscarConsultationRequest.config.DisplayService.msgCheckOff=Please check off all the specialist that offer {0}.
oscarEncounter.oscarConsultationRequest.config.DisplayService.btnUpdateServices=Update these Services Specialists
oscarEncounter.oscarConsultationRequest.config.DisplayService.specialist=Specialist
oscarEncounter.oscarConsultationRequest.config.DisplayService.address=Address
oscarEncounter.oscarConsultationRequest.config.DisplayService.phone=Phone
oscarEncounter.oscarConsultationRequest.config.DisplayService.fax=Fax

receptionist.receptionistfindprovider.title=SEARCH RESULTS
receptionist.receptionistfindprovider.2ndtitle=SEARCH PROVIDER
receptionist.receptionistfindprovider.keywords=Keyword(s):
receptionist.receptionistfindprovider.no=No
receptionist.receptionistfindprovider.lastname=Last Name
receptionist.receptionistfindprovider.firstname=First Name
receptionist.receptionistfindprovider.msgSelect=Please select by clicking on the No.
receptionist.receptionistfindprovider.btnExit=Exit

provider.providerpreference.msgMustBeNumber=You must type in a number in the field.
provider.providerpreference.msgPositivePeriod=Enter a positive Period less than Start minus End.
provider.providerpreference.msgStartHourErlierEndHour=Enter a Start Hour earlier than End Hour.
provider.providerpreference.msgHourLess24=Enter an End Hour less than 24.

oscarMessenger.DisplayMessages.msgStatusDel=deleted
oscarMessenger.DisplayMessages.msgStatusDeleted=deleted
oscarMessenger.DisplayMessages.msgStatusSent=sent
oscarMessenger.DisplayMessages.msgStatusNew=new
oscarMessenger.DisplayMessages.msgStatusRead=read
oscarMessenger.DisplayMessages.msgStatusUnread=unread
oscarMessenger.DisplayMessages.msgStatusReviewed=reviewed
oscarMessenger.DisplayMessages.msgStatusResponded=responded

oscarReport.oscarReportVisitControl.title=Visit Report
oscarReport.oscarReportVisitControl.btnManageProviderList=Manager Provider List
oscarReport.oscarReportVisitControl.msgTitle=Report
oscarReport.oscarReportVisitControl.msgSelectProvider=Select provider
oscarReport.oscarReportVisitControl.msgSelectProviderAll=All Providers
oscarReport.oscarReportVisitControl.msgServiceDateRange=Service Date-Range
oscarReport.oscarReportVisitControl.btnCreateReport=Create Report
oscarReport.oscarReportVisitControl.msgBeginDate=Begin Date
oscarReport.oscarReportVisitControl.msgEndDate=End Date

admin.providertemplate.formTemplateName=Template Name
admin.providertemplate.formTemplateText=Template Text

oscarMDS.index.btnSearch=Search
oscarMDS.index.btnClose=Close
oscarMDS.index.title=Lab Reports
oscarMDS.index.msgSelectOneLab=Please select at least one lab to be reassigned.
oscarMDS.index.btnSearchPatiente=Search
oscarMDS.index.btnClosePatient=Close
oscarMDS.index.btnForward=Forward
oscarMDS.index.btnFile=File
oscarMDS.index.btnDefaultView=Default View
oscarMDS.index.msgHealthNumber=Health Number
oscarMDS.index.msgPatientName=Patient Name
oscarMDS.index.msgSex=Sex
oscarMDS.index.msgResultStatus=Result Status
oscarMDS.index.msgDateTest=Date of Test
oscarMDS.index.msgDateCreated=Date Created
oscarMDS.index.msgOrderPriority=Order Priority
oscarMDS.index.msgRequestingClient=Requesting Client
oscarMDS.index.msgDiscipline=Discipline
oscarMDS.index.msgReportStatus=Report Status
oscarMDS.index.msgNoReports=--- no reports found matching the selected criteria ---
oscarMDS.index.msgPrevious=prev
oscarMDS.index.msgNext=next
oscarMDS.index.msgNewLabReportsFor=New Lab Reports for
oscarMDS.index.msgAcknowledgedLabReportsFor=Acknowledged Lab Reports for
oscarMDS.index.msgConfirmAcknowledge=Click OK to acknowledge
oscarMDS.index.msgConfirmAcknowledgeUnmatched=This lab has not been matched to a patient.Are you sure you want to acknowledge it?OK to acknowledge and Cancel to match to a patient
oscarMDS.index.msgAllLabReportsFor=All Lab Reports for
oscarMDS.index.msgAllPhysicians=all physicians
oscarMDS.index.msgUnclaimed=unclaimed
oscarMDS.index.msgNoMoreReports=--- no more reports found matching the selected criteria ---
oscarMDS.index.msgLabel = Label


oscarMDS.search.title=Search Lab Reports
oscarMDS.search.formPatientLastName=Patient Last Name
oscarMDS.search.formPatientFirstName=Patient First Name
oscarMDS.search.formPatientHealthNumber=Patient Health Number
oscarMDS.search.formPhysician=Physician
oscarMDS.search.formPhysicianAll=All
oscarMDS.search.formPhysicianUnclaimed=Unclaimed
oscarMDS.search.formReportStatus=Report status
oscarMDS.search.formReportStatusAll=All
oscarMDS.search.formReportStatusNew=New
oscarMDS.search.formReportStatusAcknowledged=Acknowledged
oscarMDS.search.btnSearch=Search

oscarMDS.forward.msgInstruction1=Please type the name of the provider you wish to forward to or select from your favorites on the left.
oscarMDS.forward.msgInstruction2=Double click a Provider to remove from list

tickler.ticklerMain.stActive=Active
tickler.ticklerMain.stComplete=Complete
tickler.ticklerMain.stDeleted=Deleted

oscarMessenger.config.MessengerAdmin.title=Group Configurator
oscarMessenger.config.MessengerAdmin.2ndTitle=Group Configurator
oscarMessenger.config.MessengerAdmin.msgCreateNewGroup=- Click here to create a new group on this Level
oscarMessenger.config.MessengerAdmin.msgGoToParent=- Click here to go the parent group
oscarMessenger.config.MessengerAdmin.msgChangeGroupsName=- Click here to change the current groups name
oscarMessenger.config.MessengerAdmin.msgExploreLowerLevel=All the groups In this Level - click to explore the lower level groups
oscarMessenger.config.MessengerAdmin.msgAllMembersChecked=All the Members that are in this group are checked.
oscarMessenger.config.MessengerAdmin.newGroup=New Group
oscarMessenger.config.MessengerAdmin.goBack=Go Back
oscarMessenger.config.MessengerAdmin.rename=Rename
oscarMessenger.config.MessengerAdmin.btnUpdateGroupMembers=Update group members
oscarMessenger.config.MessengerAdmin.btnDeleteThisGroup=Delete this group
oscarMessenger.config.MessengerAdmin.lastName=Last name
oscarMessenger.config.MessengerAdmin.firstName=First name
oscarMessenger.config.MessengerAdmin.providerType=Provider Type

oscarMessenger.config.MessengerCreateGroup.title=Group Configurator
oscarMessenger.config.MessengerCreateGroup.msgAddGroup=Add Group
oscarMessenger.config.MessengerCreateGroup.msgRenameGroup=Rename this group
oscarMessenger.config.MessengerCreateGroup.cancel=Cancel
oscarMessenger.config.MessengerCreateGroup.newGroupsName=New Group's Name:
oscarMessenger.config.MessengerCreateGroup.changeGroupsName=Change Group's Name:
oscarMessenger.config.MessengerCreateGroup.btnSubmit=Submit
oscarMessenger.config.MessengerCreateGroup.btnReset=Reset

oscarMDS.selectProvider.title=Select Provider
oscarMDS.selectProvider.msgSelectProvider=Select the provider(s)<br>who will receive the selected labs
oscarMDS.selectProvider.btnCancel=Cancel
oscarMDS.selectProvider.btnOk=Ok

oscarMDS.close.msgClose=Please click here to close this window

oscarMDS.segmentDisplay.olis.patientInfo=Patient
oscarMDS.segmentDisplay.olis.providerInfo=Provider
oscarMDS.segmentDisplay.olis.reportDetails=Report Details

oscarMDS.segmentDisplay.title=Lab Results
oscarMDS.segmentDisplay.msgComment=Please enter a comment (max. 255 characters)
oscarMDS.segmentDisplay.msgUnlink=Please enter a reason (max. 255 characters)
oscarMDS.segmentDisplay.btnAcknowledge=Acknowledge
oscarMDS.segmentDisplay.btnComment=Comment
oscarMDS.segmentDisplay.btnEChart=E-Chart
oscarMDS.segmentDisplay.btnUnlinkDemo=Unlink
oscarMDS.segmentDisplay.formDetailResults=Detail Results: Patient Info.
oscarMDS.segmentDisplay.formResultsInfo=Results Info
oscarMDS.segmentDisplay.formPatientName=Patient Name
oscarMDS.segmentDisplay.formDateBirth=Date of Birth
oscarMDS.segmentDisplay.formAge=Age
oscarMDS.segmentDisplay.formSex=Sex
oscarMDS.segmentDisplay.formHealthNumber=Health #
oscarMDS.segmentDisplay.formMDSIDNumber=MDS ID #
oscarMDS.segmentDisplay.formHomePhone=Home Phone
oscarMDS.segmentDisplay.formWorkPhone=Work Phone
oscarMDS.segmentDisplay.formPatientLocation=Patient Location
oscarMDS.segmentDisplay.formDateService=Date of Service
oscarMDS.segmentDisplay.formDateReceived=Date Lab Received
oscarMDS.segmentDisplay.formReportStatus=Report Status
oscarMDS.segmentDisplay.formReportOn=Reported On
oscarMDS.segmentDisplay.formClientRefer=Client Ref. #
oscarMDS.segmentDisplay.formAccession=Accession #
oscarMDS.segmentDisplay.formRequestingClient=Requesting Client
oscarMDS.segmentDisplay.formReportToClient=Report to Client
oscarMDS.segmentDisplay.formCCClient=cc: Client
oscarMDS.segmentDisplay.formTestName=Test Name(s)
oscarMDS.segmentDisplay.formProvider=Provider
oscarMDS.segmentDisplay.formResult=Result
oscarMDS.segmentDisplay.formAbn=Abn
oscarMDS.segmentDisplay.formReferenceRange=Reference Range
oscarMDS.segmentDisplay.formUnits=Units
oscarMDS.segmentDisplay.formDateTimeCompleted=Date/Time Completed
oscarMDS.segmentDisplay.formTestLocation=Test Location
oscarMDS.segmentDisplay.formNew=Status
oscarMDS.segmentDisplay.msgMicrobiology=MICROBIOLOGY
oscarMDS.segmentDisplay.msgCultureAndSensitivity=CULTURE AND SENSITIVITY
oscarMDS.segmentDisplay.msgORG=ORG
oscarMDS.segmentDisplay.msgAntibiotic=ANTIBIOTIC
oscarMDS.segmentDisplay.msgOrganism=ORGANISM
oscarMDS.segmentDisplay.msgReportEnd=END OF REPORT
oscarMDS.segmentDisplay.formAnnotate=Annotation

oscarMDS.segmentDisplay.patientSearch.title=Patient Matching
oscarMDS.segmentDisplay.patientSearch.formName=Name
oscarMDS.segmentDisplay.patientSearch.formPhone=Phone
oscarMDS.segmentDisplay.patientSearch.formDOB=DOB(yyyymmdd)
oscarMDS.segmentDisplay.patientSearch.formAddress=Address
oscarMDS.segmentDisplay.patientSearch.formHIN=HIN
oscarMDS.segmentDisplay.patientSearch.btnSearch=Search
oscarMDS.segmentDisplay.patientSearch.msgResults=Results based on keyword(s)
oscarMDS.segmentDisplay.patientSearch.msgPatientId=DEMOGP' ID
oscarMDS.segmentDisplay.patientSearch.msgLastName=LAST NAME
oscarMDS.segmentDisplay.patientSearch.msgFirstName=FIRST NAME
oscarMDS.segmentDisplay.patientSearch.msgAge=A'
oscarMDS.segmentDisplay.patientSearch.msgRosterStatus=ROSTER STATUS
oscarMDS.segmentDisplay.patientSearch.msgPatientStatus=PATIENT STATUS
oscarMDS.segmentDisplay.patientSearch.msgSex=X
oscarMDS.segmentDisplay.patientSearch.msgDOB=DOB(yy/mm/dd)
oscarMDS.segmentDisplay.patientSearch.msgDoctor=DOCTOR
oscarMDS.segmentDisplay.patientSearch.btnLastPage=Last Page
oscarMDS.segmentDisplay.patientSearch.btnNextPage=Next Page
oscarMDS.segmentDisplay.patientSearch.msgSearchMessage=Please select the patient corresponding to this lab
oscarMDS.segmentDisplay.btnApptHist=Appt History
oscarMDS.segmentDisplay.btnMaster=Master
oscarMDS.segmentDisplay.labResults=Lab Results

oscarReport.oscarReportVisitControl.msgVisitReport=Visit Report

oscarReport.oscarReportVisitControl.msgLarryKainReport=Larry Kain Report

oscarMessenger.ViewMessage.btnSearchAndWriteToEncounter=Search and Write to Encounter

oscarMessenger.ViewMessage.msgDemographicName=Demographic Name

oscarMessenger.ViewMessage.msgWriteThisMessageToEncounter=Write this Message to Encounter

oscarMessenger.ViewMessage.msgViewMessage=View Message

errors.codeNotFound=<li>{0} is an invalid {1} code</li>

oscar.admin.diseaseRegistryQuickList=Disease Registry Quick List

oscarResearch.oscarDxResearch.codingSystem=Coding System

oscarResearch.oscarDxResearch.quickList=Quick Lists

oscarResearch.oscarDxResearch.quickListItemsOf=Quick List Items of

oscarResearch.oscarDxResearch.dxCustomization.title=Customize Disease Registry Quick List

oscarResearch.oscarDxResearch.dxCustomization.addNewQuickList=Add New Quick List

oscarResearch.oscarDxResearch.dxCustomization.editQuickList=Edit Quick List

oscarResearch.oscarDxResearch.dxCustomization.editAssociations=Edit Associations

oscarResearch.oscarDxResearch.dxCustomization.selectAssociations=Select Associations

oscarResearch.oscarDxResearch.dxCustomization.selectQuickList=Select Quick List

oscarResearch.oscarDxResearch.dxCustomization.pleaseEnterTheNewQuickListName=Please enter the new quick list name

oscarResearch.oscarDxResearch.dxCustomization.pleaseSelectAQuickList=Please Select a Quick List

#oscarResearch.oscarDxResearch.btnAdd=Add
oscarResearch.oscarDxResearch.btnCodeSearch=Code Search
oscarResearch.oscarDxResearch.btnGO=GO

oscarResearch.oscarDxResearch.dxResearchCodeSearch.msgCodeSearch=Code Search

admin.admin.btnImportFormData=Import Form Data

demographic.search.noResultsWereFound=No results were found\!

demographic.record=Record

demographic.search.ccProvider.refineSearch=There are too many results. Please refine your search.
demographic.search.ccProvider.unableToAdd1=Clinicians or Organizations that cannot be found using above search are not set up with Lifelabs. As a result, they cannot be selected as part of an eOrder requisition at this time.
demographic.search.ccProvider.unableToAdd2=In order to appear in the search, the associated clinician/organization need to contact LifeLabs to be set up.
demographic.search.ccProvider.serviceUnavailable=There is a problem communicating with the Excelleris eOrder service. If issue persists, please revert to your usual process of generating lab requisitions outside of the eOrder module and report the error to your system administrator.

oscarSurveillance.Surveillance.title=Surveillance

oscarSurveillance.Surveillance.msgSurveillance=Surveillance
oscarEncounter.oscarMeasurements.Measurements.headingMax4Characters=max. 4 characters
oscar.oscarRx.hin=Health Ins.#
oscar.oscarRx.chartNo=Chart No.
oscar.oscarRx.demographicNo=Demographic #
oscar.oscarRx.bandNumber=Band Number (INAC)

#Displayed when hin already in database
demographic.demographicaddarecord.msgDuplicatedHIN=Error\: HIN already in use
demographic.demographicaddarecord.msgDuplicatedPHR=Error\: PHR username already in use
demographic.demographicaddarecord.msgDemoWithoutHin=Error\: Demographic was saved without a HIN

#eform messages
eform.errors.file_name.missing=<li>Error\: The form name field is blank</li>
eform.errors.form_name.missing.regular=Error\: The form name field is blank
eform.errors.form_html.missing=<li>Error\: The filename is missing</li>
eform.errors.form_name.exists=<li>Error\: The form name "{0}" is already used</li>
eform.errors.form_name.exists.regular=Error\: The form name is already used
eform.errors.upload.failed=Error\: Upload failed, check the file
eform.errors.submit_eorder.failed=Error submitting eOrder: {0} If issue persists, please revert to your usual process of generating lab requisitions outside of the eOrder module and report the error to your system administrator.
eform.errors.generate_eorder_barcode.failed=The order was succesfully submitted. The barcode could not be generated. Use the Print button to view and print the generated Lab Requisition from eOrder service.
eform.errors.generate_eorder_pdf.failed=There was an error saving the lab req pdf. The order has been placed.
eform.errors.submit_eorder.failed=Error submitting eOrder: "{0}". Please revert to manual Order Entry and report error to your system administrator.
eform.errors.submit_eorder.failed=Error submitting eOrder: {0}. Please revert to manual order entry and report error to your system administrator.
eform.errors.submit_eorder.success=Electronic lab order has been submitted successfully. Please provide patient with a printed copy of requisition.
eform.errors.update_eorder.notprocessed=Order updated successfully. Please provide patient with a printed copy of requisition, or fax requisition to nearest collection centre.
eform.errors.update_eorder.processed=Patient visit is completed for the order you're trying to edit. Please issue a new requisition to the patient or contact our Customer Care Team to complete a test add-on.
eform.errors.submit_eorder.questionnaireNotSupported=Some of the test(s) ordered require a questionnaire to be answered. It is not supported by the current version of OSCAR. If issue persists, please revert to your usual process of generating lab requisitions outside of the eOrder module and report the error to your system administrator.
eform.errors.submit_eorder.answerQuestionnaire=Some of the test(s) ordered require a questionnaire to be answered. Please answer the following questions and resubmit the order.
eform.errors.submit_eorder.info_msg=Informational messages from eOrder service: "{0}".
eform.instruction.submit_questionnaire.answerQuestionnaire_1=You have selected the 
eform.instruction.submit_questionnaire.answerQuestionnaire_2=test. Please answer the following question(s) and resubmit the order.
eform.instruction.submit_questionnaire.answerRequired=Please answer the question before proceeding.
eform.instruction.submit_questionnaire.invalidInt=Please enter an integer value.

# requisition email template
eform.email.requisition.inform=Your eOrder requisition was submitted to Lifelabs; no need to fax or email it.<br/>Please inform patient they can now access their requisition on MyCareCompass. An email will be sent by LifeLabs to inform your patient.<br/>Print requisition upon patient\u2019s request.<br/> 
eform.email.requisition.do_not_fax_email=Your eOrder requisition was submitted to Lifelabs; no need to fax or email it.
eform.email.requisition.in_person_visit=For in-person visits: provide printed requisition to your patient, and inform patient that they will receive an email from your clinic with further instructions. \
<a href="#" onclick="window.open('excelleris/sampleEmail.jsp?firstname={0}&lastname={1}&orderid={2}', '', \
'height=500,width=700,location=no,scrollbars=yes,menubars=no,toolbars=no,resizable=yes,top=0,left=0');return false;">(click here to see sample email-popup window)</a>
eform.email.requisition.virtual_visit=For virtual visits: proceed as usual and inform your patient that they will receive an email from your clinic with further instructions. \
<a href="#" onclick="window.open('excelleris/sampleEmail.jsp?firstname={0}&lastname={1}&orderid={2}', '', \
'height=500,width=700,location=no,scrollbars=yes,menubars=no,toolbars=no,resizable=yes,top=0,left=0');return false;">(click here to see sample email-popup window)</a>
eform.email.requisition.no_email.in_person_visit=For in person visits - print paper req
eform.email.requisition.no_email.virtual_visit=For virtual visits - proceed as usual (do not fax or email req to Lifelabs, we already got your eOrder req)
eform.email.requisition.no_email.in_person_visit=For in person visits: provide printed requisition to your patient.
eform.email.requisition.no_email.virtual_visit=For virtual visits: proceed as usual.
eform.email.requisition.template=<p>Hi {0} {1}, </p>\
<p>You have received a new electronic requisition, identified with eOrder ID {2}.\
Please proceed to a LifeLabs location of your choice and provide a member of our staff with this eOrder ID.\
Lifelabs staff will be able to obtain your requisition based on this ID.</p>\
<p>To save time and avoid waiting in line, book your LifeLabs appointment online. \
<a href="https://locations.lifelabs.com/locationfinder" target="_blank">Book now</a></p>\
<p>You can also create a MyCareCompass (formerly <i>my results</i>&trade;) account to view your results and navigate your health online, \
at no charge. <a href="https://mycarecompass.lifelabs.com/" target="_blank">Create My Account</a></p>\
<p><b>Note:</b> to view your results in MyCareCompass, you will need a Lab Visit Number, provided to you during your lab appointment.</p>\
<p>MyCareCompass is a free, secure service brought to you by LifeLabs, offering more than results. \
You can book appointments and check-in online, with many more exciting features to come.</p>\
<p>You are receiving this email because you have given consent to your clinician to send you an \
email when the clinician has used an electronic record technology to transmit your laboratory requisition directly \
to LifeLabs. Please contact your clinic if you do not want to receive these emails in the future.</p> \
<p>The content of this email does not contain any private or confidential information. \
If you are not the intended recipient of this message, please delete this email and contact the associated clinic and/or organization.</p> 

#eform groups
eform.groups.name=eForm Groups
eform.groups.contents=eForms in group
eform.groups.removeFromGroup=Remove From Group
eform.groups.noFormsInGroup=No eForms in this group.
eform.groups.addNewGroup=Add New Group:
eform.groups.selectViewGroup=Select/View Group:
eform.groups.addToGroup=Add a form to group:
eform.groups.delGroupConfirm=Are you sure you want to delete this group?
eform.groups.noGroupMsg=Please Select a Group
eform.groups.delGroup=Delete Group
eform.groups.selectGroup=Select
eform.groups.addGroup=Add Group
eform.groups.page.view=View
eform.groups.page.viewAll=View All
eform.groups.page.selectDefault=-----View All-----
eform.groups.errors.blankGroupName=You must type in the group name.


#MSP Billing Validation Alert Properties
oscar.billing.CA.BC.billingBC.error.invalidAge=Invalid Service Code:{0} - Patient's Age:{1} not within range: {2}<br/>
oscar.billing.CA.BC.billingBC.error.invalidSex=Invalid Service Code:{0} - Patient's Gender:{1} must be: {2}<br/>
oscar.billing.CA.BC.billingBC.error.noMore00120= Cannot Bill For Individual Counseling - Maximum Available For Current YearR Exhausted<br/>
oscar.billing.CA.BC.billingBC.error.codeLastBilled = {0} days have passed since service code: {1} was billed<br/>
oscar.billing.CA.BC.billingBC.error.codeNeverBilled=service code {0} has never been billed for this patient<br/>
oscar.billing.CA.BC.billingBC.error.invalidtimeselection=Wrong times selected - Start time must be less than end time<br/>
oscar.billing.CA.BC.billingBC.error.codeCond=Can't bill service code {0}, service code {1} already billed for the current year<br/>

#Service Code Association
oscar.billing.CA.BC.billingBC.error.startTimeNeeded=Service Code: {0} requires a start time<br />
oscar.billing.CA.BC.billingBC.error.startTimeandEndNeeded=Service Code: {0} requires a start time and end time<br />
oscar.billing.CA.BC.billingBC.error.nulldxcodes=Please enter at least one diagnostic code<br/>
oscar.billing.CA.BC.billingBC.error.nullservicecode=Please enter a service code<br/>
oscar.billing.CA.BC.billingBC.error.invaliddxcode=Diagnostic code: {0} doesn't exist in database<br/>
oscar.billing.CA.BC.billingBC.error.invalidsvccode=Service code: {0} doesn't exist in database<br/>
oscar.billing.CA.BC.billingBC.error.assocexists=Operation unsuccessful, this service code: {0} is already associated<br/>
oscar.billing.CA.BC.billingBC.dxcode_svccode_assoc.dxcode=Diagnostic Code:
oscar.billing.CA.BC.billingBC.dxcode_svccode_assoc.step2=Step 2: Select Diagnostic Codes
oscar.billing.CA.BC.billingBC.dxcode_svccode_assoc.step1=Step 1: Select Service Code
oscar.billing.CA.BC.billingBC.dxcode_svccode_assoc.title=Service/Diagnostic Code Association Wizard
oscar.billing.CA.BC.billingBC.manageSVCDXAssoc.title=Associate Service/Diagnostic Codes
oscar.billing.CA.BC.billingBC.manageSVCDXAssoc.svc=Service Code
oscar.billing.CA.BC.billingBC.manageSVCDXAssoc.dx=Diagnostic Codes
oscar.billing.CA.BC.billingBC.manageSVCDXAssoc.options=Options

#Receive Payments
oscar.billing.CA.BC.billingBC.error.nonNumericAmount=The amount received must be a numeric value
oscar.billing.CA.BC.billingBC.error.zeroAmount=Amount Recieved must not equal zero

#WCB
oscar.billing.CA.BC.billingBC.wcb.error.formRequired=A WCB Form must be selected.
oscar.billing.CA.BC.billingBC.wcb.error.wcbnotnumeric=WCB Claim Number must be numeric<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_empname=Please enter an employer name<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_opaddress=Please enter an operating address<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_address=Please enter an patient's address<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_opcity=Please enter an operating city<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_emparea=Employer area code must be numeric<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_empphone=Employer phone number must be numeric<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_lname=Please enter the patient's last name<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_fname=Please enter the patient's first name<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_dob=Please anter patient's date of birth<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_gender=Please enter patient's gender<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_phn=Please enter a numeric phn number<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_doi=Please enter a date of injury<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_diagnosis=Please enter a diagnosis<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_feeitem=Please enter a fee item<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_extrafeeitem=Please enter extra fee item<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_icd9=Please enter an icd9 code<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_noi=Please enter the nature of injury<br/>


oscar.billing.CA.BC.billingBC.wcb.error.w_patientDuration=Please enter how long the worker has been your patient.
oscar.billing.CA.BC.billingBC.wcb.error.w_priorProblem=Please enter prior problems
oscar.billing.CA.BC.billingBC.wcb.error.w_disabledFromWork=Please enter the data they were disabled from work.
oscar.billing.CA.BC.billingBC.wcb.error.w_ClinicalInfo=Please enter the clinic info

oscar.billing.CA.BC.billingBC.wcb.error.w_noi.numeric=Nature of Injury must be a numeric value<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_feeitem.numeric=WCB Fee Item must be a numeric value<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_extrafeeitem.numeric=MSP Fee Item must be a numeric value<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_icd9.numeric=ICD9 must be a numeric value <br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_bp.numeric=Body Part must be a numeric value <br/>


oscar.billing.CA.BC.billingBC.wcb.error.w_workdate=Please enter a date if the worker has been disabled from work<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_work=Please indicate whether the worker been disabled from work
oscar.billing.CA.BC.billingBC.wcb.error.w_capreason=Please enter the current physical and/or psychological restrictions<br/>
oscar.billing.CA.BC.billingBC.wcb.error.w_rehabtype=Please enter a rehabilitation program if the worker is now ready</b>
oscar.billing.CA.BC.billingBC.wcb.error.w_rphysician=Please indicate if you are the worker's regular physician
oscar.billing.CA.BC.billingBC.wcb.error.w_capability=Please indicate if the worker is now medically capable of working full duties, full time
oscar.billing.CA.BC.billingBC.wcb.error.w_rehab=Please indicate if the worker is now ready for a rehabilitation program
oscar.billing.CA.BC.billingBC.wcb.error.w_reportype=Please indicate if this is the Physician's First Report  or The worker's condition or treatment has changed
oscar.billing.CA.BC.billingBC.wcb.error.w_duration=Please indicate how long this person has been your patient
oscar.billing.CA.BC.billingBC.wcb.error.w_estimate=Please indicate estimated time before the worker will be able to return to the workplace.
oscar.billing.CA.BC.billingBC.wcb.error.w_wcbadvisor=Please indicate if you wish to consult with WCB physician or nurse advisor
oscar.billing.CA.BC.billingBC.wcb.error.w_tofollow=Please indicate if you would prefer Further Correspondence to Follow
oscar.billing.CA.BC.billingBC.wcb.error.enterfee=Please enter a WCB Fee or an Extra Fee Item

#receive payment strings
oscar.billing.CA.BC.title=Receive Payment
oscar.billing.CA.BC.invoice=Invoice# -
oscar.billing.CA.BC.lineNo=Line# -
oscar.billing.CA.BC.amount=Amount - $
oscar.billing.CA.BC.method=Payment Method -
oscar.billing.CA.BC.credit=Credited to
oscar.billing.CA.BC.received=Payment Received
oscar.billing.CA.BC.billingBC.receivePayment.error.amtReceived=Amount received must be numeric

#OHIP SLI Codes
oscar.billing.CA.ON.billingON.OB.SLIcode=SLI Code
oscar.billing.CA.ON.billingON.OB.SLIcode.NA=Not Applicable
oscar.billing.CA.ON.billingON.OB.SLIcode.Clinic=Clinic Number
oscar.billing.CA.ON.billingON.OB.SLIcode.HDS=HDS | Hospital Day Surgery
oscar.billing.CA.ON.billingON.OB.SLIcode.HED=HED | Hospital Emergency Department
oscar.billing.CA.ON.billingON.OB.SLIcode.HIP=HIP | Hospital In-Patient
oscar.billing.CA.ON.billingON.OB.SLIcode.HOP=HOP | Hospital Out-Patient
oscar.billing.CA.ON.billingON.OB.SLIcode.HRP=HRP | Hospital Referred Patient
oscar.billing.CA.ON.billingON.OB.SLIcode.IHF=IHF | Independant Health Facility
oscar.billing.CA.ON.billingON.OB.SLIcode.OFF=OFF | Office of community physician
oscar.billing.CA.ON.billingON.OB.SLIcode.OTN=OTN | Ontario Telemedicine Network
oscar.billing.CA.ON.billingON.OB.SLIcode.PDF=PDF | Private Diagnostic Facility
oscar.billing.CA.ON.billingON.OB.SLIcode.RTF=RTF | Rehabilitation Treatment Facility

oscar.billing.CA.ON.billingON.sobUpload.showCodesChangedPrices=Show codes with changed fees
oscar.billing.CA.ON.billingON.sobUpload.showNewCodes=Show new codes
oscar.billing.CA.ON.billingON.sobUpload.forceUpdate=Update all new and changed fees
oscar.billing.CA.ON.billingON.sobUpload.updateAssistantFees=Update assistant fees
oscar.billing.CA.ON.billingON.sobUpload.updateAnaesthetistFees=Update anaesthetist fees

oscar.billing.CA.ON.3rdpartyinvoice.printDate=Print Date
oscar.billing.CA.ON.3rdpartyinvoice.dueDate=Due Date

issueAdmin.saved=Issue has been saved successfully.
issueAdmin.missing=No issue found with this id.
issueAdmin.deleted=Issue successfully deleted.
issueAdmin.id=Issue Id
issueAdmin.description=Description
issueAdmin.code=Issue code
issueAdmin.role=role
issueAdmin.update_date=Issue update date
issueAdmin.code.exist=Issue code already exists.
issueAdmin.code.containsComma=Issue code cannot contain ','.

oscar.waitinglistname.wlNameUsed=The Waiting List name is currently being used.
oscar.waitinglistname.wlNameExists=The Waiting List name already exists.
oscar.waitinglistname.noSuchWL=There is no such Waiting List name.
oscar.waitinglistname.error=The system encountered unexpected error.
oscar.waitinglistname.createSuccess=The Waiting List name is created successfully.
oscar.waitinglistname.editSuccess=The Waiting List name is changed successfully.
oscar.waitinglistname.removeSuccess=The Waiting List name is removed successfully.

errors.billingReport.invalidDateFormat=Please use the date format: MM/dd/yyyy.
errors.billingReport.noPatient=Please enter patient name.
error.billingReport.invalidPatientName=Can't find patient by name.
error.billingReport.notSelectivePatientName=Found more than one patient by name.

oscarLab.System = System

oscarLearning.courseManager.title=OSCAR Learning - Course Manager
oscarLearning.courseManager.msgManager=Courses

oscarLearning.studentImport.title=OSCAR Learning - Student Data Importer
oscarLearning.studentImport.msgManager=Import

oscarLearning.courseView.title=OSCAR Learning - Course View
oscarLearning.courseView.msgManager=Courses

oscarEncounter.eyeform.diagnosticNotes.title=Diagnostic Notes
oscarEncounter.eyeform.pastOcularHistory.title=Past Ocular History
oscarEncounter.eyeform.patientLog.title=Patient Log
oscarEncounter.eyeform.ocularMedications.title=Ocular Medications
oscarEncounter.eyeform.currentHistory.title=Current History
global.viewOcularProcedure=Ocular Procedures
global.viewSpecsHistory=Specs History
global.viewConReport=Consultation Report

oscarEncounter.eyeform.simple.currentHistory.title=POH
oscarEncounter.eyeform.simple.pastOcularHistory.title=Drops
oscarEncounter.eyeform.simple.medicalHistory.title=PMH/meds
oscarEncounter.eyeform.simple.diagnosticNotes.title=Sx/Laser
oscarEncounter.eyeform.simple.conReport.title=Report
global.macro=Macro
global.viewAppointmentHistory=Appointment History
global.examinationHistory=Examination History
global.examTemplate=Exam Template

admin.faxStatus.fax=Fax
admin.faxStatus.faxStatus=Fax Status

admin.oscarStatus.oscar=Status
admin.oscarStatus.oscarStatus=OSCAR Status
admin.oscarStatus.restart=OSCAR Reboot / Server Reboot

admin.admin.reconciliationReport=Reconciliation
provider.providerchangepassword.msgPasswordLengthError=The password should contain 6 to 10 characters.
provider.providerchangepassword.msgPasswordLength=at least 6 characters
oscarEncounter.oscarConsultationRequest.consultationFormPrint.msgTimeHeure=dms.documentReport.msgShareFolderPartager\=folder
oscarEncounter.formFemaleAnnual.msgSeeChar=See the chart
demographicue.demographicaddarecord.msgSuccessful=Demographic added
provider.setDefaultSex.msgSubmit=Set
oscar.appt.ApptStatusData.msgSignend.N.oShow=No Show/Signed
oscarEncounter.Index.btnUnlink=Remove Issue

eFormGenerator.fax=Fax
eFormGenerator.faxDescription=Include options for faxing to this eForm.
eFormGenerator.title=OSCAR eForm Generator
eFormGenerator.expandAll=Expand All
eFormGenerator.collapseAll=Collapse All
eFormGenerator.expand=Expand
eFormGenerator.collapse=Collapse
eFormGenerator.loadImage=Load Image
eFormGenerator.imageChooseSelect=choose an image
eFormGenerator.imageUploadPrompt=If the picture does not appear on the list
eFormGenerator.imageUploadLink=upload it.
eFormGenerator.imageOrientation=Orientation of eForm:
eFormGenerator.imagePortrait=Portrait (image width should be 1500 pixels, resized to 750 pixels on screen)
eFormGenerator.imageLandscape=Landscape (image width should be 2000 pixels, resized to 1000 pixels on screen)
eFormGenerator.imageCustom=Custom
eFormGenerator.imageEnterInteger=(enter an integer)
eFormGenerator.imageLoadButton=Load Image
eFormGenerator.image.RedOutlinehint=If the eForm image extends past the red outline, you've cropped the image too long and it won't fit on a letter-sized printout. Try typing a number smaller than 750 in the "Custom" field.
eFormGenerator.eFormName=eForm Name
eFormGenerator.nameInstruction=Enter a name for the eForm here
eFormGenerator.gender=Gender and Radio Checkboxes
eFormGenerator.genderCheckbox=Gender checkboxes used in this eForm? If yes, click here
eFormGenerator.genderMale=Male
eFormGenerator.genderMaleButton=Click this, then click the top left corner of the male checkbox
eFormGenerator.genderFemale=Female
eFormGenerator.genderFemaleButton=Click this, then click the top left corner of the female checkbox
eFormGenerator.signature=Signatures
eFormGenerator.signatureCheckbox=Add Signature to this form
eFormGenerator.signatureLoad=Automatically load user's signature images
eFormGenerator.signatureFragment=User Name(i.e. oscarDB=current_user) - as a shortcut, you may enter a short segment of the name, as long as this segment is unique among all the users:
eFormGenerator.signatureImage=Corresponding signature image file:
eFormGenerator.signatureAddButton=Add to List
eFormGenerator.signatureEmptyButton=Empty List
eFormGenerator.signatureLocationButton=Click here, then drag a box around the signature area
eFormGenerator.signatureFreehand=Add a drawing area to "sign" a signature on the fly
eFormGenerator.input=Add in form input fields (one-by-one)
eFormGenerator.inputType=a) Select An Input Type
eFormGenerator.inputTypeText=Single-line text input
eFormGenerator.inputTypeTextArea=Multi-line text input
eFormGenerator.inputTypeCheckbox=Checkbox
eFormGenerator.inputAuto=b) Auto-populating Input Box
eFormGenerator.inputAutoCheck=Pre-check the checkbox
eFormGenerator.inputTypeCustom=Custom text
eFormGenerator.inputTypeData=From OSCAR Database
eFormGenerator.inputTypeDataButton=---NONE---
eFormGenerator.inputTypeMeasurements=Importing from Measurements
eFormGenerator.inputTypeMeasurementsType=Measurement Type
eFormGenerator.inputTypeMeasurementsButton=---NONE---
eFormGenerator.inputTypeMeasurementsCustom=or custom
eFormGenerator.inputTypeMeasurementsField=Field
eFormGenerator.inputTypeMeasurementsFieldButtonValue=Value
eFormGenerator.inputTypeMeasurementsFieldButtonDateObserved=Date Observed
eFormGenerator.inputTypeMeasurementsFieldButtonComment=Comment
eFormGenerator.inputFormat=c) Formating The Input Box
eFormGenerator.inputFormatFont=Font Family
eFormGenerator.inputFormatSelectSans=sans-serif
eFormGenerator.inputFormatSelectSerif=serif
eFormGenerator.inputFormatSelectMono=monospace
eFormGenerator.inputFormatStyle=Font Style
eFormGenerator.inputFormatStyleNormal=normal
eFormGenerator.inputFormatStyleItalic=italic
eFormGenerator.inputFormatWeight=Font Weight
eFormGenerator.inputFormatWeightBold=bold
eFormGenerator.inputFormatWeightBolder=bolder
eFormGenerator.inputFormatWeightLighter=lighter
eFormGenerator.inputFormatSize=Font Size
eFormGenerator.inputFormatSizehint=(in px, usually 12-14)
eFormGenerator.inputFormatAlign=Horizontal Alignment
eFormGenerator.inputFormatAlignLeft=Left
eFormGenerator.inputFormatAlignCenter=Center
eFormGenerator.inputFormatAlignRight=Right
eFormGenerator.inputFormatAlignJustify=Justify
eFormGenerator.inputFormatBackground=Background Color
eFormGenerator.inputFormatBackgroundTransparent=transparent
eFormGenerator.inputFormatBackgroundWhite=white
eFormGenerator.inputFormatBackgroundhint=Useful to have a white background to cover-up lines within the input field
eFormGenerator.inputName=c) Naming the input field:
eFormGenerator.inputNameSeq=i)Automatic Sequential Naming (quicker method for most cases).
eFormGenerator.inputNameSeqPrefix=automatic name prefix:(One Continuous Word)
eFormGenerator.inputNameSeqCustom=ii)Custom UNIQUE Name:
eFormGenerator.inputNameSeqCustomhint1=Must be one continuous word with letters/numbers only (no spaces/symbols)
eFormGenerator.inputNameSeqCustomhint2=Use custom naming to easily identify the corresponding html code if you're going to be editing the code later on
eFormGenerator.inputNameMeasurement=iii)If you would like to export the value of this input field to Measurements, select the Measurement Type and Field here:
eFormGenerator.inputNameMeasurementType=Measurement Type
eFormGenerator.inputNameMeasurementButton=---NONE---
eFormGenerator.inputNameMeasurementsCustom=or custom
eFormGenerator.inputNameMeasurementsField=Field
eFormGenerator.inputDraw=d) Drawing the input fields
eFormGenerator.inputDrawText=For one- and multi-lined textboxes:
eFormGenerator.inputDrawTexthint=Drag a box from the top left corner to the bottom right corner of the box.
eFormGenerator.inputDrawCheckbox=For checkboxes
eFormGenerator.inputDrawCheckboxhint=Click on the top left corner of the checkbox
eFormGenerator.inputDrawUndoButton=Undo
eFormGenerator.inputDrawhint=Repeat step # 3 until all input boxes are done.
eFormGenerator.tuning=Fine-tuning The Input Fields
eFormGenerator.tuningShowButton=Show/Hide Input Names
eFormGenerator.tuningUpButton=UP
eFormGenerator.tuningNoneButton=Uncheck All
eFormGenerator.tuningAllButton=Check All
eFormGenerator.tuningAlignButton=Align
eFormGenerator.tuningShiftButton=Shift
eFormGenerator.tuningNudgeButton=Nudge
eFormGenerator.tuningDeleteButton=Delete
eFormGenerator.tuningLeft=LEFT
eFormGenerator.tuningRight=RIGHT
eFormGenerator.tuningWidth=WIDTH
eFormGenerator.tuningIncreaseButton=Increase
eFormGenerator.tuningDecreaseButton=Decrease
eFormGenerator.tuningDown=DOWN
eFormGenerator.tuningHeight=HEIGHT
eFormGenerator.misc=Miscellaneous Options
eFormGenerator.miscMax=Maximize window when eForm loads.
eFormGenerator.miscMaxhint=Useful for lower resolution monitors.
eFormGenerator.miscCheckmarks=Emphasize Checkmarks
eFormGenerator.miscCheckmarksDraw="Drawing" in checkmarks during printing. Works for Firefox older than 3.5, longer code, and may not work for IE.
eFormGenerator.miscCheckmarksScale=Scaling up checkbox. (Works for Firefox 3.5 or newer, Safari (or similar WebKit) 3.1 or newer, IE 5-7 or IE 8 running compatibility mode, Opera 10.5)
eFormGenerator.generate=Generate eFormExpand/Collapse
eFormGenerator.generateLoadButton=Load HTML code in new window
eFormGenerator.generateResetButton=Start again
eFormGenerator.generateSaveButton=Save
eFormGenerator.generateRestoreSaveButton=Restore
eFormGenerator.generatehint1=The html code should open up in Edit eForm window.
eFormGenerator.generatehint2=Now you need to fill the fields shown (form name,Additional Information,etc):
eFormGenerator.generatehint3=Save the form by clicking Save button
eFormGenerator.generatehint4=DONE!!
eFormGenerator.page=Page No.
eFormGenerator.GenderCheckbox=Add Gender Checkboxes
eFormGenerator.GenderXbox=Add Gender Xboxes
eFormGenerator.freehand=Add Freehand Signature Fields
eFormGenerator.stamp=Add Signature Stamps to this form
eFormGenerator.ProSiganture=Add Pro Signature Stamp
eFormGenerator.classic=Add Classic Signature Box to this form
eFormGenerator.inputTypeXbox=Xbox
eFormGenerator.date=Date Validation
eFormGenerator.dateDescription=Add popup calendar to date or day datefields
eFormGenerator.BlackBox= Blackbox changes an X box into an entirely black mark.  Very high visibility even when using small Xbox.
eFormGenerator.PDFprint=PDF Print
eFormGenerator.includePDFprint=Include options for PDF printing this eForm.  Works with stamped signatures
eFormGenerator.precheck=Pre-check the checkbox
eFormGenerator.emptyInput=Please enter in a value for the custom input name field
eFormGenerator.duplicateName=Name already in use, please enter in another UNIQUE input name
eFormGenerator.loadFileAgain=Do you want to load the image:
eFormGenerator.Again=again?
eFormGenerator.date=Date Validation
eFormGenerator.dateDescription=Add calander date picker for fields labeled date or day.
eFormGenerator.radio=Radioboxes used in this eform? If yes, click here
eFormGenerator.radioCheckbox=Add Radio Checkboxes
eFormGenerator.radioLabel=Radio
eFormGenerator.radioButton=Click this, then click each radio checkbox
eFormGenerator.radioHint=Use an unique name for each series
eFormGenerator.parent=Parent-Child Checkboxes used in this eform? If yes, click here
eFormGenerator.parentCheckbox=AddParent Checkbox
eFormGenerator.parentButton=Click this, then click the parent checkbox
eFormGenerator.parentLabel=Parent
eFormGenerator.childLabel=Child
eFormGenerator.childButton=For each child input, click this, then the input
eFormGenerator.inputClass=Class
eFormGenerator.inputClassNone=None
eFormGenerator.inputClassParent=Parent
eFormGenerator.inputClassChild=Child (Specify Parent Below)
eFormGenerator.inputParentclass=Parent Class Name
eFormGenerator.faxnumber=Corresponding default fax number xxx-xxx-xxxx

#provider preferences
provider.pref.btnSave=Save Changes
provider.pref.title=User Preferences
provider.pref.section.general=General
provider.pref.section.scheduling=Scheduling
provider.pref.section.billing=Billing
provider.pref.section.encounter=Encounters
provider.pref.section.rx=Prescriptions
provider.pref.section.consultation=Consultations
provider.pref.section.phr=PHR
provider.pref.section.caisi=CAISI

provider.pref.general.title=General Settings
provider.pref.changepw.title=Change Password
provider.pref.changepw.current=Current Password
provider.pref.changepw.new=New Password
provider.pref.changepw.confirm=Confirm Password
provider.pref.general.fax=Fax Number
provider.pref.general.drugrefid=myDrugRef Id
provider.pref.general.signature=Signature
provider.pref.general.colour=Provider Colour
provider.pref.general.sex=Default Sex
provider.pref.general.hc_type=Default HC Type
provider.pref.general.workload=Workload Management

provider.pref.scheduling.title=Appointment/Scheduling Settings
provider.pref.scheduling.start_hour=Start Hour
provider.pref.scheduling.end_hour=End Hour
provider.pref.scheduling.period=Period (mins)
provider.pref.scheduling.group_no=Group No
provider.pref.scheduling.group_no.btn=Create/Edit

provider.pref.billing.title=Billing Settings
provider.pref.billing.diag_code=Default billing diagnostic code
provider.pref.billing.bc.referral_type=Default Referral Type
provider.pref.billing.bc.payee=Default Payee
provider.pref.billing.on.form=Default Billing Form
provider.pref.billing.search=Search

provider.pref.encounter.title=Encounter Settings
provider.pref.encounter.cme_ui=OSCAR CME UI
provider.pref.encounter.stale_date=Stale Date
provider.pref.encounter.eform_group=Favorite eForm group
provider.pref.encounter.form_length=Length of form name
provider.pref.encounter.forms=Display Encounter Forms
provider.pref.encounter.eforms=Display eForms

provider.pref.rx.title=Prescription Settings
provider.pref.rx.rx3=Use RX3
provider.pref.rx.qr=Print Qr codes
provider.pref.rx.page_size=Print Page Size
provider.pref.rx.dob=Include Patient DOB
provider.pref.rx.quantity=Default Quantity (RX3)

provider.pref.consult.title=Consultation Settings
provider.pref.consult.cutoff=Cutoff Time Period
provider.pref.consult.team=Warning Team
provider.pref.consult.paste=Paste Format

provider.pref.phr.title=PHR Settings
provider.pref.phr.id=PHR login Id
provider.pref.phr.mymeds=MyMeds

provider.pref.caisi.title=CAISI Settings
provider.pref.caisi.tickler_warning=New Tickler Warning Window
provider.pref.caisi.pmm=Default PMM
provider.pref.caisi.prev_billing=Do not delete previous billing

# General words
ADD=Add
REMOVE=Remove
NAME=Name
URL=Url

provider.cppPrefs=Configure eChart CPP
provider.olisPrefs=OLIS Preferences

appointment.searchnext.title=SEARCH RESULTS
appointment.searchnext.2ndtitle=SEARCH FOR NEXT AVAILABLE APPOINTMENT
appointment.searchnext.provider=Provider
appointment.searchnext.day_of_week=Day of Week
appointment.searchnext.time_of_day=Time of Day
appointment.searchnext.appt_type=Appointment type
appointment.searchnext.num_results=Number of results
appointment.searchnext.date=Date
appointment.searchnext.time=Time
appointment.searchnext.to=to

hrm.displayHRMDocList.reportTitle = HRM Report
hrm.displayHRMDocList.displaydocs = HRM Documents
hrm.displayHRMDocList.reportType = Report Type
hrm.displayHRMDocList.reportStatus = Report Status
hrm.displayHRMDocList.timeReceived = Received Date
hrm.displayHRMDocList.reportDate = Report Date
hrm.displayHRMDocList.description = Description
hrm.displayHRMDocList.subclass = Subclass

olis.olisSearch=OLIS Search
global.createLab=Create Lab


phr.verification.title=PHR Account Verification
phr.verification.heading=PHR Verification
phr.verification.add.fieldset.legend=Add Verification Method
phr.verification.add.fieldset.method=Method
phr.verification.add.fieldset.method.option.fax=Fax
phr.verification.add.fieldset.method.option.mail=Mail
phr.verification.add.fieldset.method.option.email=Email
phr.verification.add.fieldset.method.option.tel=Telephone
phr.verification.add.fieldset.method.option.videophone=Videophone
phr.verification.add.fieldset.method.option.inperson=In Person

phr.verification.add.date=Date
phr.verification.add.photoId=Photo Id
phr.verification.add.parentGuardian=Parent/Guardian
phr.verification.add.comments=Comments

phr.verification.table.heading=Past Verifications
phr.verification.table.username=Username
phr.verification.table.method=Method
phr.verification.table.date=Date
phr.verification.table.by=By
phr.verification.table.photoId=Photo Id
phr.verification.table.parentGuardian=Parent / Guardian
phr.verification.table.comments=Comments
phr.verification.table.Yes=Yes
phr.verification.link=PHR
phr.verification.addPatientRelationship=Add Patient Relationship
phr.verification.patientRelationshipExists=Patient Relationship Exists
phr.verification.notloggedin=Currently not logged into PHR. Cannot check patient relationship status
phr.verification.patient.not.respond=Patient will not be able to respond to messages


SendToPHR=Send to PHR
LoginToPHRFirst=Please login to PHR before perfoming this action
ItemsHaveBeenSentToPHR=The items have been sent to PHR
UnexpectedError=An unexpected error occurred, please try again or contact support
WarningNotVerified=Warning! This patient has not been verification in-person.
AreYouSureYouWantToSend=Are you sure you want to send
ToPHR=to PHR
ConfirmSending=Confirm Sending
Allergies=Allergies
Immunizations=Immunizations
Measurements=Measurements
Prescriptions=Prescriptions


provider.eRx.btnPrefLink=Set External Prescriber
provider.eRx.labelEnable=Enable
provider.eRx.labelURL=URL
provider.eRx.labelUser=Username
provider.eRx.labelPassword=Password
provider.eRx.labelFacility=Clinic Number
provider.eRx.labelTrainingMode=Training Mode
provider.eRx.labelEnableTitle=Enable the External Prescriber
SearchDrug.eRx.msgExternalPrescriber=External Prescriber

SearchDrug.drugref_therapeutic_class=search drug by therapeutic class
prescribe.label.therIntention=therapeutic Intention - One item per line, max 5 items.

demographic.demographiceditdemographic.ScannedEMR=Dossier num\u00e9ris\u00e9
SearchDrug.msgPrintLastScript=Imprimer
SearchDrug.msgPrintLastScript_ttt=Imprimer la derni\u00e8re ordonnance
report.reportindex.btnLabDaySheet=Lab Day Sheet
report.reportindex.btnBillingDaySheet=Billing Day Sheet
report.reportindex.btnCaisiReportingTools=CAISI Reporting Tools
report.reportindex.btnGeneralFormsReports=General Forms Reports
report.reportindex.btnRegistrationIntakeReport=Registration Intake Report
report.reportindex.btnFollowupIntakeReport=Follow-upIntake Report
report.reportindex.btnStreeHMHReport=Street Health Mental Health Report
report.reportindex.btnUserCreatedFormReport=User Created Form Report
report.reportindex.btnQuatroReportRunner=Quatro Report Runner
report.reportindex.btnActivityReport=Activity Report
report.preventionreporting.patientSet=Patient Set:
report.preventionreporting.preventionQuery=Prevention Query:
report.preventionreporting.asOf=As of:
report.preventionreporting.msgSetPatientQuery=There are no query templates. First, you have to create a template in Section 11 "Demographic Report Tool".
caseload.NoresultsfoundPleasetryadifferentsearch=No results found. Please try a different search.
caseload.LoadingResults=Loading results...
appointment.newAnonClient=Create new Anon Client
report.reportindex.btnClientListsReport=Client Lists Report
oscarRx.docRx.doc_web_fr=French document
oscarRx.docRx.doc_web_an=English Document
oscarRx.docRx.price_web_an=English price
oscarRx.docRx.price_web_fr=French price
admin.admin.createnewflowsheet=Create New Flowsheet
provider.hrmstatus=Hospital Report Manager (HRM) Status
provider.hrmpreference=Hospital Report Manager (HRM) Preferences
provider.hrmclassmapping=Hospital Report Manager (HRM) Class Mappings
provider.hrmcategories=Hospital Report Manager (HRM) Categories
casemanagementEntry.clientname=Client name:
casemanagementEntry.issueassociationview=Issue Association View: 
casemanagementEntry.Issue=Issue
casemanagementEntry.Acute=Acute
casemanagementEntry.Certain=Certain
casemanagementEntry.Major=Major
casemanagementEntry.Resolved=Resolved
casemanagementEntry.Type=Type
casemanagementEntry.activecommunityissue=Active Community Issue
casemanagementEntry.progressnoteentryview=Progress Note Entry View
casemanagementEntry.notenotsavedyet=this note has not been saved yet!
casemanagementEntry.spellcheck=Spell Check
casemanagementEntry.encountertype=Encounter Type:
casemanagementEntry.facetofaceencounterwithclient=face to face encounter with client
casemanagementEntry.telephoneencounterwithclient=telephone encounter with client
casemanagementEntry.telephoneencounterweekdays=Telephone encounter weekdays 8am-6pm
casemanagementEntry.telephoneencounterweekends=Telephone encounter weekends or 6pm-8am
casemanagementEntry.encounterwithoutclient=encounter without client
casemanagementEntry.Sign=Sign
casemanagementEntry.includecheckedissuesinnote=include checked issues in note
casemanagementEntry.billing=Billing:
casemanagementEntry.password=Password:

oscarEncounter.oscarMeasurements.oldmesurementindex=Old Measurements Index
oscarEncounter.oscarMeasurements.typedescription=Type Description
oscarencounter.templateflowsheet.showhide=Show/hide

# Ajout Etienne 
oscarMessenger.DisplayMessages.msgMessengerrelated=Message related to  
oscarMessenger.DisplayMessages.btnunlinkmessage=Unlink message
eform.showmyform.btnsendtophr=Send to PHR
dms.documentReport.msgPrivateDocuments=Private document
dms.documentReport.all=All
oscarprevention.preventionlistmanager.title=Prevention List Manager
oscarprevention.addpreventiondata.comments=Comments
oscarprevention.addpreventiondata.setnextdate=Set next date
oscarprevention.addpreventiondata.nextdate=Next date:
oscarprevention.addpreventiondata.neverremind=Never remind:
oscarprevention.addpreventiondata.reason=Reason:
oscarprevention.addpreventiondata.btnsave=Save
oscarprevention.addpreventiondata.location=Location:
oscarprevention.addpreventiondata.resultat=Result
oscarprevention.addpreventiondata.name=Name:
oscarprevention.addpreventiondata.completed=Completed
oscarprevention.addpreventiondata.refused=Refused
oscarprevention.addpreventiondata.ineligible=Ineligible
oscarprevention.addpreventiondata.provider=Provider:
oscarprevention.addpreventiondata.prevention=Prevention:
oscarprevention.index.oscarpreventiontitle=Preventions
oscarprevention.index.immunizationschedules=Immunization Schedules - Public Health Agency of Canada
oscarprevention.index.legend1=Legend:
oscarprevention.index.legend2=Completed or Normal
oscarprevention.index.legend3=Refused
oscarprevention.index.legend4=Ineligible
oscarprevention.index.legend5=Pending
oscarprevention.index.showhide=show/hide all other Preventions
oscarprevention.index.enableprint=Enable Print
oscarprevention.index.soustitre=Preventions
oscarprevention.index.alertwhenprint=You should check at least one prevention by selecting a checkbox next to a prevention
oscarprevention.index.preventionrecommendations=Prevention Recommendations 
oscarrx.showallergies.intolerance=Intolerance
oscarrx.showallergies.status=Status
oscarrx.showallergies.entrydate=Entry Date
oscarrx.showallergies.allergytype=Allergy Type
oscarrx.showallergies.severity=Severity
oscarrx.showallergies.onsetofreaction=Onset of Reaction
oscarrx.showallergies.reaction=Reaction
oscarrx.showallergies.startdate=Start Date
oscarrx.showallergies.lifestage=Life Stage
oscarrx.showallergies.view=view:
oscarrx.showallergies.all=All
oscarrx.showallergies.mild=Mild
oscarrx.showallergies.moderate=Moderate
oscarrx.showallergies.severe=Severe
oscarrx.showallergies.customallergy=Custom Allergy/Adverse Reaction
oscarrx.showallergies.searchcat=Search the following categories
oscarrx.showallergies.listedgen=(Listed general to specific)
oscarrx.showallergies.searchfielmissing=Search field is missing
oscarrx.showallergies.drugclasses=Drug Classes
oscarrx.showallergies.ingredients=Ingredients
oscarrx.showallergies.genericnames=Generic Names
oscarrx.showallergies.brandname=Brand Names
oscarrx.showallergies.btnselectall=Select All
oscarrx.showallergies.btnbacktosearch=Back to Search Drug
oscarrx.listdrugs.entereddate=Entered Date
oscarrx.listdrugs.daystoexp=Days to Exp
oscarrx.chartdrugprofile.btnaddmedtograph=Add Meds to Graph
oscarrx.chartdrugprofile.medlist=Med List
oscarencounter.guidelinelist.youcurrently=You currently have the following tips
oscarencounter.guidelinelist.demographicno=Demographic No:
oscarencounter.guidelinelist.title=Title
oscarencounter.guidelinelist.author=Author
oscarencounter.guidelinelist.dateimported=Date Imported
oscarencounter.guidelinelist.evaluated=Evaluated
oscarencounter.guidelinelist.passed=Passed
oscarencounter.guidelinelist.active=Active
oscarencounter.guidelinelist.failedon=Failed on
oscarencounter.guidelinelist.failed=Failed
oscarencounter.guidelinelist.moreinfo=More Info
oscarencounter.guidelinedetail.guidelineassessment=Guideline assessment:
oscarrx.showallergies.operator=Operator
oscarrx.showallergies.expected=Expected
oscarrx.showallergies.actual=Actual
oscarrx.showallergies.evaluate=Evaluate
oscarencounter.guidelinelist.fail=Fail
oscarencounter.guidelinedetail.error=Error: Cannot get patient data
oscarencounter.guidelinelist.invalid=- invalid definition
oscarencounter.guidelinedetail.btnlistguideline=List Guidelines
hrm.displayHRMDocList.category=Category
oscarencounter.templateflowsheet.showhide=Show/hide
oscarencounter.templateflowsheet.print=Print
oscarencounter.templateflowsheet.addall=Add All
oscarencounter.templateflowsheet.addoverdue=Add Overdue
oscarencounter.templateflowsheet.currentpatientcomorbiddxlist=Co-morbid Dx List
oscarencounter.templateflowsheet.currentpatientdxlist=Current Patients Dx List
oscarencounter.templateflowsheet.currentpatientrxlist=Current Patients Rx List
oscarencounter.templateflowsheet.currentpatientallergylist=Current Patients Allergy List
oscarencounter.healthtracker.skipdiagnosis=Skip Diagnosis
oscarencounter.oscarMeasurements.hasnt=hasn't been reviewed in
oscarencounter.oscarMeasurements.month=month
oscarwaitinglist.displayPatientWaitingList.waitinglist=Waiting List
ticklerplus.ticklerlist.client=Client:
ticklerplus.ticklerlist.filterticklerlist=Filter Tickler List
ticklerplus.ticklerlist.status=Status:
ticklerplus.ticklerlist.program=Program:
ticklerplus.ticklerlist.all=All
ticklerplus.ticklerlist.active=Active
ticklerplus.ticklerlist.cpmpleted=Completed
ticklerplus.ticklerlist.deleted=Deleted
ticklerplus.ticklerlist.begindate=Begin Date:			
ticklerplus.ticklerlist.enddate=End Date:
ticklerplus.ticklerlist.provider=Provider:
ticklerplus.ticklerlist.taskassignedto=Task Assigned To:
ticklerplus.ticklerlist.customfilters=Custom Filters:
ticklerplus.ticklerlist.printpreview=Print Preview
ticklerplus.ticklerlist.closewindows=Close Window
ticklerplus.ticklerlist.complete=Complete
ticklerplus.ticklerlist.delete=Delete
ticklerplus.ticklerlist.allprograms=All Programs
ticklerplus.ticklerlist.allproviders=All Providers
ticklerplus.ticklerlist.demographicname=Demographic Name
ticklerplus.ticklerlist.providername=Provider Name
ticklerplus.ticklerlist.priority=Priority
ticklerplus.ticklerlist.ticklersfound=ticklers found
ticklerplus.ticklerlist.youmust=You must choose at least 1 tickler
ticklerplus.ticklerlist.createreport=Create Report
ticklerplus.ticklerlist.legend1=Priority Legend:
ticklerplus.ticklerlist.legend2=High
ticklerplus.ticklerlist.legend3=Normal or low
ticklerplus.header.title=TicklerPlus
ticklerplus.customfilterlist.title=Custom Filter
ticklerplus.customfilterlist.name=Name
ticklerplus.customfilterlist.shortcut=Shortcut
ticklerplus.customfilterlist.backtoticklres=Back to Ticklers
ticklerplus.customfilterlist.new=New
ticklerplus.customfilterlist.delete=Delete
ticklerplus.customfilterlist.add=Add
ticklerplus.customfilterlist.remove=Remove
ticklerplus.customfilterform.title=Create New Custom Filter
ticklerplus.customfilterform.updatecustomfilter=Update Custom Filter
ticklerplus.customfilterform.filtername=Filter Name:
ticklerplus.customfilterform.demographic=Demographic:
ticklerplus.customfilterform.enddate=End Date:
ticklerplus.customfilterform.startdate=Start Date:
ticklerplus.customfilterform.status=Status:
ticklerplus.customfilterform.priority=Priority:
ticklerplus.customfilterform.program=Program:
ticklerplus.customfilterform.allprogram=All Program:
ticklerplus.customfilterform.provider=Provider:
ticklerplus.customfilterform.taskassignedto=Task assigned to:
ticklerplus.customfilterform.save=Save
ticklerplus.customfilterform.cancel=Cancel
ticklerplus.customfilterform.search=Search
ticklerplus.customfilterform.youmustname=You must provider a filter name.
ticklerplus.customfilterform.youshould=You should provide patient information by using the search button
ticklerplus.customfilterform.youmustbegindate=You must provide a valid start date
ticklerplus.customfilterform.youmustenddate=You must provide a valid end date
ticklerplus.ticklerform.servicedate=Service Date:
ticklerplus.ticklerform.servicetime=Service Time:
ticklerplus.ticklerform.demographic=Demographic:
ticklerplus.ticklerform.priority=Priority:
ticklerplus.ticklerform.taskassignedto=Task Assigned To:
ticklerplus.ticklerform.status=Status:
ticklerplus.ticklerform.save=Save
ticklerplus.ticklerform.cancel=Cancel
ticklerplus.ticklerform.select=- select -
ticklerplus.ticklerform.createnewtickler=Create New Tickler
ticklerplus.ticklerform.youmustassigntask=You must assign the task to a valid provider
ticklerplus.ticklerform.youmustservicedate=You must provide a valid service date
ticklerplus.ticklerform.youmustprovideamessage=You must provide a message
demographic.demographiccohort.currentpatientset=Current Patient Set(s)
demographic.demographiccohort.addtopatientset=Add to Patient Set:
demographic.demographiccohort.newpatientset=New Patient Set:
demographic.demographiccohort.save=Save
demographic.demographiccohort.saved=Saved
oscarreport.reportbytemplate=Report By Template
demographic.demographiccohort.to=to
ManagePharmacy.txtfld.label.serviceLocationIdentifier=Identificator
oscarrx.editfavorites.favorites=Favorites
oscarrx.editfavorites.favoritename=Favorite Name:
oscarrx.editfavorites.btnbacktosearchdrug=Back to Search For Drug
oscarrx.editfavorites.brandname=Brand Name:
oscarrx.editfavorites.genericname=Generic Name:
oscarrx.editfavorites.savechanges=Save Changes
oscarrx.editfavorites.deletefavorite=Delete Favorite
oscarrx.editfavorites.take=Take:
oscarrx.editfavorites.to=to
oscarrx.editfavorites.for=For:
oscarrx.editfavorites.quantity=Quantity:
oscarrx.editfavorites.repeats=Repeats:
oscarrx.editfavorites.nosubs=No subs:
oscarrx.editfavorites.specialinstructions=Special Instructions:
oscarrx.editfavorites.custominstructions=Custom Instructions:
oscarrx.editfavorites.backtosearchfordrug=Back to Search For Drug
oscarrx.editfavorites.msgchangessaved=Changes saved!
oscarrx.editfavorites.customdrugname=Custom Drug Name:
oscarrx.chooseallergy2.search=Search
oscarrx.chooseallergy2.searchthefollowingcategory=Search the following categories:
oscarrx.chooseallergy2.listed=(Listed general to specific)
oscarrx.chooseallergy2.drugclasses=Drug Classes
oscarrx.chooseallergy2.ingredients=Ingredients
oscarrx.chooseallergy2.genericnames=Generic Names
oscarrx.chooseallergy2.brandnames=Brand Names
oscarrx.chooseallergy2.selectall=Select All
oscarrx.chooseallergy2.clearall=Clear All
oscarrx.chooseallergy2.serachreturnednoresuts=Search returned no results. Revise your search and try again.
oscarrx.chooseallergy2.atcclass=ATC Class
oscarrx.chooseallergy2.ahfsclass=AHFS Class
oscarrx.chooseallergy2.compound=Compound
oscarrx.chooseallergy2.backtoviewallergies=Back to View Allergies
appointment.appointmentedit.cut=Cut
appointment.appointmentedit.copy=Copy
appointment.appointmentedit.paste=Paste
appointment.appointmenteditrepeatbooking.title=Repeat Booking
appointment.appointmenteditrepeatbooking.howoften=How often?
appointment.appointmenteditrepeatbooking.every=Every
appointment.appointmenteditrepeatbooking.endon=End on
provider.providerpreference.editdefaulbillingdiagnosticcode=Edit Default Billing Diagnostic Code
provider.providerpreference.expandedtokens=(expanded tokens in the url are
provider.providerpreference.and=and
ddmmyyyy=(dd/mm/yyyy)
yyyy-mm-dd=yyyy-mm-dd
day=Day
week=Week
month=Month
year=Year
enabled=Enabled
disabled=Disabled
disable=Disable
enable=Enable
Search=Search
Close=Close
edit=Edit
disable=Disable
name=Name
dob=DOB
age=Age
checkall=Check All
checknone=Check None
export=Export
print=Print
duration=Duration
location=Location
reason=Reason
save=Save
firstname=First Name
role=Role
provider.providerpreference.newticklerwarningwindows=New Tickler Warning Windows
ManagePharmacy.txtfld.label.serviceLocationIdentifier=Identificator
SelectPharmacy.deleteLink=Delete
lab.ca.all.testUploader.uploadTheLab=Upload the lab
lab.ca.all.testUploader.pleaseSelectTheLabfile=Please select the lab file
lab.ca.all.testUploader.labType=Lab type:
lab.ca.all.testUploader.pleaseSpecifyTheOtherLabType=Please specify the other lab type
lab.ca.all.testUploader.labUploadUtility=Lab Upload Utility
oscarreport.reportbytemplate=Model Report
demographic.enrollementhistory.patientinformation=Patient Information
demographic.enrollementhistory.patientenrollementhistory=Patient Enrollment History
demographic.enrollementhistory.datechanged=Date Changed
demographic.enrollementhistory.rosterlist=Roster Status
demographic.enrollementhistory.rosterto=Rostered To
demographic.enrollementhistory.updatedby=Updated By
demographic.enrollementhistory.system=System
demographic.enrollementhistory.current=(Current)
demographic.enrollementhistory.terminationreason=Termination Reason
demographic.enrollementhistory.Rostered=Rostered
demographic.enrollementhistory.notrostered=Not Rostered
demographic.enrollementhistory.terminated=Terminated
demographic.enrollementhistory.feeforservice=Fee For Service
demographic.enrollementhistory.notset=<Not Set>
demographic.demographicexport.title=Demographic Export
demographic.demographicexport.exportcategories=Export Categories:
demographic.demographicexport.personalhistory=Personal History
demographic.demographicexport.familyhistory=Family History
demographic.demographicexport.pasthealth=Past Health
demographic.demographicexport.problemlist=Problem List
demographic.demographicexport.riskfactors=Risk Factors
demographic.demographicexport.allergiesadversereaction=Allergies & Adverse Reactions
demographic.demographicexport.immunization=Immunizations
demographic.demographicexport.medicationstreatments=Medications Treatments
demographic.demographicexport.laboratoryresults=Laboratory Results
demographic.demographicexport.appointments=Appointments
demographic.demographicexport.clinicalnotes=Clinical Notes
demographic.demographicexport.reportsreceived=Reports Received
demographic.demographicexport.careelements=Care Elements
demographic.demographicexport.alertsandspecialneeds=Alerts And Special Needs
demographic.demographicexport.progressSheets=Progress Sheets
demographic.demographicexport.msgsorry=Sorry! Only administrators can export demographics.
demographic.demographicexport.msgerror=Error! Cannot perform demographic export. Please contact support.
demographic.demographicexport.diabetesexport=Diabetes Export
demographic.demographicexport.cihiexport=CIHI Export
demographic.demographicexport.rourke2009export=Rourke 2009 Export
demographic.demographicexport.exportingdemographicno=Exporting Demographic No. 
demographic.demographicexport.patientset=Patient Set:
demographic.demographicexport.selectset=--Select Set--
demographic.demographicexport.exporttemplate=Export Template:
demographic.demographicexport.msgwarning=WARNING: PGP Encryption NOT available - cannot export!
demographic.demographicaddrecordhtm.cbselectwaitinglist=--Select Waiting List--
casemgmt.ongoingconcerns=Ongoing Concerns
admin.provideraddrole.rolename=Role Name
admin.provideraddrole.title2=Type in a role name and search it first to see if it is available.
admin.provideraddrole.msgyoumusttype=You must type in a role name.
appointment.addappointment.title2=Appointment Types
appointment.addappointment.editappointmenttype=EDIT APPOINTMENT TYPE
admin.providerrole.title2=Provider-Role List



# Fin Etienne

demographic.demographiceditdemographic.msgCopy=Copy Patient Info in new chart
demographic.search.importNewDemographic=Import Demographic
oscarMDS.createLab.laboratoryInformation=Laboratory Information
oscarMDS.createLab.labName=Lab Name
oscarMDS.createLab.accession=Accession #
oscarMDS.createLab.labReqDate=Lab Req Date/Time
oscarMDS.createLab.patientInformation=Patient Information
oscarMDS.createLab.orderingProvider=Ordering Provider
oscarMDS.createLab.lastname=Last Name
oscarMDS.createLab.firstname=First Name
oscarMDS.createLab.dob=DOB
oscarMDS.createLab.hin=NAM
oscarMDS.createLab.phone=Phone
oscarMDS.createLab.add=ADD
oscarMDS.createLab.submitOscar=Submit to OSCAR
oscarMDS.createLab.billingNum=Billing #

demographic.demographiceditdemographic.prefPharmaSectionName=Favorite pharmacy
demographic.demographiceditdemographic.prefPharmaSectionTitle=Modified from the prescriber.
demographic.demographiceditdemographic.noPrefPharma=No pharmacy was selected favorite. It is modified from the prescriber.
demographic.demographiceditdemographic.prefPharmaName=Name
demographic.demographiceditdemographic.prefPharmaTel=Phone
demographic.demographiceditdemographic.prefPharmaFax=Fax
demographic.demographiceditdemographic.prefPharmaAdd=Address
demographic.demographiceditdemographic.prefPharmaMail=Email


oscarMDS.forward.msgInstruction1=Please type the name of the provider you wish to forward to or select from your favorites on the left.
oscarMDS.forward.msgInstruction2=Double click a Provider to remove from list
password.policy.violation.msgUsernameLengthError=Username needs a minimum of 8 characters

admin.admin.sessionTimeout=Set a session time out per user type
admin.admin.sessionTimeout.timeDelay=Session delay
admin.admin.sessionTimeout.timeDelayInfo=Delay is in minute ( 5 \u00e0 30).
admin.admin.sessionTimeout.doctor=Doctor
admin.admin.sessionTimeout.resident=Resident
admin.admin.sessionTimeout.midwife=Midwife
admin.admin.sessionTimeout.nurse=Nurse
admin.admin.sessionTimeout.receptionist=Receptionist
admin.admin.sessionTimeout.admin=Administrator
admin.admin.sessionTimeout.quatro=Quatro
admin.admin.sessionTimeout.msgTrue=saved ok with value 
admin.admin.sessionTimeout.msgFalse=failed with value 
admin.admin.sessionTimeout.update=Update 
admin.admin.demographicSensibility=Manage sensibles demographics

provider.selectClinicSite=Select Clinic Site
provider.clinicSite=Clinic Site 





# MARC-HI
demographic.demographiceditdemographic.patientDocuments=Patient Documents
demographic.demographiceditdemographic.demographicSharing=Demographic Sharing
demographic.demographiceditdemographic.demographicSharing.viewInfo=This patient is sharing demographic information with the following facilities
demographic.demographiceditdemographic.demographicSharing.editInfo=Select the external system to disable sharing with.

marc-hi.sendToAffinityDomain=Send to Affinity Domain

marc-hi.affinityDomains.addNew=Add New
marc-hi.affinityDomains.manageExisting=Manage Existing

marc-hi.affinityDomains.name=Name
marc-hi.affinityDomains.facilityName=Facility Name
marc-hi.affinityDomains.uniqueId=Unique Id
marc-hi.affinityDomains.policyUrl=Policy URL
marc-hi.affinityDomains.actors=Actors
marc-hi.affinityDomains.action=Action

marc-hi.affinityDomains.errors.noAffinityDomainsFound=There are no affinity domains to manage.
marc-hi.affinityDomains.errors.unableToDelete=There was an error deleting the affinity domain.
marc-hi.affinityDomains.errors.unableToRegisterDocument=Unable to register documents.
marc-hi.affinityDomains.errors.noDocuments=No documents to register.
marc-hi.affinityDomains.errors.unableToRegisterPatient=There was an error registering the patient.
marc-hi.affinityDomains.errors.unableToRegisterBppc=There was an error sending the sharing consent.

marc-hi.affinityDomains.success.deleted=The affinity domain was successfully deleted.
marc-hi.affinityDomains.success.documentRegistered=The documents have been successfully registered on the affinity domain.
marc-hi.affinityDomains.success.patientRegistered=The patient was successfully registered on the affinity domain.
marc-hi.affinityDomains.success.bppcRegistered=The patient's sharing consent document was sent successfully.

marc-hi.affinityDomains.edit=Edit
marc-hi.affinityDomains.delete=Delete

marc-hi.affinityDomains.yes=YES
marc-hi.affinityDomains.no=NO
marc-hi.affinityDomains.affinityDomainInformation=Affinity Domain Information
marc-hi.affinityDomains.actor=Actor
marc-hi.affinityDomains.actorName=Actor Name
marc-hi.affinityDomains.endPoint=Endpoint
marc-hi.affinityDomains.actorType=Actor Type
marc-hi.affinityDomains.binaryFormat=Binary Format
marc-hi.affinityDomains.localUniqueId=Local Unique Id
marc-hi.affinityDomains.localFacilityName=Local Facility Name
marc-hi.affinityDomains.remoteUniqueId=Remote Unique Id
marc-hi.affinityDomains.remoteFacilityName=Remote Facility Name
marc-hi.affinityDomains.remove=Remove
marc-hi.affinityDomains.save=Save
marc-hi.affinityDomains.affinityDomainNotFound=No affinity domains were found.
marc-hi.affinityDomains.affinityDomainActors=Affinity Domain Actors
marc-hi.affinityDomains.addActor=Add Actor
marc-hi.affinityDomains.shareAgreement=Share Patient Information
marc-hi.affinityDomains.shareSubmit=Share
marc-hi.affinityDomains.shareCancel=Cancel
marc-hi.affinityDomains.addNew=Add New
marc-hi.affinityDomains.manageExisting=Manage Existing
marc-hi.affinityDomains.eDocs=eDocs
marc-hi.affinityDomains.firstPolicyStatement=You are about to share patient information with an external system.
marc-hi.affinityDomains.secondPolicyStatement=Please read the policy document below and click the "Share" button.
marc-hi.affinityDomains.patientLabelName=Patient:
marc-hi.affinityDomains.decisionMakerLabelName=Decision Maker (Consent Giver):

# MARC-HI Headers
marc-hi.affinityDomains.headers.register=DOCUMENT REGISTRATION
marc-hi.affinityDomains.headers.affinityDomain=AFFINITY DOMAINS

marc-hi.patientDocuments.title=PATIENT DOCUMENTS
marc-hi.patientDocuments.links.selectAll=Select All
marc-hi.patientDocuments.table.colSelect=Select
marc-hi.patientDocuments.table.colTitle=Title
marc-hi.patientDocuments.table.colMimetype=Mimetype
marc-hi.patientDocuments.table.colAuthor=Author
marc-hi.patientDocuments.table.colTimeCreated=Time Created
marc-hi.patientDocuments.button.downloadSelected=Download Selected
marc-hi.patientDocuments.button.fetchDocuments=Fetch Documents

marc-hi.labels.affinityDomain=Affinity domain:
marc-hi.labels.confidentialityCode=Confidentiality Code:


# MARC-HI's Sharing Center
sharingcenter.title=Sharing Center

sharingcenter.affinitydomain.manage=Manage
sharingcenter.affinitydomain.mappings=Code/SVS Mappings

sharingcenter.clinicinfo.namespacelabel=Namespace ID
sharingcenter.clinicinfo=Clinic Info

sharingcenter.security=Security

sharingcenter.documents.shareddocuments=Shared Documents
sharingcenter.networks.sharingnetworks=Sharing Networks

consultationList.editSpecialists=Edit Specialists Listing
consultationList.addConsultation=Add Consultation Request
consultationList.editConsultation=Edit Consultation Record
consultationList.selectTeam=Select Team
consultationList.searchOptions=Search Options
consultationList.startDate=Start Date
consultationList.endDate=End Date
consultationList.referralDate=Referral Date
consultationList.appointmentDate=Appointment Date
consultationList.includeCompleted=Include Completed Requests
consultationList.urgency.urgent=Urgent
consultationList.urgency.non-urgent=Non-Urgent
consultationList.urgency.return=Return
consultationList.urgency.semi-urgent=Semi-Urgent
consultationList.status.nothing=Nothing
consultationList.status.specialistCallback=Specialist Callback
consultationList.status.patientCallback=Patient Callback
consultationList.status.completed=Completed
consultationList.status.preliminary=Preliminary
consultationList.header.action=Action
consultationList.header.patient=Patient
consultationList.header.service=Service
consultationList.header.consultant=Consultant
consultationList.header.urgency=Urgency
consultationList.header.team=Team
consultationList.header.status=Status
consultationList.header.mrp=MRP
consultationList.header.referralDate=Referral Date
consultationList.header.appointmentDate=Appointment Date
consultationList.header.followup=Last Followup
consultationList.btn.newConsult=Add New

system_message.saved=Saved

## FOR THE PHARMA CLINIC FORM BPMH (formBPMH)
colcamex.formBPMH.error.unknown=Unknown
colcamex.formBPMH.error.missing.provider=Missing Pharmacist's ID
colcamex.formBPMH.title=Best Possible Medication History (BPMH)
colcamex.formBPMH.preparedby=Prepared by:
colcamex.formBPMH.preparedon=Prepared on (dd/mm/yyy):
colcamex.formBPMH.printedon=Printed on (dd/mm/yyy):
colcamex.formBPMH.notes=Notes:
colcamex.formBPMH.print=print
colcamex.formBPMH.save=save
colcamex.formBPMH.confirm=Patient was asked and is taking the non-prescription or natural health products listed above, or otherwise not taking any at this time.
colcamex.formBPMH.patient=PATIENT
colcamex.formBPMH.patient.name=First and Last Name:
colcamex.formBPMH.patient.insurance=PHN:
colcamex.formBPMH.patient.dob=Date of Birth <span class="smallText">(dd/mm/yyy)</span>
colcamex.formBPMH.patient.gender=Gender:
colcamex.formBPMH.patient.phone=Phone #:
colcamex.formBPMH.patient.allergies=Known allergies and reactions:
colcamex.formBPMH.familyDr=FAMILY PHYSICIAN
colcamex.formBPMH.familyDr.name=Full Name:
colcamex.formBPMH.familyDr.phone=Phone #:
colcamex.formBPMH.familyDr.fax=Fax #:
colcamex.formBPMH.drugs=MEDICATIONS I TAKE
colcamex.formBPMH.drugs.sub=-Prescription, non-prescription, natural health products
colcamex.formBPMH.drugs.what=WHAT I TAKE
colcamex.formBPMH.drugs.what.sub=Name,strength & form of medication as noted on the prescription or medication package label
colcamex.formBPMH.drugs.how=HOW I TAKE IT
colcamex.formBPMH.drugs.how.sub=For eample, when to take it, take with/without food, warnings, etc.
colcamex.formBPMH.drugs.why=WHY I TAKE IT
colcamex.formBPMH.drugs.why.sub=Disease, condition or symptoms it addresses
colcamex.formBPMH.drugs.instructions=SPECIAL INSTRUCTIONS
colcamex.formBPMH.drugs.instructions.sub=(if applicable)
colcamex.formBPMH.formowner=UBC Pharmacists Clinic
colcamex.formBPMH.error.confirm=Confirmation not checked

global.uploadWarningHeader=Warning
global.uploadWarningBody=Do not upload this file unless you have confirmed that it is from a trusted source and have verified the content to be free of harmful content including viruses, malware and/or unknown external links.  Failure to complete this due diligence on uploaded files may compromise the security of OSCAR and the privacy of patient health information



prevention.message.needs.X.vaccine=Needs $PREVENTION_TYPE Vaccine
prevention.message.needs.first.X.vaccine=Needs First $PREVENTION_TYPE Vaccine
prevention.message.needs.second.X.vaccine=Needs Second $PREVENTION_TYPE Vaccine
prevention.message.needs.third.X.vaccine=Needs Third $PREVENTION_TYPE Vaccine
prevention.message.needs.fourth.X.vaccine=Needs Fourth $PREVENTION_TYPE Vaccine
prevention.message.needs.fifth.X.vaccine=Needs Fifth $PREVENTION_TYPE Vaccine
prevention.message.needs.six.X.vaccine=Needs Six $PREVENTION_TYPE Vaccine
prevention.message.needs.seven.X.vaccine=Needs Seven $PREVENTION_TYPE Vaccine
prevention.message.needs.eigtht.X.vaccine=Needs Eighth $PREVENTION_TYPE Vaccine
prevention.message.needs.nineth.X.vaccine=Needs Nineth $PREVENTION_TYPE Vaccine
prevention.message.needs.repeat.X.vaccine=Needs repeat $PREVENTION_TYPE Vaccine

prevention.message.coming.due=$PREVENTION_TYPE is coming due for this patient
prevention.message.overdue=$PREVENTION_TYPE is overdue for this patient
prevention.message.no.record.not.found=No $PREVENTION_TYPE records can be found for this patient
prevention.message.last.prevention.done=Last $PREVENTION_TYPE was done $NUMMONTHS month(s) ago

prevention.message.colonscopy.5years.old=Colonoscopy is over 5 years old
prevention.message.colonscopy.inelligible=Colonoscopy makes FOBT ineligible
prevention.message.need.to.ask.smoking=Need to ask about Smoking
prevention.message.need.bone.mbd.test=Need Bone Mineral Density Test
prevention.message.PHR.never.reviewed=Periodic Health Visit has never been reviewed
prevention.message.phv.occurred.over.year=Periodic Health Visit has not occurred in over a year

message.save=<img src="{0}/images/Information16x16.gif"> <font class="message">Saved Successfully!</font>
message.delete=<img src="{0}/images/Information16x16.gif"> <font class="message">Deleted Successfully!</font>

dashboard.dashboardmanager.title=Dashboard Manager
dashboard.dashboardmanager.import.browse=Browse...
dashboard.dashboardmanager.import.title=Indicator XML Template
dashboard.dashboardmanager.import.button=Import
dashboard.dashboardmanager.dashboard.create=Create Dashboard
dashboard.dashboardmanager.dashboard.edit=Edit Dashboard
dashboard.dashboardmanager.dashboard.name=Name
dashboard.dashboardmanager.dashboard.description=Description
dashboard.dashboardmanager.dashboard.active=Active
dashboard.dashboardmanager.dashboard.save=Save
dashboard.dashboardmanager.dashboard.close=Close

dashboard.dashboarddisplay.menu.title=Dashboard
dashboard.dashboarddisplay.page.title=Dashboard

yourcare.patientportal.registrationPending=Registration Pending
yourcare.patientportal.notRegistered=Not Registered
yourcare.patientportal.registered=Registered
yourcare.patientportal.unregister=Unregister
yourcare.patientportal.resendPortalInvite=Resend Portal Invite
yourcare.patientportal.sendPortalInvite=Send Portal Invite
yourcare.patientportal.patientPortal=Patient Portal
yourcare.patientportal.pushToPortal=Share To Portal

loadServices
