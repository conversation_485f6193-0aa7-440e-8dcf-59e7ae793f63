# SystemPreferences that indicate the estimated print times for each attachment type.
# Default values are taken from local times taken to print each attachment type.
# A 0 print weight means that printing multiple types of the attachment negligibly
# increases print times.
CALL insertSystemPreference('attachment_manager.print.threshold', '50');
CALL insertSystemPreference('attachment_manager.print_weight.lab', '0.5');
CALL insertSystemPreference('attachment_manager.print_weight.document', '0.5');
CALL insertSystemPreference('attachment_manager.print_weight.hrm', '0.5');
CALL insertSystemPreference('attachment_manager.print_weight.prevs', '0');
CALL insertSystemPreference('attachment_manager.print_weight.meds', '0');
CALL insertSystemPreference('attachment_manager.print_weight.notes', '0');
CALL insertSystemPreference('attachment_manager.print_weight.fam_his', '0');
CALL insertSystemPreference('attachment_manager.print_weight.med_his', '0');
CALL insertSystemPreference('attachment_manager.print_weight.ong_con', '0');
CALL insertSystemPreference('attachment_manager.print_weight.soc_his', '0');
CALL insertSystemPreference('attachment_manager.print_weight.ris_fac', '0');
CALL insertSystemPreference('attachment_manager.print_weight.reminder', '0');
CALL insertSystemPreference('attachment_manager.print_weight.othermed', '0');
CALL insertSystemPreference('attachment_manager.print_weight.eforms', '3');
CALL insertSystemPreference('attachment_manager.print_weight.antenata', '0.5');
CALL insertSystemPreference('attachment_manager.print_weight.edocs', '0.5');
CALL insertSystemPreference('attachment_manager.print_weight.form', '0.5');
CALL insertSystemPreference('attachment_manager.print_weight.smart_en', '0.5');
