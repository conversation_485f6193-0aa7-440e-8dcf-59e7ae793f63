# Remove truncated invalid JSON attachments
# CHAR_LENGTH(attachments) = 65535 was chosen to match
# the maximum length of a the attachments fields in the database
# and RIGHT(attachments, 1) != ']' to ensure that the JSON is incomplete
# out-of-spec systems with modified schemas that allow for larger attachments will not be affected

DELETE
FROM eform_values
WHERE var_name = 'attachment-control-printables'
  AND CHAR_LENGTH(var_value) = 65535
  AND RIGHT(var_value, 1) != ']';

UPDATE consultationRequests
SET attachments = ''
WHERE CHAR_LENGTH(attachments) = 65535
  AND RIGHT(attachments, 1) != ']';


UPDATE form_smart_encounter
SET attachments = ''
WHERE CHAR_LENGTH(attachments) = 65535
  AND RIGHT(attachments, 1) != ']';