<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"  isIgnorePagination="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Chartlabel2" pageWidth="350" pageHeight="75" columnWidth="330" leftMargin="0" rightMargin="0" topMargin="6" bottomMargin="0">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="demo" class="java.lang.String"/>
	<queryString>
		<![CDATA[select d.last_name, IFNULL(d.chart_no,'') AS chart_no, d.first_name, 
			IFNULL(d.address,'') AS address, IFNULL(d.city,'') AS city,
			IFNULL(d.province,'') AS province, IFNULL(d.postal,'') AS postal,
			IFNULL(d.phone,'') AS phone, IFNULL(d.phone2,'') AS phone2,
			d.year_of_birth, d.month_of_birth, d.date_of_birth,
			IFNULL(d.hin,'') AS hin, IFNULL(d.ver,'') AS ver, d.sex,
			CONCAT(p.last_name, ', ' ,p.first_name) AS doc_name
			from demographic d left outer join provider p on d.provider_no = p.provider_no
			where demographic_no =$P{demo}]]>
	</queryString>
	<field name="last_name" class="java.lang.String"/>
	<field name="chart_no" class="java.lang.String"/>
	<field name="first_name" class="java.lang.String"/>
	<field name="address" class="java.lang.String"/>
	<field name="city" class="java.lang.String"/>
	<field name="province" class="java.lang.String"/>
	<field name="postal" class="java.lang.String"/>
	<field name="phone" class="java.lang.String"/>
	<field name="phone2" class="java.lang.String"/>
	<field name="year_of_birth" class="java.lang.String"/>
	<field name="month_of_birth" class="java.lang.String"/>
	<field name="date_of_birth" class="java.lang.String"/>
	<field name="hin" class="java.lang.String"/>
	<field name="ver" class="java.lang.String"/>
	<field name="sex" class="java.lang.String"/>
	<field name="doc_name" class="java.lang.String"/>
	<title>
		<band height="65" splitType="Stretch">
			<textField>
				<reportElement x="17" y="0" width="330" height="17"/>
				<textElement>
					<font isBold="true" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{last_name} + "," + $F{first_name} + "   " + "CHART#:" + $F{chart_no}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" mode="Opaque" x="17" y="14" width="330" height="17"/>
				<textElement/>
				<textFieldExpression><![CDATA["HIN:" + $F{hin} + " " + $F{ver} + "   " + "SEX:" + $F{sex} + "   " + "DOB:" +$F{date_of_birth} + "/" + $F{month_of_birth} + "/" + $F{year_of_birth}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" mode="Opaque" x="17" y="28" width="330" height="17"/>
				<textElement/>
				<textFieldExpression><![CDATA[$F{address} + " " + $F{city} + "," + $F{province} + " " + $F{postal}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" mode="Opaque" x="17" y="43" width="330" height="17">
					<printWhenExpression><![CDATA[Boolean.valueOf($F{doc_name} != null)]]></printWhenExpression>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA["HOME: " + $F{phone} + "   " + "Dr. " + $F{doc_name}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" mode="Opaque" x="17" y="43" width="330" height="17">
					<printWhenExpression><![CDATA[Boolean.valueOf($F{doc_name} == null)]]></printWhenExpression>
				</reportElement>
				<textElement/>
				<textFieldExpression><![CDATA["HOME: " + $F{phone}]]></textFieldExpression>
			</textField>
		</band>
	</title>
</jasperReport>
