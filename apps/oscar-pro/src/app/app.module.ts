import { NgModule, inject, provideAppInitializer } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { AppComponent } from './app.component';
import { RouterModule } from '@angular/router';
import {
  InitPropertyService,
  AuthGuard,
  AuthInterceptor,
  AuthModule,
  BillingGuard,
} from '@oscar-pro/auth';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule } from 'ngx-toastr';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { environment } from '../environments/environment';
import { StoreRouterConnectingModule } from '@ngrx/router-store';
import { APP_CONFIG } from '@oscar-pro/config';
import { CoreStateModule } from '@oscar-pro/core-state';
import { AppInitService } from '../lib/services/app.init.service';
import { AuthOktaService } from '@oscar-pro/auth-okta';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { providePrimeNG } from 'primeng/config';
import Aura from '@primeng/themes/aura';

@NgModule({
  declarations: [AppComponent],
  bootstrap: [AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    AuthModule,
    CoreStateModule,
    ToastrModule.forRoot(),
    RouterModule.forRoot(
      [
        {
          path: '',
          loadChildren: () =>
            import('@oscar-pro/feature/login').then((module) => module.FeatureLoginModule),
        },
        {
          path: 'user/logout',
          loadChildren: () =>
            import('@oscar-pro/feature/logout').then((module) => module.FeatureLogoutModule),
        },
        {
          path: 'new-billing',
          title: 'Create Invoice',
          canActivate: [AuthGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/new-billing').then((module) => module.FeatureBillingModule),
        },
        {
          path: 'billing',
          title: 'Create Invoice',
          canActivate: [AuthGuard, BillingGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/billing').then((module) => module.FeatureBillingModule),
        },
        {
          path: 'patient-portal',
          canActivate: [AuthGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/patient-portal').then((module) => module.FeaturePatientPortalModule),
        },
        {
          path: 'fax',
          title: 'fax',
          canActivate: [AuthGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/fax').then((module) => module.FeatureFaxModule),
        },
        {
          path: 'inbox',
          title: 'Inbox',
          canActivate: [AuthGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/inbox').then((module) => module.FeatureInboxModule),
        },
        {
          path: 'document',
          redirectTo: 'inbox/document',
          pathMatch: 'full',
        },
        {
          path: 'hrm',
          redirectTo: 'inbox/hrm',
          pathMatch: 'full',
        },
        {
          path: 'oidc',
          canActivate: [AuthGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/oidc').then((module) => module.FeatureOidcModule),
        },
        {
          path: 'one-id',
          loadChildren: () =>
            import('@oscar-pro/feature/one-id').then((module) => module.FeatureOneIdModule),
        },
        {
          path: 'restore-demographic/demographic/:demographicNo',
          title: 'Restore Demographic',
          canActivate: [AuthGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/restore-demographic').then((module) => module.FeatureRestoreDemographicModule),
        },
        {
          path: 'third-party-app',
          loadChildren: () =>
            import('@oscar-pro/feature/third-party-app').then((module) => module.FeatureThirdPartyAppModule),
        },
        {
          path: 'apps-health/gateway',
          loadChildren: () =>
            import('@oscar-pro/feature/gateway').then((module) => module.FeatureAppsHealthGatewayModule),
        },
        {
          path: 'attachment-manager',
          title: 'Attachment Manager',
          canActivate: [AuthGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/attachment-manager').then((module) => module.FeatureAttachmentManagerModule),
        },
        {
          path: 'roster-reconciliation',
          title: 'Roster Reconciliation',
          canActivate: [AuthGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/reconciliation').then((module) => module.FeatureReconciliationModule),
        },
        {
          path: 'swagger-api',
          title: 'Billing REST API',
          canActivate: [AuthGuard],
          loadChildren: () =>
            import('@oscar-pro/feature/api-documentation').then((module) => module.FeatureApiDocumentationModule)
        }
      ],
      {initialNavigation: 'enabledBlocking', useHash: true}
    ),
    StoreModule.forRoot({}, {
      metaReducers: !environment.production ? [] : [],
      runtimeChecks: {
        strictActionImmutability: true,
        strictStateImmutability: true,
      },
    }),
    EffectsModule.forRoot([]),
    !environment.production
      ? StoreDevtoolsModule.instrument({connectInZone: true})
      : [],
    StoreRouterConnectingModule.forRoot()],
  providers: [
    AuthOktaService,
    AppInitService,
    InitPropertyService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    {
      provide: APP_CONFIG,
      useValue: environment,
    },
    provideAppInitializer(() => {
        const initializerFn = ((is: AppInitService) => function () {
        return is.load();
      })(inject(AppInitService));
        return initializerFn();
      }),
    provideHttpClient(withInterceptorsFromDi()),
    provideAnimationsAsync(),
    providePrimeNG({
      theme: {
        preset: Aura,
        options: {
          cssLayer: {
            name: 'primeng'
          },
          darkModeSelector: false,
        }
      }
    })
  ],
})
export class AppModule {
}
