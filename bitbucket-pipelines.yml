image: node:20.14.0

definitions:
  caches:
    node-modules:
      key:
        files:
          - package.json
      path: ./node_modules

  scripts:
    npm-ci: &npm-ci |
      if [ -d "node_modules" ] && [ -f "node_modules/.package-lock.json" ]; then
        echo "Node modules already installed, skipping npm ci"
      else
        echo "Installing node modules..."
        npm ci
      fi

  install: &install
    name: Install npm packages
    size: 8x
    caches:
      - node-modules
    script:
      - *npm-ci

  lint: &lint
    name: Run lint
    caches:
      - node-modules
    script:
      - npm run lint

  test: &test
    name: Run unit tests
    size: 8x
    caches:
      - node-modules
    script:
      - npm run test
      - npm install -g lcov-result-merger
      - lcov-result-merger 'coverage/**/lcov.info' > coverage/lcov.info
    artifacts:
      - coverage/lcov.info

  build: &build
    name: Build app
    caches:
      - node-modules
    script:
      - npm run build:oscar-pro

  build-legacy: &buildAndPublishLegacy
    name: Build package and upload to Nexus
    caches:
      - node-modules
    script:
      - *npm-ci
      - npm run build:oscar-pro:prod
      - mv .npmrc_config .npmrc
      - pipe: atlassian/npm-publish:0.2.0
        variables:
          NPM_TOKEN: $NEXUS_NPM_TOKEN
          FOLDER: "dist/apps/oscar-pro"
          EXTRA_ARGS: "--registry=$NEXUS_NPM_URL"
          DEBUG: "true"

  build-snapshot: &buildAndPublishSnapshot
    name: Build package and upload to Nexus
    size: 4x
    caches:
      - node-modules
    script:
      - *npm-ci
      - npm run build:oscar-pro:snapshot
      - mv .npmrc_snapshot_config .npmrc
      - pipe: atlassian/npm-publish:0.2.0
        variables:
          NPM_TOKEN: $NEXUS_NPM_TOKEN
          FOLDER: "dist/apps/oscar-pro"
          EXTRA_ARGS: "--registry=$NEXUS_NPM_SNAPSHOT_URL"
          DEBUG: "true"

  build-release: &buildAndPublishRelease
    name: Build package and upload to Nexus
    size: 4x
    caches:
      - node-modules
    script:
      - *npm-ci
      - npm run build:oscar-pro:release
      - mv .npmrc_config .npmrc
      - pipe: atlassian/npm-publish:0.2.0
        variables:
          NPM_TOKEN: $NEXUS_NPM_TOKEN
          FOLDER: "dist/apps/oscar-pro"
          EXTRA_ARGS: "--registry=$NEXUS_NPM_URL"
          DEBUG: "true"

  finalize-release: &finalize-release
    name: "Merge release into main and create PR to develop"
    image: oscarpro/maven3-jdk18-bc
    script:
      - bash release-tools.sh release

  create-release-from-develop: &create-release-from-develop
    name: "Create release from develop branch"
    image: oscarpro/maven3-jdk18-bc
    script:
      - bash release-tools.sh branch

pipelines:
  default:
    - step: *install
    - parallel:
        fail-fast: true
        steps:
          - step: *lint
          - step: *test
          - step: *build

  branches:
    release/*:
      - step: *install
      - step:
          <<: *buildAndPublishSnapshot

    develop:
      - step: *install
      - parallel:
          steps:
            - step: *test
            - step:
                <<: *buildAndPublishSnapshot

  tags:
    release-*:
      - step:
          <<: *buildAndPublishRelease

  custom:
    legacy:
      - step:
          <<: *buildAndPublishLegacy

    run-tests:
      - step:
          <<: *test

    finalize-release:
      - step:
          <<: *finalize-release

    create-release-branch:
      - variables:
          - name: RELEASE_VERSION
      - step:
          <<: *create-release-from-develop
