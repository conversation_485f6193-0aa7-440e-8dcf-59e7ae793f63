ALTER TABLE polaris_configuration
    ADD COLUMN IF NOT EXISTS is_cpar_enabled                  BOOLEAN DEFAULT false,
    ADD COLUMN IF NOT EXISTS admin_polaris_fhir_endpoint      TEXT,
    ADD COLUMN IF NOT EXISTS admin_polaris_fhir_client_id     TEXT,
    ADD COLUMN IF NOT EXISTS admin_polaris_fhir_client_secret TEXT,
    ADD COLUMN IF NOT EXISTS admin_polaris_base_path          TEXT;

UPDATE polaris_configuration
SET is_cpar_enabled = (SELECT CASE LOWER(value)
                                  WHEN 'true' THEN TRUE
                                  ELSE FALSE
                                  END
                       FROM property
                       WHERE name = 'integration.cpar.enabled'
                       LIMIT 1)
WHERE EXISTS (SELECT 1
              FROM property
              WHERE name = 'integration.cpar.enabled');

UPDATE polaris_configuration
SET admin_polaris_fhir_endpoint = (SELECT value
                                   FROM property
                                   WHERE name = 'admin_polaris_fhir_endpoint'
                                   LIMIT 1)
WHERE admin_polaris_fhir_endpoint IS NULL
  AND EXISTS (SELECT 1
              FROM polaris_configuration);

UPDATE polaris_configuration
SET admin_polaris_fhir_client_id = (SELECT value
                                    FROM property
                                    WHERE name = 'admin_polaris_fhir_client_id'
                                    LIMIT 1)
WHERE admin_polaris_fhir_client_id IS NULL
  AND EXISTS (SELECT 1
              FROM polaris_configuration);

UPDATE polaris_configuration
SET admin_polaris_fhir_client_secret = (SELECT value
                                        FROM property
                                        WHERE name = 'admin_polaris_fhir_client_secret'
                                        LIMIT 1)
WHERE admin_polaris_fhir_client_secret IS NULL
  AND EXISTS (SELECT 1
              FROM polaris_configuration);

UPDATE polaris_configuration
SET admin_polaris_base_path = (SELECT value
                               FROM property
                               WHERE name = 'admin_polaris_base_path'
                               LIMIT 1)
WHERE admin_polaris_base_path IS NULL
  AND EXISTS (SELECT 1
              FROM polaris_configuration);

DELETE
FROM property
WHERE NAME IN (
               'integration.cpar.enabled',
               'admin_polaris_base_path',
               'admin_polaris_fhir_client_id',
               'admin_polaris_fhir_client_secret',
               'admin_polaris_fhir_endpoint'
    );