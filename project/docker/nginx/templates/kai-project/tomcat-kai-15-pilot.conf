location /oscar/ {
    proxy_redirect          https://dev.well.company/ https://dev.well.company:$server_port/;
    proxy_redirect          https://dev.well.company:{{ nginx_port }}/ https://dev.well.company:$server_port/;

    proxy_set_header        Host                      $host:{{ nginx_port }};
    proxy_set_header        X-Forwarded-Proto         $scheme;
    proxy_set_header        X-Forwarded-For           $proxy_add_x_forwarded_for;
    proxy_set_header        X-Real-IP                 $remote_addr;
    proxy_set_header        SSL_CLIENT_CERT_URL       $ssl_client_escaped_cert;

    proxy_pass              https://dev.well.company/oscar/ ;
    proxy_read_timeout      86400;
}
