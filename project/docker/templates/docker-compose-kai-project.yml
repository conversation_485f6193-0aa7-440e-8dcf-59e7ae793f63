version: '3.8'

volumes:
  mysqldb-data:
    driver: local
  mysqldb-log:
    driver: local

networks:
  kai-oscar:

services:
  mysql:
    image: mysql:kai
    build:
      context: mysql
      args:
        TZ: "${TZ}"
        TIME_ZONE: "${TZ}"
    env_file:
      - .env
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
    ports:
      - '${MYSQL_PORT:-3306}:3306'
    volumes:
      - mysqldb-data:/var/lib/mysql
      - mysqldb-log:/var/log/mysql
      - '${PROJECT_DIR}/project/docker/mysql/backups:/backups'
    networks:
      - kai-oscar
    healthcheck:
      test: [ 'CMD', 'mysql', '-h', 'localhost', '-P', 'str(${MYSQL_PORT})', '-u', '${MYSQL_USER}', '--password=${MYSQL_PASSWORD}', '-e', 'select 1', '${MYSQL_DATABASE}' ]
      interval: 30s
      timeout: 3s
      retries: 30

  nginx:
    image: nginx:kai
    build:
      context: nginx
      args:
        NGINX_DNAME: "/CN=dev.well.company/OU=EMR/O=WELL/L=Toronto/ST=ON/C=CA"
        NGINX_PORT: "443"
    cap_add:
      - CAP_SYS_ADMIN
      - NET_ADMIN
      - NET_RAW
    env_file:
      - .env
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-443}:443"
    volumes:
      - "${PROJECT_DIR}/project/docker/nginx/resources/error.html:/etc/nginx/html/error.html"
      - "${PROJECT_DIR}/project/docker/nginx/resources/nginx.conf:/etc/nginx/nginx.conf"
      - "${PROJECT_DIR}/project/docker/nginx/resources/proxy.conf:/etc/nginx/sites-enabled/proxy.conf"
      - "${PROJECT_DIR}/project/docker/nginx/resources/tomcat-kai-15-pilot.conf:/opt/nginx-proxies/tomcat-kai-15-pilot.conf"
      - "${PROJECT_DIR}/project/docker/nginx/40-set-dockerhost.sh:/docker-entrypoint.d/40-set-dockerhost.sh"
    networks:
      - kai-oscar