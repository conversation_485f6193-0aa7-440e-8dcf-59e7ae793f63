import os
import subprocess

import util
from extentions import ExtensionBase

from enums import ProjectType


class Extension(ExtensionBase):
    __name__ = 'OSCAR Pro'
    __short_name__ = 'PRO'

    # # Extension commands
    # def list(self):
    #     return f'- WPDE {__name__}' + self.tag_start
    #

    def add(self, *args, **kwargs):
        super(Extension, self).add(*args, **kwargs)
        if self.conf.project_type != ProjectType.IC:
            return
        self.logger.trace('Building OSCAR Pro frontend')
        oscar_pro_dir = os.path.join(self.conf.project_dir, 'oscar-pro', 'frontend')
        util.npm_install(oscar_pro_dir)
        params = '--silent --no-progress'
        if self.logger.is_debug_enabled():
            params = ''
        build_script = self.conf.get('project', 'pro_build_script')
        stdout, stderr = super().get_subprocess_stdout()
        subprocess.call(f'npm {params} run {build_script}', stdout=stdout, shell=True)

    # def remove(self):
    #     super(Extension, self).remove()
    #
    # # Tool commands
    # def config(self):
    #     super(Extension, self).config()
    #
    # def config_docker(self, *args, **kwargs):
    #     super(Extension, self).config_docker(*args, **kwargs)
    #
    # def config_nginx(self, *args, **kwargs):
    #     super(Extension, self).config_nginx(*args, **kwargs)
    #
    # def config_tomcat_keystores(self):
    #     super(Extension, self).config_tomcat_keystores()
    #
    # def config_tomcat_properties(self, *args, **kwargs):
    #     super(Extension, self).config_tomcat_properties(*args, **kwargs)
    #
    # def init(self):
    #     super(Extension, self).init()
