# Alternate port config for BC.
# Requires manual modification of tomcat port in server.xml and run configurations.
project:
  type: kai-project
  workspace: kai-project-bc-alt
  idea_dir: kai-project-bc
  tomcat_template_dir: kai-project-bc
  bootstrap_db: kai-project-bc
  repository: well-project
  branch: master
  # module that hosts documents for oscar
  documents: kai-documents
  # schema to use for oscar and Pro
  oscar_schema: kai15
  migrator_order:
    - oscar
  bootstrap_schemas:
    - drugref
    - kai15
  # OSCAR related component support
  oscar_pro_frontend: false
  oscar_pro: false
  # build command used to compile oscar pro typescript [ 'build:prod' or 'build:staging' ]
  pro_build_script: build:staging
  jvm_options:
    - -Xss1024k
    - -Xmx4096m
    - -Xms1024m
    - -XX:MaxNewSize=1024m
    - -Dhttps.protocols=TLSv1.2
    - -Djdk.tls.client.protocols=TLSv1.2
    - -Dsun.security.ssl.allowUnsafeRenegotiation=false
    - -Djavax.accessibility.assistive_technologies=""
    - -Djava.awt.headless=true
    - -XX:+UseG1GC
    - -Djava.net.preferIPv4Stack=true
    - -Djava.security.provider.100=org.bouncycastle.jce.provider.BouncyCastleProvider
    - -Dlogging.level.ca.kai=DEBUG
    - -Dorg.slf4j.simpleLogger.log.ca.kai=DEBUG
    - -Djava
    - -Duser.home="$PROJECT_DIR$/project/tomcat/resources"
    - -Duser.dir="$PROJECT_DIR$/project/tomcat/resources"
  application_server: TOMCAT-KAI-BC-ALT

mysql:
  username: mysql
  password: mysql

docker:
  mysql:
    port: 3308
  nginx:
    port: 444
  tomcat:
    port: 8082
    # location of catalina base files to use for application server
    base: project/resources/kai/catalina_base/tomcat9
    # location to download tomcat application server from
    url: https://archive.apache.org/dist/tomcat/tomcat-9/v9.0.33/bin/apache-tomcat-9.0.33.tar.gz

modules:
  # Tomcat apps
  oscar:
    repository: oscar
    branch: develop
    iml: project/resources/kai/iml/kai-15-pilot.iml
  drugref2:
    repository: drugref2
    branch: develop
    iml: project/resources/kai/iml/drugref2.iml
  # Resources
  kai-documents:
    repository: kai-documents
    branch: develop
  # Migrators
  bc-15-database:
    repository: bc-15-database
    branch: develop

migrators:
  oscar:
    module: bc-15-database
    db: kai15
    jar: bc-15-database-18.0.0-jar-with-dependencies.jar
    pass: mysql
    user: mysql
    port: 3308

extensions:
  - tools
  - oscar-pro
  - oscar-pro-database