# Windows Dependency Instructions

Run some commands in PowerShell. Some commands in Command Prompt (noted in instructions).

Unless explicitly stated commands do not need to be run as administrator.

## Enable execution policy bypass

In powerShell, as administrator:
```shell
PowerShell -NoProfile -ExecutionPolicy Bypass -Command "& {Start-Process PowerShell -ArgumentList 'Set-ExecutionPolicy Bypass -Force' -Verb RunAs}"
```

## Note: Adding Environment Variables

Certain configuration steps may require adding System Variables. 

1. Start the *Run* box and enter `sysdm.cpl` to open up the System Properties window.
2. Go to the *Advanced* tab and click the **Environment Variables** button.
3. Under the *System variables*
4. To add a new variable, click **New** and add the required variable, eg:
    1. `JAVA_HOME`=`C:\Program Files\Java\jdk1.8.0_261\`
5. To add to the `Path` variable:
    1. Find the **Path** variable and click **Edit**.
   2. Add the required path. eg:
       1. `C:\apache-maven-3.8.4\bin`

## Kernel Update: Download Windows Subsystem for Linux & Ubuntu

1. Ensure your machine is running Windows 10, updated to version 2004, Build 18362 or higher. ()
2. Enable WSL, install a Linux distribution, and update to WSL 2: https://docs.microsoft.com/en-us/windows/wsl/install-win10
   * Follow the *Manual Instructions Steps* to install Ubuntu.
   * or from a command prompt/IDE
   ```shell
   wsl --install # then reboot and continue setting up username and password for WSL account
   ```
3. Download and install the Linux kernel update package.
4. Limit WSL2 RAM and CPU usage.
    1. Create a new `.wslconfig` file in your user directory. ie `C:Users\User\.wslconfig`.
    2. Add the following:

       ```
       [wsl2]
       memory=4GB # Limits VM memory in WSL 2 to 4 GB
       processors=5 # Makes the WSL 2 VM use two virtual processors
       ```

    3. Restart the service in a PowerShell run as Administrator: `Restart-Service LxssManager`


### Docker Desktop
#### Manually download and install docker desktop
Install Docker Desktop: https://docker.com/get-started

* You do not configure docker, ie. clone a repository, etc.

#### Use winget to install docker desktop
```shell
winget install -e --id Docker.DockerDesktop
```

### Visual Studio Code
Install Visual Studio Code: https://code.visualstudio.com/

## Git
### Download and install Manually
Download Git: https://git-scm.com/download/win 

* Installation Configuration Options:
  1. **Configuring the line ending conversions:** checkout as-is, commit as Unix-style line endings
  2. **Choose the default behavior of `git pull`:** merge

### OR use winget from command prompt/IDE
```shell
winget install -e --id GitHub.GitHubDesktop
```

### Git configuration
Update line endings and enable symbolic links.
```shell
git config --global core.eol lf
git config --global core.autocrlf input
git config --global core.symlinks true
```

#### Generate SSH key and associate to your bitbucket account
```shell
ssh-keygen -t ed25519 -b 256 -C "[YOU]@well.company" # change [YOU] to your email alias
type %HOMEDRIVE%%HOMEPATH%\.ssh\id_ed25519.pub # copy the output from here to bitbuucket ssh keys
```

### MSBuild tools

#### Manually install MSBuild tools
Download and install build tools https://visualstudio.microsoft.com/visual-cpp-build-tools/

#### Using winget to install MSBuild tools
```shell
winget install --id=Microsoft.VisualStudio.2022.BuildTools  -e
```

### IntelliJ

> Manually Download and Install IntelliJ: https://www.jetbrains.com/idea/download/#section=windows

OR Use winget to install IntellioJ Ultimate Edition
```shell
winget install -e --id JetBrains.IntelliJIDEA.Ultimate
```

### Install OpenJDK 8
Install Java 8 OpenJDK: https://github.com/AdoptOpenJDK/openjdk8-upstream-binaries/releases/tag/jdk8u265-b01

### Python 3.10

#### Manually download and install python 3.10
Install Python 3.10 using the Installer: https://www.python.org/downloads/windows/

* Installation Configuration Options:
    1. Ensure you check off `Add to Path system environment variable.` (If not prompted: manually add the Python install directory to the `Path` system variable after installation.
    2. At the end select `Disable path limit`

#### Use winget to install python 3.10
```shell
winget install -e --id Python.Python.3.10
```

### Install NodeJS

#### Manually download and install nodejs 14.x
NodeJS: https://nodejs.org/dist/latest-v14.x/

* Installation Configuration Options:
    * Automatically install necessary tools

#### Use winget to install NVM for Windows
```shell
winget install -e --id CoreyButler.NVMforWindows
nvm install 14
#nvm alias dafault 14
nvm use 14
```

### Maven
1. Download Maven: https://maven.apache.org/download.cgi
2. Extract to C:\Program Files\maven
3. Configure System Variables:
   1. Set `JAVA_HOME` environment variable to your Java location i.e., `C:\Program Files\Java\jdk1.8.0_291`
   2. Add `C:\Program Files\maven\bin` to the `Path` system variable.

### wkhtmltopdf
Install wkhtmltopdf: https://wkhtmltopdf.org/downloads.html

* On windows you will need to add the following to oscar.properties after init is complete
  `WKHTMLTOPDF_COMMAND=c:\\Program Files\\wkhtmltopdf\\bin\\wkhtmltopdf.exe`

