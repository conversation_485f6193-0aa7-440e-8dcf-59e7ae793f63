## Mac Dependency Instructions

### Install CLT
```shell
sudo rm -rf /Library/Developer/CommandLineTools
sudo xcode-select --install
```

### Install brew
```shell
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

### Install coreutils
```shell
brew install coreutils
```

### Install openjdk 8
```shell
brew install openjdk@8
sudo ln -sfn /usr/local/opt/openjdk@8/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-8.jdk
```

### Update maven
```shell
brew install maven
```

### Install node
```shell
brew install node@12
```
