
# IntelliJ

Install Nx Console Idea plugin
https://plugins.jetbrains.com/plugin/15101-nx-console-idea

Install the Multirun configuration
https://plugins.jetbrains.com/plugin/7248-multirun

## Run configurations

* **KAI PRO** Tomcat run configuration for  KAI OSCAR + OSCAR PRO
* **NX** NPM run configuration for Nx dev project
* **BILLING/INBOX/LOGIN** IntelliJ JavaScript Debugger configuration
* **SERVE** Multirun configuration to launch the Tomcat and Angular components.

Mutirun configuration requires installation of the Multirun plugin.

## JavaScript Debugger

There are limitations to the JavaScript Debugger support via IntelliJ and Chrome.
Chrome runs every tab/window in new process. You must use the Chrome window launched by
the debugger for break points to catch **AND** the tab initially opened is the only tab
the break points will catch on.

To address this multiple JavaScript Debugger configurations for each feature must exist.

Enable custom user directory Settings -> Tools -> Browser

DEBUG the SERVE multirun configuration to:

* Start Tomcat
* Start Angular development server

When tomcat has finished initializing, <PERSON>BUG the LOGIN configuration to login to OSCAR.
Then DEBUG the configuration for the feature you are working on.

> **NOTE** The initial page load may occur before the debugger has attached.
Refresh the page to catch breakpoints you expect to trigger on initialization.