# OCA OSCAR Configuration

## Required repositories

* oca
* oca-database
* oca-documents
* oca-portal

## Clone WPDE
```shell
<NAME_EMAIL>:kai-innovations/well-project.git oca-project
```

### Initialize project

* On Linux `$CMD` is a bash script run via `run.sh`
* On Windows `$CMD` is either:
    * a PowerShell script run via `run.ps1`
    * or a Batch file run via `run.cmd` (launch this when opening from explorer)
* The script can be run interactively without parameters at the command line.
  When running interactively any of the following commands can be run without specifying `$CMD`

```shell
$CMD -c oca-project init
```

After initialization is complete add the OCA extension:

```shell
<NAME_EMAIL>:kai-innovations/well-project-extensions.git extensions
$CMD extension add oca
```

## Finish WPDE Configurations
Complete the [First Time Setup](../FIRST-TIME.MD) and [IntelliJ](../INTELLIJ.MD) as necessary.
