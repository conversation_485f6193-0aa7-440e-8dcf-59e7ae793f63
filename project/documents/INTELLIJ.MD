# IntelliJ Configuration

## Application settings
* Set memory to 4GB
    * Help → Change Memory Settings

## Plugins
* Install Jetbrains Python plugin
    * File → Preferences/Settings: Plugins
* Install EnvFile plugin https://plugins.jetbrains.com/plugin/7861-envfile
    * File → Preferences/Settings: Plugins    
* Install Multirun configuration plugin https://plugins.jetbrains.com/plugin/7248-multirun
    * File → Preferences/Settings: Plugins
* Install Prettier plugin https://plugins.jetbrains.com/plugin/10456-prettier and install fork https://github.com/btmills/prettier
    * Install fork globally: open terminal and run `npm install --global @btmills/prettier`
    * File → Preferences/Settings: Plugins
* Install Save Actions plugin to automatically format code using Google Style guidelines on file save https://plugins.jetbrains.com/plugin/21538-save-actions-tool
    * File → Preferences/Settings: Plugins → Marketplace → Search for Save Actions → Install

## Maven Configuration
* Change Maven import JDK to the openJDK downloaded previously:
  * File → Preferences/Settings → Build, Execution, Deployment → Build Tools → Maven → Imports/Importing → JDK for importer
* Change Maven to always update snapshots:
  * File → Preferences/Settings → Build, Execution, Deployment → Build Tools → Maven → Check `Always update snapshots`
* Mark modified files:
  * File → Preferences/Settings → Editor → General: Editor Tabs → Mark modified

## Prettier Configuration

Install the Prettier plugin globally using `npm`:

```shell
npm install --global @btmills/prettier
```

* Navigate to Prettier Configurations
    * File → Settings... → Languages & Frameworks → Javascript → Prettier
* Point the plugin to the globally installed @btmills/prettier repo by setting the "Prettier package" field to:
    * Windows: `~/AppData/Roaming/npm/node_modules/@btmills/prettier`
    * Linux: `~/.nvm/versions/node/v16.15.0/lib/node_modules/@btmills/prettier`
    * Mac: `~/usr/local/lib/node_modules/@btmills/prettier`
* Ensure "Run for files" field is set to `{**/*,*}.{js,ts,jsx,tsx}`
* Ensure automatic on-save Prettier formatting for .ts and .js files
    * Check "On Save" checkbox, click "All actions on save", and check "Run Prettier" checkbox

## Google Java Style Configuration
Download intellij-java-google-style.xml https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml

* Go to File → Settings → Editor → Code Style
* Click on the settings cog next to "Scheme:"
* Click on "Import Scheme"
* Select "IntelliJ Idea Code Style XML"
* Select the "intellij-java-google-style.xml" file you downloaded.
* Select the "GoogleStyle" Scheme

## Save Actions Plugin Configuration
* Go to File → Settings → Other Settings → Save Actions
* Under General, enable Activate save actions on shortcut (default "CTRL + SHIFT +S")
* Under 'Formatting Actions', enable Optimize imports and Reformat only changed code

## Google Java Style Format Plugin

* Go to File → Settings → Plugins
* Search google-java-format and install by Google
* Restart IDE
* Go to File → Settings → google-java-format Settings
* Enable google-java-format

> IntelliJ JRE Config

The google-java-format plugin uses some internal classes that aren't available
without extra configuration. To use the plugin, go to `Help → Edit Custom VM
Options...` and paste in these lines:

```
--add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED
--add-exports=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED
--add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED
--add-exports=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED
--add-exports=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED
--add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED
```

Once you've done that, restart the IDE.

## Disable Wildcard (*) Imports
Ensure the option to Use single class import is selected. 

This will prevent wildcard import statements. (See: IntelliJ—Auto Import.)
 
## Using Docker from inside IntelliJ
There are four docker run configurations.

* Build - create containers
* Start - run containers
* Stop  - shutdown containers
* Down  - remove containers and volumes

## Enable Docker Intellij Service Integration
* Settings → Build, Execution, Deployment → Docker
* Press +
* Configure Docker at Unix socket
* Services → Docker → Connect

## Troubleshooting

### Java errors
If you get java errors, "java: package net.sf.json does not exist"

1. View → Tool Windows → Maven:
    * Reload All Maven Projects
    * (down arrow) Download Sources or Documentation → Download Sources
2. File → Invalidate Caches (uncheck all boxes) → Invalidate and Restart

### Classpath too long
You may get an error trying to build the project that read like:
```
Error running "...". Command line is too long. 

Shorten the command line via JAR manifest or via a classpath file and rerun. 
```
To solve this problem, click the `JAR manifest` link in the error message.
### Database view

The mysql connector in IntelliJ may give errors due to the server time zone.
Adding `serverTimezone=America/Toronto` to connection string for DB configuration in IntelliJ will address this