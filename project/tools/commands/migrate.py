import glob
import os
import subprocess

from commands.skeleton import Skeleton<PERSON>ommand
from template import Template<PERSON><PERSON><PERSON>


def get_original_parameters(params):
    original_params = params
    if original_params is None:
        original_params = ''
    return original_params


class MigrateCommand(SkeletonCommand):

    def __init__(self, conf, args=None):
        super().__init__(conf, args=args)
        self.supported_params = ['user', 'pass', 'port', 'host', 'db', 'location', 'out-of-order', 'oscar-type']
        self.oscar_type_codes_map = {'kai-project-bc': 'BC', 'kai-project-on': 'ON'}
        self.migrators = self.conf.get('migrators')
        self.template_renderer = TemplateRenderer(self.conf)
        self.usage = """
Running with no parameters executes against all configured migrations
Usage:
\tclean\t\tdrop all objects 
\tinfo\t\tprint detail and status
\trepair\t\trepair schema history table
\tupdate\t\tperform database migrations
"""

    def clean(self, *args):
        self.logger.info('Running clean')
        self.run(self.params, '--clean', build=False)

    def info(self, *args):
        self.logger.info('Running info')
        self.run(self.params, '--info', build=False)

    def update(self, *args):
        self.logger.info('Running update')
        self.run(self.params)

    def repair(self, *args):
        self.logger.info('Running repair ')
        self.run(self.params, '--repair', build=False)

    def default(self):
        self.logger.info('Running migrators')
        self.run([])

    def run(self, migrators=None, params=None, build=True, *args):
        original_params = get_original_parameters(params)
        migrators = self.get_migrators(migrators)
        self.logger.debug(f'migrators: {migrators}')
        for migrator in migrators:
            if not self.migrator_exists(migrator):
                continue
            self.logger.debug(f'Running {migrator}')            

            # todo: confirm there are no side effects making order matter
            params = f'{original_params} {self.get_params(self.migrators[migrator])}'

            migrator_directory = self.get_migrator_path(
                self.migrators[migrator]['module']
            )
            if migrator_directory is None or not os.path.exists(migrator_directory):
                self.logger.error(f'Missing migrator directory {migrator_directory}.')
                return

            self.logger.debug('Changing to migrator dir: %s' % migrator_directory)
            os.chdir(migrator_directory)

            stdout, stderr = super().get_subprocess_stdout(
                ['--info', '--repair'], original_params)
            if build:
                self.logger.debug(f"PATH: {os.environ.get('PATH')}")
                mvn_command = ['mvn', 'clean', 'package'] \
                    if os.name == 'nt' else ['mvn clean package']
                subprocess.call(
                    mvn_command,
                    stdout=stdout,
                    stderr=stderr,
                    shell=True
                )
            java_home = os.environ.get('JAVA_HOME')
            java_command = 'java' if java_home is None \
                else os.path.join(java_home, 'bin', 'java')
            jar_path = os.path.join(
                migrator_directory,
                'target',
                self.migrators[migrator]['jar']
            )
            command = f'"{java_command}" -Xmx2048m -jar "{jar_path}" {params}'
            self.logger.debug(f'Params: {params}')
            self.logger.debug(f'Command: {command}')
            subprocess.call(
                command,
                stdout=stdout,
                stderr=stderr,
                shell=True
            )

    def get_migrators(self, migrators):
        if migrators is None or len(migrators) == 0:
            migrators = self.conf.get('project', 'migrator_order')
        return migrators

    def migrator_exists(self, migrator):
        if migrator not in self.migrators:
            self.logger.error(f'migrator {migrator} not found.')
            return False
        return True

    def get_params(self, migrator):
        params = ''
        for p in self.supported_params:
            params += self.generate_param(migrator, p)

        params += self.generate_pro_migrator_params(migrator)

        return params

    def generate_param(self, migrator, p):
        if p in migrator:
            if p == 'location':
                location = 'classpath:db/migration'
                locations = migrator[p]
                if not isinstance(locations, list):
                    locations = [locations]
                for location_dir in locations:
                    location_dir = os.path.realpath(os.path.join(
                        self.conf.project_dir, location_dir))
                    if os.path.exists(os.path.join(location_dir, 'templates')):
                        self.process_migration_templates(location_dir)
                        location_dir = os.path.join(location_dir, 'patches')
                    if os.path.exists(location_dir):
                        location += ',filesystem:%s' % location_dir
                    else:
                        self.logger.error("Invalid migrator location: %s" % migrator[p])
                return f' --{p} {location}'
            elif p == 'out-of-order':
                return f' --{p} '
            else:
                return f' --{p} {migrator[p]} '
        return ''

    def generate_pro_migrator_params(self, migrator):
        if migrator['module'] == 'oscar-pro-database' \
                and self.conf.get('project', 'workspace') in self.oscar_type_codes_map:
            oscar_type = self.oscar_type_codes_map[self.conf.get('project', 'workspace')]
            return f' --oscar-type {oscar_type}'
        return ''

    def get_migrator_path(self, migrator):
        return os.path.join(self.conf.project_dir, migrator)

    def process_migration_templates(self, location_dir, pattern=None):
        if pattern is None:
            pattern = '*'
        template_dir = os.path.join(location_dir, 'templates')
        self.logger.debug(f'generating migration templates for {location_dir}')
        for file in glob.glob(os.path.join(template_dir, pattern)):
            file = os.path.basename(file)
            self.logger.debug(f'Generating template for {file}')
            template = os.path.join(location_dir, 'patches', file)
            rendered = self.template_renderer.get_template(template_dir, file)
            if rendered is None:
                continue
            with open(template, 'w') as f:
                self.logger.debug(f'Writing template to: {f}')
                f.write(rendered)
