import os.path
import tarfile
from pathlib import Path
from urllib.parse import urlparse
from xml.etree import ElementTree as ET
from xml.etree.ElementTree import SubElement

import requests
import ruamel.yaml

import util
from commands.skeleton import SkeletonCommand
from template import Template<PERSON>enderer
from util import *


class ConfigCommand(SkeletonCommand):
    args: {}
    positional: []

    def __init__(self, conf, args=None):
        super().__init__(conf, args=args)
        self.project_type = self.conf.project["type"]
        self.yaml = ruamel.yaml.YAML()
        self.nginx_dir = os.path.join(self.conf.project_dir, 'project', 'docker', 'nginx')
        self.nginx_resources_dir = os.path.join(self.nginx_dir, 'resources')
        self.create_directory(self.nginx_resources_dir)
        self.tomcat_dir = os.path.join(self.conf.project_dir, 'project', 'tomcat')
        # allow overriding of resources directory for a covering project
        resources_dir = self.conf.get('project', 'tomcat_resources')
        if resources_dir is None:
            resources_dir = 'resources'

        self.tomcat_resource_dir = os.path.join(self.tomcat_dir, resources_dir)
        # copy some resources from the primary project to the covering project
        if resources_dir != 'resources':
            primary_resources_dir = os.path.join(self.tomcat_dir, 'resources')
            if not os.path.exists(primary_resources_dir):
                self.logger.error(
                    f'{primary_resources_dir} not found for copying resources')
                return
            else:
                self.logger.debug(
                    f'Copying resources from {primary_resources_dir} to {self.tomcat_resource_dir}')
                shutil.copytree(primary_resources_dir, self.tomcat_resource_dir,
                                dirs_exist_ok=True)
        self.create_directory(self.tomcat_resource_dir)
        self.tomcat_template_dir = os.path.join(self.tomcat_dir, 'templates', self.conf.get('project', 'tomcat_template_dir'))
        self.tomcat_server_dir = os.path.join(self.conf.project_dir, 'project', 'tomcat', 'server')
        self.catalina_base_dir = os.path.join(self.conf.project_dir, self.conf.get('docker', 'tomcat', 'base'))
        self.template_renderer = TemplateRenderer(self.conf)
        self.usage = """
Usage:
\tNo params\t\tinitialize project components
\tall\t\tgenerates all configuration
\tdocker\t\tgenerates docker configuration
\tnginx\t\tgenerates nginx configuration
\ttomcat\t\tgenerates tomcat configuration
\tget\t\tlookup data in current config
"""

    def create_directory(self, directory):
        try:
            if not os.path.exists(directory):
                self.logger.debug('Missing Tomcat resource directory, attempting to create')
                os.makedirs(directory)
        except:
            self.logger.error('Unable to create directory: %s' % directory, True)

    def default(self):
        self.all()

    def all(self, *args):
        self.logger.info('Configuring project')
        self.project(*args)
        self.docker(*args)
        self.nginx(*args)
        self.tomcat(*args)
        self.idea(*args)
        self.conf.export()

    def project(self, *args):
        project_config_file = os.path.join(self.conf.conf_dir, 'local.yaml')
        conf_file = self.conf.yaml_file
        if not os.path.exists(conf_file):
            self.logger.error(f'Missing conf file for {self.project_type}')
            return False
        self.logger.debug('Setting up config file local.yml')
        if conf_file != project_config_file:
            self.logger.debug(f'Creating local.yaml from {conf_file}')
            shutil.copy(conf_file, project_config_file)
        return True

    def docker(self, *args):
        self.logger.debug('Generating docker configuration')
        self.logger.debug('Generating docker environment variables')
        self.conf.docker_conf.export()
        docker_dir = self.conf.docker_conf.get('DOCKER_DIR')
        docker_compose = os.path.join(docker_dir, 'docker-compose.yml')
        # Look for project specific docker compose
        docker_compose_file = self.conf.get('project', 'docker_compose')
        project_compose = None
        if docker_compose_file is not None:
            project_compose_file = f'docker-compose-{docker_compose_file}.yml'
            project_compose = os.path.join(docker_dir, 'templates', project_compose_file)
        if project_compose is None or not os.path.exists(project_compose):
            # Fall back to project type docker compose
            project_compose_file = f'docker-compose-{self.project_type}.yml'
            project_compose = os.path.join(docker_dir, 'templates', project_compose_file)
        self.logger.debug('Setting up docker-compose.yml')
        docker_compose_data = {}
        # Load yaml from template
        if not os.path.exists(project_compose):
            self.logger.error(f'{project_compose} not found')
            return False
        with open(project_compose, 'r') as f:
            project_yaml = self.yaml.load(f)
            merge_dictionaries(docker_compose_data, project_yaml)
        # If already configured merge any new properties in
        if os.path.exists(docker_compose):
            # Load yaml from disk
            with open(docker_compose, 'r') as f:
                compose_yaml = self.yaml.load(f)
                merge_dictionaries(docker_compose_data, compose_yaml)
        # Load configuration from extensions
        for extension in self.conf.extensions:
            self.conf.extensions[extension].config_docker(docker_compose_data)
        # Save merged data
        with open(docker_compose, 'w') as f:
            self.yaml.dump(docker_compose_data, f)
        return True

    def nginx(self, *args):
        self.logger.debug('Generating nginx config')
        templates = []
        self.template_renderer.render_template_dir(
            self.get_nginx_dir(),
            self.nginx_resources_dir,
            templates=templates,
        )
        # Configure extension nginx templates
        for extension in self.conf.extensions:
            self.conf.extensions[extension].config_nginx(self.template_renderer)

    def get_nginx_dir(self):
        nginx_template_dir = self.conf.get('project', 'nginx_template_dir')
        # Default to project type template directory if nginx template directory not specified in configuration
        if nginx_template_dir is not None:
            nginx_template_dir = os.path.join(self.nginx_dir, 'templates', nginx_template_dir)
        # Fall-back to project dir if config dir does not exist.
        if nginx_template_dir is None or not os.path.exists(nginx_template_dir):
            nginx_template_dir = os.path.join(self.nginx_dir, 'templates', self.conf.get('project', 'type'))
        return nginx_template_dir

    def tomcat(self, *args):
        self.logger.info('Finalizing tomcat configuration.')
        self.tomcat_init()
        self.tomcat_properties()
        self.tomcat_keystores()
        self.tomcat_setenv()

    def tomcat_setenv(self):
        if not os.path.exists(self.tomcat_server_dir):
            self.logger.warn('Missing tomcat skipping setenv.')
            return
        tomcat_set_env = os.path.join(self.tomcat_server_dir, 'bin', 'setenv.sh')
        self.logger.debug(f'Generating {tomcat_set_env}')
        class_path = f'{self.tomcat_resource_dir}'
        with open(tomcat_set_env, 'w') as f:
            f.write(f'#!/usr/bin/env bash\nexport CLASSPATH={class_path}\n')

    def tomcat_keystores(self):
        self.logger.debug('Copying keystores')
        keystore_src = os.path.join(self.tomcat_dir, '.keystore')
        keystore_dest = os.path.join(self.tomcat_resource_dir, '.keystore')
        if os.path.exists(keystore_src) and not os.path.exists(keystore_dest):
            shutil.copy(keystore_src, self.tomcat_resource_dir)
        for file in get_glob(self.tomcat_template_dir, '*.keystore'):
            self.logger.debug(f'Copying keystore {file}')
            shutil.copy(file, self.tomcat_resource_dir)
        for file in get_glob(self.tomcat_template_dir, '*.jks'):
            self.logger.debug(f'Copying keystore {file}')
            shutil.copy(file, self.tomcat_resource_dir)
        # Configure extension keystores
        for extension in self.conf.extensions:
            self.conf.extensions[extension].config_tomcat_keystores()

    def tomcat_properties(self):
        templates = []
        self.template_renderer.render_template_dir(
            self.tomcat_template_dir,
            self.tomcat_resource_dir,
            templates,
            '*.properties',
        )
        # Configure extension templates
        for extension in self.conf.extensions:
            self.conf.extensions[extension].config_tomcat_properties(self.template_renderer)

    def tomcat_init(self):
        if os.path.exists(self.tomcat_server_dir):
            self.logger.info(
                f'Tomcat found at {self.tomcat_server_dir}, skipping download.'
            )
            return
        if not os.path.exists(self.catalina_base_dir):
            self.logger.warn('CATALINA_BASE not found! Expected: %s' % self.catalina_base_dir)
            return
        self.logger.debug('Initializing tomcat')
        self.download_tomcat()
        self.tomcat_base()

    def download_tomcat(self):
        self.logger.info("Downloading tomcat.")
        os.chdir(self.tomcat_dir)
        tomcat_download = os.path.realpath(
            os.path.join(self.tomcat_dir, 'tomcat.tar.gz'))
        url = self.conf.get('docker', 'tomcat', 'url')
        if not os.path.exists(tomcat_download):
            self.logger.debug('Downloading tomcat')
            r = requests.get(url)
            with open(tomcat_download, 'wb') as f:
                f.write(r.content)
        self.logger.debug('Extracting tomcat')
        t = tarfile.open(tomcat_download, 'r')
        t.extractall(self.tomcat_dir)
        directory = Path(Path(urlparse(url).path).stem).stem
        os.rename(
            os.path.join(self.tomcat_dir, directory), self.tomcat_server_dir
        )

    def tomcat_base(self):
        if not os.path.exists(self.tomcat_server_dir):
            self.logger.error(
                f'Tomcat server not found! {self.tomcat_server_dir}'
            )
        else:
            self.logger.info('Copying CATALINA_BASE.')
            self.logger.debug(f'From: {self.catalina_base_dir}')
            self.logger.debug(f'To: {self.tomcat_server_dir}')
            shutil.copytree(
                self.catalina_base_dir,
                self.tomcat_server_dir,
                dirs_exist_ok=True
            )

    def cardswipe(self):
        cardswipe_classes = os.path.join(
            self.conf.project_dir,
            'cardswipe', 'target', 'CardSwipe-1.0.0', 'WEB-INF', 'classes'
        )
        cardswipe_target = os.path.join(cardswipe_classes, 'clientKeystore.properties')
        cardswipe_source = os.path.join(
            self.conf.project_dir,
            'project', 'tomcat', 'resources', 'clientKeystore.properties')
        self.logger.debug(f'Classes: {cardswipe_classes}\nSource: {cardswipe_source}\nTarget: {cardswipe_target}')
        self.logger.debug('Checking for cardswipe dir')
        if os.path.exists(cardswipe_classes):
            self.logger.debug('Copying cardswipe config')
            shutil.copy(cardswipe_source, cardswipe_target)

    def get(self, *args):
        print('Lookup for %s' % ':'.join(args))
        if len(args) == 1 and ':' in args[0]:
            args = args[0].split(':')
        results = self.conf.get(*args)
        base_context = results
        last_context = None
        if len(args) > 0:
            base_context = {}
            context = base_context
            arg = None
            for arg in args:
                context[arg] = {}
                last_context = context
                context = context[arg]
            last_context[arg] = results
        self.yaml.dump(base_context, sys.stdout)

    def idea(self, *args):
        run_config_dir = os.path.join(
            self.conf.project_dir, '.idea', 'runConfigurations')
        for file in util.get_glob(run_config_dir, '*.xml'):
            # we only need to process regular run configurations - not linked
            if '_Linked' in file:
                # do not override the linked configs from the project settings
                self.logger.info(f'Skipping run configuration: {file}')
                continue
            self.logger.debug(f'Found run configuration: {file}')
            root = ET.parse(file)
            configuration = root.find('.//configuration')
            if not configuration:
                continue
            server = self.conf.get('project', 'application_server')
            dirty = False
            if ('APPLICATION_SERVER_NAME' in configuration.attrib
                    and configuration.attrib['APPLICATION_SERVER_NAME'] != server):
                self.logger.debug(f'Changing application server to {server}')
                configuration.attrib['APPLICATION_SERVER_NAME'] = server
                dirty = True
            jvm_options = ' '.join(self.conf.get('project', 'jvm_options'))
            jvm_node = configuration.find(".//option[@name='COMMON_VM_ARGUMENTS']")
            if jvm_node is None:
                jvm_node = SubElement(configuration, 'option')
                jvm_node.attrib['name'] = 'COMMON_VM_ARGUMENTS'
                jvm_node.attrib['value'] = jvm_options
                dirty = True
            elif jvm_node.attrib['value'] != jvm_options:
                self.logger.debug(f'Setting jvm opts {jvm_options}')
                jvm_node.attrib['value'] = jvm_options
                dirty = True
            if dirty:
                root.write(file, encoding="UTF-8", xml_declaration=False)
