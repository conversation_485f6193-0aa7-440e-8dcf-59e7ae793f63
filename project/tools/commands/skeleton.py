import os
from argparse import Namespace

import util

from logger import LOGGER as LOG, LogLevel


class SkeletonCommand:
    args: Namespace
    commands: dict

    def __init__(self, conf, args=None):
        self.conf = conf
        self.modules = self.conf.modules()
        self.command = None
        self.working_dir = None
        self.params = []
        self.args = args
        self.logger = LOG
        self.LogLevel = LogLevel

    def execute(self, args):
        self.args = args
        args_len = len(args.positional)
        if args_len > 0:
            self.command = args.positional[0]
        if args_len > 1:
            self.params = args.positional[1:]
        if self.working_dir is not None:
            os.chdir(self.working_dir)
        invoke = None
        if self.command is None and hasattr(self, 'default'):
            invoke = getattr(self, 'default')
        if self.command is not None and hasattr(self, self.command):
            invoke = getattr(self, self.command)
        if invoke is None:
            print(f'Command "{self.command}" not found.')
            self.help()
        else:
            invoke(*self.params)

    def help(self):
        print(self.usage)

    def get_subprocess_stdout(self, cmd_list=None, cmd=None):
        return util.get_subprocess_stdout(cmd_list, cmd, self.args)
