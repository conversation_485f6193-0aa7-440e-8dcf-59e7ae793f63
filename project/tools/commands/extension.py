import os
import ruamel.yaml


from commands.skeleton import SkeletonCommand
from extentions import ExtensionUtil


class ExtensionCommand(SkeletonCommand):
    conf: 'conf.ProjectConf'
    args: {}

    def __init__(self, conf, args=None):
        super().__init__(conf, args=args)
        self.yaml = ruamel.yaml.YAML()
        self.extension_config = os.path.join(self.conf.project_dir, 'project', 'conf', 'extensions.yaml')
        self.usage = """
Usage:
\tlist\t\t\tShow a list of all extensions
\tadd\t\t\tAdds an extension
\tremove\t\t\tRemove an extension
\trun\t\t\tTakes positional arguments runs commands matching: 
\t\t\t\t\t - extension against first positional argument
\t\t\t\t\t - command against second positional argument


"""

    def load_extension(self, extension):
        return ExtensionUtil.load(extension, self.conf, self.args)

    def default(self):
        print(self.usage)

    def list(self):
        if not self.conf.extensions:
            print('No extensions installed')
            return
        self.logger.debug('Extensions: %s' % ', '.join(self.conf.extensions))
        print('Extensions: ')
        for name in self.conf.extensions:
            extension = self.load_extension(name)
            print(f'{extension.list()}')

    def add(self, name, config=True, init=True, register=True):
        if register and not self.register(name):
            return
        try:
            extension = self.conf.extensions[name]
            extension.add(config=config, init=init)
            self.logger.info(f'Added extension: {name}')
        except Exception as e:
            self.logger.error(str(e))
            self.remove(name, silent=True)

    def register(self, name):
        self.logger.debug(f'Attempting to add extension {name}')
        extensions = self.conf.extensions
        if extensions is None:
            extensions = {}
        if name in extensions:
            self.logger.error('Extension already registered')
            return False
        if not ExtensionUtil.get_extension_dir(name):
            self.logger.error('Extension not found')
            return False
        extension = self.load_extension(name)
        extensions[extension.module] = extension
        self.conf.extensions = extensions
        self.conf.export_extensions()
        return True

    def remove(self, extension, silent=False):
        self.logger.debug(f'Attempting to remove extension {extension}')
        if self.conf.extensions is None or extension not in self.conf.extensions:
            self.logger.error('Extension not found')
            return
        self.conf.extensions.pop(extension)
        self.conf.export_extensions()
        self.load_extension(extension).remove()
        msg = """Removed extension, additional cleanup may be necessary.
If manual cleanup is insufficient you may reset your environment as follows, 
be careful if there is data you need to preserve.

Close IntelliJ and run:
$CMD clean idea
$CMD clean git
$CMD clean config
$CMD init            
            """
        if not silent:
            self.logger.info(msg)

    def run(self, *args):
        args_count = len(args)
        if args_count == 0:
            self.logger.error('Missing extension and command')
            return
        if args_count == 1:
            self.logger.error('Missing command')
            return
        extension = args[0]
        command = args[1]
        parameters = args[2:] if args_count > 2 else []
        self.logger.debug(f'extension {extension}, command {command}, parameters: {parameters}')
        module = self.load_extension(extension)
        if module is None:
            return
        command_fn = getattr(module, command, None)
        if callable(command_fn):
            command_fn(*parameters)
        else:
            self.logger.error('No matching command in extension')
