import os

from commands.docker import Dock<PERSON><PERSON>ommand
from commands.skeleton import Skeleton<PERSON>ommand
from enums import ProjectType


class BootstrapCommand(SkeletonCommand):
    args: {}

    def __init__(self, conf, args=None):
        super().__init__(conf, args=args)

        self.working_dir = self.conf.docker_conf.get('DOCKER_DIR')
        self.bootstrap_db_schemas = self.conf.get('project', 'bootstrap_schemas')
        self.test_schema = self.conf.get('project', 'test_schema')

        # bootstrap database names
        self.bootstrap_name = None
        self.set_bootstrap_database_name()
        self.bootstrap_file = '%s.sql.gz' % self.bootstrap_name
        self.test_database_file = 'oscar_test.sql'
        # local file paths
        self.local_mysql_backup_directory = os.path.join(
            self.conf.project_dir, 'project', 'docker', 'mysql', 'backups'
        )
        self.local_bootstrap_database = os.path.join(
            self.local_mysql_backup_directory, self.bootstrap_file
        )
        self.local_test_database = os.path.join(
            self.local_mysql_backup_directory, self.test_database_file
        )
        # mysql configurations
        self.mysql_user = self.conf.get('mysql', 'username')
        self.mysql_password = self.conf.get('mysql', 'password')
        self.mysql_cmd = f'/usr/bin/mysql -u{self.mysql_user} -p{self.mysql_password} -h 127.0.0.1'
        self.oscar_schema = self.conf.get('project', 'oscar_schema')

        self.usage = """
\tNo params\t\tbootstrap all components
\tdb\t\t\tbootstrap db
\tcredentials\t\tbootstrap credentials
\tdb_dump\t\t\tcreate a new bootstrap dump
\tschema_dump {the_schema}\t\t\tcreate a new dump with just one schema
"""

    def set_bootstrap_database_name(self):
        self.bootstrap_name = self.conf.get('project', 'bootstrap_db')
        if self.bootstrap_name is None:
            self.bootstrap_name = self.conf.get('project', 'workspace')

    def default(self):
        self.logger.info('Bootstrapping database.')
        self.db()
        if self.conf.project_type != ProjectType.OCA:
            self.credentials()

    def db(self):
        self.logger.info(
            f'Bootstrapping database from {self.local_bootstrap_database}'
        )
        self.logger.debug(
            f'Checking for bootstrap db {self.local_bootstrap_database}'
        )
        if not os.path.exists(self.local_bootstrap_database):
            self.logger.error('Missing project bootstrap file.')
            return
        docker = DockerCommand(self.conf, self.args)
        self.set_docker_working_directory(docker)
        # ensure docker containers are running before proceeding.
        docker.up()
        # drop existing schemas
        self.drop_schemas(docker)
        # load bootstrap database
        self.load_bootstrap(self.bootstrap_file)

    def credentials(self):
        self.logger.info('Bootstrapping credentials.')
        docker = DockerCommand(self.conf, self.args)
        self.set_docker_working_directory(docker)
        # validate credentials file
        local_credentials_file = os.path.join(
            self.local_mysql_backup_directory, 'credentials.sql'
        )
        if not os.path.exists(local_credentials_file):
            self.logger.error(f'Missing credentials bootstrap file.')
            return
        cmd_credentials = f'"{self.mysql_cmd} {self.oscar_schema} ' \
                          f'< /backups/credentials.sql"'
        self.logger.debug('Bootstrapping credentials with: ' + cmd_credentials)
        docker.exec('-T', 'mysql', '/bin/bash', '-c', cmd_credentials)

    def set_docker_working_directory(self, docker):
        self.logger.debug(f'Setting working dir: {docker.working_dir}')
        os.chdir(docker.working_dir)

    def drop_schemas(self, docker):
        self.logger.debug(f'Removing existing schemas: {self.bootstrap_db_schemas}')
        cmd_drop = ''.join(
            [f'DROP SCHEMA IF EXISTS {s};' for s in self.bootstrap_db_schemas])
        docker.exec('-T', 'mysql', '/bin/bash', '-c',
                    f'"echo \'{cmd_drop}\' | {self.mysql_cmd}"')

    def drop_and_create_bootstrap_schema(self, docker, schema):
        self.logger.debug(f'Dropping and creating schema: {schema}')
        cmd_drop = f'DROP SCHEMA IF EXISTS {schema}; CREATE SCHEMA {schema};';
        docker.exec('-T', 'mysql', '/bin/bash', '-c',
                    f'"echo \'{cmd_drop}\' | {self.mysql_cmd}"')

    def load_bootstrap(self, bootstrap_file=None, schema=None):
        self.logger.info('Loading bootstrap schemas.')
        docker = DockerCommand(self.conf, self.args)
        self.set_docker_working_directory(docker)
        # drop existing schemas
        if schema is not None:
            self.drop_and_create_bootstrap_schema(docker, schema)
        # validate bootstrap file
        local_bootstrap_file = os.path.join(
            self.local_mysql_backup_directory, bootstrap_file
        )
        if bootstrap_file is None or not os.path.exists(local_bootstrap_file):
            self.logger.error(f'Missing bootstrap file {bootstrap_file}.')
            return
        # load bootstrap file
        self.logger.info(
            f'Loading bootstrap database file {local_bootstrap_file}'
        )
        cmd_load = f'"zcat /backups/{bootstrap_file} | {self.mysql_cmd} {"" if schema is None else schema}"'
        docker.exec('-T', 'mysql', '/bin/bash', '-c', cmd_load)

    def db_dump(self):
        self.logger.debug(f'Checking for file {self.local_bootstrap_database}')
        if os.path.exists(self.local_bootstrap_database):
            self.logger.info('Removing old bootstrap')
            os.remove(self.local_bootstrap_database)
        schemas = ' '.join(self.bootstrap_db_schemas)
        cmd_mysql_dump = f'/usr/bin/mysqldump -umysql -pmysql ' \
                         f'--routines --databases {schemas}'
        cmd_docker_exec = f'/bin/bash -c "{cmd_mysql_dump} ' \
                          f'| gzip -c > /backups/{self.bootstrap_file}"'
        docker = DockerCommand(self.conf, self.args)
        docker.exec('-T', 'mysql', cmd_docker_exec)

    def schema_dump(self, schema):
        self.logger.debug(f'Checking for file {self.local_bootstrap_database}')
        if os.path.exists(self.local_bootstrap_database):
            self.logger.info('Removing old bootstrap')
            os.remove(self.local_bootstrap_database)

        self.logger.info(f'Dumping schema for {schema}')

        cmd_mysql_dump = f'/usr/bin/mysqldump -umysql -pmysql ' \
                         f'--routines {schema}'
        cmd_docker_exec = f'/bin/bash -c "{cmd_mysql_dump} ' \
                          f'| gzip -c > /backups/{self.bootstrap_file}"'
        docker = DockerCommand(self.conf, self.args)
        docker.exec('-T', 'mysql', cmd_docker_exec)

    def schema_clone(self, sourceSchema, destSchema):
        self.logger.info(f'Cloning schema for {sourceSchema} to {destSchema}')

        # drop and create destination schema
        sql_recreate_schema = f'DROP SCHEMA IF EXISTS {destSchema}; ' \
                              f'CREATE SCHEMA {destSchema} character set latin1 collate latin1_swedish_ci;'
        cmd_recreate_schema = f"{self.mysql_cmd} -e '{sql_recreate_schema}'"
        cmd_mysql_dump = f'/usr/bin/mysqldump ' \
                         f'-u{self.mysql_user} ' \
                         f'-p{self.mysql_password} ' \
                         f'--routines {sourceSchema}'
        cmd_docker_exec = f'/bin/bash -c "{cmd_recreate_schema} && ' \
                          f'{cmd_mysql_dump} | {self.mysql_cmd} {destSchema}"'
        docker = DockerCommand(self.conf, self.args)
        docker.exec('-T', 'mysql', cmd_docker_exec)

    def unit_tests(self):
        self.logger.info('Initializing test database.')
        self.logger.debug(f'Checking for file {self.local_test_database}')
        if os.path.exists(self.local_test_database):
            self.logger.debug('Removing old bootstrap oscar_test schema')
            os.remove(self.local_test_database)

        self.logger.debug('Remove existing schemas')
        docker = DockerCommand(self.conf, self.args)
        self.logger.info('Ready to bootstrap ' + self.test_schema + ' using ' + self.oscar_schema)

        # create test database schema
        # defaults:
        #   self.test_schema: 'oscar_test'
        cmd_drop = f'DROP SCHEMA IF EXISTS {self.test_schema}; CREATE SCHEMA {self.test_schema};'
        docker.exec('-T', 'mysql', '/bin/bash', '-c', f'"echo \'{cmd_drop}\' | {self.mysql_cmd}"')

        # dump oscar database and load into test schema
        cmd_oscar_test_dump = f'/usr/bin/mysqldump ' \
                         f'-umysql -pmysql --no-data ' \
                         f'{self.oscar_schema} ' \
                         f'| /usr/bin/mysql -umysql -pmysql {self.test_schema}'
        cmd_docker_exec = f'/bin/bash -c "{cmd_oscar_test_dump}"'
        self.logger.info('mysqldump: ' + cmd_oscar_test_dump)
        docker.exec('-T', 'mysql', cmd_docker_exec)

        # run stored procedure to append '_maventest' to all table names.
        cmd_oscar_test_rename = f'/usr/bin/mysql -umysql -pmysql < ' \
                                f'{self.get_unit_tests_bootstrap_file()}'
        cmd_docker_exec = f'/bin/bash -c "{cmd_oscar_test_rename}"'
        self.logger.info('mysqldump: ' + cmd_oscar_test_rename)
        docker.exec('-T', 'mysql', cmd_docker_exec)

    def get_unit_tests_bootstrap_file(self):
        if self.conf.project_type == ProjectType.IC:
            return '/backups/bootstrap-indivicare-tests.sql'
        else:
            return '/backups/bootstrap-oscar-pro-tests.sql'
