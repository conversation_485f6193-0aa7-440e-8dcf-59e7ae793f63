import os
import subprocess
import sys

import util
from commands.skeleton import SkeletonCommand


class DockerCommand(SkeletonCommand):

    def __init__(self, conf, args=None):
        super().__init__(conf, args=args)
        self.working_dir = self.conf.docker_conf.get('DOCKER_DIR')
        self.usage = """
Usage:
\tbuild\t\tcreate containers
\tdown\t\tdelete container data
\texec\t\trun command against container
\tps\t\tshow container status
\trestart\t\trestart containers
\tstop\t\tstop containers
\tup\t\tstart containers
"""
        self.COMMANDS = {
            'build': self.build,
            'down': self.down,
            'exec': self.exec,
            'ps': self.ps,
            'restart': self.restart,
            'stop': self.stop,
            'up': self.up
        }

    def build(self, *args):
        command = "build "
        self.run_command(command, args)

    def down(self, *args):
        if not util.confirm(
            'This will remove volumes and data may be lost, '
                'are you sure you want to proceed?', default_no=True):
            self.logger.debug('Skipping')
            return
        command = "down -v "
        self.run_command(command, [])

    def exec(self, *args):
        command = "exec "
        self.run_command(command, args)

    def ps(self, *args):
        command = "ps "
        self.run_command(command, [])

    def restart(self, *args):
        command = "restart "
        self.run_command(command, args)

    def stop(self, *args):
        command = "stop "
        self.run_command(command, args)

    def up(self, *args):
        self.stop()
        if self.args.docker_daemon:
            command = "up -d "
        else:
            command = "up "
        self.run_command(command, args)

    def run_command(self, command, args):
        os.chdir(self.working_dir)
        docker_command = f'docker-compose -p {self.conf.get("project", "workspace")} {command}'
        docker_command += self.add_args(args)
        self.logger.debug(docker_command)
        stdout, stderr = super().get_subprocess_stdout(['exec', 'ps'], command)
        subprocess.call(
            docker_command,
            stdout=stdout,
            stderr=stderr,
            shell=True
        )

    @staticmethod
    def add_args(args):
        return ' '.join(args) if len(args) > 0 else ''
