from argparse import Namespace

import git
import os
import shutil
import ruamel.yaml
from git import GitCommandError

import util
from commands.bootstrap import Bootstrap<PERSON>ommand
from commands.config import ConfigCommand
from commands.docker import DockerCommand
from commands.git import GitCommand
from commands.migrate import <PERSON>gra<PERSON><PERSON>ommand
from commands.skeleton import Skel<PERSON><PERSON>ommand
from enums import ProjectType
from template import TemplateRenderer


class InitCommand(SkeletonCommand):
    args: Namespace
    positional: list
    docker: 'DockerCommand'

    def __init__(self, conf, args=None):
        super().__init__(conf, args=args)
        self.project_type = self.conf.project["type"]
        self.template_renderer = TemplateRenderer(self.conf)
        self.yaml = ruamel.yaml.YAML()
        self.usage = """
Usage:
\tNo params\t\tinitialize project components
\tproject\tinitialize project structure requires a config file be supplied with -c --config
"""

    def default(self):
        # Initialize git early to collect all user input at the beginning.
        self.conf.init_git()
        self.register_extensions()
        self.docker = DockerCommand(self.conf, self.args)
        config_command = ConfigCommand(self.conf, args=self.args)
        config_command.all()
        self.intellij_init()
        if not self.args.init_skip_git:
            self.logger.info('Initializing git.')
            self.git_checkout_modules()
        self.copy_iml_files()
        self.docker_init()
        if not self.args.init_skip_db:
            self.docker.up()
            self.logger.info('Initializing database.')
            self.bootstrap_databases()
            self.migrate_databases()
        if self.conf.project_type == ProjectType.IC:
            self.logger.info('Initializing driver.')
            if self.args.init_skip_git:
                self.logger.warn(
                    'Indivicare 4 requires git configured for tomcat configs.'
                )
            self.git_checkout_modules()
            self.docker_restart_driver()
        config_command.tomcat(self.args)
        self.init_extensions()

    def register_extensions(self):
        from commands.extension import ExtensionCommand
        command = ExtensionCommand(self.conf, self.args)
        extensions = self.conf.get('extensions')
        # validate extensions
        if extensions is None:
            self.logger.debug('No extensions found')
            return
        # load extensions
        for extension in extensions:
            self.logger.debug(f'Found extension {extension}')
            # Skipping the tools extension should be considered for advanced
            # cases. Those users can remove it manually from the config prior to
            # running init
            if not extension == 'tools' \
                and self.args.init_confirm_extensions \
                    and not util.confirm(
                        f'Do you want to install {extension}', default_no=True):
                print(f'Skipping: {extension}')
                continue
            # Only want to load extension data and save configuration
            command.register(extension)

    def init_extensions(self):
        from commands.extension import ExtensionCommand
        command = ExtensionCommand(self.conf, self.args)
        extensions = self.conf.extensions.keys()
        if extensions is None:
            self.logger.debug('No extensions found')
            return
        for extension in extensions:
            self.logger.debug(f'Found extension {extension}')
            # Only want to run extension initialization tasks
            command.add(extension, config=False, init=False, register=False)

    def intellij_init(self):
        self.logger.info('Initializing IntelliJ configuration.')
        self.initialize_idea_directory()
        self.initialize_extensions()

    def initialize_idea_directory(self):
        return self.initialize_intellij_directory('idea', True)

    def initialize_run_directory(self):
        return self.initialize_intellij_directory('run')

    def initialize_intellij_directory(self, name, project_specific=False):
        project_directory = self.get_project_directory_path(
            name, project_specific
        )
        local_directory = os.path.join(self.conf.project_dir, f'.{name}')
        if not os.path.exists(project_directory):
            self.logger.info(f'Missing {project_directory}')
            self.create_intellij_directory(name)
        self.logger.debug(f'Setting up project .{name} directory')
        self.logger.debug(f'From: {project_directory}')
        self.logger.debug(f'To: {local_directory}')
        shutil.copytree(
            project_directory, local_directory, dirs_exist_ok=True
        )

    def create_intellij_directory(self, name):
        self.logger.info(f'Missing {name} dir for project {self.project_type}')
        local_directory = os.path.join(self.conf.project_dir, f'.{name}')
        self.logger.info(f'Making directory: {local_directory}')
        os.mkdir(local_directory)

    def get_project_directory_path(self, name, project_specific=False):
        path = os.path.join(
            self.conf.project_dir,
            'project',
            'resources',
            'intellij',
            name
        )
        if project_specific:
            path = os.path.join(
                path,
                # self.conf.project['idea_dir']
                #   legacy name for the project top level directory
                self.conf.project['idea_dir']
            )
        return path

    def initialize_extensions(self):
        for extension in self.conf.extensions:
            self.conf.extensions[extension].init_idea()

    def git_checkout_modules(self):
        # Init git
        self.logger.debug('Checking for upstream')
        repo = git.Repo(self.conf.project_dir)
        git_command = GitCommand(self.conf, args=self.args)
        remote = git_command.get_remote_user()
        if remote != 'origin':
            try:
                exception = repo.remotes['upstream']
                self.logger.debug('Upstream already configured.')
            except IndexError:
                self.logger.debug('Adding upstream.')
                git.Remote(repo, 'origin').add(
                    repo,
                    'upstream',
                    GitCommand.get_url(remote, self.conf.project)
                )
            if not self.args.skip_set_upstream:
                repo.remotes['upstream'].fetch()
                repo.git.branch('-u', 'upstream/master')

        self.logger.info('Cloning git modules')
        for module in self.modules:
            try:
                git_command.clone(module)
            except GitCommandError:
                self.logger.debug("Git error ignored, attempting to continue.")

    def copy_iml_files(self):
        self.logger.info('Copying iml files')
        for module_name in self.modules:
            self.copy_iml_file(module_name)

    def copy_iml_file(self, module_name):
        module = self.modules[module_name]
        if 'iml' not in module:
            self.logger.debug(
                f'Skipping {module_name}, missing iml definition.'
            )
            return
        iml_file = os.path.join(self.conf.project_dir, module['iml'])
        if not os.path.exists(iml_file):
            self.logger.error(
                f"module {module_name} missing iml ({module['iml']})"
            )
            return
        module_dir = os.path.join(self.conf.project_dir, module['repository'])
        if not os.path.exists(module_dir):
            self.logger.error(
                f'module {module_name} has not been checked out to {module_dir}'
            )
            return
        if not os.path.isdir(module_dir):
            self.logger.error(f'module {module_dir} is not a directory')
            return
        self.logger.debug(f"Copying {iml_file} to {module_dir}")
        shutil.copy(iml_file, module_dir)

    def docker_init(self):
        self.logger.info('Initializing docker containers')
        self.logger.debug(f'Setting working dir {self.docker.working_dir}')
        os.chdir(self.docker.working_dir)
        self.add_mysql_dockerfile_to_docker_compose()
        self.docker.build()
        self.docker.up()

    def bootstrap_databases(self):
        BootstrapCommand(self.conf, self.args).default()

    def migrate_databases(self):
        MigrateCommand(self.conf, args=self.args).default()

    def docker_restart_driver(self):
        os.chdir(self.docker.working_dir)
        self.docker.restart('driver')

    def add_mysql_dockerfile_to_docker_compose(self):
        if self.project_type != "kai-project":
            return
        version = input('Enter MySQL version (Versions: 5.7, 8): ')
        if version not in ['5.7', '8']:
            print('Invalid version, using 8 as default.')
            version = '8'
        docker_compose = os.path.join(
            self.docker.working_dir,
            'docker-compose.yml'
        )
        mysql_dockerfile = {
            'dockerfile': f'MySQL{version}-run.Dockerfile'
        }
        with open(docker_compose, 'r') as file:
            data = self.yaml.load(file)
            data['services']['mysql']['build'].update(mysql_dockerfile)
        if data:
            with open(docker_compose, 'w') as file:
                self.yaml.dump(data, file)
