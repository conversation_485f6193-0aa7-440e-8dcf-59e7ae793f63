import jinja2
from jinja2 import Environment, FileSystemLoader
from jinja2.meta import find_undeclared_variables
from pathlib import Path

from parser import JavaPropertyParser
from util import *


class TemplateRenderer(object):
    def __init__(self, conf):
        self.conf = conf
        self.log = LOG

    def get_template(self, template_dir, file):
        self.log.debug(f'Rendering {file}')
        template_context = self.get_template_context()
        env = Environment(
            undefined=jinja2.DebugUndefined,
            loader=FileSystemLoader(Path(template_dir))
        )
        template = env.get_template(file)
        rendered = template.render(template_context)
        ast = env.parse(rendered)
        undefined = find_undeclared_variables(ast)
        if undefined:
            self.log.error("Found undefined variables:")
            self.log.error(undefined)
            return None
        return rendered

    def get_template_context(self):
        self.log.trace('rendering template with')
        self.log.trace(self.conf)
        oscar_schema = self.conf.get('project', 'oscar_schema')
        extensions = {}
        if self.conf.extensions is not None:
            extensions = {extension: True for extension in self.conf.extensions}
        context = {
            'project_dir': escape_path(self.conf.project_dir),
            # list of installed extensions
            'extensions': extensions,
            # top level application document directory names
            'documents': escape_path(os.path.join(
                self.conf.project_dir,
                self.conf.get('project', 'documents')
            )),
            # tomcat
            'tomcat_path': escape_path(os.path.join(
                self.conf.project_dir,
                'project', 'tomcat', 'server'
            )),
            # schemas
            'oscar_schema': oscar_schema,
            # mysql credentials
            'mysql_user': self.conf.get('mysql', 'username'),
            'mysql_password': self.conf.get('mysql', 'password'),
            # dependency ports
            'mysql_port': self.conf.get('docker', 'mysql', 'port'),
            'tomcat_port': self.conf.get('docker', 'tomcat', 'port'),
            'tomcat_covering_port': self.conf.get('docker', 'tomcat-covering',
                                                  'port'),
            'nginx_port': self.conf.get('docker', 'nginx', 'port'),
            'nginx_covering_port': self.conf.get('docker', 'nginx-covering',
                                                 'port'),
            # application ports
            'invoice_port': self.conf.get('docker', 'invoice', 'port'),
        }
        if self.conf.git:
            context['git'] = {
                'username': self.conf.git.get_user()
            }
        for extension in extensions:
            if self.conf.get(extension):
                context[extension] = self.conf.get(extension)
        return {**context, **{'conf': self.conf.conf}}

    def render_template_dir(self, template_dir, target_dir, templates=None, pattern='*'):
        """
        Render all templates in a directory and write them to the target directory

        :param template_dir: the directory to look for templates in
        :param target_dir: the directory to write templates to
        :param templates: a list of templates to ignore
        :param pattern: file pattern for templates
        """
        if templates is None:
            templates = []
        self.log.trace(
            f"Iterating over templates in {template_dir} matching '{pattern}'")
        for file in get_glob(template_dir, pattern):
            if os.path.isdir(file):
                continue
            self.log.trace(f'Found template: {file}')
            template_file = os.path.basename(file)
            # Skip templates already processed
            if template_file in templates:
                continue
            templates += [template_file]
            rendered = self.render_template(
                template_dir,
                template_file,
                target_dir
            )
            # If nothing was rendered skip
            if rendered is None:
                continue
            self.log.debug(f'Writing {file}')
            with open(os.path.join(target_dir, template_file), 'w') as f:
                f.write(rendered)

    def render_template(
            self, template_dir, template_file,
            target_dir=None, target_file=None, write=False):
        """
        Render a template with jinja

        NOTE: if the template already exists in target_dir it will merge in any
        new values while preserving any changes in the previously rendered result.

        :param template_dir: the directory where the template can be found
        :param template_file: the name of the template file to render
        :param target_dir: the directory where the rendered content will be placed
        :param target_file: the filename to use for the rendered template
        :param write: when true will write the template to disk
        :returns the rendered template
        """
        if target_file is None:
            target_file = template_file
        rendered = self.get_template(template_dir, template_file)
        # If this template already exists we want to merge in any new values
        # that don't overwrite existing values
        if rendered is None:
            return
        if target_dir is not None:
            template = os.path.join(target_dir, target_file)
            if os.path.exists(template):
                rendered = self.merge_template_file(template, rendered)
        if write:
            with open(os.path.join(target_dir, target_file), 'w') as f:
                f.write(rendered)
        return rendered

    def merge_template_file(self, target_file, rendered_template):
        """
        Merge a rendered template into a template file
        :param target_file: the existing file to merge into
        :param rendered_template: the rendered content to merge
        :return: a combination of the existing file and the rendered template
         giving precedence to values already present in the existing file
        """
        if not os.path.exists(target_file):
            return rendered_template
        template_text = Path(target_file).read_text()
        return self.merge_template(template_text, rendered_template)

    def merge_template(self, template_text, rendered):
        """
        Merge a rendered template string into another template string
        :param template_text: original template string
        :param rendered: rendered template string
        :return: a combination of the existing template string and the
         rendered template giving precedence to values already present
         in the template string
        """
        parser = JavaPropertyParser()
        template_properties = parser.parse_string(template_text)
        rendered_properties = parser.parse_string(rendered)
        # Find properties that exist in template and don't in rendered.
        source_properties = parser.get_unique_reference_properties(
            template_properties,
            rendered_properties)
        # Add those to rendered
        if len(source_properties) > 0:
            properties_string = [
                '%s=%s' % (prop, source_properties[prop])
                for prop in source_properties
            ]
            new_properties = '\n'.join(properties_string)
            template_text += '\n%s\n' % new_properties
        return template_text
