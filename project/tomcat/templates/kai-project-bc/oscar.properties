db_name={{ oscar_schema }}?zeroDateTimeBehavior=round&useOldAliasMetadataBehavior=true&jdbcCompliantTruncation=false&useSSL=false
db_uri=**********************:{{ mysql_port }}/
db_username={{ mysql_user }}
db_password={{ mysql_password }}

drugref_url=http://localhost:8080/drugref/DrugrefService

buildDateTime=${build.dateTime}
buildtag=Is it working?

oscar_port = 8080

BASE_DOCUMENT_DIR={{ documents }}/oscar

# Billing download folder
HOME_DIR = {{ documents }}/oscar/billing/download/

# Documents directory
DOCUMENT_DIR = {{ documents }}/oscar/
INCOMINGDOCUMENT_DIR={{ documents }}/oscar/incoming

MY_OSCAR = no
myOSCAR.url=http://127.0.0.1:8095/myoscar_client/
myoscar_server_base_url=http://localhost:8090/myoscar_server/ws

admin_user_billing_control=true

# rtl_template_url = D:/Development/FlyingSaucer/testTemplate.html

WKHTMLTOPDF_COMMAND=wkhtmltopdf
WKHTMLTOPDF_ARGS = --print-media-type --javascript-delay 1000 --debug-javascript --no-stop-slow-scripts

ModuleNames=REST
angular.debug=true
#insig.api_key=DEV_KEY_FOR_TESTING
oscar_override_properties={{ project_dir}}/project/tomcat/resources/wpde-override.properties
WS_LOG_LEVEL=full
# used for loading test scripts
basedir={{ project_dir }}/oscar


allow_online_booking = true
enable_appointment_reminders = true
clinic_endpoint_URL = test-site.bookmd.ca
clinic_URL = https://test-site.kai-oscar.com


#cme_js=kai
#cme_js=kai
#cme_js=kai
cme_js=kai

rx_fax_enabled=true

## BC Settings
billregion=BC
hctype=BC
## yes : TURNS ON NEW BC BILLING
## no : TURNS OFF NEW BC BILLING
NEW_BC_TELEPLAN = yes

## yes: turn on the default BC alt billing
## no : turn off the default BC alt billing
#BC_DEFAULT_ALT_BILLING=yes

BC_BILLING_CODE_MANAGEMENT = yes
tomcat_path = {{ project_dir}}/project/tomcat/server
project_home = oscar

province_names = AB|AB-Alberta|BC|BC-British Columbia|MB|MB-Manitoba|NB|NB-New Brunswick|NF|NF-Newfoundland & Labrador|NT|NT-Northwest Territory|NS|NS-Nova Scotia|NU|NU-Nunavut|ON|ON-Ontario|PE|PE-Prince Edward Island|QC|QC-Quebec|SK|SK-Saskatchewan|YT|YT-Yukon|US|US resident

cardswipe=false
hide_eConsult_link=yes
default_city=
workflow_enhance=true
mandatory_password_reset=true
rx_enhance=true
new_flowsheet_enabled = true

clinic.url=https://dev.well.company/

## CONTROLS WHICH LABS ARE USED
#PATHNET_LABS=yes
HL7TEXT_LABS = yes
#MDS_LABS=yes
CML_LABS = yes
#Epsilon_LABS=yes

oscar.auth.encrypted=false

## OMD setting
OMD_hrm = {{ documents }}/oscar/hrm/
OMD_directory = {{ documents }}/oscar/hrm/OMD/
OMD_log_directory = {{ documents }}/oscar/hrm/logs/
OMD_stored = {{ documents }}/oscar/hrm/stored/
OMD_downloads = {{ documents }}/oscar/hrm/sftp_downloads/

# Enable or disable the apps.health Gateway
apps_health.gateway.enabled=false