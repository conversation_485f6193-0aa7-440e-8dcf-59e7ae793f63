# OSCAR override properties used for unit testing.
wpde=true
wpde.directory=/home/<USER>/WellProjects/kai-project-on/
wpde.oscar.directory=/home/<USER>/WellProjects/kai-project-on/oscar

# DaoTestFixtures.java requirements:
# - db_name
# - db_schema_properties
# - db_username
# - db_password
# - db_url
# - db_driver
db_schema=oscar_test
db_host=127.0.0.1
db_user=mysql
db_password=mysql
db_uri=***************************/
db_url_prefix=***************************/
db_driver=com.mysql.jdbc.Driver

# zeroDateTimeBehavior:
#   Change the behavior of date 0000-00-00 to automatically become a valid date by rounding. The new
#   default is to throw an exception, whereas previous versions of Connector/J converted to null.
#   For type safety and to lower the number of exceptions thrown by OSCAR, round is the logical
#   choice.
# useOldAliasMetadataBehavior:
#   Due to issues with Hibernate column mapping, queries which rename columns will cause Hibernate
#   to throw an exception. Previous version of Connector/J used a different method of determining
#   column names which allowed the renaming of columns.
# jdbcCompliantTruncation:
#   Fields which are not included in a query, and do not contain a default value in the database,
#   will raise an exception in the JDBC specification. Previous versions of Connector/J were not
#   JDBC compliant and did not follow the truncation specifications.
# IMPORTANT:
#   The fields listed after the ? are required! Otherwise, OSCAR will not behave properly as it
#   depends on legacy functionality. Ensure that you leave the tags behind the field when renaming
#   the database.
db_name=oscar_test?zeroDateTimeBehavior=round&useOldAliasMetadataBehavior=true&jdbcCompliantTruncation=false&useSSL=false
db_schema_properties=?zeroDateTimeBehavior=round&useOldAliasMetadataBehavior=true&jdbcCompliantTruncation=false&useSSL=false

# Used to initialize OscarProperties in test cases
test_context=oscar
buildDateTime=foo

# Integration Properties
## When true e2e test cases will be executed
integration.e2e_enabled=false
## URL the integration E2E test cases will call
integration.e2e_base_url=http://localhost:4010/api/v1

# REST Testing
test.ws.rest.enabled=true
