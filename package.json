{"name": "oscar-pro", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "nx", "nx": "nx", "start": "nx serve --port 4200 --host 0.0.0.0 --disable-host-check --configuration=dev", "build": "nx build", "build:oscar-pro": "nx build oscar-pro --baseHref ./ && npm run postbuild:oscar-pro && ./scripts/gen-package.sh", "build:oscar-pro:prod": "nx build oscar-pro --prod --baseHref ./ && npm run postbuild:oscar-pro && ./scripts/gen-package.sh", "build:oscar-pro:snapshot": "nx build oscar-pro --prod --baseHref ./ && npm run postbuild:oscar-pro && ./scripts/gen-package.sh SNAPSHOT", "build:oscar-pro:release": "nx build oscar-pro --prod --baseHref ./ && npm run postbuild:oscar-pro && ./scripts/gen-package.sh RELEASE", "postbuild:oscar-pro": "cd dist/apps/oscar-pro && make-dir static && cpy * '!static/' '!package.json' '!index.html' static/ && del * '!static/' '!index.html' '!package.json' && cd ../../..", "publish:oscar-pro": "npm run build:oscar-pro && cd dist/apps/oscar-pro && npm publish --registry=https://nexus.kai-oscar.com/repository/releases-npm/", "test": "nx run-many --target=test --runInBand --codeCoverage", "lint": "nx run-many --target=lint --quiet", "affected:apps": "nx affected:apps", "affected:libs": "nx affected:libs", "affected:build": "nx affected:build", "affected:test": "nx affected:test", "affected:lint": "nx affected:lint", "affected:dep-graph": "nx affected:dep-graph", "affected": "nx affected", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "update": "nx migrate latest", "workspace-generator": "nx workspace-generator", "dep-graph": "nx dep-graph", "help": "nx help"}, "private": true, "dependencies": {"@angular/animations": "19.2.14", "@angular/cdk": "19.2.18", "@angular/cdk-experimental": "19.2.18", "@angular/common": "19.2.14", "@angular/compiler": "19.2.14", "@angular/core": "19.2.14", "@angular/forms": "19.2.14", "@angular/platform-browser": "19.2.14", "@angular/platform-browser-dynamic": "19.2.14", "@angular/router": "19.2.14", "@fortawesome/angular-fontawesome": "^0.14.0", "@fortawesome/fontawesome-svg-core": "^1.2.35", "@fortawesome/free-solid-svg-icons": "^5.15.3", "@ngrx/component-store": "19.0.0", "@ngrx/effects": "19.0.0", "@ngrx/entity": "19.0.0", "@ngrx/operators": "19.0.0", "@ngrx/router-store": "19.0.0", "@ngrx/store": "19.0.0", "@nx/angular": "20.8.2", "@okta/okta-angular": "^5.2.0", "@okta/okta-signin-widget": "^6.4.1", "@oscarpro/well-ui": "2.0.0", "@primeng/themes": "^19.0.0", "@schematics/angular": "19.2.9", "canvas": "^2.11.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "filepond": "^4.30.3", "filepond-plugin-file-metadata": "^1.0.8", "filepond-plugin-file-poster": "^2.5.1", "filepond-plugin-file-validate-type": "^1.2.6", "filepond-plugin-image-preview": "^4.6.10", "filepond-plugin-image-resize": "^2.0.10", "jest-canvas-mock": "^2.4.0", "lodash": "^4.17.21", "moment": "^2.29.3", "ngx-filepond": "^7.0.0", "ngx-infinite-scroll": "^19.0.0", "ngx-moment": "^6.0.0", "ngx-toastr": "^19.0.0", "primeng": "^19.0.0", "rxjs": "~7.8.0", "swagger-ui-dist": "^5.25.2", "sweetalert2": "^11.14.2", "tslib": "^2.3.0", "zone.js": "0.15.1"}, "devDependencies": {"@angular-builders/custom-webpack": "^19.0.0", "@angular-devkit/build-angular": "19.2.9", "@angular-devkit/core": "19.2.9", "@angular-devkit/schematics": "19.2.9", "@angular-eslint/eslint-plugin": "19.2.0", "@angular-eslint/eslint-plugin-template": "19.2.0", "@angular-eslint/template-parser": "19.2.0", "@angular/cli": "~19.2.0", "@angular/compiler-cli": "19.2.14", "@angular/language-service": "19.2.14", "@ngneat/tailwind": "^7.0.3", "@ngrx/schematics": "19.0.0", "@ngrx/store-devtools": "19.0.0", "@nx/eslint": "20.8.2", "@nx/eslint-plugin": "20.8.2", "@nx/jest": "20.8.2", "@nx/js": "20.8.2", "@nx/node": "20.8.2", "@nx/workspace": "20.8.2", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@tailwindcss/aspect-ratio": "0.2.1", "@tailwindcss/forms": "0.3.3", "@tailwindcss/line-clamp": "0.2.1", "@tailwindcss/typography": "0.4.1", "@types/crypto-js": "^4.2.2", "@types/jest": "29.5.14", "@types/lodash": "^4.14.171", "@types/node": "18.16.9", "@types/webpack": "^5.0.0", "@typescript-eslint/eslint-plugin": "7.16.0", "@typescript-eslint/parser": "7.16.0", "@typescript-eslint/utils": "^7.16.0", "autoprefixer": "10.4.0", "cpy-cli": "^4.2.0", "del-cli": "^5.0.0", "dotenv": "10.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "10.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-preset-angular": "14.4.2", "make-dir": "^3.1.0", "make-dir-cli": "^3.0.0", "move-file-cli": "^3.0.0", "ng-mocks": "^14.0.0", "ng-packagr": "19.2.2", "nx": "20.8.2", "postcss": "^8.4.5", "postcss-import": "14.1.0", "postcss-loader": "^4.2.0", "postcss-nesting": "^12.1.5", "postcss-preset-env": "^6.7.0", "postcss-scss": "^3.0.5", "postcss-url": "^10.1.1", "prettier": "2.6.2", "stylelint-config-recommended": "^5.0.0", "tailwindcss": "3.0.2", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "5.7.3", "webpack": "^5.73.0"}, "optionalDependencies": {"@esbuild/linux-x64": "^0.25.5", "@nx/nx-darwin-arm64": "18.2.4", "@nx/nx-darwin-x64": "18.2.4", "@nx/nx-linux-x64-gnu": "18.2.4", "@nx/nx-win32-x64-msvc": "18.2.4", "@rollup/rollup-linux-x64-gnu": "4.9.5"}}