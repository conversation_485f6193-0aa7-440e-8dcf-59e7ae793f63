<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>ca.kai</groupId>
  <artifactId>kai-emr</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>war</packaging>

  <name>kaiemr</name>
  <description>KAI EMR</description>

  <distributionManagement>
    <repository>
      <id>nexus</id>
      <name>Nexus</name>
      <url>https://nexus.kai-oscar.com/repository/releases/</url>
    </repository>
  </distributionManagement>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>1.5.3.RELEASE</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <!--  as used in https://github.com/oktadeveloper/okta-spring-boot-angular-auth-code-flow-example-->
    <okta.version>0.5.0</okta.version>

    <!-- properties for controlling test execution -->
    <skipTests>false</skipTests>
    <skip.unit.tests>${skipTests}</skip.unit.tests>
    <skip.it.tests>${skipTests}</skip.it.tests>

    <!-- properties for controlling build execution -->
    <maven.war.skip>false</maven.war.skip>

    <skipTests>false</skipTests>
    <skip.unit.tests>${skipTests}</skip.unit.tests>
    <skip.it.tests>${skipTests}</skip.it.tests>
    <oscar.dbinit.skip>false</oscar.dbinit.skip>
    <kotlin.version>2.0.0</kotlin.version>
    <kotlin.compiler.incremental>true</kotlin.compiler.incremental>
  </properties>
  <repositories>
    <repository>
      <!-- resolves issue with jasperreports dependency unable to download itext -->
      <id>jaspersoft-third-party</id>
      <url>https://jaspersoft.jfrog.io/jaspersoft/third-party-ce-artifacts/</url>
    </repository>
    <repository>
      <id>oscarpro</id>
      <url>https://cobalt.azu.oscar-emr.net:63827/repository/public-releases/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
    <repository>
      <id>local_repo</id>
      <url>file://${basedir}/local_repo</url>
    </repository>
  </repositories>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-bom</artifactId>
        <version>2.24.3</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- KOTLIN -->
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib</artifactId>
      <version>${kotlin.version}</version>
    </dependency>
    <!--		Without these slf4j, logs are not written to tomcat log file-->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.32</version>
    </dependency>

    <!-- Log4j 2 -->
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-layout-template-json</artifactId>
    </dependency>

    <!-- Log4j 1 to Log4j 2 Bridge -->
    <!-- Routes legacy Log4j calls to Log4j 2 -->
    <!-- Allows gradual migration of legacy Log4j code to Log4j 2 -->
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-1.2-api</artifactId>
    </dependency>

    <!-- Logback Classic Implementation -->
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.2.3</version> <!-- Use the appropriate version -->
      <scope>test</scope> <!-- Use this if you only need Logback for testing -->
    </dependency>

    <!-- Logback Core Module -->
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
      <version>1.2.3</version> <!-- Use the appropriate version -->
      <scope>test</scope> <!-- Use this if you only need Logback for testing -->
    </dependency>

    <dependency>
      <groupId>org.simplejavamail</groupId>
      <artifactId>simple-java-mail</artifactId>
      <version>4.4.5</version>
    </dependency>
    <dependency>
      <groupId>com.twilio.sdk</groupId>
      <artifactId>twilio</artifactId>
      <version>(7.11.0,7.12.0]</version>
      <exclusions>
        <exclusion>
          <groupId>io.jsonwebtoken</groupId>
          <artifactId>jjwt</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.twilio.sdk</groupId>
      <artifactId>twilio-java-sdk</artifactId>
      <version>(6.0,6.9)</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.jsonwebtoken</groupId>
          <artifactId>jjwt</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
      <exclusions>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>log4j-over-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>jul-to-slf4j</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.security.oauth</groupId>
      <artifactId>spring-security-oauth2</artifactId>
      <version>2.2.1.RELEASE</version>
      <!--		Spring Boot 2.x has this upgraded version by default, Spring Boot 1.x does not. - https://github.com/okta/okta-spring-boot/issues/41-->
      <!--		oscar-pro was using this 2.0.14.RELEASE, but fails with Okta 0.4.0 and 0.5.0-->
      <!--		<version>2.0.14.RELEASE</version>-->
    </dependency>
    <dependency>
      <!-- enables use of redis session persistence options -->
      <groupId>org.springframework.session</groupId>
      <artifactId>spring-session-data-redis</artifactId>
    </dependency>
    <!--without this-->
    <!--		Property: security.oauth2.resource.tokenInfoUri-->
    <!--		Value: null-->
    <!--		Reason: Missing tokenInfoUri and userInfoUri and there is no JWT verifier key-->
    <dependency>
      <groupId>com.okta.spring</groupId>
      <artifactId>okta-spring-boot-starter</artifactId>
      <version>0.5.0</version>
      <exclusions>
        <exclusion>
          <groupId>com.okta.spring</groupId>
          <artifactId>okta-spring-security-oauth2</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-jwt</artifactId>
      <version>1.0.7.RELEASE</version>
      <exclusions>
        <exclusion>
          <groupId>org.bouncycastle</groupId>
          <artifactId>bcpkix-jdk15on</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.33</version>
      <scope>runtime</scope>
    </dependency>

    <!-- This can be removed as soon as we update Spring Boot to version 2 or higher, which uses Hibernate 5.2+ -->
    <!-- This allows for using Java Time in Hibernate, and was merged into the core Hibernate package. -->
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-java8</artifactId>
      <version>5.0.1.Final</version>
    </dependency>

    <!-- SPRING -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-tomcat</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.restdocs</groupId>
      <artifactId>spring-restdocs-mockmvc</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.github.springtestdbunit</groupId>
      <artifactId>spring-test-dbunit</artifactId>
      <version>1.2.1</version>
      <scope>test</scope>
    </dependency>

    <!-- TESTING -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
      <version>5.9.0</version>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>5.9.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.25.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.8.0</version>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <version>3.8.0</version>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-module-junit4</artifactId>
      <version>2.0.9</version>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-api-mockito2</artifactId>
      <version>2.0.9</version>
    </dependency>
    <dependency>
      <groupId>com.jayway.jsonpath</groupId>
      <artifactId>json-path</artifactId>
      <version>2.0.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.dbunit</groupId>
      <artifactId>dbunit</artifactId>
      <version>2.5.0</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.6</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.apache.pdfbox/pdfbox -->
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox</artifactId>
      <version>2.0.21</version>
    </dependency>
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>jbig2-imageio</artifactId>
      <version>3.0.3</version>
    </dependency>
    <dependency>
      <groupId>com.github.jai-imageio</groupId>
      <artifactId>jai-imageio-jpeg2000</artifactId>
      <version>1.3.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.librepdf</groupId>
      <artifactId>openpdf</artifactId>
      <version>1.0.5</version>
      <exclusions>
        <exclusion>
          <groupId>org.bouncycastle</groupId>
          <artifactId>bcpkix-jdk15on</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.bouncycastle</groupId>
          <artifactId>bcprov-jdk15on</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.openhtmltopdf</groupId>
      <artifactId>openhtmltopdf-pdfbox</artifactId>
      <version>1.0.10</version>
    </dependency>
    <dependency>
      <groupId>net.sf.jtidy</groupId>
      <artifactId>jtidy</artifactId>
      <version>r938</version>
    </dependency>
    <dependency>
      <groupId>org.apache.tika</groupId>
      <artifactId>tika-core</artifactId>
      <version>1.16</version>
    </dependency>
    <dependency>
      <groupId>com.github.dhorions</groupId>
      <artifactId>boxable</artifactId>
      <version>1.5</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-rest-core</artifactId>
      <version>2.6.4.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <version>2.5</version>
    </dependency>
    <dependency>
      <groupId>org.eclipse.persistence</groupId>
      <artifactId>org.eclipse.persistence.moxy</artifactId>
      <version>2.5.0</version>
    </dependency>
    <dependency>
      <groupId>org.jdom</groupId>
      <artifactId>jdom</artifactId>
      <version>1.1.3</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.7</version>
    </dependency>
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-structures-dstu2.1</artifactId>
      <version>5.1.0</version>
    </dependency>
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-validation-resources-dstu2.1</artifactId>
      <version>5.1.0</version>
    </dependency>

    <!-- cds hrm -->
    <dependency>
      <groupId>cds</groupId>
      <artifactId>cds_hrm</artifactId>
      <version>1.0</version>
    </dependency>

    <dependency>
      <groupId>com.helger</groupId>
      <artifactId>ph-schematron</artifactId>
      <version>5.0.5</version>
    </dependency>

    <!-- HRM module -->
    <dependency>
      <groupId>org.oscarehr.hrm</groupId>
      <artifactId>hrm-jaxb</artifactId>
      <version>4.1a</version>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcmail-jdk15on</artifactId>
      <version>1.51</version>
      <exclusions>
        <exclusion>
          <groupId>org.bouncycastle</groupId>
          <artifactId>bcprov-jdk15on</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>net.sf.jasperreports</groupId>
      <artifactId>jasperreports</artifactId>
      <version>6.7.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.bouncycastle</groupId>
          <artifactId>bcprov-jdk15on</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>net.sf.jasperreports</groupId>
      <artifactId>jasperreports-fonts</artifactId>
      <version>6.0.0</version>
    </dependency>

    <dependency>
      <groupId>com.jcraft</groupId>
      <artifactId>jsch</artifactId>
      <version>0.1.55</version>
    </dependency>

    <!-- FDB -->
    <dependency>
      <groupId>fdbmkfi</groupId>
      <artifactId>fdb-mkfi</artifactId>
      <version>4.0.2</version>
    </dependency>
    <!-- JWT Dependencies -->
    <dependency>
      <groupId>com.auth0</groupId>
      <artifactId>java-jwt</artifactId>
      <version>3.3.0</version>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-api</artifactId>
      <version>0.10.5</version>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-impl</artifactId>
      <version>0.10.5</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-jackson</artifactId>
      <version>0.10.5</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.9.0</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.8.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.googlecode.owasp-java-html-sanitizer</groupId>
      <artifactId>owasp-java-html-sanitizer</artifactId>
      <version>20190610.1</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>28.0-jre</version>
    </dependency>
    <!-- Jackson version required by HAPI FHIR -->
    <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-annotations -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.9.10</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-core -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.9.10</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.9.10.6</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>2.7.0</version>
    </dependency>
    <!-- HAPI FHIR -->
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-base</artifactId>
      <version>5.1.0</version>
    </dependency>
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-structures-r4</artifactId>
      <version>5.1.0</version>
    </dependency>
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-structures-r5</artifactId>
      <version>5.1.0</version>
    </dependency>
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-structures-hl7org-dstu2</artifactId>
      <version>5.1.0</version>
    </dependency>
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-validation-resources-r4</artifactId>
      <version>5.1.0</version>
    </dependency>
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-server</artifactId>
      <version>5.1.0</version>
    </dependency>
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-client</artifactId>
      <version>5.1.0</version>
    </dependency>
    <dependency>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <artifactId>hapi-fhir-validation</artifactId>
      <version>5.1.0</version>
    </dependency>
    <!-- Temporarily excluded org.hl7.fhir.utilities library because of a conflict between this
       and the FDB library -->
    <dependency>
      <artifactId>org.hl7.fhir.utilities</artifactId>
      <groupId>ca.uhn.hapi.fhir</groupId>
      <version>5.1.0</version>
      <scope>provided</scope>
    </dependency>
    <!-- Temporarily include WELL version of org.hl7.fhir.utilities library to fix
       Message.properties fileconflict between this library and FDB library -->
    <dependency>
      <groupId>org.hl7</groupId>
      <artifactId>hapi-fhir-utilities</artifactId>
      <version>5.1.0</version>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.3</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpmime</artifactId>
      <version>4.5.3</version>
    </dependency>

    <!-- Temporarily excluded okta-spring-security-oauth2 library because of a conflict token service used by Okta and OSCAR -->
    <!--
    <dependency>
      <groupId>com.okta.spring</groupId>
      <artifactId>okta-spring-security-oauth2</artifactId>
      <version>0.5.0</version>
      <scope>provided</scope>
    </dependency>
    -->
    <dependency>
      <groupId>ca.well</groupId>
      <artifactId>well-okta-spring-security-oauth2</artifactId>
      <version>0.6.2</version>
    </dependency>

    <!-- JMS activemq -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-activemq</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>activemq-broker</artifactId>
      <version>5.14.5</version>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>activemq-spring</artifactId>
      <version>5.14.5</version>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>activemq-jdbc-store</artifactId>
      <version>5.14.5</version>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>activemq-pool</artifactId>
      <version>5.14.5</version>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq.protobuf</groupId>
      <artifactId>activemq-protobuf</artifactId>
      <version>1.1</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-pool2</artifactId>
      <version>2.4.2</version>
    </dependency>
    <dependency>
      <groupId>org.apache.geronimo.specs</groupId>
      <artifactId>geronimo-jta_1.0.1B_spec</artifactId>
      <version>1.0.1</version>
    </dependency>
    <dependency>
      <groupId>org.apache.xbean</groupId>
      <artifactId>xbean-spring</artifactId>
      <version>4.2</version>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>activemq-kahadb-store</artifactId>
      <version>5.14.5</version>
    </dependency>

    <!--  subscription related  -->
    <dependency>
      <groupId>org.springframework.restdocs</groupId>
      <artifactId>spring-restdocs-core</artifactId>
      <version>2.0.0.RELEASE</version>
    </dependency>

    <!-- annotations -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.22</version>
      <scope>provided</scope>
    </dependency>

    <!-- Internal libraries -->
    <dependency>
      <groupId>ca.oscarpro</groupId>
      <artifactId>encryption-provider</artifactId>
      <version>1.1.2</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.springdoc/springdoc-openapi-ui -->
    <!--
         This library is the latest Open Source release supporting Spring Boot 2.x and 1.x.
         For SpringBoot version 3.x is necessary to use springdoc-openapi-starter-webmvc-ui
    -->
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-ui</artifactId>
      <version>1.8.0</version>
    </dependency>
    <!-- Azure Application Insights -->
    <dependency>
      <groupId>com.microsoft.azure</groupId>
      <artifactId>applicationinsights-core</artifactId>
      <version>3.6.2</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-maven-plugin</artifactId>
        <version>${kotlin.version}</version>
        <extensions>true</extensions> <!-- You can set this option
            to automatically take information about lifecycles -->
        <executions>
          <execution>
            <id>compile</id>
            <goals>
              <goal>compile</goal> <!-- You can skip the <goals> element
                        if you enable extensions for the plugin -->
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                <sourceDir>${project.basedir}/src/main/java</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
          <execution>
            <id>test-compile</id>
            <goals>
              <goal>test-compile</goal> <!-- You can skip the <goals> element
                    if you enable extensions for the plugin -->
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                <sourceDir>${project.basedir}/src/test/java</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <version>3.0.0</version>
        <configuration>
          <skip>${maven.war.skip}</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>1.5.3.RELEASE</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
            <configuration>
              <skip>${maven.war.skip}</skip>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Run the Unit Tests -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.2.5</version>
        <configuration>
          <runOrder>alphabetical</runOrder>
          <skipTests>${skip.unit.tests}</skipTests>
          <excludes>
            <exclude>**/*RepositoryTest.java</exclude>
          </excludes>
          <parallel>classes</parallel>
          <forkCount>3</forkCount>
          <reuseForks>true</reuseForks>
          <!--for junit5-->
          <properties>
            <configurationParameters>
              junit.jupiter.execution.parallel.enabled=true
              junit.jupiter.execution.parallel.mode.default=concurrent
            </configurationParameters>
          </properties>
        </configuration>
      </plugin>

      <!-- Run the Integration Tests -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>3.2.5</version>
        <configuration>
          <runOrder>alphabetical</runOrder>
          <skipTests>${skip.it.tests}</skipTests>
          <includes>
            <include>**/*IT.java</include>
            <include>**/*RepositoryTest.java</include>
          </includes>
          <systemPropertyVariables>
            <buildDirectory>${project.build.directory}</buildDirectory>
          </systemPropertyVariables>
          <parallel>classes</parallel>
          <forkCount>3</forkCount>
          <reuseForks>true</reuseForks>
        </configuration>
        <executions>
          <execution>
            <id>integration-test</id>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
            <phase>integration-test</phase>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.5.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.11</version>
        <configuration>
          <formats>XML</formats>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
