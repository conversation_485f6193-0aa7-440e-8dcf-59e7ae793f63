<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>ca.kai</groupId>
	<artifactId>kai-15-pilot</artifactId>
	<packaging>war</packaging>
	<version>18.0.0</version>
	<name>kai-15-pilot</name>
	<description>OSCAR McMaster is a web-based electronic medical record (EMR) system initially
		developed for academic primary care clinics. It has grown into a comprehensive EMR and billing
		system used by many doctor's offices and private medical clinics in Canada and other parts of
		the world. The name is derived from where it was created and an acronym; OSCAR stands for Open
		Source Clinical Application and Resource and McMaster refers to McMaster University, where it
		was developed. It enables the delivery of evidence resources at the point of care.
	</description>

	<issueManagement>
		<system>SourceForge Tracker</system>
		<url>https://sourceforge.net/tracker/?group_id=66701</url>
	</issueManagement>
	<ciManagement>
		<system>Jenkins</system>
		<url>https://demo.oscarmcmaster.org:11042/</url>
	</ciManagement>
	<inceptionYear>2001</inceptionYear>
	<mailingLists>
		<mailingList>
			<name>oscarmcmaster-devel</name>
			<subscribe>https://lists.sourceforge.net/lists/listinfo/oscarmcmaster-devel</subscribe>
			<post><EMAIL></post>
			<archive>https://sourceforge.net/mailarchive/forum.php?forum_name=oscarmcmaster-devel
			</archive>
		</mailingList>
	</mailingLists>
	<licenses>
		<license>
			<name>GPLv2</name>
			<url>http://www.gnu.org/licenses/gpl-2.0.txt</url>
		</license>
	</licenses>
	<url>http://www.oscarcanada.org</url>
	<scm>
		<url>http://oscarmcmaster.git.sourceforge.net/git/gitweb.cgi?p=oscarmcmaster/oscar</url>
		<connection>scm:git:git://oscarmcmaster.git.sourceforge.net/gitroot/oscarmcmaster/oscar
		</connection>
		<developerConnection>scm:git:ssh://<EMAIL>:29418/oscar</developerConnection>
		<tag>master</tag>
	</scm>
	<properties>
		<hapifhir_version>5.2.0</hapifhir_version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<netbeans.hint.deploy.server>Tomcat60</netbeans.hint.deploy.server>
		<timestamp>${build.time}</timestamp>
		<skipTests>false</skipTests>
		<skip.unit.tests>${skipTests}</skip.unit.tests>
		<skip.it.tests>${skipTests}</skip.it.tests>
		<oscar.dbinit.skip>false</oscar.dbinit.skip>
		<kotlin.version>2.0.0</kotlin.version>
		<kotlin.compiler.incremental>true</kotlin.compiler.incremental>
	</properties>

	<repositories>
		<repository>
			<id>local_repo</id>
			<url>file://${basedir}/local_repo</url>
		</repository>

		<!-- Jasper Reports Repo for itext js8 -->
		<repository>
			<id>jaspersoft-third-party</id>
			<url>https://jaspersoft.jfrog.io/jaspersoft/third-party-ce-artifacts/</url>
		</repository>
		<repository>
			<id>oscarpro</id>
			<url>https://cobalt.azu.oscar-emr.net:63827/repository/public-releases/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>

	<pluginRepositories>
		<!-- Cambian local repo -->
		<pluginRepository>
			<id>cambian_local_repo</id>
			<url>file://${basedir}/cambian_local_repo</url>
		</pluginRepository>

		<pluginRepository>
			<id>local_repo</id>
			<url>file://${basedir}/local_repo</url>
		</pluginRepository>

		<pluginRepository>
			<id>oscar_repo</id>
			<url>https://oscarmcmaster.sourceforge.net/m2</url>
		</pluginRepository>
	</pluginRepositories>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-bom</artifactId>
				<version>2.24.3</version>
				<scope>import</scope>
				<type>pom</type>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<!-- KOTLIN -->
		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-stdlib</artifactId>
			<version>${kotlin.version}</version>
		</dependency>

		<!-- Log4j 2 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-layout-template-json</artifactId>
		</dependency>

		<!-- Log4j 1 to Log4j 2 Bridge -->
		<!-- Routes legacy Log4j calls to Log4j 2 -->
		<!-- Allows gradual migration of legacy Log4j code to Log4j 2 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
		</dependency>

		<!-- general libraries -->
		<dependency>
			<groupId>net.bull.javamelody</groupId>
			<artifactId>javamelody-core</artifactId>
			<version>1.53.0</version>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.1.1</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.30</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-jdk14</artifactId>
			<version>1.7.30</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-simple</artifactId>
			<version>1.7.30</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
			<version>1.7.30</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.7.30</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.4</version>
		</dependency>
		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
			<version>1.3.1</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.4</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.3.3</version>
		</dependency>

		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.2</version>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>4.0.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>javax.servlet.jsp-api</artifactId>
			<version>2.3.3</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>javax.el-api</artifactId>
			<version>2.2.1</version>
			<type>jar</type>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.jcs</groupId>
			<artifactId>jcs</artifactId>
			<version>1.3</version>
		</dependency>

		<!-- TESTING -->
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
			<version>5.9.0</version>
		</dependency>
		<dependency>
			<groupId>org.junit.vintage</groupId>
			<artifactId>junit-vintage-engine</artifactId>
			<version>5.9.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>3.8.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>3.8.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>4.3.9.RELEASE</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>3.20.0-GA</version>
			<scope>test</scope>
		</dependency>

		<!-- hapi/HL7 -->
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-base</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v26</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v25</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v23</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v22</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v231</artifactId>
			<version>1.0.1</version>
		</dependency>

		<!-- spring -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>3.1.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<version>3.1.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
			<version>3.1.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<version>3.2.1.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-ftp</artifactId>
			<version>2.1.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-sftp</artifactId>
			<version>2.2.5.RELEASE</version>
			<exclusions>
				<exclusion>
					<!-- exclude old version of jsch newer one needed for larger key lengths -->
					<groupId>com.jcraft</groupId>
					<artifactId>jsch</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
			<version>2.5.5</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-mock</artifactId>
			<version>2.0.8</version>
		</dependency>

		<!-- hibernate / sql -->
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.33</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-entitymanager</artifactId>
			<version>3.4.0.GA</version>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib-nodep</artifactId>
			<version>2.2</version>
		</dependency>


		<!-- pdf / jpedal -->
		<dependency>
			<groupId>org</groupId>
			<artifactId>jpedal</artifactId>
			<version>lgpl</version>
		</dependency>

		<!--  pdfbox -->
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.21</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>jbig2-imageio</artifactId>
			<version>3.0.3</version>
		</dependency>
		<dependency>
			<groupId>com.twelvemonkeys.imageio</groupId>
			<artifactId>imageio-jpeg</artifactId>
			<version>3.3.1</version>
		</dependency>

		<!-- google collections lib -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>15.0-rc1</version>
		</dependency>
		<!-- struts -->
		<dependency>
			<groupId>struts</groupId>
			<artifactId>struts</artifactId>
			<version>1.2.7</version>
		</dependency>
		<dependency>
			<groupId>org.apache.struts</groupId>
			<artifactId>struts-menu</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>struts</groupId>
			<artifactId>struts-el</artifactId>
			<version>1.2.7</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc-struts</artifactId>
			<version>2.5.6.SEC03</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-webmvc</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-web</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>1.2</version>
		</dependency>
		<dependency>
			<groupId>taglibs</groupId>
			<artifactId>standard</artifactId>
			<version>1.1.2</version>
		</dependency>

		<!-- pdf / lowagie -->
		<!--
		<dependency>
			<groupId>com.lowagie</groupId>
			<artifactId>itext</artifactId>
			<version>2.1.7</version>
		</dependency>
		-->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>5.5.13.1</version>
		</dependency>
		<dependency>
			<groupId>com.itextpdf.tool</groupId>
			<artifactId>xmlworker</artifactId>
			<version>5.5.13.1</version>
		</dependency>


		<!-- rtf / lowagie -->
		<dependency>
			<groupId>com.lowagie</groupId>
			<artifactId>itext-rtf</artifactId>
			<version>2.1.7</version>
			<exclusions>
				<exclusion>
					<groupId>bouncycastle</groupId>
					<artifactId>bcprov-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>bouncycastle</groupId>
					<artifactId>bcmail-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bctsp-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- json -->
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.3</version>
			<classifier>jdk15</classifier>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.6.2</version>
		</dependency>

		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20170516</version>
		</dependency>

		<!-- apache xmlrpc -->
		<dependency>
			<groupId>xmlrpc</groupId>
			<artifactId>xmlrpc</artifactId>
			<version>1.2-b1</version>
		</dependency>

		<!-- apache xmlrpc -->
		<dependency>
			<groupId>drools</groupId>
			<artifactId>drools-all</artifactId>
			<version>2.0</version>
		</dependency>

		<!-- jasper reports -->
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports</artifactId>
			<version>6.16.0</version>
			<exclusions>
				<exclusion>
					<groupId>bouncycastle</groupId>
					<artifactId>bcprov-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>bouncycastle</groupId>
					<artifactId>bcmail-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bctsp-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-annotations</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>jasperreports</groupId>
			<artifactId>htmlcomponent</artifactId>
			<version>6.8.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.9.9</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.9.9</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.9.9</version>
		</dependency>
		<dependency>
			<groupId>net.sourceforge.barbecue</groupId>
			<artifactId>barbecue</artifactId>
			<version>1.0.6b</version>
		</dependency>

		<!-- joda time -->
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>1.4</version>
		</dependency>

		<!-- caisi integrator -->
		<dependency>
			<groupId>org.oscarehr.caisi_integrator</groupId>
			<artifactId>caisi_integrator_client_stubs</artifactId>
			<version>0.9</version>
		</dependency>

		<!-- macplus -->
		<dependency>
			<groupId>ca.mcmaster.plus</groupId>
			<artifactId>macplus_client_stubs</artifactId>
			<version>SNAPSHOT</version>
		</dependency>

		<!-- apache xmlgraphics batik -->
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-dom</artifactId>
			<version>1.10</version>
			<exclusions>
				<exclusion>
					<groupId>xml-apis</groupId>
					<artifactId>xml-apis</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>xml-apis</groupId>
			<artifactId>xml-apis</artifactId>
			<version>1.4.01</version>
		</dependency>

		<!-- apache commons codec -->
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.4</version>
		</dependency>

		<!-- indivo -->
		<dependency>
			<groupId>org.indivo</groupId>
			<artifactId>indivo-core</artifactId>
			<version>3.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.indivo</groupId>
			<artifactId>indivo-model-core</artifactId>
			<version>3.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.indivo</groupId>
			<artifactId>indivo-model-phr-jackson</artifactId>
			<version>3.1-SNAPSHOT</version>
		</dependency>

		<!-- jcharts -->
		<dependency>
			<groupId>net.sf.jcharts</groupId>
			<artifactId>krysalis-jCharts</artifactId>
			<version>0.7.5</version>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-awt-util</artifactId>
			<version>1.10</version>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-svggen</artifactId>
			<version>1.10</version>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-util</artifactId>
			<version>1.10</version>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-xml</artifactId>
			<version>1.10</version>
		</dependency>

		<!-- cds -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds</artifactId>
			<version>1.1.2</version>
		</dependency>

		<!-- cds cihi phc vrs -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds_cihi_phcvrs</artifactId>
			<version>1.0</version>
		</dependency>

		<!-- cds cihi -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds_cihi</artifactId>
			<version>1.0</version>
		</dependency>

		<!-- cds rourke -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds_rourke</artifactId>
			<version>1.0</version>
		</dependency>

		<!-- cds hrm -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds_hrm</artifactId>
			<version>1.1.2</version>
		</dependency>

		<!-- diabetes mellitus cds module, created from a XML beans source -->
		<!-- this version is a modified jar that removes DiabetesComplicationScreening classes -->
		<!-- that are incompatible with the 1.1.2 cds library -->
		<dependency>
			<groupId>dm</groupId>
			<artifactId>cds_diabetes</artifactId>
			<version>0.1.1</version>
		</dependency>

		<!-- HRM module -->
		<dependency>
			<groupId>org.oscarehr.hrm</groupId>
			<artifactId>hrm-jaxb</artifactId>
			<version>4.1a</version>
		</dependency>

		<!-- HRM module 4.3 -->
		<dependency>
			<groupId>omd</groupId>
			<artifactId>hrm</artifactId>
			<version>4.3</version>
		</dependency>

		<!-- hsfo -->
		<dependency>
			<groupId>hsfo</groupId>
			<artifactId>hsfo</artifactId>
			<version>2007-02-12</version>
		</dependency>

		<dependency>
			<groupId>hsfo2</groupId>
			<artifactId>hsfo2</artifactId>
			<version>2.0</version>
		</dependency>
		<!--
         <dependency>
                            <groupId>wlws</groupId>
                            <artifactId>wlws</artifactId>
                            <version>1.0</version>
                    </dependency>
    -->
		<!-- apache xml beans -->
		<dependency>
			<groupId>org.apache.xmlbeans</groupId>
			<artifactId>xmlbeans</artifactId>
			<version>2.4.0</version>
		</dependency>

		<!-- HttpClient -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.6</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
			<version>4.5.6</version>
		</dependency>

		<!-- quartz -->
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>1.8.5</version>
		</dependency>

		<!-- cookie revolver -->
		<dependency>
			<groupId>net.sf.cookierevolver</groupId>
			<artifactId>cookierevolver</artifactId>
			<version>0.2.5</version>
		</dependency>

		<!-- commons-betwixt ... what is this for? -->
		<dependency>
			<groupId>commons-betwixt</groupId>
			<artifactId>commons-betwixt</artifactId>
			<version>0.7</version>
		</dependency>

		<!-- email -->
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-email</artifactId>
			<version>1.5</version>
		</dependency>

		<!-- jfree charts -->
		<dependency>
			<groupId>jfree</groupId>
			<artifactId>jfreechart</artifactId>
			<version>1.0.12</version>
		</dependency>

		<!-- ocan.jar , suspect this is generated classes and should be removed as a jar in the future -->
		<dependency>
			<groupId>ocan</groupId>
			<artifactId>ocan</artifactId>
			<version>0.0-SNAPSHOT</version>
		</dependency>

		<!-- FOR BORN Project - AR2005 in XML format -->
		<dependency>
			<groupId>org.oscarehr.ar2005</groupId>
			<artifactId>ar2005</artifactId>
			<version>1.2</version>
		</dependency>

		<!-- FOR BORN18MWBV Project - Rourke, NDDS, 18M Summary in XML format -->
		<dependency>
			<groupId>ca.bornontario</groupId>
			<artifactId>x18MEWBV</artifactId>
			<version>1.8.2</version>
		</dependency>
		<dependency>
			<groupId>ca.bornontario</groupId>
			<artifactId>BORNWB</artifactId>
			<version>2.1.5</version>
		</dependency>
		<dependency>
			<groupId>ca.bornontario</groupId>
			<artifactId>BORNWBCSD</artifactId>
			<version>2.2.1</version>
		</dependency>

		<!-- FOR ORN Project - CKD config in XML format -->
		<dependency>
			<groupId>org.oscarehr.ckd</groupId>
			<artifactId>ckd</artifactId>
			<version>1.0</version>
		</dependency>


		<dependency>
			<groupId>jspellchecker</groupId>
			<artifactId>jazzy-core</artifactId>
			<version>unknown</version>
		</dependency>

		<!-- patientSiteVisit.jar , suspect this is generated classes and should be removed as a jar in the future -->
		<dependency>
			<groupId>patientSiteVisit</groupId>
			<artifactId>patientSiteVisit</artifactId>
			<version>0.0-SNAPSHOT</version>
		</dependency>

		<!-- surveyModel.jar , suspect this is generated classes and should be removed as a jar in the future -->
		<dependency>
			<groupId>surveyModel</groupId>
			<artifactId>surveyModel</artifactId>
			<version>0.0-SNAPSHOT</version>
		</dependency>

		<!-- jaxm api ... why do we need this? -->
		<dependency>
			<groupId>javax.xml</groupId>
			<artifactId>jaxm-api</artifactId>
			<version>UNKNOWN</version>
		</dependency>

		<!-- apache poi -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.6</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- ostermillerutils_1_04_03_for_java_1_4.jar we need to stop using this and replace it with apache commons -->
		<dependency>
			<groupId>com.ostermiller</groupId>
			<artifactId>ostermillerutils</artifactId>
			<version>1.4.3</version>
		</dependency>

		<!-- sun xacml -->
		<dependency>
			<groupId>com.sun</groupId>
			<artifactId>xacml</artifactId>
			<version>1.2</version>
		</dependency>

		<!-- sun pdfview -->
		<dependency>
			<groupId>com.sun</groupId>
			<artifactId>pdfview</artifactId>
			<version>UNKNOWN</version>
		</dependency>

		<!-- display tag -->
		<dependency>
			<groupId>displaytag</groupId>
			<artifactId>displaytag</artifactId>
			<version>1.1.1</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl104-over-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- zxing / 2d bar codes -->
		<dependency>
			<groupId>zxing</groupId>
			<artifactId>zxing-core</artifactId>
			<version>1.5</version>
		</dependency>
		<dependency>
			<groupId>zxing</groupId>
			<artifactId>zxing-j2se</artifactId>
			<version>1.5</version>
		</dependency>

		<!-- chip / ping libararies -->
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>oscar-ping</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>ping-client</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>ping-core</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>ping-server</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>ping-xml</artifactId>
			<version>UNKNOWN</version>
		</dependency>

		<!-- jtidy -->
		<dependency>
			<groupId>net.sf.jtidy</groupId>
			<artifactId>jtidy</artifactId>
			<version>r938</version>
		</dependency>

		<!-- xerces -->
		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
			<version>2.12.0</version>
		</dependency>

		<!-- janino -->
		<dependency>
			<groupId>janino</groupId>
			<artifactId>janino</artifactId>
			<version>2.3.2</version>
		</dependency>

		<!-- *sigh* we really need to get rid of this -->
		<dependency>
			<groupId>pluginframework</groupId>
			<artifactId>pluginframework</artifactId>
			<version>0.9.13</version>
		</dependency>

		<!-- *sigh* I have no clue what this is -->
		<!-- <dependency> <groupId>rx</groupId> <artifactId>rx</artifactId> <version>UNKNOWN</version> </dependency> -->

		<!-- something something ocan something -->
		<dependency>
			<groupId>ocan</groupId>
			<artifactId>ocan-iar</artifactId>
			<version>3.0</version>
		</dependency>

		<dependency>
			<groupId>ocan</groupId>
			<artifactId>ocan-iar-consent</artifactId>
			<version>UNKNOWN</version>
		</dependency>

		<dependency>
			<groupId>ocan</groupId>
			<artifactId>ocan-iar-phr</artifactId>
			<version>UNKNOWN</version>
		</dependency>

    <dependency>
      <groupId>com.jcraft</groupId>
      <artifactId>jsch</artifactId>
      <version>0.1.55</version>
    </dependency>

		<!-- OLIS -->
		<dependency>
			<groupId>ca.ssha.www</groupId>
			<artifactId>olis-service</artifactId>
			<version>20111111</version>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcmail-jdk15on</artifactId>
			<version>1.51</version>
			<exclusions>
			<exclusion>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk15on</artifactId>
			</exclusion>
			</exclusions>
		</dependency>

		<!-- AXIS2 (for OLIS) -->

		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2</artifactId>
			<version>1.5.4</version>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-transport-http</artifactId>
			<version>1.5.4</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.ws.commons.schema</groupId>
					<artifactId>XmlSchema</artifactId>
				</exclusion>

				<exclusion>
					<groupId>org.apache.neethi</groupId>
					<artifactId>neethi</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-transport-local</artifactId>
			<version>1.5.4</version>
		</dependency>
		<dependency>
			<groupId>org.apache.ws.commons.axiom</groupId>
			<artifactId>axiom-api</artifactId>
			<version>1.2.11</version>
		</dependency>
		<dependency>
			<groupId>org.apache.ws.commons.axiom</groupId>
			<artifactId>axiom-impl</artifactId>
			<version>1.2.11</version>
		</dependency>

		<!-- velocity -->
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity</artifactId>
			<version>1.7</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-tools</artifactId>
			<version>2.0</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>org.apache.struts</groupId>
					<artifactId>struts-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.struts</groupId>
					<artifactId>struts-taglib</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.struts</groupId>
					<artifactId>struts-tiles</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- CXF -->
		<dependency>
			<groupId>com.sun.xml.messaging.saaj</groupId>
			<artifactId>saaj-impl</artifactId>
			<version>1.3.18</version>
		</dependency>

		<!-- CXF -->
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>apache-cxf</artifactId>
			<version>2.7.11</version>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<groupId>org.apache.geronimo.specs</groupId>
					<artifactId>geronimo-servlet_2.5_spec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.geronimo.specs</groupId>
					<artifactId>geronimo-servlet_3.0_spec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.cxf.services.ws-discovery</groupId>
					<artifactId>cxf-services-ws-discovery-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.cxf.services.ws-discovery</groupId>
					<artifactId>cxf-services-ws-discovery-service</artifactId>
				</exclusion>

			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.codehaus.woodstox</groupId>
			<artifactId>woodstox-core-asl</artifactId>
			<version>4.4.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-frontend-jaxws</artifactId>
			<version>2.7.11</version>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-http</artifactId>
			<version>2.7.11</version>
		</dependency>


		<dependency>
			<groupId>org.oscarehr</groupId>
			<artifactId>myoscar_client_utils</artifactId>
			<version>2013.07.22.TLS</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.oscarehr.myoscar_server</groupId>
			<artifactId>myoscar_server_client_stubs2</artifactId>
			<version>2014.10.24</version>
		</dependency>

		<!-- MARC-HI - Everest Framework -->
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-core</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-rmim-ca-r02-04-03</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-rmim-uv-cdar2</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-formatters-xml-its1</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-formatters-xml-dt-r1</artifactId>
			<version>1.1.0</version>
		</dependency>

		<!-- MARC-HI - Shared Health Integration Components (SHIC) Library -->
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-core</artifactId>
			<version>1.0.8</version>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-xds</artifactId>
			<version>1.0.8</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jul-to-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>log4j-over-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-simple</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.santuario</groupId>
					<artifactId>xmlsec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.opensaml</groupId>
					<artifactId>opensaml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.woodstox</groupId>
					<artifactId>woodstox-core-asl</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-atna</artifactId>
			<version>1.0.8</version>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-pix</artifactId>
			<version>1.0.8</version>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-svs</artifactId>
			<version>1.0.8</version>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-cda</artifactId>
			<version>1.0.8</version>
			<exclusions>
				<exclusion>
					<groupId>org.reflections</groupId>
					<artifactId>reflections</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Apache Tika - MimeType handling for downloaded files (ie. XDS) -->
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-core</artifactId>
			<version>1.5</version>
		</dependency>

		<dependency>
			<groupId>com.atlassian.commonmark</groupId>
			<artifactId>commonmark</artifactId>
			<version>0.10.0</version>
		</dependency>

		<!--tagsoup-->
		<dependency>
			<groupId>org.ccil.cowan.tagsoup</groupId>
			<artifactId>tagsoup</artifactId>
			<version>1.2.1</version>
		</dependency>

		<dependency>
			<groupId>com.cbi.ws</groupId>
			<artifactId>cbi_ws_client</artifactId>
			<version>1.1</version>
		</dependency>

		<dependency>
			<groupId>rome</groupId>
			<artifactId>rome</artifactId>
			<version>1.0</version>
		</dependency>

		<dependency>
			<groupId>org.oscarehr.integration.ebs</groupId>
			<artifactId>ebs-client</artifactId>
			<version>0.0.7</version>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk16</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.oscarehr.integration.ebs</groupId>
			<artifactId>edt-stubs</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.oscarehr.integration.ebs</groupId>
			<artifactId>hcv-stubs</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-jaxrs</artifactId>
			<version>1.9.13</version>
		</dependency>

		<dependency>
			<groupId>org.jasypt</groupId>
			<artifactId>jasypt</artifactId>
			<version>1.8</version>
		</dependency>


		<!--	custom/legacy oscar dependencies	-->
		<dependency>
			<groupId>org.oscarehr</groupId>
			<artifactId>util</artifactId>
			<version>2013.07.17</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.oscarehr.caisi_integrator</groupId>
			<artifactId>integrator-objects</artifactId>
			<version>0.0.3</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.14</version>
		</dependency>


		<dependency>
			<groupId>org.owasp.encoder</groupId>
			<artifactId>encoder</artifactId>
			<version>1.2.1</version>
		</dependency>
		<dependency>
			<groupId>org.owasp</groupId>
			<artifactId>csrfguard</artifactId>
			<version>3.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.medseek</groupId>
			<artifactId>SSOClinicalConnect</artifactId>
			<version>20171101</version>
		</dependency>

		<dependency>
			<groupId>com.medseek</groupId>
			<artifactId>PatientService</artifactId>
			<version>20161213</version>
		</dependency>
		<!-- Added commons-text because its in use and was removed when updating flyingsaucer which had this dependency in the older version -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.4</version>
		</dependency>
		<!-- Flying Saucer uses openpdf which is based off of itext, care should be taken when changing the versions as it can break jasperreports -->
		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-core</artifactId>
			<version>9.1.20</version>
		</dependency>

		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-pdf-openpdf</artifactId>
			<version>9.1.20</version>
			<exclusions>
				<exclusion>
					<groupId>com.github.librepdf</groupId>
					<artifactId>openpdf</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>ca.on.health</groupId>
			<artifactId>ou</artifactId>
			<version>1.0</version>
		</dependency>

		<dependency>
			<groupId>com.cognos.developer</groupId>
			<artifactId>rcx</artifactId>
			<version>0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
			<version>2.3.28</version>
		</dependency>

		<dependency>
			<groupId>pcds</groupId>
			<artifactId>pcds</artifactId>
			<version>1.0</version>
		</dependency>

		<!-- JWT Dependencies -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>0.10.5</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>0.10.5</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>0.10.5</version>
			<scope>runtime</scope>
		</dependency>
		<!-- Cambian Dynacare API BEGIN -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.11.3</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.11.3</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.11.3</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>2.11.3</version>
		</dependency>
		<dependency>
			<groupId>org.oscarehr.integration.dynacare</groupId>
			<artifactId>dynacare-lab-api</artifactId>
			<version>2.0</version>
		</dependency>

		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.3.0</version>
		</dependency>


		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.9</version>
		</dependency>

		<dependency>
			<groupId>net.sf.saxon</groupId>
			<artifactId>Saxon-HE</artifactId>
			<!-- <version>9.8.0-3</version>-->
			<version>9.5.1-5</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/xmlpull/xmlpull -->
		<dependency>
			<groupId>xmlpull</groupId>
			<artifactId>xmlpull</artifactId>
			<version>1.1.3.1</version>
		</dependency>
		<dependency>
			<groupId>es.nitaur.markdown</groupId>
			<artifactId>txtmark</artifactId>
			<version>0.16</version>
		</dependency>
		<!-- 
		<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.6</version>
		</dependency>	
		 -->

		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-base</artifactId>
			<version>${hapifhir_version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-structures-dstu2</artifactId>
			<version>${hapifhir_version}</version>
		</dependency>

		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-structures-dstu3</artifactId>
			<version>${hapifhir_version}</version>
		</dependency>

		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-client</artifactId>
			<version>${hapifhir_version}</version>
		</dependency>

		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-structures-r4</artifactId>
			<version>${hapifhir_version}</version>
		</dependency>
		<!-- Cambian Excelleris FHIR API END -->

		<!-- annotations -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.22</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>ca.oscarpro</groupId>
			<artifactId>encryption-provider</artifactId>
			<version>1.1.2</version>
		</dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-core</artifactId>
      <version>4.2.2.RELEASE</version>
      <scope>compile</scope>
    </dependency>
		<dependency>
		<groupId>org.hibernate</groupId>
		<artifactId>hibernate-ehcache</artifactId>
		<version>3.3.2.GA</version>
		<exclusions>
			<exclusion>
				<groupId>net.sf.ehcache</groupId>
				<artifactId>ehcache</artifactId>
			</exclusion>
		</exclusions>
	</dependency>
		<dependency>
			<groupId>net.sf.ehcache</groupId>
			<artifactId>ehcache-core</artifactId>
			<version>2.5.1</version>
		</dependency>
	</dependencies>

	<build>
		<resources>
			<resource>
				<directory>${basedir}/src/main/resources</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>oscar/fonts/*</exclude>
					<!-- Cambian: do not apply filter on keystore and truststore -->
					<exclude>*.pkcs12</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>${basedir}/src/main/resources</directory>
				<filtering>false</filtering>
				<includes>
					<include>oscar/fonts/*</include>
					<include>*.pkcs12</include>
				</includes>
			</resource>
		</resources>
		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.6</version>
				<configuration>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>pdf</nonFilteredFileExtension>
						<nonFilteredFileExtension>jar</nonFilteredFileExtension>
						<nonFilteredFileExtension>xls</nonFilteredFileExtension>
						<nonFilteredFileExtension>dat</nonFilteredFileExtension>
						<nonFilteredFileExtension>xsd</nonFilteredFileExtension>
						<nonFilteredFileExtension>jasper</nonFilteredFileExtension>
						<nonFilteredFileExtension>doc</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>buildnumber-maven-plugin</artifactId>
				<version>1.1</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>create</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-site-plugin</artifactId>
				<version>3.0</version>
				<dependencies>
					<!-- dependency>
                    <groupId>org.apache.maven.doxia</groupId>
                    <artifactId>doxia-core</artifactId>
                    <version>1.2</version>
                  </dependency>
                  <dependency>
                     <groupId>org.apache.maven.doxia</groupId>
                     <artifactId>doxia-sink-api</artifactId>
                     <version>1.3</version>
                  </dependency  -->
					<dependency>
						<groupId>org.apache.maven.doxia</groupId>
						<artifactId>doxia-module-markdown</artifactId>
						<version>1.3</version>
					</dependency>
				</dependencies>
				<configuration>
					<inputEncoding>UTF-8</inputEncoding>
					<outputEncoding>UTF-8</outputEncoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.mycila.maven-license-plugin</groupId>
				<artifactId>maven-license-plugin</artifactId>
				<version>1.10.b1</version>
				<configuration>
					<header>utils/headers/mcmaster.txt</header>
					<validHeaders>
						<validHeader>utils/headers/os.txt</validHeader>
						<validHeader>utils/headers/quatro.txt</validHeader>
						<validHeader>utils/headers/quatro2.txt</validHeader>
						<validHeader>utils/headers/md.txt</validHeader>
						<validHeader>utils/headers/caisi.txt</validHeader>
						<validHeader>utils/headers/hs.txt</validHeader>
						<validHeader>utils/headers/pc.txt</validHeader>
						<validHeader>utils/headers/andromedia.txt</validHeader>
						<validHeader>utils/headers/lgpl.txt</validHeader>
						<validHeader>utils/headers/asl.txt</validHeader>
						<validHeader>utils/headers/cmi.txt</validHeader>
						<validHeader>utils/headers/indivica.txt</validHeader>
						<validHeader>utils/headers/peaceworks.txt</validHeader>
						<validHeader>utils/headers/ubc.txt</validHeader>
						<validHeader>utils/headers/uvic.txt</validHeader>
						<validHeader>utils/headers/kai.txt</validHeader>
					</validHeaders>
					<includes>
						<include>src/main/java/**/*.java</include>
						<include>src/test/java/**/*.java</include>
						<include>src/main/webapp/**/*.jsp</include>
					</includes>
					<excludes>
						<exclude>src/main/webapp/billing/CA/BC/billingAccountReports.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/billingPreferences.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/billingSVCTrayAssoc.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/billTransactions.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/billType_frag.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/deletePrivateCode.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/dxcode_svccode_assoc.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/manageSVCDXAssoc.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/receivePayment.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/saveAssocs.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/settleBG.jsp</exclude>
						<exclude>src/main/webapp/report/tabulardaysheetreport.jsp</exclude>

						<exclude>src/main/webapp/form/formadf.jsp</exclude>
						<exclude>src/main/webapp/form/formadfv2.jsp</exclude>
						<exclude>src/main/webapp/form/formbcar.jsp</exclude>
						<exclude>src/main/webapp/form/formbcarpg1.jsp</exclude>
						<exclude>src/main/webapp/form/formbcarpg1namepopup.jsp</exclude>
						<exclude>src/main/webapp/form/formbcarpg2.jsp</exclude>
						<exclude>src/main/webapp/form/formbcarpg3.jsp</exclude>
						<exclude>src/main/webapp/form/formbcbirthsummo.jsp</exclude>
						<exclude>src/main/webapp/form/formbcclientchartchecklist.jsp</exclude>
						<exclude>src/main/webapp/form/formbcinr.jsp</exclude>
						<exclude>src/main/webapp/form/formbcnewborn.jsp</exclude>
						<exclude>src/main/webapp/form/formbcnewbornpg1.jsp</exclude>
						<exclude>src/main/webapp/form/formbcnewbornpg2.jsp</exclude>
						<exclude>src/main/webapp/form/formbcnewbornpg3.jsp</exclude>
						<exclude>src/main/webapp/form/formGrowth0_36.jsp</exclude>
						<exclude>src/main/webapp/form/formGrowth0_36Print.jsp</exclude>
						<exclude>src/main/webapp/form/formGrowthChart.jsp</exclude>
						<exclude>src/main/webapp/form/formGrowthChartPrint.jsp</exclude>
						<exclude>src/main/webapp/form/formimmunallergy.jsp</exclude>
						<exclude>src/main/webapp/form/formInvoice.jsp</exclude>
						<exclude>src/main/webapp/form/formonar.jsp</exclude>
						<exclude>src/main/webapp/form/formonarpg1.jsp</exclude>
						<exclude>src/main/webapp/form/formonarpg2.jsp</exclude>
						<exclude>src/main/webapp/form/formonarpg3.jsp</exclude>
						<exclude>src/main/webapp/form/formonarpg4.jsp</exclude>
						<exclude>src/main/webapp/form/formovulation.jsp</exclude>
						<exclude>src/main/webapp/form/study/ar2ping.jsp</exclude>
						<exclude>src/main/webapp/form/study/dm2ping.jsp</exclude>
						<exclude>src/main/webapp/form/study/dmdata.jsp</exclude>
						<exclude>src/main/webapp/form/study/formar2ping.jsp</exclude>
						<exclude>src/main/webapp/form/study/formarpg1.jsp</exclude>
						<exclude>src/main/webapp/form/study/formarpg2.jsp</exclude>
						<exclude>src/main/webapp/form/study/formarpg3.jsp</exclude>
						<exclude>src/main/webapp/form/study/formdiabete2ping.jsp</exclude>
						<exclude>src/main/webapp/report/onbillingtotal.jsp</exclude>
						<exclude>src/main/webapp/report/reportBCARDemo2.jsp</exclude>
						<exclude>src/main/webapp/report/reportBCARDemo.jsp</exclude>
						<exclude>src/main/webapp/report/reportbcedblist.jsp</exclude>
						<exclude>src/main/webapp/report/reportbilledvisit1.jsp</exclude>
						<exclude>src/main/webapp/report/reportbilledvisit2.jsp</exclude>
						<exclude>src/main/webapp/report/reportbilledvisit3.jsp</exclude>
						<exclude>src/main/webapp/report/reportbilledvisit.jsp</exclude>
						<exclude>src/main/webapp/report/reportdxvisit.jsp</exclude>
						<exclude>src/main/webapp/report/reportFilter.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormCaption.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormConfig.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormDemoConfig.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormOrder.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormRecord.jsp</exclude>
						<exclude>src/main/webapp/report/reportonbilledphcp.jsp</exclude>
						<exclude>src/main/webapp/report/reportonbilledvisit.jsp</exclude>
						<exclude>src/main/webapp/report/reportonbilledvisitprovider.jsp</exclude>
						<exclude>src/main/webapp/report/reportonedblist.jsp</exclude>
						<exclude>src/main/webapp/report/reportResult.jsp</exclude>

						<exclude>src/main/webapp/billing/billingBrazil.jsp</exclude>
						<exclude>src/main/webapp/billing/billingBrazilSuccess.jsp</exclude>
						<exclude>src/main/webapp/billing/billingConsFatMedBrazil.jsp</exclude>
						<exclude>src/main/webapp/billing/billingConsFatPatBrazil.jsp</exclude>
						<exclude>src/main/webapp/popup/atividade.jsp</exclude>
						<exclude>src/main/webapp/popup/cid.jsp</exclude>
						<exclude>src/main/webapp/popup/procedimento.jsp</exclude>
						<exclude>src/main/java/oscar/entities/S21.java</exclude>
						<exclude>src/main/java/oscar/entities/S22.java</exclude>
						<exclude>src/main/java/oscar/entities/S23.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/CDMReminderHlp.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/CreateBillingReportActionForm.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/CreateBillingReportAction.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/ServiceCodeValidationLogic.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/ServiceCodeValidator.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/SexValidator.java</exclude>
						<exclude>src/main/java/oscar/oscarDemographic/PrintDemoAddressLabelAction.java</exclude>
						<exclude>src/main/java/oscar/oscarDemographic/PrintDemoChartLabelAction.java</exclude>
						<exclude>src/main/java/oscar/oscarDemographic/PrintDemoLabelAction.java</exclude>
						<exclude>src/main/java/oscar/OscarDocumentCreator.java</exclude>
						<exclude>src/main/webapp/oscarReport/patientlist.jsp</exclude>
						<exclude>src/main/java/oscar/oscarReport/data/DoctorList.java</exclude>
						<exclude>src/main/webapp/jspspellcheck/*.jsp</exclude>
						<exclude>src/main/java/net/sf/jasperreports/renderers/BarbecueRenderer.java</exclude>
						<exclude>src/main/java/oscar/oscarDB/ResultSetBuilder.java</exclude>
						<exclude>src/main/java/oscar/login/UAgentInfo.java</exclude>
						<exclude>src/main/webapp/lab/CA/ON/uploadComplete.jsp</exclude>
						<exclude>src/main/webapp/lab/CA/ALL/uploadComplete.jsp</exclude>
						<exclude>src/**/*.txt</exclude>
						<exclude>src/**/*.js</exclude>
						<exclude>database/**</exclude>
						<exclude>docs/**</exclude>
						<exclude>local_repo/**</exclude>
						<exclude>**/.gitignore</exclude>
						<exclude>src/main/java/oscar/form/FrmAdfRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmAdfV2Record.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCARRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCBrithSumMoRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCClientChartChecklistRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCINRRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCNewBornRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmGrowth0_36Record.java</exclude>
						<exclude>src/main/java/oscar/form/FrmGrowthChartRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmInvoiceRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmONARRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmOvulationRecord.java</exclude>
						<exclude>src/main/java/oscar/form/graphic/FrmPdfGraphicAR.java</exclude>
						<exclude>src/main/java/oscar/form/graphic/FrmPdfGraphicGrowthChart.java</exclude>
						<exclude>src/main/java/oscar/form/pdfservlet/FrmPDFServlet.java</exclude>
						<exclude>src/main/java/oscar/form/study/FrmStudyPING_DiabetesRecord.java</exclude>

						<exclude>src/main/java/org/apache/xml/security/encryption/XMLCipher.java</exclude>
						<exclude>src/main/java/org/apache/xml/security/encryption/DocumentSerializer.java</exclude>
						<exclude>src/main/webapp/library/bootstrap2-datepicker/**</exclude>
						<exclude>src/main/webapp/library/ng-table/**</exclude>
					</excludes>
					<strictCheck>true</strictCheck>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>2.1.1</version>
				<configuration>
					<failOnMissingWebXml>false</failOnMissingWebXml>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
						</manifest>
						<manifestEntries>
							<git-SHA-1>${buildNumber}</git-SHA-1>
						</manifestEntries>
					</archive>
					<webResources>
						<webResource>
							<directory>${basedir}/src/main/webapp/admin</directory>
							<filtering>true</filtering>
						</webResource>
					</webResources>

				</configuration>
			</plugin>

			<plugin>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-maven-plugin</artifactId>
				<version>${kotlin.version}</version>
				<extensions>true</extensions> <!-- You can set this option
            to automatically take information about lifecycles -->
				<executions>
					<execution>
						<id>compile</id>
						<goals>
							<goal>compile</goal> <!-- You can skip the <goals> element
                        if you enable extensions for the plugin -->
						</goals>
						<configuration>
							<sourceDirs>
								<sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
								<sourceDir>${project.basedir}/src/main/java</sourceDir>
							</sourceDirs>
						</configuration>
					</execution>
					<execution>
						<id>test-compile</id>
						<goals>
							<goal>test-compile</goal> <!-- You can skip the <goals> element
                    if you enable extensions for the plugin -->
						</goals>
						<configuration>
							<sourceDirs>
								<sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
								<sourceDir>${project.basedir}/src/test/java</sourceDir>
							</sourceDirs>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.5.1</version>
				<configuration>
					<source>8</source>
					<target>8</target>
				</configuration>
				<executions>
					<!-- Replacing default-compile as it is treated specially by Maven -->
					<execution>
						<id>default-compile</id>
						<phase>none</phase>
					</execution>
					<!-- Replacing default-testCompile as it is treated specially by Maven -->
					<execution>
						<id>default-testCompile</id>
						<phase>none</phase>
					</execution>
					<execution>
						<id>java-compile</id>
						<phase>compile</phase>
						<goals>
							<goal>compile</goal>
						</goals>
					</execution>
					<execution>
						<id>java-test-compile</id>
						<phase>test-compile</phase>
						<goals>
							<goal>testCompile</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>1.7</version>
				<executions>
					<execution>
						<id>set_timestamp</id>
						<phase>process-classes</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<tstamp>
									<format property="build.dateTime.value" pattern="yyyy-MM-dd hh:mm aa" />
								</tstamp>

								<echo message="build time : ${build.dateTime.value}" />

								<replace file="target/classes/oscar_mcmaster.properties" token="${build.dateTime}" value="${build.dateTime.value}" />

								<property environment="env" />
								<echo message="JOB_NAME - BUILD_NUMBER : ${env.JOB_NAME} - ${env.BUILD_NUMBER}" />
								<replace file="target/classes/oscar_mcmaster.properties" token="${build.JOB_NAME}" value="${env.JOB_NAME}" />
								<replace file="target/classes/oscar_mcmaster.properties" token="${build.BUILD_NUMBER}" value="${env.BUILD_NUMBER}" />
							</tasks>
						</configuration>
					</execution>

					<execution>
						<!-- We're doing jspc this way because the codehaus jspc seems to be abandonded, it doesn't go beyond jdk 1.5 and the documentation is sparse, and the source code links to a broken page and I can't find the goals it supports. -->
						<id>jspc</id>
						<phase>verify</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<property name="build.compiler" value="extJavac" />
								<property name='jspc_target' value="${project.name}-${project.version}" />
								<ant antfile="jspc.xml" target="jspc" />
							</tasks>
						</configuration>
					</execution>

					<execution>
						<id>deploy_local_catalina_base</id>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<copy todir="catalina_base/webapps/oscar">
									<fileset dir="target/${project.name}-${project.version}" />
								</copy>
							</tasks>
						</configuration>
					</execution>
					<execution>
						<id>clean_local_catalina_base</id>
						<phase>clean</phase>
						<configuration>
							<tasks>
								<delete dir="catalina_base/webapps/oscar" />
								<delete includeemptydirs="true" quiet="true">
									<fileset dir="catalina_base/logs" excludes=".gitignore" />
									<fileset dir="catalina_base/work" excludes=".gitignore" />
								</delete>
							</tasks>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-pmd-plugin</artifactId>
				<version>2.7.1</version>
				<!--
	      <executions>
	        <execution>
	          <phase>process-sources</phase>
	          <goals>
	            <goal>pmd</goal>
	            <goal>check</goal>
	          </goals>
	        </execution>
	      </executions>
	       -->
				<configuration>
					<sourceEncoding>ISO-8859-1</sourceEncoding>
					<targetJdk>1.6</targetJdk>
					<rulesets>
						<ruleset>utils/pmd_rules.xml</ruleset>
					</rulesets>
					<excludes>
						<exclude>**/*.jsp</exclude>
						<exclude>**/*.xml</exclude>
					</excludes>
				</configuration>
			</plugin>

			<!-- Run the Unit Tests -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<runOrder>alphabetical</runOrder>
					<skipTests>${skip.unit.tests}</skipTests>
					<argLine>${surefire.jacoco.args}</argLine>
					<excludes>
						<exclude>**/AR2005*.java</exclude>
						<exclude>**/OntarioMDSpec4DataTest.java</exclude>
						<exclude>**/ONAREnhancedBornConnectorTest.java</exclude>
						<exclude>**/*DaoTest.java</exclude>
						<exclude>**/*DAOTest.java</exclude>
						<exclude>**/*ModelTest.java</exclude>
						<exclude>**/*PopulatorTest.java</exclude>
					</excludes>
					<systemPropertyVariables>
						<oscar.dbinit.skip>${oscar.dbinit.skip}</oscar.dbinit.skip>
						<buildDirectory>${project.build.directory}</buildDirectory>
					</systemPropertyVariables>
				</configuration>
				<version>3.2.5</version>
			</plugin>

			<!-- Run the Integration Tests -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-failsafe-plugin</artifactId>
				<configuration>
					<runOrder>alphabetical</runOrder>
					<skipTests>${skip.it.tests}</skipTests>
					<argLine>${failsafe.jacoco.args}</argLine>
					<includes>
						<include>**/*IT.java</include>
						<include>**/*DaoTest.java</include>
						<include>**/*DAOTest.java</include>
						<include>**/*ModelTest.java</include>
						<include>**/*PopulatorTest.java</include>
					</includes>
					<systemPropertyVariables>
						<oscar.dbinit.skip>${oscar.dbinit.skip}</oscar.dbinit.skip>
						<buildDirectory>${project.build.directory}</buildDirectory>
					</systemPropertyVariables>
					<parallel>none</parallel>
				</configuration>
				<executions>
					<execution>
						<id>integration-test</id>
						<goals>
							<goal>integration-test</goal>
							<goal>verify</goal>
						</goals>
						<phase>integration-test</phase>
					</execution>
				</executions>
				<version>3.2.5</version>
			</plugin>

			<plugin>
				<groupId>org.oscarehr</groupId>
				<artifactId>oscar-il18n-check-plugin</artifactId>
				<version>1.0</version>
				<configuration>
					<referenceResourceFile>oscarResources_en.properties</referenceResourceFile>
					<otherResourceFiles>
						<param>oscarResources_es.properties</param>
						<param>oscarResources_fr.properties</param>
						<param>oscarResources_pt_BR.properties</param>
					</otherResourceFiles>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>com.google.code</groupId>
						<artifactId>google-api-translate-java</artifactId>
						<version>0.95</version>
					</dependency>
				</dependencies>
				<!--
                                <executions>
                                    <execution>
                                        <phase>process-sources</phase>
                                        <goals>
                                            <goal>check</goal>
                                        </goals>
                                    </execution>
                                </executions>
                -->
			</plugin>

			<plugin>
				<groupId>org.mortbay.jetty</groupId>
				<artifactId>maven-jetty-plugin</artifactId>
				<version>6.1.26</version>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>native2ascii-maven-plugin</artifactId>
				<version>1.0-beta-1</version>
				<executions>
					<execution>
						<id>native2ascii-utf8-resources</id>
						<goals>
							<goal>native2ascii</goal>
						</goals>
						<configuration>
							<dest>src/main/resources</dest>
							<src>src/main/resources/native-bundles</src>
							<encoding>UTF8</encoding>
							<include>uiResources_*.properties</include>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!--
                        <plugin>
                            <groupId>org.apache.axis2</groupId>
                            <artifactId>axis2-wsdl2code-maven-plugin</artifactId>
                            <version>1.5.4</version>
                            <executions>
                                <execution>
                                    <goals>
                                        <goal>wsdl2code</goal>
                                    </goals>
                                    <configuration>
                                        <packageName>test</packageName>
                                        <wsdlFile>src/main/resources/olis.wsdl</wsdlFile>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>

                <plugin>
                    <groupId>com.sun.tools.xjc.maven2</groupId>
                    <artifactId>maven-jaxb-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>generate</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <generatePackage>ca.ssha._2005.hial</generatePackage>
                        <schemaDirectory>${basedir}/src/main/webapp/olis</schemaDirectory>
                        <includeSchemas>
                            <includeSchema>response.xsd</includeSchema>
                        </includeSchemas>
                        <strict>true</strict>
                        <verbose>true</verbose>
                    </configuration>
                </plugin>
            -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<version>3.0.0</version>
				<executions>
					<execution>
						<id>timestamp-property</id>
						<goals>
							<goal>timestamp-property</goal>
						</goals>
						<configuration>
							<name>build.time</name>
							<pattern>yy.MM.dd-HHmmss</pattern>
							<locale>en_US</locale>
							<timeZone>Canada/Eastern</timeZone>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>
											org.apache.maven.plugins
										</groupId>
										<artifactId>
											maven-antrun-plugin
										</artifactId>
										<versionRange>
											[1.3,)
										</versionRange>
										<goals>
											<goal>run</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore></ignore>
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>
											org.codehaus.mojo
										</groupId>
										<artifactId>
											native2ascii-maven-plugin
										</artifactId>
										<versionRange>
											[1.0-beta-1,)
										</versionRange>
										<goals>
											<goal>native2ascii</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore></ignore>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
				<!-- NOTE: We are forcing the use of maven-deploy-plugin 2.7 here.
					We MUST update this version independently if you update other maven parts.
					Ideally, we remove the override in the future. It seems to be caused by a change by adding Kotlin -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-deploy-plugin</artifactId>
					<version>2.7</version>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<profiles>
		<profile>
			<id>jdk7</id>
			<activation>
				<activeByDefault>true</activeByDefault>
				<jdk>1.7</jdk>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<version>2.9</version>
						<configuration>
							<minmemory>128m</minmemory>
							<maxmemory>1g</maxmemory>
							<tags>
								<tag>
									<name>todo</name>
									<placement>a</placement>
									<head>To Do:</head>
								</tag>
							</tags>
							<quiet>true</quiet>
						</configuration>
						<executions>
							<execution>
								<phase>verify</phase>
								<goals>
									<goal>javadoc</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>jdk8</id>
			<activation>
				<activeByDefault>false</activeByDefault>
				<jdk>1.8</jdk>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<version>2.9</version>
						<configuration>
							<minmemory>128m</minmemory>
							<maxmemory>1g</maxmemory>
							<tags>
								<tag>
									<name>todo</name>
									<placement>a</placement>
									<head>To Do:</head>
								</tag>
							</tags>
							<quiet>true</quiet>
						</configuration>
						<executions>
							<execution>
								<phase>verify</phase>
								<goals>
									<goal>javadoc</goal>
								</goals>
								<configuration>
									<additionalparam>-Xdoclint:none</additionalparam>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<!-- jacoco plugin for code coverage reports -->
					<plugin>
						<groupId>org.jacoco</groupId>
						<artifactId>jacoco-maven-plugin</artifactId>
						<version>0.8.10</version>
						<configuration>
							<formats>XML</formats>
						</configuration>
						<executions>
							<!-- Surefire unit tests executions -->
							<execution>
								<goals>
									<goal>prepare-agent</goal>
								</goals>
								<configuration>
									<destFile>
										${project.build.directory}/jacoco-output/jacoco-unit-tests.exec
									</destFile>
									<propertyName>surefire.jacoco.args</propertyName>
								</configuration>
							</execution>
							<execution>
								<id>report</id>
								<phase>test</phase>
								<goals>
									<goal>report</goal>
								</goals>
								<configuration>
									<dataFile>
										${project.build.directory}/jacoco-output/jacoco-unit-tests.exec
									</dataFile>
									<outputDirectory>
										${project.reporting.outputDirectory}/jacoco-unit-test-coverage-report
									</outputDirectory>
								</configuration>
							</execution>
							<!-- Failsafe integration tests executions -->
							<execution>
								<id>before-integration-test-execution</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>prepare-agent</goal>
								</goals>
								<configuration>
									<destFile>
										${project.build.directory}/jacoco-output/jacoco-integration-tests.exec
									</destFile>
									<propertyName>failsafe.jacoco.args</propertyName>
								</configuration>
							</execution>
							<execution>
								<id>after-integration-test-execution</id>
								<phase>post-integration-test</phase>
								<goals>
									<goal>report</goal>
								</goals>
								<configuration>
									<dataFile>
										${project.build.directory}/jacoco-output/jacoco-integration-tests.exec
									</dataFile>
									<outputDirectory>
										${project.reporting.outputDirectory}/jacoco-integration-test-coverage-report
									</outputDirectory>
								</configuration>
							</execution>
							<!-- merge surefire and failsafe reports -->
							<execution>
								<id>merge-unit-and-integration</id>
								<phase>post-integration-test</phase>
								<goals>
									<goal>merge</goal>
								</goals>
								<configuration>
									<fileSets>
										<fileSet>
											<directory>${project.build.directory}/jacoco-output/</directory>
											<includes>
												<include>*.exec</include>
											</includes>
										</fileSet>
									</fileSets>
									<destFile>${project.build.directory}/jacoco-output/merged.exec</destFile>
								</configuration>
							</execution>
							<!-- create the final report in the default jacoco location -->
							<execution>
								<id>create-merged-report</id>
								<phase>post-integration-test</phase>
								<goals>
									<goal>report</goal>
								</goals>
								<configuration>
									<dataFile>${project.build.directory}/jacoco-output/merged.exec</dataFile>
									<outputDirectory>${project.reporting.outputDirectory}/jacoco</outputDirectory>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>
